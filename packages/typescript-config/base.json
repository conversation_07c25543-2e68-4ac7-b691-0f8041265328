{"compilerOptions": {"noEmitOnError": false, "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "noImplicitAny": false, "strict": true, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "types": ["node", "../infrastructure/typings.d.ts", "../infrastructure/openapi/typings.d.ts"], "experimentalDecorators": true, "noUnusedLocals": false, "noUnusedParameters": false}, "ts-node": {"esm": true}, "exclude": ["node_modules"]}