import * as packageJson from "./package.json";

// @ts-ignore
export const ENV = import.meta.env;

export const APP_VERSION = packageJson.version;

export const PROJECT_URLS: {
  MAIN_PROJECT: string;
  MAIN_PROJECT_API: string;
  GO_PROJECT: string;
  GO_PROJECT_API: string;
  GIANT_SCREEN: string;
  WS_URL: string;
  TIANDITU_KEY: string;
  WECHAT_LOGIN_REDIRECT_DOMAIN: string;
  APP_TARGET_RELEASE: string;
} = {
  MAIN_PROJECT: ENV.VITE_MAIN_PROJECT_URL as string,
  MAIN_PROJECT_API: ENV.VITE_MAIN_PROJECT_API_URL as string,
  GIANT_SCREEN: `${ENV.VITE_MAIN_PROJECT_URL}/screen.html?token=`,
  WS_URL: ENV.VITE_WEBSOCKET_URL as string,
  GO_PROJECT: ENV.VITE_GO_PROJECT_URL as string,
  GO_PROJECT_API: ENV.VITE_GO_PROJECT_API_URL as string,
  TIANDITU_KEY: ENV.VITE_TIANDITU_KEY as string,
  WECHAT_LOGIN_REDIRECT_DOMAIN: ENV.VITE_WECHAT_LOGIN_REDIRECT_DOMAIN as string,
  APP_TARGET_RELEASE: ENV.VITE_APP_TARGET_RELEASE as string,
};

// @ts-ignore
export const getClientRole = (): 'Company' | 'Manager' => {
  const url = window?.location?.href || "";
  if (url.includes(PROJECT_URLS.MAIN_PROJECT)) {
    return "Company";
  } else if (url.includes(PROJECT_URLS.GO_PROJECT)) {
    return "Manager";
  }
};

