// declare '@repo/ui/components/form'

declare module '@repo/ui/components/form' {
  import { CrudForm, StepForm, ModalForm, InputComponents, inputWidgetAttributes, inputComponents } from './form';

  export { CrudForm, StepForm, ModalForm, InputComponents, inputWidgetAttributes, inputComponents };
}

declare module '@repo/ui/components/form/inputComponents' {
  import {
    textInput,
    numberInput,
    selectInput,
    switchInput,
    checkboxInput,
    treeSelectInput,
    dateInput,
    dateRangeInput,
    tagInput,
    textareaInput,
    uploadInput,
    coverUploadInput,
    richInput,
  } from './form/inputComponents';

  export {
    textInput,
    numberInput,
    selectInput,
    switchInput,
    checkboxInput,
    treeSelectInput,
    dateInput,
    dateRangeInput,
    tagInput,
    textareaInput,
    uploadInput,
    coverUploadInput,
    richInput,
    areaSelectInput,
  };
}

declare module '@repo/ui/components/data-display' {
  import VideoPlayer from './data-display/videoPlayer.vue';
  import GameFrame from './data-display/gameFrame.vue';
  import DataDisplay from './data-display/index.vue';

  export { VideoPlayer, GameFrame, DataDisplay };
}

declare module '@repo/ui/components/data-display/components' {
  import {
    DateDisplay,
    ForeignDisplay,
    BooleanDisplay,
    AvatarDisplay,
    ImagePreviewDisplay,
  } from './data-display/components';

  export { DateDisplay, ForeignDisplay, BooleanDisplay, AvatarDisplay, ImagePreviewDisplay };
}

declare module '@repo/ui/components/description' {
  import Description from './description/index.vue';
  import Grouping from './description/grouping.vue';

  export { Description, Grouping };
}

declare module '@repo/ui/components/record-detail' {
  import RecordDetailDrawer from './record-detail/drawer.vue';
  import RecordDetail from './record-detail/index.vue';
  import RecordDetailModal from './record-detail/modal.vue';

  export { RecordDetail, RecordDetailModal, RecordDetailDrawer };
}

declare module '@repo/ui/components/table' {
  import AdvancedSearch from './table/advancedSearch.vue';
  import ColumnFilter from './table/columnFilter.vue';
  import CrudTable from './table/table.vue';
  import TableAction from './table/tableAction.vue';
  import TableLayout from './table/tableLayout.vue';
  import TableRowAction from './table/tableRowAction.vue';
  import {
    getColumnFilterable,
    columnTypeWidth,
    getColumnWidth,
    getBatchRowActions,
    getDefaultRowActions,
    getTableScrollX,
  } from './table/tableHelper';

  export {
    AdvancedSearch,
    ColumnFilter,
    CrudTable,
    TableAction,
    TableLayout,
    TableRowAction,
    getColumnFilterable,
    columnTypeWidth,
    getColumnWidth,
    getBatchRowActions,
    getDefaultRowActions,
    getTableScrollX,
  };
}
