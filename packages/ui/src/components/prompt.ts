import { h, ref } from 'vue';
import { Input, Message, Modal, DatePicker, Textarea, Select } from '@arco-design/web-vue';
import { isRegExp, isString } from 'lodash';

export type UsePromptOptions = {
  title?: string;
  placeholder?: string;
  raw?: string;
  inputPattern?: RegExp;
  message?: string;
  inputErrorMessage?: string;
  inputWidget?: string;
  inputWidgetProps?: Record<string, any>;
  validate?: (val: string) => boolean;
};

const usePrompt = () => {
  const prompt = async (config?: UsePromptOptions): Promise<any> => {
    const options: UsePromptOptions = {
      title: '请输入',
      placeholder: '请输入',
      ...config,
    };
    const val = ref(options.raw || '');
    return new Promise((resolve, reject) => {
      let cmp: any;
      switch (options?.inputWidget) {
        case 'textarea':
          cmp = Textarea;
          break;
        case 'date':
          cmp = DatePicker;
          break;
        case 'select':
          cmp = Select;
          break;
        default:
          cmp = Input;
          break;
      }
      Modal.confirm({
        title: options.title,
        content: () => {
          return h(
            'div',
            {
              style: {
                display: 'flex',
                flexDirection: 'column',
              },
            },
            [
              options.message && h('div', { class: 'mb-2' }, options.message),
              h(cmp, {
                autofocus: true,
                placeholder: options.placeholder,
                modelValue: val.value,
                ...options.inputWidgetProps,
                onInput: (content: string) => {
                  val.value = content;
                },
                onChange: (content: string) => {
                  val.value = content;
                },
                onUpdateModelValue: (content: string) => {
                  val.value = content;
                },
              }),
            ],
          );
        },
        onBeforeOk: () => {
          if (options.validate && !options.validate(val.value)) {
            return false;
          }

          if (options.inputPattern) {
            let regExp = options.inputPattern;
            if (!isRegExp(regExp)) {
              regExp = new RegExp(regExp);
            }
            if (!regExp.test(val.value)) {
              Message.error(options.inputErrorMessage || '输入格式不正确');
              return false;
            }
          }
          if (isString(val.value)) {
            resolve(val.value?.trim());
          } else {
            resolve(val.value);
          }
          return true;
        },
        onCancel: () => {
          reject();
        },
      });
    });
  };

  return {
    prompt,
  };
};

export default usePrompt;
