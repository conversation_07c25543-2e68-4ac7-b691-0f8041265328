const custom = [
  // 红色系
  '#ffebee', // 0 极浅红
  '#ffcdd2', // 1 浅粉红
  '#ef9a9a', // 2 粉红
  '#f43f5e', // 3 火龙果红
  '#ef4444', // 4 番茄红
  '#f53f3f', // 5 鲜艳红
  '#e11d48', // 6 玫瑰红
  '#dc2626', // 7 大红
  '#d8196c', // 8 洋红
  '#be123c', // 9 砖红
  '#9f1239', // 10 酒红
  '#7f1d1d', // 11 深红

  // 橙色/黄色系
  '#fff8e1', // 12 极浅黄
  '#ffecb3', // 13 浅黄
  '#facc15', // 14 柠檬黄
  '#eab308', // 15 香蕉黄
  '#ffb400', // 16 金色
  '#fcd34d', // 17 芥末黄
  '#ff7d00', // 18 鲜艳橙
  '#f97316', // 19 橘黄
  '#ff5722', // 20 深橙
  '#ea580c', // 21 焦橙
  '#a16207', // 22 卡其色
  '#b45309', // 23 沙色
  '#c2410c', // 24 棕色
  '#7c2d12', // 25 深棕

  // 绿色系
  '#f0fdf4', // 26 极浅绿
  '#dcfce7', // 27 浅绿
  '#5eead4', // 28 薄荷青
  '#2dd4bf', // 29 水鸭绿
  '#7bc616', // 30 亮绿
  '#22c55e', // 31 森林绿
  '#00b42a', // 32 鲜艳绿
  '#00a854', // 33 草绿
  '#10b981', // 34 薄荷绿
  '#059669', // 35 海绿
  '#14b8a6', // 36 绿松石
  '#17a398', // 37 孔雀绿
  '#047857', // 38 深绿
  '#064e3b', // 39 墨绿

  // 蓝色系
  '#f0f9ff', // 40 极浅蓝
  '#e0f2fe', // 41 浅蓝
  '#bae6fd', // 42 天蓝
  '#38bdf8', // 43 亮蓝
  '#168cff', // 44 天蓝
  '#0ea5e9', // 45 清亮蓝
  '#06b6d4', // 46 水蓝色
  '#3b82f6', // 47 靛青
  '#165dff', // 48 鲜艳蓝
  '#0891b2', // 49 海洋蓝
  '#6366f1', // 50 深蓝
  '#1d4ed8', // 51 海军蓝
  '#1e3a8a', // 52 午夜蓝

  // 紫色系
  '#f5f3ff', // 53 极浅紫
  '#ede9fe', // 54 浅紫
  '#a78bfa', // 55 淡紫
  '#8b5cf6', // 56 中紫
  '#7c3aed', // 57 葡萄紫
  '#9333ea', // 58 蓝紫
  '#a855f7', // 59 兰花紫
  '#bf46ef', // 60 紫罗兰
  '#b71de8', // 61 紫红
  '#7816ff', // 62 鲜艳紫
  '#6d28d9', // 63 深紫
  '#4c1d95', // 64 暗紫

  // 粉色系
  '#fdf2f8', // 65 极浅粉
  '#fce7f3', // 66 浅粉
  '#fbcfe8', // 67 粉红
  '#f9a8d4', // 68 亮粉
  '#eb0aa4', // 69 鲜艳粉
  '#db2777', // 70 深粉
  '#be185d', // 71 梅红

  // 灰色系
  '#f9fafb', // 72 极浅灰
  '#f3f4f6', // 73 浅灰
  '#e5e7eb', // 74 淡灰
  '#cbd5e1', // 75 灰
  '#9ca3af', // 76 中灰
  '#6b7280', // 77 深灰
  '#4b5563', // 78 暗灰
  '#374151', // 79 炭灰
  '#1f2937', // 80 黑灰
];

const avatarColors = [
  '#f53f3f',
  '#7816ff',
  '#00b42a',
  '#eb0aa4',
  '#ff7d00',
  '#165dff',
  '#7bc616',
  '#86909c',
  '#b71de8',
  '#0fc6c2',
  '#ffb400',
  '#168cff',
  '#ff5722',
];

const lightColorIndex = [
  // 红色系浅色
  0, 1, 2,
  // 橙色/黄色系浅色
  12, 13, 14, 15, 16, 17,
  // 绿色系浅色
  26, 27, 28, 29, 30,
  // 蓝色系浅色
  40, 41, 42, 43, 44, 45, 46,
  // 紫色系浅色
  53, 54, 55,
  // 粉色系浅色
  65, 66, 67, 68,
  // 灰色系浅色
  72, 73, 74,
];

const darkIndex = [
  // 红色系深色
  7, 8, 9, 10, 11,
  // 橙色/黄色系深色
  20, 21, 22, 23, 24, 25,
  // 绿色系深色
  36, 37, 38, 39,
  // 蓝色系深色
  50, 51, 52,
  // 紫色系深色
  60, 61, 62, 63, 64,
  // 粉色系深色
  70, 71,
  // 灰色系深色
  77, 78, 79, 80,
];

// 随机颜色
const randomColor = (colorIndex: number = Math.floor(Math.random() * custom.length)): string => {
  if (colorIndex === -1) return '';
  // eslint-disable-next-line default-case
  switch (colorIndex) {
    case -1:
      return '';
    case -2:
      return '#000000';
  }
  return custom[colorIndex];
};
// 随机浅色
const randomLightColor = () => custom[lightColorIndex[Math.floor(Math.random() * lightColorIndex.length)]];

// 随机深色
const randomDarkColor = () => custom[darkIndex[Math.floor(Math.random() * darkIndex.length)]];

const randomHighChromaColor = () => avatarColors[Math.floor(Math.random() * avatarColors.length)];

const randomHighChromaColorByCharLength = (str: string) => {
  const hash = str.split('').reduce((acc, char) => char.charCodeAt(0) + acc, 0);
  const index = hash % avatarColors.length || Math.floor(Math.random() * avatarColors.length);
  return avatarColors[index];
};

// eslint-disable-next-line import/prefer-default-export
export { randomColor, randomLightColor, randomDarkColor, randomHighChromaColor, randomHighChromaColorByCharLength };
