<script setup lang="ts">
  import { computed, ref } from 'vue';

  const props = defineProps({
    message: {
      type: String,
      default: '系统错误，请联系管理员',
    },
    detail: {
      type: String,
      default: '',
    },
  });

  const detailVisible = ref(false);
</script>

<template>
  <div>
    <p>{{ props.message }}</p>
    <a-button v-if="detail" size="mini" class="mt-4 mb-1" @click="() => (detailVisible = !detailVisible)">
      {{ detailVisible ? '隐藏' : '显示' }}详细错误信息
    </a-button>
    <div v-if="detailVisible" class="rounded-lg bg-slate-100 p-2 my-2">
      {{ detail || '暂无详细错误信息' }}
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
