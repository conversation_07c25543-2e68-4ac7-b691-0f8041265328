import { h, ref } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import errorModalContent from './errorModalContent.vue';

const showError = (error: any) => {
  // console.log(error);
  const message = error.message || error.reason || '';
  const isStartWithChinese = /^[\u4e00-\u9fa5]/;

  if (isStartWithChinese.test(message)) {
    Message.error(message);
    return;
  }

  Modal.error({
    title: '非常抱歉，系统走神了',
    content: () => h(errorModalContent, { message: '系统开小差了，请稍后再试～ 或联系管理员 :(', detail: message }),
  });
};

const initWindowErrorHandler = () => {
  // window.onerror = (message, source, lineno, colno, error) => {
  //   console.log(444, message);
  // };

  window.handleRequestError = (message: string, error: any) => {
    showError({ message, error });
  };

  // window.addEventListener('unhandledrejection', (event) => {
  //   showError(event);
  // });
};

export { showError, initWindowErrorHandler, errorModalContent };
