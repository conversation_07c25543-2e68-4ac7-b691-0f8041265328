import { merge } from 'lodash';

export type ItemAttachmentTypeOptions = {
  defaultOptions?: string[];
  allowCreate?: boolean;
  allowSearch?: boolean;
  multiple?: boolean;
};

export const getItemAttachmentsColumns = (options: ItemAttachmentTypeOptions) => {
  options = merge(
    {
      defaultOptions: [],
      allowCreate: true,
      allowSearch: true,
      multiple: true,
    },
    options || {},
  );
  return [
    {
      label: '类型',
      key: 'category',
      inputWidget: 'selectInput',
      inputWidgetProps: {
        placeholder: '请选择或输入',
        options: options.defaultOptions || [],
        ...options,
        multiple: false,
      },
    },
    {
      label: '附件',
      key: 'attachments',
      inputWidget: 'uploadInput',
      inputWidgetProps: {
        draggable: false,
        multiple: options.multiple !== false,
      },
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
  ];
};

export const ItemTagsInput = '';
