<script setup lang="ts">
  import 'vue-cropper/dist/index.css';
  import { VueCropper } from 'vue-cropper';
  import { computed, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modalVisible: {
      type: Boolean,
      default: false,
    },
    raw: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['update:modalVisible', 'finish']);
  const cropperRef = ref<any>(null);
  const visible = computed({
    get() {
      return props.modalVisible;
    },
    set(value) {
      emit('update:modalVisible', value);
    },
  });

  /**
   * :output-size="cropperOptions.size"
   *       :output-type="cropperOptions.outputType"
   *       :info="true"
   *       :full="cropperOptions.full"
   *       :can-move="cropperOptions.canMove"
   *       :can-move-box="cropperOptions.canMoveBox"
   *       :original="cropperOptions.original"
   *       :auto-crop="cropperOptions.autoCrop"
   *       :fixed="cropperOptions.fixed"
   *       :fixed-number="cropperOptions.fixedNumber"
   *       :center-box="cropperOptions.centerBox"
   *       :info-true="cropperOptions.infoTrue"
   *       :fixed-box="cropperOptions.fixedBox"
   *
   *
   *       info: true, // 裁剪框的大小信息
   *           outputSize: 1, // 裁剪生成图片的质量
   *           outputType: 'jpeg', // 裁剪生成图片的格式
   *           canScale: false, // 图片是否允许滚轮缩放
   *           autoCrop: true, // 是否默认生成截图框
   *           // autoCropWidth: 300, // 默认生成截图框宽度
   *           // autoCropHeight: 200, // 默认生成截图框高度
   *           // fixedBox: true, // 固定截图框大小 不允许改变
   *           fixed: false, // 是否开启截图框宽高固定比例
   *           fixedNumber: [7, 5], // 截图框的宽高比例
   *           full: true, // 是否输出原图比例的截图
   *           canMoveBox: true, // 截图框能否拖动
   *           original: false, // 上传图片按照原始比例渲染
   *           centerBox: true, // 截图框是否被限制在图片里面
   *           infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
   */
  const defaultOptions = {
    outputSize: 1,
    info: true,
    outputType: 'jpeg',
    full: true,
    canMove: true,
    canMoveBox: true,
    original: false,
    autoCrop: true,
    fixed: false,
  };

  const handleBeforeOk = async () => {
    const blobData = await new Promise((resolve, reject) => {
      cropperRef.value.getCropBlob((blob: any) => {
        if (blob) {
          resolve(blob);
        } else {
          reject();
        }
      });
    });

    if (blobData) {
      emit('finish', blobData);
      return true;
    }

    Message.error('裁剪失败');
    return false;
  };

  const handleClose = () => {
    visible.value = false;
  };
</script>

<template>
  <a-modal
    v-model:visible="visible"
    width="60%"
    title="图片裁剪"
    style="z-index: 3102"
    :on-before-ok="handleBeforeOk"
    @close="handleClose"
    @cancel="handleClose"
  >
    <div class="flex flex-col">
      <vue-cropper
        ref="cropperRef"
        :img="raw"
        v-bind="{
          ...defaultOptions,
          ...$attrs,
        }"
        style="min-height: 60vh"
      />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
