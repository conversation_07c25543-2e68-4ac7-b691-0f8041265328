type FileCategory =
  | 'image'
  | 'document'
  | 'video'
  | 'audio'
  | 'archive'
  | 'spreadsheet'
  | 'presentation'
  | 'text'
  | 'code'
  | 'other';

const getFileType = (url: string = ''): FileCategory => {
  if (!url || url.indexOf('.') === -1) {
    return 'other';
  }
  const path = url.split('?')[0].split('#')[0];
  const suffix = path.split('.').pop()?.toLowerCase();
  if (!suffix) {
    return 'other';
  }
  const imgTypeSet = new Set(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg', 'ico', 'avif']);
  const docTypeSet = new Set(['pdf', 'doc', 'docx', 'odt', 'rtf']);
  const spreadsheetTypeSet = new Set(['xls', 'xlsx', 'csv', 'ods']);
  const presentationTypeSet = new Set(['ppt', 'pptx', 'odp']);
  const videoTypeSet = new Set(['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'mpg', 'mpeg']);
  const audioTypeSet = new Set(['mp3', 'wav', 'aac', 'flac', 'ogg', 'wma', 'm4a']);
  const archiveTypeSet = new Set(['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz']);
  const textTypeSet = new Set(['txt', 'md', 'json', 'xml', 'log', 'ini', 'cfg']);
  const codeTypeSet = new Set([
    'js',
    'ts',
    'jsx',
    'tsx',
    'html',
    'htm',
    'css',
    'scss',
    'less',
    'vue',
    'py',
    'java',
    'c',
    'cpp',
    'h',
    'hpp',
    'php',
    'rb',
    'go',
    'rs',
    'sh',
  ]);

  if (imgTypeSet.has(suffix)) {
    return 'image';
  }
  if (docTypeSet.has(suffix)) {
    return 'document';
  }
  if (spreadsheetTypeSet.has(suffix)) {
    return 'spreadsheet';
  }
  if (presentationTypeSet.has(suffix)) {
    return 'presentation';
  }
  if (videoTypeSet.has(suffix)) {
    return 'video';
  }
  if (audioTypeSet.has(suffix)) {
    return 'audio';
  }
  if (archiveTypeSet.has(suffix)) {
    return 'archive';
  }
  if (codeTypeSet.has(suffix)) {
    return 'code';
  }
  if (textTypeSet.has(suffix)) {
    return 'text';
  }
  return 'other';
};

export default getFileType;
