<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { getElectronApi, isElectron } from '@repo/infrastructure/electron';
  import { Message } from '@arco-design/web-vue';

  const modalVisible = ref(false);
  const updateStatus = ref<'waiting' | 'pending' | 'success' | 'failed' | 'cancelled'>('waiting');
  const okText = computed(() => {
    switch (updateStatus.value) {
      case 'waiting':
        return '立即升级';
      case 'pending':
        return '升级中... 请稍后';
      case 'success':
        return '完成';
      case 'failed':
        return '升级失败';
      default:
        return '立即升级';
    }
  });

  const handleCancel = () => {
    updateStatus.value = 'waiting';
  };

  const handleBeforeOk = async () => {
    if (updateStatus.value === 'pending') {
      return false;
    }

    // updateStatus.value = 'pending';
    const electronApi = getElectronApi();
    // if (!electronApi) {
    //   updateStatus.value = 'failed';
    //   Message.error('当前环境不支持升级');
    //   return false;
    // }

    if (updateStatus.value === 'success') {
      electronApi.send('quitAndInstall');
      return false;
    }

    // try {
    //   await electronApi.checkForUpdates();
    //   updateStatus.value = 'success';
    //
    //   // 重启应用
    // } catch (error: any) {
    //   Message.error(`升级失败，请稍后重试: ${error.message}`);
    //   updateStatus.value = 'failed';
    // }
    return false;
  };

  onMounted(async () => {
    if (isElectron) {
      const electronApi = getElectronApi();

      // if (isElectron) {
      //   console.log(1111);
      // "checking-for-update": () => void;
      // "update-not-available": (info: UpdateInfo) => void;
      // "update-available": (info: UpdateInfo) => void;
      // "update-downloaded": (event: UpdateDownloadedEvent) => void;
      // "download-progress": (info: ProgressInfo) => void;
      // "update-cancelled": (info: UpdateInfo) => void;
      // "appimage-filename-updated": (path: string) => void;

      // }

      electronApi.on('checking-for-update', () => {
        updateStatus.value = 'waiting';
        Message.info('正在检查更新...');
      });

      electronApi.on('update-not-available', (payload: { message: any }) => {
        updateStatus.value = 'waiting';
        Message.clear();
        Message.success(`当前已是最新版本: ${payload.message.version}`);
      });

      electronApi.on('update-available', (payload: { message: any }) => {
        updateStatus.value = 'waiting';
        Message.clear();
        Message.success(`检测到新版本: ${payload.message.version}`);
        modalVisible.value = true;
      });

      electronApi.on('update-downloaded', () => {
        updateStatus.value = 'success';
        Message.success('系统自动升级成功，请重启应用');
      });

      electronApi.on('update-cancelled', () => {
        updateStatus.value = 'cancelled';
        Message.error('升级已取消');
      });

      electronApi.on('download-progress', (payload: { message: any }) => {
        updateStatus.value = 'pending';
        Message.clear();
        Message.info(`正在下载更新: ${payload.message.percent}%`);
      });

      setTimeout(async () => {
        await electronApi.send('checkForUpdate');
      }, 1000);
    }
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :render-to-body="true"
    simple
    title="系统更新"
    cancel-text="暂不升级"
    :ok-loading="updateStatus === 'pending'"
    :ok-text="okText"
    :hide-cancel="updateStatus === 'pending' || updateStatus === 'success' || updateStatus === 'failed'"
    :on-before-ok="handleBeforeOk"
    :mask-closable="false"
    :closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
  >
    <p v-if="updateStatus === 'waiting'">检测到新版本，是否立即升级？</p>
    <p v-else-if="updateStatus === 'pending'">正在升级中，请稍后...</p>
    <p v-else-if="updateStatus === 'success'">系统自动升级成功，请重启应用后生效</p>
    <p v-else-if="updateStatus === 'failed'">升级失败，请稍后重试</p>
    <p v-else-if="updateStatus === 'cancelled'">升级已取消</p>
  </a-modal>
</template>

<style scoped lang="scss"></style>
