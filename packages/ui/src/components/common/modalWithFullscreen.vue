<script setup lang="ts">
  import { computed, ref } from 'vue';

  const props = defineProps({
    fullscreen: {
      type: Boolean,
      default: false,
    },
  });

  const modalFullscreen = ref(props.fullscreen);

  const handleToggleFullscreen = () => {
    modalFullscreen.value = !modalFullscreen.value;
  };
</script>

<template>
  <a-modal v-bind="$attrs" :fullscreen="modalFullscreen">
    <template #title>
      <div class="w-full flex justify-between">
        <slot name="title">{{ $attrs.title }}</slot>
        <a-button size="mini" type="secondary" class="mr-8" @click="handleToggleFullscreen">
          <template #icon>
            <IconFullscreen v-if="!modalFullscreen" />
            <IconFullscreenExit v-else />
          </template>
          {{ modalFullscreen ? '退出全屏' : '全屏' }}
        </a-button>
      </div>
    </template>
    <template #footer>
      <slot name="footer"></slot>
    </template>
    <slot></slot>
  </a-modal>
</template>

<style scoped lang="scss"></style>
