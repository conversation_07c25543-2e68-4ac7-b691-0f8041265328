<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  // import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import { Message } from '@arco-design/web-vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { AttachmentToUpload } from '@repo/infrastructure/upload';

  const props = defineProps({
    saveType: {
      type: String,
    },
    subFolder: {
      type: String,
      required: true,
    },
    accept: {
      type: String,
      default: '*/*',
    },
    buttonText: {
      type: String,
      default: '上传',
    },
    maxSize: {
      // kb
      type: Number,
      default: 1024 * 500,
    },
    minSize: {
      type: Number,
      default: 0,
    },
    showFileList: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['uploaded', 'start']);

  const uploadRef = ref<any>(null);
  const items = ref<any[]>([]);
  const uploadedResources = ref<any[]>([]);
  const inProgressItem = ref<Record<string, any>>({});
  const processor: IAttachmentProcessor = getOssProcessor();
  const { loading, setLoading } = useLoading();

  const checkFileSize = (file: File) => {
    if (file.size / 1024 > props.maxSize) {
      Message.error(`文件大小不能超过 ${props.maxSize / 1024}MB`);
      return false;
    }

    if (file.size / 1024 < props.minSize) {
      Message.error(`文件大小不能小于 ${props.minSize / 1024}MB`);
      return false;
    }

    return true;
  };

  const avgProgress = computed(() => {
    const progressList = Object.values(inProgressItem.value).map((item) => item.progress);
    const progress =
      (progressList.length ? progressList.reduce((prev, next) => prev + next) / progressList.length : 0) * 100;
    return Number(progress.toFixed(2));
  });

  const handleUpload = async (options: any) => {
    if (!options?.fileItem?.file) return;

    setLoading(true);
    emit('start', options.fileItem.file);
    try {
      const index = options?.fileItem.uid;
      inProgressItem.value[index] = {
        coverImage: '',
        name: options.fileItem.file?.name,
        progress: 0,
      };

      const attachment: AttachmentToUpload = {
        file: options.fileItem?.file,
        saveType: props.saveType || 'attachments',
        subFolder: props.subFolder,
        handleUploadProgress: (progress) => {
          options.onProgress?.(progress);

          inProgressItem.value[index].progress = progress;
        },
        handleUploadComplete: async (url: any, idx: any, uploadedResource: any) => {
          options.onSuccess?.(url);
          items.value.push({
            url,
            name: options.fileItem.file?.name,
          });
          uploadedResources.value.push(uploadedResource);
          delete inProgressItem.value[index];
          if (Object.values(inProgressItem.value).length === 0) {
            emit('uploaded', items.value, uploadedResources.value, options?.fileItem?.file);
            Message.success(`成功上传 ${items.value.length} 个文件`);
            items.value = [];
            uploadedResources.value = [];
          }
        },
      };

      await processor.uploadLarge(attachment);
    } catch (e: any) {
      setLoading(false);
    }
  };

  watch(
    () => avgProgress.value,
    (progress) => {
      if (progress >= 100 || progress <= 0) {
        setLoading(false);
      }
    },
  );

  defineExpose({
    items,
    processor,
    loading,
  });
</script>

<template>
  <a-upload
    ref="uploadRef"
    :accept="accept"
    :custom-request="handleUpload as any"
    :on-before-upload="checkFileSize"
    :draggable="false"
    :show-file-list="showFileList"
    multiple
    v-bind="$attrs"
  >
    <template #upload-button>
      <slot name="upload-button">
        <a-button
          :loading="loading"
          size="mini"
          type="primary"
          style="position: relative; top: -1px"
          :disabled="disabled"
        >
          <template #icon>
            <IconUpload />
          </template>
          {{ avgProgress > 0 ? `上传中: ${avgProgress}%` : buttonText }}
        </a-button>
      </slot>
    </template>
  </a-upload>
</template>

<style lang="less" scoped></style>
