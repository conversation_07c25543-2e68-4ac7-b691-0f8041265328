<script lang="ts" setup>
  import { ref, reactive, computed, watch, nextTick } from 'vue';

  const editVisible = ref(false);
  const currentFile = ref<File>();
  const imgRef = ref<HTMLImageElement | null>(null);
  const containerRef = ref<HTMLElement | null>(null); // 外部容器

  const imageData = ref<string>('');
  const currentType = ref<string>('customized');

  const cropBox = reactive({
    left: 0,
    top: 0,
    width: 100,
    height: 100,
  });

  const imgRect = ref({
    left: 0,
    top: 0,
    width: 100,
    height: 100,
  });

  const cropBoxStyle = computed(() => ({
    left: `${cropBox.left}px`,
    top: `${cropBox.top}px`,
    width: `${cropBox.width}px`,
    height: `${cropBox.height}px`,
  }));

  let isDragging = false;
  let isResizing = false;
  let startX = 0;
  let startY = 0;

  const onDrag = (e: MouseEvent) => {
    if (!isDragging) return;

    const dx = e.clientX - startX;
    const dy = e.clientY - startY;

    let newLeft = cropBox.left + dx;
    let newTop = cropBox.top + dy;

    // 限制裁剪框不超出图片左上边界
    newLeft = Math.max(imgRect.value.left, newLeft);
    newTop = Math.max(imgRect.value.top, newTop);

    // 限制裁剪框右下边界不超出图片右下边界
    newLeft = Math.min(newLeft, imgRect.value.left + imgRect.value.width - cropBox.width);
    newTop = Math.min(newTop, imgRect.value.top + imgRect.value.height - cropBox.height);

    cropBox.left = newLeft;
    cropBox.top = newTop;

    startX = e.clientX;
    startY = e.clientY;
  };

  const onResize = (e: MouseEvent) => {
    if (!isResizing) return;

    const dx = e.clientX - startX;
    const dy = e.clientY - startY;

    const newWidth = cropBox.width + dx;
    const newHeight = cropBox.height + dy;

    const maxWidth = imgRect.value.left + imgRect.value.width - cropBox.left;
    const maxHeight = imgRect.value.top + imgRect.value.height - cropBox.top;

    cropBox.width = Math.max(30, Math.min(newWidth, maxWidth));
    cropBox.height = Math.max(30, Math.min(newHeight, maxHeight));

    startX = e.clientX;
    startY = e.clientY;
  };

  const stopAction = () => {
    isDragging = false;
    isResizing = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mousemove', onResize);
    document.removeEventListener('mouseup', stopAction);
  };

  const loadImagePreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      imageData.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  };

  const startDrag = (e: MouseEvent) => {
    isDragging = true;
    startX = e.clientX;
    startY = e.clientY;
    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopAction);
  };

  const startResize = (e: MouseEvent) => {
    isResizing = true;
    startX = e.clientX;
    startY = e.clientY;
    if (currentType.value === 'customized') document.addEventListener('mousemove', onResize);
    document.addEventListener('mouseup', stopAction);
  };

  const handlePreUpload = (file: File) => {
    currentFile.value = file;
    loadImagePreview(file);
    editVisible.value = true;
  };

  const handleClick = (type: 'customized' | 'a') => {
    currentType.value = type;
    if (type === 'a') {
      cropBox.width = 295;
      cropBox.height = 413;
    }
  };

  watch(imageData, async (val) => {
    if (!val) return;
    await nextTick();

    const img = imgRef.value;
    const container = containerRef.value;
    if (!img || !container) return;

    cropBox.left = img.offsetLeft;
    cropBox.top = img.offsetTop;
    cropBox.width = img.clientWidth;
    cropBox.height = img.clientHeight;

    imgRect.value.left = img.offsetLeft;
    imgRect.value.top = img.offsetTop;
    imgRect.value.width = img.clientWidth;
    imgRect.value.height = img.clientHeight;
  });

  watch(editVisible, (visible) => {
    if (!visible) {
      currentFile.value = undefined;
      imageData.value = '';
      cropBox.left = 0;
      cropBox.top = 0;
      cropBox.width = 100;
      cropBox.height = 100;
    }
  });
</script>

<template>
  <a-upload :show-file-list="false" :limit="1" :on-before-upload="handlePreUpload">
    <template #upload-button>
      <div
        :class="[
          'w-[100px] h-[100px] rounded-lg border-2 border-dashed border-gray-300 flex flex-col',
          'justify-center items-center cursor-pointer hover:border-blue-500 bg-gray-50',
        ]"
      >
        <div v-if="!uploadedUrl" class="text-center text-gray-400">
          <icon-plus size="24" />
          <div class="text-xs mt-1">上传图片</div>
        </div>
        <img v-else :src="uploadedUrl" class="w-full h-full object-cover rounded-lg" alt="已上传图片" />
      </div>
    </template>
  </a-upload>

  <a-modal
    v-model:visible="editVisible"
    :footer="false"
    :title="false"
    :closable="false"
    width="60%"
    :body-style="{ padding: 0 }"
  >
    <div class="w-full flex justify-center h-[600px] relative select-none p-4 space-x-4">
      <div ref="containerRef" class="w-3/4 h-full relative overflow-hidden flex justify-center items-center">
        <img ref="imgRef" :src="imageData" class="object-contain max-w-full max-h-full" />
        <div class="absolute border-2 border-dashed border-red-500" :style="cropBoxStyle" @mousedown="startDrag">
          <div
            class="absolute w-2 h-2 rounded-full border border-black bg-white cursor-se-resize"
            style="bottom: -4px; right: -4px"
            @mousedown.stop="startResize"
          />
        </div>
      </div>
      <div class="w-1/4 h-full shadow-lg p-2 text-white text-center flex flex-col space-y-2 relative">
        <div class="w-full max-w-120px bg-gradient-to-br from-blue-300/60 to-blue-400 p-2" @click="handleClick('a')">
          证件照
        </div>
        <div
          class="w-full max-w-120px bg-gradient-to-br from-blue-300/60 to-blue-400 p-2"
          @click="handleClick('customized')"
        >
          自定义
        </div>
        <!--action-->
        <div class="absolute bottom-1 w-full">
          <div class="flex justify-end space-x-2 mr-4">
            <a-button size="medium" shape="round" type="primary"> 确认 </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
