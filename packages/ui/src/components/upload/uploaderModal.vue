<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import Uploader from './uploader.vue';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
    },
    btnText: {
      type: String,
      default: '查看或上传',
    },
    btnTextEmpty: {
      type: String,
      default: '上传',
    },
    subFolder: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = ref(false);
  const uploadedList = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });
  const btnTextVisible = computed(() => (uploadedList.value?.length ? props.btnText : props.btnTextEmpty));

  const handleShowUploadModal = () => {
    visible.value = true;
  };
</script>

<template>
  <a-button size="mini" @click="handleShowUploadModal">
    <template #icon>
      <IconAttachment v-if="uploadedList?.length" />
      <IconUpload v-else />
    </template>
    {{ btnTextVisible }}
  </a-button>
  <a-modal v-model:visible="visible" :title="btnTextVisible" hide-cancel ok-text="完成">
    <uploader v-if="visible" v-model="uploadedList" :sub-folder="subFolder" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
