<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { ref } from 'vue';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AvatarDisplay } from '../data-display/components';
  import ImageCropper from '../utils/imageCropper.vue';

  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();
  const { userInfo } = userStore;
  const processor: IAttachmentProcessor = getOssProcessor();

  const rawImage = ref(userInfo.avatar);
  const cropperVisible = ref(false);
  const selected = ref([]);

  const handleAvatarSelected = (items: any) => {
    rawImage.value = items[0]?.url;
    cropperVisible.value = true;
    selected.value = [];
  };

  const handleAvatarCropped = async (blobData: any) => {
    const file = new File([blobData], 'avatar.jpg', { type: 'image/jpeg' });
    setLoading(true);
    try {
      const url = await processor.uploadSimply(
        {
          file,
        },
        `user-avatar/${userInfo.id % 9}`,
      );

      await request('/org/session/profile', {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...userInfo,
          avatar: url,
        },
      });

      userStore.setInfo({
        ...userInfo,
        avatar: url,
      });

      userInfo.avatar = url;
    } finally {
      setLoading(false);
    }
  };
</script>

<template>
  <a-spin :loading="loading">
    <avatar-display :user-info="userInfo" size="100" v-bind="$attrs" :show-trigger="true">
      <template #trigger-icon>
        <a-upload
          v-model:file-list="selected"
          accept="image/*"
          :auto-upload="false"
          :show-file-list="false"
          @change="handleAvatarSelected"
        >
          <template #upload-button>
            <IconCamera />
          </template>
        </a-upload>
      </template>
    </avatar-display>
    <image-cropper
      v-if="rawImage && cropperVisible"
      v-model:visible="cropperVisible"
      :fixed-number="[1, 1]"
      :fixed="true"
      :raw="rawImage"
      @finish="handleAvatarCropped"
    />
  </a-spin>
</template>

<style scoped lang="scss"></style>
