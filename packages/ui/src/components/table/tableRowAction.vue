<template>
  <a-table-column title="操作" :width="columnWidth" fixed="right">
    <template #cell="{ record }">
      <row-actions
        v-bind="$attrs"
        :row-action-width="columnWidth"
        :schema="schema"
        :load-data="loadData"
        :row-actions="schema.rowActions"
        :set-loading="setLoading"
        :record="record"
        :module-path="modulePath"
        @row-action="handleRowAction"
      />
    </template>
  </a-table-column>
</template>

<script lang="ts" setup>
  import { PropType } from 'vue';
  import { Schema, TableRowAction } from '@repo/infrastructure/types';
  import RowActions from './rowActions.vue';

  const props = defineProps({
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    rowActionWidth: {
      type: Number,
      default: 220,
    },
    rowActions: {
      type: Array as PropType<TableRowAction[]>,
      default: () => [],
    },
    loadData: {
      type: Function as PropType<() => any>,
      required: false,
    },
    actionsKeys: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    table: {
      type: Object,
      required: true,
    },
    setLoading: {
      type: Function as PropType<(loading: boolean) => void>,
      required: false,
    },
    modulePath: {
      type: String as PropType<string>,
      required: false,
    },
  });

  const emits = defineEmits(['rowAction']);

  const columnWidth = props.schema?.listViewProps?.rowActionWidth || props.rowActionWidth;

  const handleRowAction = (action: TableRowAction, record: Record<string, any>) => {
    emits('rowAction', action, record);
  };
</script>

<style scoped></style>
