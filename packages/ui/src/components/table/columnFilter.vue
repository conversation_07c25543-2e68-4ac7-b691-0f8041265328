<template>
  <div class="custom-filter">
    <a-space direction="vertical" fill>
      <a-select v-if="searchColumnDefine.filterProps?.supportOperators?.length" v-model="currentOperator">
        <a-option v-for="(op, oi) in searchColumnDefine.filterProps.supportOperators" :key="oi" :value="op">
          {{ searchColumnDefine.label }}
          {{ FILTER_OPERATORS[`common.comparator.${op}`] }}
        </a-option>
      </a-select>
      <component
        :is="component"
        v-model="inputValue"
        class="custom-filter-component"
        :schema-field="searchColumnDefine"
        :allow-clear="true"
      />
      <div class="custom-filter-footer">
        <a-space fill>
          <a-button type="outline" @click="handleFilterConfirm">确定</a-button>
          <a-button @click="handleFilterReset">重置</a-button>
        </a-space>
      </div>
    </a-space>
  </div>
</template>

<script setup lang="ts">
  import { ref, PropType, Ref, computed } from 'vue';
  import { isArray, isObject } from 'lodash';
  import { Schema, QueryFilter, TableColumn } from '@repo/infrastructure/types';
  import { useTableFilterStore } from '@repo/infrastructure/store';
  import { FILTER_OPERATORS, getColumnFilterable } from './tableHelper';

  const props = defineProps({
    filterConfig: {
      type: Object,
      required: true,
    },
    column: {
      type: Object as PropType<TableColumn>,
      required: true,
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    scene: {
      type: String,
    },
  });

  const emits = defineEmits(['filterChange']);

  const { component, columnDefine } = getColumnFilterable(props.column);
  const searchColumnDefine = columnDefine;

  const filterStore = useTableFilterStore();
  const filter = filterStore.getFilterItem(props.schema.api, columnDefine, props.scene);

  const currentOperator = computed({
    get() {
      return filter.operator;
    },
    set(value) {
      filterStore.updateOperator(props.schema.api, props.column.key as string, value, props.scene);
    },
  });
  const inputValue: Ref<any> = ref<any>(filter.value);
  const valueFormatter = (value: any): QueryFilter => {
    if (!props.column?.filterProps?.defaultOperator) {
      if (isArray(value)) {
        return {
          field: props.column.key as string,
          value,
          operator: (currentOperator.value || (props.column?.valueType === 'Date' ? 'Between' : 'In')) as any,
          formatter: columnDefine.inputWidgetProps?.formatValue,
        };
      }
    }

    return value.field && isObject(value)
      ? value
      : {
          field: props.column.key as string,
          value,
          operator: currentOperator.value || props.column?.filterProps?.defaultOperator || 'Equal',
          formatter: columnDefine.inputWidgetProps?.formatValue,
        };
  };

  const handleFilterConfirm = () => {
    const formattedFilter: QueryFilter = valueFormatter(inputValue.value);
    filterStore.updateFilterValue(props.schema.api, columnDefine, inputValue.value, currentOperator.value, props.scene);
    emits('filterChange', props.column.key, formattedFilter);

    props.filterConfig.setFilterValue(inputValue.value);
    props.filterConfig.handleFilterConfirm(inputValue.value);
  };

  const handleFilterReset = () => {
    props.filterConfig.handleFilterReset();
    filterStore.updateFilterValue(props.schema.api, columnDefine, undefined, currentOperator.value, props.scene);
    emits('filterChange', props.column.key, undefined);
  };
</script>

<style scoped>
  .custom-filter {
    padding: 10px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: var(--border-radius-medium);
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  }

  .custom-filter-footer {
    display: flex;
    justify-content: center;
  }
</style>
