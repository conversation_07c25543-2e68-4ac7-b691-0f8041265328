<script setup lang="ts">
  import { PropType, ref } from 'vue';
  import { cloneDeep } from 'lodash';
  import { CustomSchema } from '@repo/infrastructure/types';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import CrudTable from './table.vue';

  const props = defineProps({
    schema: {
      type: Object,
      required: true,
    },
    visibleColumns: {
      type: Array as PropType<string[] | undefined>,
      default: () => undefined,
    },
    defaultQueryParams: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  });

  const tableRef = ref<any>(null);

  const queryParams = {
    ...(props.defaultQueryParams || {}),
    _ot: 1,
  };

  const handleRestore = async (row: any) => {
    // @todo go
    await request(`${props.schema.api}/restore/${row.id}`, {
      method: 'put',
      baseURL: props.schema.baseURL || PROJECT_URLS.MAIN_PROJECT_API,
    });
    Message.success('操作成功');
    tableRef.value.loadData();
  };

  const handleDestroy = (row: any) => {
    Modal.confirm({
      title: '彻底删除',
      content: '确定要彻底删除这条数据吗？此操作不可恢复！而且可能会存在对其他关联数据的影响，请再次确认！',
      async onOk() {
        Message.info('正在尝试彻底删除此条数据，请稍后');
        // throw new Error(123);
        // @todo go
        await request(`${props.schema.api}/${row.id}`, {
          method: 'delete',
          baseURL: props.schema.baseURL || PROJECT_URLS.MAIN_PROJECT_API,
        });

        Message.success('删除成功');
        tableRef.value?.loadData();
      },
    });
  };

  const recycleBinSchema: CustomSchema = cloneDeep(props.schema);
  recycleBinSchema.rowActions = [
    {
      key: 'restore',
      label: '还原',
      confirm: '确定要还原这条数据吗？',
      handler: handleRestore,
      expose: true,
    },
    {
      key: 'destroy',
      icon: 'icon-delete',
      btnProps: {
        status: 'danger',
        type: 'outline',
      },
      label: '彻底删除',
      handler: handleDestroy,
    },
    // { key: 'view', visible: false, handler() {} },
    { key: 'edit', visible: false, handler() {} },
    { key: 'delete', visible: false, handler() {} },
  ];
</script>

<template>
  <div class="flex justify-between items-center">
    <div>{{ schema?.label || '' }}</div>
    <a-space>
      <a-button size="mini" @click="() => tableRef.loadData()">
        <template #icon>
          <IconRefresh />
        </template>
        刷新
      </a-button>
    </a-space>
  </div>
  <crud-table
    ref="tableRef"
    class="mt-2"
    :schema="recycleBinSchema"
    :visible-columns="visibleColumns"
    :default-query-params="queryParams"
    v-bind="{ ...$attrs, size: 'mini' }"
  />
</template>

<style scoped lang="scss"></style>
