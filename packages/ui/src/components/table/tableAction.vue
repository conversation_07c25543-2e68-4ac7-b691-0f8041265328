<template>
  <div class="table-action-wrapper">
    <div class="flex justify-between items-center">
      <div class="title">
        <slot name="title"></slot>
      </div>
      <div class="flex justify-end gap-2 flex-1 items-center">
        <a-space>
          <!--补充按钮插槽-->
          <slot name="supplementary-button" :size="componentSize"></slot>
          <a-button
            v-if="visibleComponents.indexOf('add') >= 0"
            v-permission="schema.permissions?.create"
            type="primary"
            :size="componentSize"
            @click="handleGoAdd"
          >
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
          <a-button-group v-if="schema.importable || schema.exportable" :size="componentSize">
            <a-button
              v-if="getEnable(schema, 'exportable')"
              v-permission="schema.permissions?.export"
              :size="componentSize"
              @click="handleExport"
            >
              导出
            </a-button>
            <a-button
              v-if="getEnable(schema, 'importable')"
              v-permission="schema.permissions?.import"
              :size="componentSize"
              @click="handleShowImport"
            >
              导入
            </a-button>
          </a-button-group>
          <a-dropdown-button
            v-if="visibleRowActions.length > 0 && table.selectedItems?.length > 0"
            :size="componentSize"
            @select="handleSelectedItemsAction"
          >
            选中 {{ table.selectedItems.length }} 项
            <template #content>
              <a-doption
                v-for="(ra, ri) in multipleRowActions"
                :key="ri"
                :value="ra"
                :disabled="typeof ra.disabled === 'function' ? ra.disabled(table.selectedItems) : false"
              >
                <template v-if="ra.icon" #icon>
                  <FunctionalIcon :icon="ra.icon" />
                </template>
                <span v-if="ra.label">{{ ra.label }}</span>
              </a-doption>
            </template>
            <template #icon>
              <IconDown />
            </template>
          </a-dropdown-button>
          <slot name="extra-actions"></slot>
          <recycle-bin-button
            v-if="schema.listViewProps?.recycleBin !== false && table && visibleComponents.indexOf('recycleBin') >= 0"
            :visible-columns="table.getVisibleColumns()"
            :schema="schema"
            :default-query-params="table.defaultQueryParams"
          />
        </a-space>
        <div class="flex gap-2 items-center">
          <a-tooltip
            v-if="
              schema.quickSearchProps?.fields?.length &&
              visibleComponents.indexOf('quickSearch') >= 0 &&
              !isSearchAdvanced
            "
            content="输入或从文本、Excel中粘贴开始快速搜索"
          >
            <a-input
              v-if="!isSearchAdvanced && schema.quickSearchProps?.multiple === false"
              v-model.trim="quickSearchKeywords"
              :size="componentSize"
              allow-clear
              :placeholder="quickSearchPlaceholder"
              :multiple="schema.quickSearchProps?.multiple === false"
              :style="{
                width: `${schema.quickSearchProps?.inputWidth || 200}px`,
              }"
              @clear="handleQuickSearch"
              @change="handleQuickSearch"
              @keydown.enter="handleQuickSearch"
              @paste="handleQuickSearchPaste"
            >
              <template #prefix>
                <IconSearch />
              </template>
            </a-input>
            <a-input-tag
              v-else-if="!isSearchAdvanced"
              v-model.trim="quickSearchKeywords"
              :size="componentSize"
              allow-clear
              allow-create
              :placeholder="quickSearchPlaceholder"
              :multiple="schema.quickSearchProps?.multiple !== false"
              :max-tag-count="schema.quickSearchProps?.maxTagCount || 2"
              :style="{
                width: `${schema.quickSearchProps?.inputWidth || 200}px`,
              }"
              @clear="handleQuickSearch"
              @change="handleQuickSearch"
              @paste="handleQuickSearchPaste"
            >
              <template #prefix>
                <IconSearch />
              </template>
            </a-input-tag>
          </a-tooltip>
          <a-button-group :size="componentSize">
            <a-badge v-if="schema.listViewProps?.searchType === 'advance'" :max-count="10" :count="filtersLength">
              <a-button
                :size="componentSize"
                :type="isSearchAdvanced ? 'outline' : undefined"
                @click="handleToggleSearchAdvanced"
              >
                {{ isSearchAdvanced ? '收起高级搜索' : '高级搜索' }}
              </a-button>
            </a-badge>
            <a-button v-if="schema.searchTreeEnabled" :size="componentSize" @click="handleToggleSearchAdvanced">
              树搜索？
            </a-button>
            <a-button :size="componentSize" @click="table.loadData">
              <template #icon>
                <icon-refresh />
              </template>
            </a-button>
          </a-button-group>
          <table-layout v-if="showLayoutManage" :component-size="componentSize" :table="table" :schema="schema" />
        </div>
      </div>
    </div>

    <div v-show="isSearchAdvanced" v-if="schema.listViewProps?.searchType === 'advance'">
      <a-divider :margin="10" />
      <AdvancedSearch :schema="schema" :table="table" />
    </div>

    <import-modal ref="importModal" v-model="importVisible" :schema="schema" @ok="() => table.loadData()" />
  </div>
</template>

<script setup lang="ts">
  import { Schema, SchemaField, TableRowAction } from '@repo/infrastructure/types';
  import { computed, inject, onMounted, PropType, ref } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { translate } from '@repo/infrastructure/utils';
  import { useTableFilterStore, useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { isArray } from 'lodash';
  import { getClientRole } from '@repo/env-config';
  import FunctionalIcon from '../icon/functionalIcon.vue';
  import { getBatchRowActions, getDefaultRowActions } from './tableHelper';
  import AdvancedSearch from './advancedSearch.vue';
  import TableLayout from './tableLayout.vue';
  import ImportModal from './importModal.vue';
  import RecycleBinButton from './recycleBinButton.vue';

  const props = defineProps({
    schema: {
      type: Object as () => Schema,
      required: true,
    },
    table: {
      type: Object as any,
      required: true,
    },
    rowActions: {
      type: Array as PropType<TableRowAction[]>,
      default: () => [],
    },
    componentSize: {
      type: String as PropType<any>,
      default: 'small',
    },
    visibleComponents: {
      type: Array as PropType<string[]>,
      default: () => ['add', 'quickSearch', 'refresh', 'layout', 'recycleBin'],
    },
    modulePath: {
      type: String as PropType<string>,
      default: '',
    },
    showLayoutManage: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const filterStore = useTableFilterStore();

  const emits = defineEmits(['rowAction', 'filtersChange']);

  const router: any = inject('router');
  const quickSearchKeywords = ref<any>([]);
  const isSearchAdvanced = ref<any>(false);
  const enableMultiQuickSearch = computed(() => props.schema.quickSearchProps?.multiple !== false);

  const multipleRowActions = ref<TableRowAction[]>([]);
  const allRowActions = ref<TableRowAction[]>([]);
  const customSchema = {
    ...props.schema,
    modulePath: props.modulePath || props.schema.modulePath || props.schema.api,
  };
  if (props.rowActions?.length) {
    allRowActions.value = props.rowActions;
  } else {
    const rawAllActions = getDefaultRowActions(
      customSchema,
      props.table.loadData,
      [],
      props.schema?.rowActions || [],
      props.table.setLoading,
    );

    allRowActions.value = [...rawAllActions.expose, ...(rawAllActions.others || [])];
  }

  const visibleRowActions = computed(() => {
    return allRowActions.value?.filter((item: any) => {
      if (!item.multiple) {
        return false;
      }
      if (typeof item.visible === 'function') {
        return props.table.selectedItems?.every((record) => item.visible(record));
      }
      if (typeof item.visible === 'boolean') {
        return item.visible;
      }
      return item.visible !== false;
    });
  });

  const importVisible = ref<boolean>(false);
  const handleShowImport = () => {
    importVisible.value = true;
  };

  const filtersLength = computed(() => {
    const { filters } = filterStore.getFilter(props.schema.api);
    return Object.values(filters).filter((item: any) => {
      return (
        item.value !== undefined &&
        item.formatValue &&
        item.formatValue(item.value) !== undefined &&
        !item.virtualSearch
      );
    }).length;
  });

  const handleQuickSearch = () => {
    const quickSearchFields = props.schema.quickSearchProps?.fields || [];
    if (!quickSearchFields.length) {
      Message.warning(translate('common.quickSearchNotConfigured'));
      return;
    }
    let keywords: any;

    if (!enableMultiQuickSearch.value) {
      // eslint-disable-next-line prefer-destructuring
      keywords = quickSearchKeywords.value;
      if (isArray(keywords)) {
        keywords = keywords.join('');
      }
    } else {
      keywords = Array.from(new Set(quickSearchKeywords.value.filter((item: string) => item && item.trim())));
    }

    const queryField = quickSearchFields.join('|');
    filterStore.updateFilterValue(
      props.schema.requestApi?.list || props.schema.api,
      {
        key: queryField,
        filterProps: {
          virtualSearch: true,
        },
      },
      // eslint-disable-next-line no-nested-ternary
      enableMultiQuickSearch.value ? (keywords.length === 0 ? undefined : keywords) : keywords,
      enableMultiQuickSearch.value ? 'LikeIn' : 'Like',
    );
    // 这里默认每次快搜从第一页开始
    props.table.resetPage();
    props.table.loadData();
  };

  const quickSearchPlaceholder = computed(() => {
    if (props.schema.quickSearchProps?.placeholder) {
      return props.schema.quickSearchProps.placeholder;
    }
    const fields: SchemaField[] = [];
    props.schema.quickSearchProps?.fields?.forEach((field: string) => {
      const schemaField = props.schema?.schemaFieldsMap[field] as any;
      if (schemaField) {
        fields.push(schemaField);
      }
    });
    if (fields.length > 0) {
      return translate('common.quickSearchBy', [fields.map((field) => translate(field.label)).join(', ')]);
    }
    return translate('common.quickSearch');
  });

  const handleGoAdd = () => {
    if (props.schema.modalEdit) {
      emits('rowAction', { key: 'add' });
    } else {
      router.push(`${props.modulePath || props.schema.modulePath || props.schema.api}/add`);
    }
  };

  const handleToggleSearchAdvanced = () => {
    isSearchAdvanced.value = !isSearchAdvanced.value;
    if (isSearchAdvanced.value) {
      quickSearchKeywords.value = [];
    }
  };

  const handleQuickSearchPaste = (e: ClipboardEvent, b: any) => {
    e.preventDefault();
    const raw = e
      .clipboardData!.getData('text')
      .replace(/\\r/g, '')
      .split('\n')
      .map((item) => {
        const match = item.match(/[0-9A-Za-z\u4e00-\u9fa5]+/);
        return match ? match[0] : '';
      })
      .filter((item) => item && item.trim());

    quickSearchKeywords.value = raw as any[];
    handleQuickSearch();
  };

  const handleSelectedItemsAction = (action: TableRowAction, e: Event) => {
    e.stopPropagation();
    const actionHandle = () => {
      if (typeof action.handler === 'function') {
        const rows = props.table.data.filter((item: any) => props.table.selectedItems.includes(item.id));
        action.handler(props.table.selectedItems, props.table.loadData, {
          rows,
          table: props.table,
        });
      }
      emits('rowAction', action, props.table.selectedItems, {
        rows: props.table.data.filter((item: any) => props.table.selectedItems.includes(item.id)),
        table: props.table,
      });
    };
    if (action.confirm) {
      Modal.warning({
        title: translate('common.confirmPlease'),
        content: translate(action.confirm, props.table.selectedItems.length, {
          count: props.table.selectedItems.length,
        }),
        onOk() {
          actionHandle();
        },
        hideCancel: false,
      });
      return;
    }
    actionHandle();
  };

  const getOrgNature = () => {
    if (getClientRole() === 'Company') {
      const userStore = useUserStore();
      return userStore.getUserNature();
    }
    const menuStore = useUserMenuStore();
    return menuStore.getCurrentMenuInfo().app?.label;
  };

  const handleExport = async () => {
    Message.info('正在导出，请稍后在右上角导出结果下载中查看');
    try {
      if (typeof props.schema.exportable?.handler === 'function') {
        await props.schema.exportable?.handler(props.schema);
      } else {
        await request({
          url: props.schema.exportable?.api || `${props.schema.api}/export`,
          baseURL: props.schema.baseURL,
          method: 'POST',
          params: {
            ...props.defaultQueryParams,
          },
          data: {
            api: props.schema.api,
            columns: props.schema?.exportable?.columns || [],
            filters: [
              ...Object.values(filterStore.getFilter(props.schema.api).filters || {}),
              { field: 'nature', value: getOrgNature() },
            ],
          },
        });
      }
    } catch (error) {
      Message.error('导出失败');
    }
  };

  const getEnable = (schema: any, filed: 'importable' | 'exportable') => {
    if (typeof schema[filed]?.enabled === 'function') {
      return schema[filed]?.enabled();
    }
    return schema[filed]?.enabled;
  };

  onMounted(async () => {
    const cs = {
      ...props.schema,
      modulePath: props.modulePath || props.schema.modulePath || props.schema.api,
    };

    const rowActions = props.rowActions?.length ? props.rowActions : props.schema?.rowActions;
    multipleRowActions.value = getBatchRowActions(cs, props.table.loadData, [], rowActions);
  });
</script>
