<script setup lang="ts">
  import { computed } from 'vue';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import { Modal } from '@arco-design/web-vue';
  import { getToken } from '@repo/infrastructure/auth';
  import { IconLibrary } from '@vime/vue-next';

  const props = defineProps({
    schema: {
      type: Object,
      required: true,
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue', 'ok']);

  const modalVisible = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const headers = {
    'Authorization': `Bearer ${getToken()}`,
    'Request-Client-Role': getClientRole(),
  };

  const uploadApi = computed(() => {
    if (props.schema.importable?.api) {
      return `${props.schema.baseURL}${props.schema.importable.api}`.trim();
    }
    if (props.schema.baseURL === PROJECT_URLS.MAIN_PROJECT_API) {
      return `${PROJECT_URLS.MAIN_PROJECT_API}/common/importTask`;
    }
    return '/api/upload';
  });

  const handleOk = () => {
    emit('ok');
    modalVisible.value = false;
  };

  const handleCancel = () => {
    modalVisible.value = false;
  };

  const handleError = (error) => {
    Modal.error({
      title: '导入失败',
      content: error.response?.errors || '请检查导入模板是否正确',
    });
  };

  const templateDownload = computed(() => {
    if (props.schema.importable?.template) {
      return props.schema.importable?.template;
    }
    const api = props.schema?.importable?.templateApi || props.schema?.importable?.api || props.schema?.api;
    return `${PROJECT_URLS.MAIN_PROJECT_API}/common/importTask?api=${api}`;
  });

  const handleDownloadTemplate = () => {
    window.open(templateDownload.value);
  };
</script>

<template>
  <a-modal v-model:visible="modalVisible" title="导入" :render-to-body="false" @close="handleCancel">
    <template #footer>
      <div class="flex justify-between items-end w-full py-1">
        <a-button class="bg-sky-500" size="small" @click="handleDownloadTemplate">
          <icon-download />
          模板下载
        </a-button>
        <div class="flex space-x-2">
          <!--          <a-button @click="handleCancel">关闭</a-button>-->
          <a-button type="primary" size="small" @click="handleOk">完成</a-button>
        </div>
      </div>
    </template>

    <div v-if="modalVisible">
      <a-upload
        :headers="headers"
        :action="uploadApi"
        :data="{ api: schema.api }"
        draggable
        accept=".xls,.xlsx"
        :show-upload-list="false"
        :tip="schema.importable.tip"
        @error="handleError"
        @success="handleOk"
      />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>

<!--ReactNode | ((cancelButtonNode: ReactNode, okButtonNode: ReactNode) => ReactNode)-->
