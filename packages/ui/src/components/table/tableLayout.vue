<template>
  <a-button-group>
    <a-popover trigger="click" position="bl" @popup-visible-change="popupVisibleChange">
      <a-button :size="componentSize">
        <template #icon>
          <IconSwap />
        </template>
      </a-button>
      <template #content>
        <div id="tableSetting">
          <div v-for="(item, index) in showColumns" :key="item.key" class="setting flex justify-between">
            <a-space>
              <IconDragArrow />
              <a-checkbox
                v-model="item.listProps.visible"
                @change="(e: any) => handleColumnVisibleChange(e, item as TableColumn, index)"
              >
                {{ item.label }}
              </a-checkbox>
            </a-space>
            <a-select
              v-model="item.listProps.fixed"
              style="width: 90px"
              allow-clear
              :size="componentSize"
              :placeholder="translate('common.fixedOn')"
              @change="handleFixedChange"
            >
              <a-option v-for="(f, fi) in fixedOptions" :key="fi" :value="f.value" :label="f.label">{{
                f.label
              }}</a-option>
              <a-option></a-option>
            </a-select>
          </div>
          <a-divider />
          <div class="flex-container justify-between">
            <a-select style="width: 150px"></a-select>
            <a-button type="outline">保存布局</a-button>
          </div>
        </div>
      </template>
    </a-popover>
    <a-dropdown @select="handleTableSettingSelect">
      <a-button :size="componentSize">
        <template #icon>
          <IconDown />
        </template>
      </a-button>
      <template #content>
        <a-dsubmenu> 切换布局 </a-dsubmenu>
        <a-dsubmenu>
          组件尺寸
          <template #content>
            <a-doption
              v-for="item in componentSizes"
              :key="item.value"
              :disabled="item.value === table.tableSize"
              :value="{ type: 'componentSize', value: item.value }"
            >
              <span>{{ item.name }}</span>
            </a-doption>
          </template>
        </a-dsubmenu>
      </template>
      <template #icon>
        <IconDown />
      </template>
    </a-dropdown>
  </a-button-group>
</template>

<script lang="ts" setup>
  import { nextTick, PropType, ref } from 'vue';
  import Sortable from 'sortablejs';
  import { Schema, TableColumn } from '@repo/infrastructure/types';
  import { cloneDeep } from 'lodash';
  import { translate } from '@repo/infrastructure/utils';
  import { IconDragArrow, IconSwap, IconDown } from '@arco-design/web-vue/es/icon';
  import { componentSizes } from '../constants';

  const props = defineProps({
    table: Object as any,
    schema: Object as PropType<Schema>,
    componentSize: {
      type: String,
      default: 'small',
    },
  });

  const showColumns = ref<any>(cloneDeep(props.table.showColumns));
  const fixedOptions = [
    { label: translate('common.fixedOn.left'), value: 'left' },
    { label: translate('common.fixedOn.right'), value: 'right' },
  ];

  const exchangeArray = <T extends Array<any>>(array: T, beforeIdx: number, newIdx: number, isDeep = false): T => {
    const newArray = isDeep ? cloneDeep(array) : array;
    if (beforeIdx > -1 && newIdx > -1) {
      // beforeIdx 移动到 newIdx
      const beforeItem = newArray[beforeIdx];
      newArray.splice(beforeIdx, 1);
      newArray.splice(newIdx, 0, beforeItem);
    }
    return newArray;
  };

  const handleFixedChange = () => {
    props.table.resetCurrentVisibleColumns(showColumns.value);
  };

  const popupVisibleChange = (val: boolean) => {
    if (val) {
      nextTick(() => {
        const el = document.getElementById('tableSetting') as HTMLElement;
        const sortable = new Sortable(el, {
          onEnd(e: any) {
            const { oldIndex, newIndex } = e;
            // exchangeArray(cloneColumns.value, oldIndex, newIndex);
            nextTick(() => {
              exchangeArray(showColumns.value, oldIndex, newIndex);
              props.table.resetCurrentVisibleColumns(showColumns.value);
            });
          },
        });
      });
    }
  };

  const handleColumnVisibleChange = (checked: boolean, column: TableColumn, index: number) => {
    column.listProps = column.listProps || {};
    column.listProps.visible = checked;
    props.table.handleSetColumnVisible(checked, column, index);
  };

  const handleTableSettingSelect = (value: any) => {
    switch (value.type) {
      case 'componentSize':
        props.table.handleSetTableSize(value.value);
        break;
      default:
        break;
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'TableLayout',
  };
</script>

<style scoped>
  .setting {
    min-width: 280px;
    margin-top: 5px;
    line-height: 180%;

    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
</style>
