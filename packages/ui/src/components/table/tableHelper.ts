import { Message } from '@arco-design/web-vue';
import { cloneDeep, isArray, isString, merge } from 'lodash';
import {
  Schema,
  TableRowAction,
  TableRowActionView,
  DataTypeOf,
  DataTypes,
  TableColumn,
} from '@repo/infrastructure/types';
import { getCurrentInstance, inject, isProxy, markRaw, toRaw } from 'vue';
import { translate } from '@repo/infrastructure/utils';
import { CommonApi } from '@repo/infrastructure/crud';
import { InputComponents, inputWidgetAttributes, inputComponents } from '@repo/ui/components/form';
import { useUserStore } from '@repo/infrastructure/store';

export const getColumnFilterable = (column: TableColumn) => {
  const dataType = DataTypeOf(column.valueType!);
  let component = column.filterProps?.inputWidget ? inputComponents[column.filterProps.inputWidget] : null;
  const columnDefine = cloneDeep(column);
  columnDefine.inputWidgetProps = columnDefine.inputWidgetProps || {};
  if (!component) {
    switch (dataType) {
      case DataTypes.Boolean:
        component = InputComponents.selectInput;
        columnDefine.inputWidgetProps =
          columnDefine.filterProps?.inputWidgetProps || columnDefine.inputWidgetProps || {};
        columnDefine.inputWidgetProps.options =
          columnDefine.filterProps?.inputWidgetProps?.options ||
          columnDefine.inputWidgetProps?.options ||
          inputWidgetAttributes.booleanOptions;
        columnDefine.inputWidgetProps.formatValue = (raw: any) => {
          if (raw && raw.field && raw.value !== undefined) {
            return !!raw.value;
          }
          if (raw === undefined || raw === '') {
            return undefined;
          }
          if (raw === 'false') {
            return false;
          }
          return !!raw;
        };
        break;
      case DataTypes.Foreign:
        component = InputComponents.selectInput;
        columnDefine.inputWidgetProps = merge(
          columnDefine.filterProps?.inputWidgetProps || {},
          columnDefine.inputWidgetProps || {},
        );
        columnDefine.foreignField = merge(
          columnDefine.filterProps?.foreignField || {},
          columnDefine.foreignField || {},
        );
        columnDefine.inputWidgetProps.allowClear = true;
        columnDefine.inputWidgetProps.multiple = true;
        columnDefine.inputWidgetProps.formatValue = (raw: any[]) => {
          if (raw === undefined || raw.length === 0) {
            return undefined;
          }
          return raw.map((item) => item.id || item).filter((item) => !!item);
        };
        break;
      case DataTypes.Date:
        component = InputComponents.dateRangeInput;
        columnDefine.inputWidgetProps =
          columnDefine.filterProps?.inputWidgetProps || columnDefine.inputWidgetProps || {};
        columnDefine.inputWidgetProps.allowClear = true;
        columnDefine.inputWidgetProps.format = columnDefine.inputWidgetProps.format || 'YYYY-MM-DD';
        break;
      case DataTypes.Number:
        component = InputComponents.textInput;
        columnDefine.inputWidgetProps = merge(
          columnDefine.filterProps?.inputWidgetProps || {},
          columnDefine.inputWidgetProps || {},
          {
            mode: 'button',
            min: 0,
            showWordLimit: false,
          },
        );
        columnDefine.inputWidgetProps.allowClear = true;
        columnDefine.inputWidgetProps.format = columnDefine.inputWidgetProps.format || 'YYYY-MM-DD';
        break;
      default:
        component = InputComponents.textInput;
    }

    columnDefine.inputWidgetProps.allowClear = true;
    if (!columnDefine.inputWidgetProps.formatValue) {
      columnDefine.inputWidgetProps.formatValue = (raw: any) => {
        if (isString(raw)) {
          raw = raw.trim();
        }
        return raw === '' ? undefined : raw;
      };
    }
  }

  return {
    component: markRaw(component as any),
    columnDefine,
  };
};

export const getDefaultRowActions = (
  schema: Schema,
  loadData?: () => any,
  actionKeys?: any[],
  customActions?: TableRowAction[],
  setLoading?: (loading: boolean) => any,
): TableRowActionView => {
  const api = CommonApi.getInstance(schema);
  let tableActions = (schema.tableActions || []) as TableRowAction[];

  if (typeof setLoading !== 'function') {
    setLoading = () => {};
  }

  const defaultActions: TableRowAction[] = [
    {
      key: 'view',
      label: '查看',
      expose: true,
    },
    {
      key: 'edit',
      label: '修改',
      icon: 'icon-edit',
      permNode: schema.permissions?.update,
      collaborate: 'Edit',
      disabled(record) {
        if (!record) {
          return true;
        }
        return record.submitStatus && record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected';
      },
      handler: (record: Record<string, any>, a: any, config?: any) => {
        const router = config?.router || inject('router');
        if (schema.modalEdit !== true) {
          const editUrl = `${config?.modulePath || schema.modulePath || schema.api}/edit?id=${record.id}`;
          router.push(editUrl);
        }
      },
    },
    {
      key: 'delete',
      label: '删除',
      collaborate: 'Owner',
      btnProps: {
        status: 'danger',
      },
      icon: 'icon-delete',
      multiple: true,
      permNode: schema.permissions?.delete,
      confirm: '确定要删除这条数据吗？',
      disabled(record) {
        if (!record) {
          return true;
        }

        if (isArray(record)) {
          return record.some(
            (item) => item.submitStatus && item.submitStatus !== 'Draft' && item.submitStatus !== 'Rejected',
          );
        }
        return record.submitStatus && record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected';
      },
      handler: (record: Record<string, any> | any[]) => {
        setLoading(true);
        let req: any = null;
        if (isProxy(record)) {
          record = toRaw(record);
        }

        if (isArray(record)) {
          req = api.batchDeleteRecord(record, {
            baseURL: schema.baseURL,
          });
        } else {
          req = api.deleteRecord(record.id, {
            baseURL: schema.baseURL,
          });
        }
        req
          ?.then(() => {
            Message.success('删除成功');
            if (loadData) {
              loadData();
            }
          })
          .finally(() => {
            setLoading(false);
          });
      },
    },
  ];

  if (schema.interfaces?.includes('CanDisable')) {
    defaultActions.push({
      key: 'disable',
      label: 'common.disable',
      icon: 'icon-close',
      multiple: true,
      collaborate: 'Edit',
      confirm: 'common.disableConfirm',
      visible: (record: Record<string, any> | any[]) => {
        if (isProxy(record)) {
          record = toRaw(record);
        }
        return isArray(record) || record.enabled;
      },
      handler: (record: Record<string, any> | number[]) => {
        let req: any;
        if (isProxy(record)) {
          record = toRaw(record);
        }

        if (isArray(record)) {
          req = api.batchToggleRecordEnabled(record, false);
        } else {
          req = api.toggleRecordEnabled(record.id, false);
        }
        req.then(() => {
          Message.success(translate('common.disableSuccess'));
          if (loadData) {
            loadData();
          }
        });
      },
    });
    defaultActions.push({
      key: 'enable',
      label: 'common.enable',
      icon: 'icon-check',
      multiple: true,
      collaborate: 'Edit',
      confirm: 'common.enableConfirm',
      visible: (record: Record<string, any> | any[]) => {
        if (isProxy(record)) {
          record = toRaw(record);
        }
        return isArray(record) || !record.enabled;
      },
      handler: (record: Record<string, any> | any[]) => {
        let req: any;
        if (isProxy(record)) {
          record = toRaw(record);
        }

        if (isArray(record)) {
          req = api.batchToggleRecordEnabled(record, true);
        } else {
          req = api.toggleRecordEnabled(record.id, true);
        }
        req.then(() => {
          Message.success(translate('common.enableSuccess'));
          if (loadData) {
            loadData();
          }
        });
      },
    });
  }

  // actions in schema
  const exists = tableActions.map((action) => action.key);
  defaultActions.forEach((action) => {
    if (exists.indexOf(action.key) === -1) {
      tableActions.push(action);
    }
  });
  const customActionsMap = {} as Record<string, TableRowAction>;

  // custom actions in view
  if (customActions) {
    customActions.forEach((action) => {
      customActionsMap[action.key] = action;
      if (exists.indexOf(action.key) === -1) {
        tableActions.push(action);
        exists.push(action.key);
      }
    });
    tableActions = tableActions.map((action) => {
      if (customActionsMap[action.key]) {
        return {
          ...action,
          ...customActionsMap[action.key],
        };
      }
      return action;
    });
  }

  const userStore = useUserStore();

  tableActions = tableActions
    .filter((item) => item.visible !== false)
    .map((action) => {
      if (schema.collaborate && action.collaborate) {
        const collaborateDisableFunc = (record: Record<string, any>) => {
          return !record.collaborators
            ?.filter((collaborator: any) => {
              if (action.collaborate === 'Owner') {
                return collaborator.action === 'Owner';
              }

              return collaborator.action !== 'View';
            })
            .map((collaborator: any) => collaborator.userId)
            .includes(userStore.userInfo.id);
        };
        if (typeof action.disabled !== 'function') {
          action.disabled = collaborateDisableFunc;
        } else {
          const disabledAction = action.disabled;
          action.disabled = (record: Record<string, any> | any[]) => {
            if (isArray(record)) {
              return record.some((item) => disabledAction(item) || collaborateDisableFunc(item));
            }
            return disabledAction(record) || collaborateDisableFunc(record);
          };
        }
      }
      return action;
    });

  // filter available actions
  if (actionKeys && actionKeys.length > 0) {
    tableActions = tableActions.filter((action) => {
      return actionKeys.indexOf(action.key) > -1;
    });
  }

  const result = {
    expose: tableActions.filter((action) => {
      if (action.visible === false || !action.expose) {
        return false;
      }
      if (action.permNode) {
        return userStore.isAuthorized(action.permNode);
      }
      if (schema.permissions && schema.permissions[action.key]) {
        if (isArray(schema.permissions[action.key])) {
          return userStore.isAnyAuthorized(schema.permissions[action.key]);
        }
        return userStore.isAuthorized(schema.permissions[action.key]);
      }
      return true;
    }),
    others: tableActions.filter((action) => {
      if (action.visible === false || action.expose) {
        return false;
      }
      if (action.permNode) {
        return userStore.isAuthorized(action.permNode);
      }
      if (schema.permissions && schema.permissions[action.key]) {
        if (isArray(schema.permissions[action.key])) {
          return userStore.isAnyAuthorized(schema.permissions[action.key]);
        }
        return userStore.isAuthorized(schema.permissions[action.key]);
      }

      return true;
    }),
  };

  if (!result.expose.length) {
    result.expose = result.others;
    result.others = [];
  }

  return result;
};

export const getBatchRowActions = (
  schema: Schema,
  loadData?: () => any,
  actionKeys?: any[],
  customActions?: TableRowAction[],
  setLoading?: (loading: boolean) => void,
): TableRowAction[] => {
  const all = getDefaultRowActions(schema, loadData, actionKeys, customActions, setLoading);
  return (
    all.others
      ?.filter((action: TableRowAction) => action.multiple)
      .concat(all.expose.filter((action: TableRowAction) => action.multiple)) || []
  );
};

export const columnTypeWidth: Record<string, number> = {
  Boolean: 100,
  Date: 120,
};

export const getColumnWidth = (column: TableColumn, defaultWidth?: number): number | undefined => {
  if (column.listProps?.columnWidth && column.listProps?.columnWidth > 0) {
    return column.listProps?.columnWidth;
  }
  if (columnTypeWidth[column.valueType!] && columnTypeWidth[column.valueType!]) {
    return columnTypeWidth[column.valueType!];
  }
  return defaultWidth;
};

export const getTableScrollX = (columns: TableColumn[]) => {
  let scrollX = 0;
  columns.forEach((column) => {
    scrollX += getColumnWidth(column) || 200;
  });
  return scrollX;
};

export const FILTER_OPERATORS = {
  'common.comparator.Equal': '等于',
  'common.comparator.NotEqual': '不等于',
  'common.comparator.LessThan': '小于',
  'common.comparator.LessThanOrEqual': '小于等于',
  'common.comparator.GreaterThan': '大于',
  'common.comparator.GreaterThanOrEqual': '大于等于',
  'common.comparator.Between': '介于',
  'common.comparator.NotBetween': '不介于',
  'common.comparator.In': '在...中',
  'common.comparator.NotIn': '不在...中',
  'common.comparator.Like': '包含',
  'common.comparator.LikeIn': '可能包含(以,分隔)',
  'common.comparator.NotLike': '不包含',
  'common.comparator.IsNull': '为空',
  'common.comparator.IsNotNull': '不为空',
};
