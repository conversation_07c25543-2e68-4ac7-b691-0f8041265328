<template>
  <div :class="{ 'table-container': fixedHeight }">
    <a-table
      v-if="!rendering"
      ref="tableRef"
      v-bind="$attrs"
      v-model:selected-keys="selectedItems"
      column-resizable
      :bordered="{ cell: true }"
      :data="listData"
      :row-key="rowKey"
      :loading="loading"
      :pagination="schema.listViewProps?.pagination === false ? false : pagination"
      :table-layout-fixed="true"
      :columns="currentVisibleColumns"
      :row-selection="rowSelection || undefined"
      height="100%"
      :scroll="tableScroll"
      :size="tableSize"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sorter-change="handleSorterChange"
      @selection-change="onSelectionChange"
    >
      <template #columns>
        <slot name="columns" :columns="currentVisibleColumns">
          <a-table-column v-if="schema.listViewProps?.showIndex !== false" title="#" :width="60" fixed="left">
            <template #cell="{ rowIndex }">
              <div class="text-center">
                {{ listPageIndex(rowIndex) }}
              </div>
            </template>
          </a-table-column>
          <a-table-column
            v-for="(c, ci) in currentVisibleColumns"
            :key="ci"
            :data-index="c.key"
            :title="c.label"
            :width="getColumnWidth(c)"
            :fixed="c.listProps?.fixed"
            :filterable="
              schema.listViewProps?.searchType === 'column' && c.filterable
                ? {
                    slotName: `${c.key}-filter`,
                  }
                : (undefined as any)
            "
            :sortable="c.sortable ? { sortDirections: ['ascend', 'descend'] } : undefined"
            v-bind="c.listProps || {}"
          >
            <template #cell="{ record, rowIndex }">
              <slot :name="`custom-column-${c.key}`" :column="c" :record="record as any">
                <data-display
                  :schema-field="c"
                  :record="record"
                  :string-filters="filtersWithString"
                  @update:record="(val) => handleRecordUpdate(val, rowIndex)"
                />
              </slot>
            </template>
          </a-table-column>
        </slot>
        <slot name="additional-columns" :columns="currentVisibleColumns"></slot>
        <slot name="row-actions">
          <table-row-action
            v-if="showRowActions"
            :table="tableRef"
            :module-path="modulePath"
            :row-action-width="operationColumnWith"
            :schema="schema"
            :load-data="loadData"
            :row-actions="schema.rowActions"
            :set-loading="setLoading"
            @row-action="handleAfterRowAction"
          />
        </slot>
      </template>

      <template
        v-for="(c, ci) in currentVisibleColumns.filter((c) => c.filterable)"
        :key="ci"
        #[`${c.key}-filter`]="filterConfig"
      >
        <slot :name="`custom-filter-${c.key}`">
          <ColumnFilter :filter-config="filterConfig" :column="c" :schema="schema" @filter-change="loadData" />
        </slot>
      </template>

      <template #expand-row="{ record }">
        <slot name="expand-row" :record="record"></slot>
      </template>
    </a-table>
    <RecordDetailModal
      v-if="schema.detailViewProps?.type === 'modal' && detailVisible"
      ref="detailModalRef"
      v-model="detailVisible"
      v-model:raw="currentClickedRecord"
      :schema="schema"
      :module-path="modulePath"
      v-bind="schema.detailViewProps || {}"
      @row-action="handleAfterRowAction"
    />

    <RecordDetailDrawer
      v-else-if="currentClickedRecord && detailVisible"
      ref="detailDrawerRef"
      v-model="detailVisible"
      v-model:raw="currentClickedRecord"
      :schema="schema"
      :module-path="modulePath"
      @row-action="handleAfterRowAction"
    />
  </div>
</template>

<script lang="ts" setup>
  import { Schema, SchemaField, TableColumn } from '@repo/infrastructure/types';
  import { computed, ComputedRef, defineEmits, nextTick, onMounted, PropType, Ref, ref, watch } from 'vue';
  import { translate } from '@repo/infrastructure/utils';
  import { cloneDeep, sortBy } from 'lodash';
  import { RecordDetailModal, RecordDetailDrawer } from '@repo/ui/components/record-detail';
  import { useList } from '@repo/infrastructure/crud';
  import { DataDisplay } from '@repo/ui/components/data-display';
  import ColumnFilter from './columnFilter.vue';
  import TableRowAction from './tableRowAction.vue';
  import { getColumnWidth, getTableScrollX } from './tableHelper';

  const props = defineProps({
    data: {
      type: Array as () => Record<string, any>[],
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    schema: {
      type: Object as () => Schema,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    columnResizable: {
      type: Boolean,
      default: false,
    },
    visibleColumns: {
      type: Array as PropType<string[] | undefined>,
      default: () => undefined,
    },
    hiddenColumns: {
      type: Array as PropType<string[]>,
      default: () => ['id', 'deleted'],
    },
    fixedHeight: {
      type: Boolean,
      default: true,
    },
    rowSelection: {
      type: [Object, Boolean] as PropType<any>,
      default() {
        return {
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: true,
        };
      },
    },
    rowActionWidth: {
      type: Number,
      default: 200,
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    axiosConfig: {
      type: Object,
      default: () => ({}),
    },
    showRowActions: {
      type: Boolean,
      default: true,
    },
    modulePath: {
      type: String as PropType<string>,
      required: false,
    },
    pagination: {
      type: [Object, Boolean],
      default: undefined,
    },
    size: {
      type: String as PropType<'mini' | 'small' | 'medium' | 'large'>,
      default: 'small',
    },
  });

  const tableRef: Ref<any> = ref<any>(null);
  const currentClickedRecord: Ref<Record<string, any> | undefined> = ref<any>();
  const detailVisible: Ref<boolean> = ref<any>(false);
  const rendering: Ref<boolean> = ref<any>(false);
  const showColumns: Ref<TableColumn[]> = ref<any[]>([]);
  const tableScroll: ComputedRef<{
    x?: number | undefined;
    y?: number | undefined;
  }> = computed(() => {
    return {
      x: getTableScrollX(currentVisibleColumns.value),
    };
  });
  const listEmit = defineEmits(['update:data', 'rowAction', 'refresh']);
  const operationColumnWith = ref<number>(props.schema?.listViewProps?.rowActionWidth || props.rowActionWidth);

  const detailModalRef = ref<any>(null);
  const detailDrawerRef = ref<any>(null);

  const {
    loading,
    setLoading,
    sorter,
    selectedItems,
    listData,
    pagination,
    listPageIndex,
    filtersWithString,
    listInit,
    loadData: listLoadData,
    queryParams,
    handleSelectionChange,
    handlePageChange,
    resetPage,
    handlePageSizeChange,
    handleSorterChange,
  } = useList<any>({
    schema: props.schema,
    pageSize: props.pageSize,
    rawData: props.data as any[],
    rowKey: props.rowKey,
    defaultQueryParams: props.defaultQueryParams,
    axiosConfig: {
      ...props.axiosConfig,
      baseURL: props.schema.baseURL,
    },
  });

  const loadData = async (options?: any) => {
    listEmit('refresh');
    if (props.data && !props.autoLoad) {
      return;
    }

    await listLoadData(options);
  };

  const tableSize: Ref<'mini' | 'small' | 'medium' | 'large'> = ref<any>(
    props.schema?.listViewProps?.size || props.size || 'small',
  ) as any;

  const handleSetTableSize = (size: 'mini' | 'small' | 'medium' | 'large') => {
    tableSize.value = size;
  };

  const currentVisibleColumns: Ref<TableColumn[]> = ref<any[]>([]);
  const resetCurrentVisibleColumns = (allColumns?: TableColumn[]) => {
    if (allColumns) {
      showColumns.value = allColumns;
    }
    nextTick(() => {
      currentVisibleColumns.value = showColumns.value.filter((item: TableColumn) => item.listProps?.visible);
      const hasFixedChange = currentVisibleColumns.value.some((item: TableColumn) => item.listProps?.fixed);
      if (hasFixedChange) {
        rendering.value = true;
        nextTick(() => {
          rendering.value = false;
        });
      }
    });
  };

  const initShowColumns = (): TableColumn[] => {
    let fields = cloneDeep(props.schema.schemaFields) as SchemaField[];
    const definedVisibleColumns = cloneDeep(props.visibleColumns || props.schema.listViewProps?.visibleColumns || []);

    if (definedVisibleColumns?.length) {
      fields = fields.map((item) => {
        item.listProps = item.listProps || {};
        if (definedVisibleColumns.indexOf(item.key!) < 0) {
          item.listProps.visible = false;
        }
        return item;
      });
      // sort fields by visibleColumns
      fields = sortBy(fields, (item) => {
        return definedVisibleColumns.indexOf(item.key!);
      });
    }

    const definedHiddenColumn = cloneDeep(props.hiddenColumns || props.schema.listViewProps?.hiddenColumns || []);
    if (definedHiddenColumn) {
      fields = fields.map((item) => {
        item.listProps = item.listProps || {};
        if (definedHiddenColumn.indexOf(item.key!) >= 0) {
          item.listProps.visible = false;
        }
        return item;
      });
    }

    fields = fields
      .filter((field) => {
        if (typeof field.visibleInTable === 'function') return field.visibleInTable();
        return !field.invisible && field.visibleInTable !== false;
      })
      .map((item) => {
        item = cloneDeep(item);
        item.label = translate(item.label);
        item.listProps = item.listProps || {};
        item.listProps.visible = item.listProps.visible !== false;
        return item;
      }) as TableColumn[];

    return fields;
  };

  const onSelectionChange = (selectedRowKeys: any) => {
    handleSelectionChange(selectedRowKeys);
    if (tableRef.value && selectedItems.value.length) {
      tableRef.value.selectAll(false);
    }
  };

  const handleSetColumnVisible = (checked: boolean, column: TableColumn, index: number) => {
    const c = showColumns.value.find((item) => item.key === column.key);
    if (!c) {
      return;
    }
    c.listProps = c.listProps || {};
    c.listProps.visible = checked;
    resetCurrentVisibleColumns();
  };

  const handleAfterRowAction = (action: any, record: Record<string, any>) => {
    currentClickedRecord.value = record;
    if (action.key === 'view') {
      detailVisible.value = true;
    }
    listEmit('rowAction', action, record);
  };

  const handleLoadData = async () => {
    await loadData();
    const ref = detailModalRef.value || detailDrawerRef.value;
    if (ref) {
      ref.loadData();
    }
  };

  const handleRecordUpdate = (record: Record<string, any>, index) => {
    listData.value[index] = record;
    listEmit('update:data', listData.value);
  };

  onMounted(async () => {
    showColumns.value = initShowColumns();
    resetCurrentVisibleColumns();
    await listInit();

    if (props.autoLoad) {
      await handleLoadData();
    } else {
      listData.value = props.data || [];
    }
    if (props.pagination !== undefined) {
      pagination.value = props.pagination;
    }
  });

  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        listData.value = newVal || [];
      }
    },
  );

  watch(
    () => props.pagination,
    (newVal) => {
      if (newVal !== undefined) {
        pagination.value = newVal;
      }
    },
  );

  defineExpose({
    loading,
    setLoading,
    loadData,
    sorter,
    data: listData,
    queryParams,
    pagination,
    resetPage,
    tableRef,
    selectedItems,
    tableSize,
    handleSetTableSize,
    handleSetColumnVisible,
    showColumns,
    resetCurrentVisibleColumns,
    handleLoadData,
    getVisibleColumns: () => props.visibleColumns,
    defaultQueryParams: props.defaultQueryParams,
  });
</script>

<script lang="ts">
  export default {
    name: 'CrudTable',
  };
</script>

<style scoped lang="less">
  //.table-container {
  //  min-height: calc(100vh - 240px);
  //}

  :deep .in-cell-highlight {
    padding: 0 1px;
    color: red;
    font-weight: 500;
  }
</style>
