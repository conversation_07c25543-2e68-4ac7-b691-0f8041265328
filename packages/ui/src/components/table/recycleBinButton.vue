<script setup lang="ts">
  import { PropType, ref } from 'vue';
  import ModalWithFullscreen from '../common/modalWithFullscreen.vue';
  import RecycleBinTable from './recycleBinTable.vue';

  const props = defineProps({
    btnSize: {
      type: String,
      default: 'mini',
    },
    schema: {
      type: Object,
      required: true,
    },
    visibleColumns: {
      type: Array as PropType<string[] | undefined>,
      default: () => undefined,
    },
    defaultQueryParams: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  });

  const visible = ref(false);
  const emit = defineEmits(['refresh']);

  const handleShowRecycleBin = () => {
    visible.value = true;
  };

  const handleClose = () => {
    visible.value = false;
    emit('refresh');
  };
</script>

<template>
  <slot>
    <a-button :size="btnSize" @click="handleShowRecycleBin">
      <template #icon>
        <IconDelete />
      </template>
      回收站
    </a-button>
  </slot>

  <modal-with-fullscreen v-model:visible="visible" :fullscreen="true" hide-cancel ok-text="关闭" @close="handleClose">
    <template #title>
      <a-space>
        <IconDelete />
        回收站
      </a-space>
    </template>
    <recycle-bin-table
      v-if="schema && visible"
      :default-query-params="defaultQueryParams"
      :schema="schema"
      v-bind="$attrs"
      :visible-columns="visibleColumns"
    />
  </modal-with-fullscreen>
</template>

<style scoped lang="scss"></style>
