<script setup lang="ts">
  import { translate } from '@repo/infrastructure/utils';
  import { computed, inject, onMounted, PropType, ref, Ref } from 'vue';
  import { Schema, TableRowAction, TableRowActionView } from '@repo/infrastructure/types';
  import { Modal } from '@arco-design/web-vue';
  import { getDefaultRowActions } from './tableHelper';
  import FunctionalIcon from '../icon/functionalIcon.vue';

  const props = defineProps({
    record: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    rowActionWidth: {
      type: Number,
      default: 200,
    },
    rowActions: {
      type: Array as PropType<TableRowAction[]>,
      default: () => [],
    },
    loadData: {
      type: Function as PropType<() => any>,
      required: false,
    },
    actionsKeys: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    setLoading: {
      type: Function as PropType<(loading: boolean) => void>,
      required: false,
    },
    invisibleActions: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    exposeAll: {
      type: Boolean,
      default: false,
    },
    modulePath: {
      type: String as PropType<string>,
      required: false,
    },
  });

  const router = inject('router');
  const emits = defineEmits(['rowAction']);
  const handlerConfig = computed(() => {
    return {
      modulePath: props.modulePath,
      router,
    };
  });
  const actions: Ref<TableRowActionView> = ref<any>({} as TableRowActionView);

  const handleRowActionClick = (action: TableRowAction, record: Record<string, any>) => {
    if (action.handler) {
      action.handler(record, props.loadData, handlerConfig.value);
    }
    emits('rowAction', action, record, handlerConfig.value);
  };

  const handleRowActionSelect = (action: TableRowAction, record: Record<string, any>) => {
    if (action.confirm) {
      Modal.warning({
        title: translate('common.confirmPlease'),
        content: translate(action.confirm, {
          count: 1,
        }),
        hideCancel: false,
        onOk: () => {
          if (action.handler) {
            action.handler(record, props.loadData, handlerConfig.value);
            emits('rowAction', action, record, handlerConfig.value);
          }
        },
      });
    } else if (action.handler) {
      action.handler(record, props.loadData, handlerConfig.value);
      emits('rowAction', action, record, handlerConfig.value);
    }
  };

  const disableRowActionHandler = (record, op): boolean => {
    if (typeof props.schema?.disableRowActionHandler === 'function') {
      return props.schema?.disableRowActionHandler(record, op);
    }
    return false;
  };

  onMounted(async () => {
    const customSchema = {
      ...props.schema,
      modulePath: props.modulePath || props.schema.modulePath || props.schema.api,
    };

    const rawActionsMap = getDefaultRowActions(
      customSchema,
      props.loadData,
      props.actionsKeys,
      props.rowActions || props.schema?.rowActions || [],
      props.setLoading,
    );

    if (props.invisibleActions) {
      rawActionsMap.expose =
        rawActionsMap.expose.filter(
          (action) => !props.invisibleActions.includes(action.key) && action.hideInRow !== true,
        ) || [];
      rawActionsMap.others =
        rawActionsMap.others?.filter(
          (action) => !props.invisibleActions.includes(action.key) && action.hideInRow !== true,
        ) || [];
    }

    if (props.exposeAll) {
      rawActionsMap.expose = [...rawActionsMap.expose, ...(rawActionsMap.others || [])];
      rawActionsMap.others = [];
    }

    actions.value = rawActionsMap;
  });
</script>

<template>
  <div class="row-actions-wrapper">
    <div class="flex gap-2">
      <div
        v-for="(ep, ei) in actions.expose"
        v-show="typeof ep.visible === 'function' ? ep.visible(record) : true"
        :key="ei"
      >
        <a-popconfirm
          v-if="ep.confirm"
          :content="translate(ep.confirm, { count: 1 })"
          @ok="() => handleRowActionClick(ep, record)"
        >
          <a-button
            v-if="typeof ep.visible === 'function' ? ep.visible(record) : true"
            v-bind="ep.btnProps || {}"
            :size="ep.size || 'mini'"
            :disabled="
              (typeof ep.disabled === 'function' ? ep.disabled(record) : false) || disableRowActionHandler(record, ep)
            "
          >
            <template v-if="ep.icon" #icon>
              <FunctionalIcon :icon="ep.icon" />
            </template>
            <span v-if="!ep?.noWord">{{ translate(ep.label || 'common.' + ep.key) }}</span>
          </a-button>
        </a-popconfirm>
        <a-button
          v-else-if="typeof ep.visible === 'function' ? ep.visible(record) : true"
          :size="ep.size || 'mini'"
          :disabled="typeof ep.disabled === 'function' ? ep.disabled(record) : false"
          v-bind="ep.btnProps || {}"
          @click="() => handleRowActionClick(ep, record)"
        >
          <template v-if="ep.icon" #icon>
            <FunctionalIcon :icon="ep.icon" />
          </template>

          <!--{{ translate(ep.label || 'common.' + ep.key) }}-->
          <span v-if="!ep?.noWord">{{ translate(ep.label || 'common.' + ep.key) }}</span>
        </a-button>
      </div>
      <a-dropdown v-if="actions?.others?.length" @select="(action: any) => handleRowActionSelect(action, record)">
        <a-button size="mini">
          <template #icon>
            <icon-down />
          </template>
        </a-button>
        <template #content>
          <a-doption
            v-for="(op, oi) in actions.others"
            v-show="typeof op.visible === 'function' ? op.visible(record) : true"
            :key="oi"
            :value="op"
            :disabled="
              (typeof op.disabled === 'function' ? op.disabled(record) : false) || disableRowActionHandler(record, op)
            "
            :action="op"
          >
            <template v-if="op.icon" #icon>
              <FunctionalIcon :icon="op.icon" />
            </template>
            {{ translate(op.label || 'common.' + op.key) }}
          </a-doption>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
