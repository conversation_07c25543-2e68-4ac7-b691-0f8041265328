<template>
  <a-row :gutter="20">
    <a-col flex="1">
      <div class="grid grid-cols-3 gap-2">
        <div v-for="(c, ci) in rawFiltersDefinition" :key="ci">
          <div class="flex">
            <a-select
              v-if="c.column.filterProps.supportOperators?.length"
              v-model="filters[c.key].operator"
              class="text-right filter-field-title"
              size="mini"
              :class="{
                searching:
                  filters[c.key]?.formatValue &&
                  filters[c.key]?.formatValue(filters[c.key].value) !== undefined &&
                  filters[c.key].value !== undefined,
              }"
            >
              <a-option v-for="(op, oi) in c.column.filterProps.supportOperators" :key="oi" :value="op">
                {{ c.column.label }}
                {{ FILTER_OPERATORS[`common.comparator.${op}`] }}
              </a-option>
            </a-select>
            <a-input
              v-else
              readonly
              size="mini"
              class="text-right filter-field-title"
              :class="{
                searching:
                  filters[c.key]?.formatValue &&
                  filters[c.key]?.formatValue(filters[c.key].value) !== undefined &&
                  filters[c.key].value !== undefined,
              }"
              :default-value="`${c.column.label}`"
            />
            <component
              :is="c.component"
              v-model="filters[c.key].value"
              size="mini"
              class="custom-filter-component flex-1"
              :schema-field="c.columnDefine"
              :allow-clear="true"
            />
          </div>
        </div>
      </div>
    </a-col>
    <a-col :flex="'60px'" style="text-align: right" class="pl-2 border-l border-slate-200">
      <div class="flex gap-2 flex-col">
        <a-button size="mini" long type="outline" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
        <a-button size="mini" long @click="handleResetFilters">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button>
        <!--        <a-button long>-->
        <!--          {{ $t('common.saveCurrentSearchCondition') }}-->
        <!--        </a-button>-->
      </div>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import { Schema, TableColumn } from '@repo/infrastructure/types';
  import { useTableFilterStore } from '@repo/infrastructure/store';
  import { FILTER_OPERATORS, getColumnFilterable } from './tableHelper';

  const props = defineProps({
    table: {
      type: Object,
      required: true,
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
  });

  const filterStore = useTableFilterStore();
  const filters = ref<any>(filterStore.getFilter(props.schema.api).filters);
  const rawFiltersDefinition = ref<any>([]);

  const handleSearch = async () => {
    filterStore.setFilters(props.schema.api, filters.value);
    await props.table.loadData();
  };
  const handleResetFilters = async () => {
    filterStore.resetFilter(props.schema.api);
    Object.keys(filters.value).forEach((key) => {
      filters.value[key].value = undefined;
    });
    await props.table.loadData();
  };

  onMounted(() => {
    const filterableFields: any[] = [];
    if (props.schema?.listViewProps?.filterFields?.length) {
      filterableFields.push(
        ...Object.values(props.schema?.fieldsMap)
          .filter((c: TableColumn) => props.schema?.listViewProps?.filterFields.includes(c.key))
          .sort((a: TableColumn, b: TableColumn) => {
            return (
              // eslint-disable-next-line no-unsafe-optional-chaining
              props.schema?.listViewProps?.filterFields.indexOf(a.key) -
              // eslint-disable-next-line no-unsafe-optional-chaining
              props.schema?.listViewProps?.filterFields.indexOf(b.key)
            );
          }),
      );
    } else {
      filterableFields.push(...Object.values(props.schema?.fieldsMap).filter((c: TableColumn) => c.filterable));
    }
    rawFiltersDefinition.value = filterableFields.map((column: TableColumn) => {
      const { component, columnDefine } = getColumnFilterable(column);
      filterStore.getFilterItem(props.schema.api, columnDefine);
      filters.value[columnDefine.key!] = (filters.value[columnDefine.key!] || {
        operator: columnDefine.filterProps?.defaultOperator,
        value: undefined,
        field: columnDefine.filterProps?.targetField || columnDefine.key,
        formatValue:
          typeof columnDefine.inputWidgetProps?.formatValue === 'function'
            ? columnDefine.inputWidgetProps?.formatValue
            : (v: any) => v,
      }) as any;

      return {
        key: column.key,
        column: columnDefine,
        component,
        columnDefine,
      };
    });
  });
</script>

<style scoped lang="less">
  .searching,
  :deep .searching .arco-select-view-value {
    color: rgb(var(--primary-6)) !important;
  }
  :deep .filter-field-title {
    min-width: 0;
    max-width: 140px;
  }
</style>
