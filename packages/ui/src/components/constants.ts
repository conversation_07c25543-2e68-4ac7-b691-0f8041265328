import { computed } from 'vue';
import { translate } from '@repo/infrastructure/utils';

export const componentSizes = computed(() => [
  {
    name: translate('common.size.mini'),
    value: 'mini',
  },
  {
    name: translate('common.size.small'),
    value: 'small',
  },
  {
    name: translate('common.size.medium'),
    value: 'medium',
  },
  {
    name: translate('common.size.large'),
    value: 'large',
  },
]);
