<template>
  <a-space :size="16" direction="vertical" fill>
    <div
      v-for="(g, gi) in groups"
      v-show="g.label !== 'common.others' || schema.detailViewProps.showOthers !== false"
      :key="gi"
    >
      <crud-description
        bordered
        :columns="g.columns"
        :schema="schema"
        :show-columns="g.fields.map((item: any) => item.key!) || []"
        :raw="raw"
        :title="g.label === 'common.others' ? '' : g.label"
        @refresh="handleRefresh"
      />
    </div>
    <slot></slot>
  </a-space>
</template>

<script setup lang="ts">
  import { PropType } from 'vue';
  import CrudDescription from './index.vue';

  defineProps({
    schema: {
      type: Object as PropType<any>,
      required: true,
    },
    raw: {
      type: Object as PropType<any>,
      required: true,
    },
    groups: {
      type: Array as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['refresh']);
  const handleRefresh = () => {
    emit('refresh');
  };
</script>

<script lang="ts">
  export default {
    name: 'CrudDescriptionGroup',
  };
</script>

<style scoped></style>
