<template>
  <a-descriptions
    v-if="showData.length"
    bordered
    size="medium"
    v-bind="$attrs"
    :data="showData"
    :column="columns"
    :title="title === '__DEFAULT__' ? '' : title"
  >
    <template #value="{ data }">
      <data-display v-if="data.field" v-model:record="rawData" :schema-field="data.field" @refresh="handleRefresh" />
    </template>
  </a-descriptions>
</template>

<script lang="ts" setup>
  import { computed, onMounted, PropType, ref } from 'vue';
  import { Schema } from '@repo/infrastructure/types';
  import { translate } from '@repo/infrastructure/utils';
  import DataDisplay from '../data-display/index.vue';
  import { DescriptionData } from './types';

  const props = defineProps({
    raw: {
      type: Object as PropType<Record<string, any>>,
      default: () => {
        return {} as Record<string, any>;
      },
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    showColumns: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    columns: {
      type: Number,
      default: 2,
    },
    title: {
      type: String,
      default: '',
    },
  });

  const showData = ref<DescriptionData[]>([]);

  const emit = defineEmits(['refresh', 'update:raw']);
  const handleRefresh = () => {
    emit('refresh');
  };

  const rawData = computed({
    get: () => props.raw,
    set: (val) => {
      emit('update:raw', val);
    },
  });

  const isColumnVisible = (field) => {
    if (field.displayProps?.dynamicVisible) {
      return field.displayProps.dynamicVisible(props.raw);
    }
    return true;
  };

  onMounted(() => {
    const data: any = [];
    if (props.showColumns?.length) {
      props.showColumns.forEach((key) => {
        const field = props.schema.schemaFieldsMap[key];
        if (field && isColumnVisible(field)) {
          data.push({
            key,
            label: field.label === undefined ? key : field.label,
            value: '',
            span: field.displayProps?.detailSpan || 1,
            field,
          });
        }
      });
    } else {
      Object.keys(props.schema.schemaFieldsMap).forEach((key) => {
        const field = props.schema.schemaFieldsMap[key];
        if (field && isColumnVisible(field)) {
          data.push({
            key,
            label: field.label === undefined ? key : field.label,
            value: '',
            span: field.displayProps?.detailSpan || 1,
            field,
          });
        }
      });
    }

    showData.value = data || [];
  });
</script>
