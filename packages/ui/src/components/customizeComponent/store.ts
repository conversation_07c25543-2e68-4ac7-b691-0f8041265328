import { defineStore } from 'pinia';

const useFormTemplateStore = defineStore('useFormTemplateStore', {
  state: () => ({
    apiCache: {} as Record<string, any>, // 用于缓存 API 请求的数据
    isLoading: false,
  }),

  actions: {
    async handleOptions(getOptions: () => any, inputWidgetProps: any): Promise<any> {
      const key = this.generateCacheKey(inputWidgetProps?.api, inputWidgetProps?.params);
      if (this.apiCache[key]) {
        console.log(key, 'keyis', this.apiCache[key]);
        return this.apiCache[key];
      }
      try {
        this.isLoading = true;
        const res = await getOptions();
        const labelField = inputWidgetProps?.labelField || 'name';
        const valueField = inputWidgetProps?.valueField || 'id';

        this.apiCache[key] =
          res?.map((item: any) => ({
            label: item[labelField],
            value: item[valueField],
            // item,
          })) || [];

        return this.apiCache[key];
      } catch (error) {
        return [];
      } finally {
        this.isLoading = false;
      }
    },
    generateCacheKey(api: string, params: any): string {
      const paramsString = params ? JSON.stringify(params) : '';
      return `${api}-${paramsString}`;
    },
  },
});

export default useFormTemplateStore;
