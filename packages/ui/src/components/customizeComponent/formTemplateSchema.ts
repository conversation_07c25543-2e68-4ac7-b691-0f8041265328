import { Component } from 'vue';

export const genderOption: any[] = [
  { label: '男', value: 1 },
  { label: '女', value: 0 },
];

export const nationOption: string[] =
  '汉族|蒙古族|回族|藏族|苗族|维吾尔族|彝族|壮族|布依族|白族|朝鲜族|侗族|哈尼族|哈萨克族|满族|土家族|瑶族|达斡尔族|东乡族|高山族|景颇族|柯尔克孜族|拉祜族|纳西族|畲族|傣族|黎族|傈僳族|仫佬族|羌族|水族|土族|佤族|阿昌族|布朗族|毛南族|普米族|撒拉族|塔吉克族|锡伯族|仡佬族|保安族|德昂族|俄罗斯族|鄂温克族|京族|怒族|乌孜别克族|裕固族|独龙族|鄂伦春族|赫哲族|基诺族|珞巴族|门巴族|塔塔尔族'.split(
    '|',
  );
export const templateType: Record<string, any> = { SEND_PLAN: 'SEND_PLAN' };

export type Option = {
  label: string;
  value: number | string;
};

export type InputType =
  | 'text'
  | 'textarea'
  | 'datePicker'
  | 'attachment'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'number'
  | 'uploadArea'
  | 'richInput';

export type ForeignField = {
  api?: string;
};

export type InputWidgetProps = {
  size?: string;
  labelField?: string;
  valueField?: string;
  options?: (string | number | any)[];
  api: string;
  params?: any;
  getOptions?: (params?: Record<string, any>) => Promise<(string | number | any)[]>;
};

export type SchemaField = {
  label: string;
  field: string;
  inputType: InputType;
  disabledDate?: any[];
  required?: boolean;
  allowClear?: boolean;
  multiple?: boolean;
  limit?: number;
  max?: number; // 字数 or 可选
  direction?: string;
  options?: any;
  span?: number;

  foreignField?: ForeignField;
  inputWidgetProps?: InputWidgetProps;
};

export type ComponentProps = {
  label?: string;
  field: string;
  orderVisible?: boolean;
  inputWidget: Component;
  data?: any;
  columns?: (InputWidgetProps | any)[];
  inputWidgetProps?: InputWidgetProps;
  style: any;
  class: any;
};

export type FormTemplateSchema = {
  description?: string;
  descriptionColumn?: number;
  orientation?: string;
  fields?: SchemaField[];
  component?: ComponentProps;
};
