<script setup lang="ts">
  import { onMounted, PropType, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { richInput } from '@repo/ui/components/form/inputComponents';
  import { request } from '@repo/infrastructure/request';
  import dayjs from 'dayjs';
  import useFormTemplateStore from './store';

  import { FormTemplateSchema, SchemaField, templateType } from './formTemplateSchema.ts';
  import Uploader from '../upload/uploader.vue';

  const props = defineProps({
    record: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    type: {
      type: String,
      default: null,
      required: true,
    },
    title: {
      type: String,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'mini',
    },
    formSchema: {
      type: Array as PropType<any>,
      required: true,
      default: () => [],
    },
    handleSubmitData: {
      type: Function,
      default: () => {},
    },
  });

  const formData = ref<any>(props.record || { content: {} });
  const formConfig = ref<FormTemplateSchema[]>(props.formSchema);

  const getDeviceType = (): string | null => {
    const userAgent = navigator.userAgent.toLowerCase();
    // 判断是否是手机
    const isMobile = /mobile|android.*mobile|iphone|ipod|windows phone|blackberry|opera mini/.test(userAgent);
    // 判断是否是平板（iPad 或 Android 平板）
    const isTablet = /ipad|android(?!.*mobile)/.test(userAgent);
    // 判断是否是PC
    const isPC = !isMobile && !isTablet;

    if (isMobile) return 'UniAPP';

    if (isTablet) return null;

    if (isPC) return 'Web';

    return null;
  };

  const uploadVisible = ref(false);
  const formRef = ref();
  const componentRef = ref();

  const validateRes = async () => {
    let validationResults = [];
    if (componentRef.value?.length) {
      validationResults = await Promise.all(
        componentRef.value
          .filter((item) => item && typeof item.validateRes === 'function')
          .map((item) => item.validateRes()),
      );
    }
    const self = await formRef.value?.validate();
    const result = !(self && Object.keys(self).length > 0) && validationResults.every((res) => res);
    if (!result) {
      Message.warning('数据待完善');
    }
    return result;
  };

  const handleSubmit = async () => {
    if (await validateRes())
      try {
        const result = await props.handleSubmitData(formData.value); // 处理数据
        await request('/resourceRoom/customizeComponentTable/save', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'post',
          data: {
            type: templateType[props.type],
            client: getDeviceType(),
            ...result,
          },
        });
        Message.success('保存成功');
      } catch (e) {
        /**/
      }
  };

  const uploadedList = ref<any>([]);
  const currentUploadType = ref('Other');
  const currentUploadField = ref<SchemaField | null>(null);

  const handleUploads = async (options: any) => {
    if (!options?.fileItem?.file) return;
    const formDataSet = new FormData();
    formDataSet.set('type', currentUploadType.value === 'attachment' ? 'Wps' : 'Other');
    formDataSet.set('file', options.fileItem.file);
    formDataSet.set('fileName', options.fileItem.name);
    try {
      request('/common/uploadedResource/uploadAndGetDirectUrl', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: formDataSet,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((res) => {
        uploadedList.value.push({
          name: res.data.name,
          url: res.data.directVisitUrl,
          id: res.data.id,
        });
      });
    } finally {
      /**/
    }
  };

  const handleSelectChange = (val: any) => {};
  const handleShowUploadModal = (val: SchemaField) => {
    if (val) currentUploadField.value = val;
    else return;
    const { field } = val;
    if (Array.isArray(formData.value?.content[field]) && formData.value?.content[field].length > 0) {
      uploadedList.value = [...formData.value[field]];
    } else {
      formData.value.content[field] = [];
      uploadedList.value = [];
    }

    uploadVisible.value = true;
  };
  const handleUploadPreOk = async () => {
    if (currentUploadField.value !== null) {
      formData.value.content[currentUploadField.value.field] = [];
      formData.value.content[currentUploadField.value.field].push(...uploadedList.value);
    }
    uploadVisible.value = false;
    uploadedList.value = [];
    currentUploadField.value = null;
  };
  const getDir = (col: any) => {
    return col?.direction || 'horizontal';
  };
  const enable = (col: any, type: string) => {
    return col?.inputType === type;
  };

  const disabledDate = (current, range) => {
    if (!range || range?.length === 0) return false;
    const start = range[0];
    const end = range[1];
    return !dayjs(current).isBetween(start, end, 'day', '[]');
  };

  const ready = ref(false);
  const store = useFormTemplateStore();
  onMounted(async () => {
    const promises = formConfig.value.map(async (row) => {
      if (row.component?.inputWidget) {
        if (formData.value?.content[row.component.field])
          row.component.data = formData.value?.content[row.component.field];
      }
      if (Array.isArray(row.fields)) {
        const fieldPromises = row.fields.map(async (field) => {
          if (field.inputWidgetProps && typeof field.inputWidgetProps.getOptions === 'function') {
            try {
              const option = await store.handleOptions(field.inputWidgetProps.getOptions, field.inputWidgetProps);
              field.options = option;
              console.log(option, '数据');
            } finally {
              /**/
            }
          }
        });
        await Promise.all(fieldPromises);
      }
    });
    await Promise.all(promises).then(() => {
      ready.value = true;
    });
  });
  const handleUpdateFormData = (val: any, field: string) => {
    formData.value.content[field] = val;
  };
  defineExpose({ handleSubmit, validateRes, formData });
</script>

<template>
  <a-card v-if="ready" :bordered="false" :style="{ width: '100%', marginBottom: '20px' }">
    <!--<template #title>
      <span v-if="props.title" class="font-bold">{{ isEdit ? '修改' : '新增' + title }}</span>
    </template>-->
    <a-form ref="formRef" auto-label-width :model="formData.content" :size="size" @submit="handleSubmit">
      <a-row v-for="(row, indexRow) in formConfig" :key="indexRow" class="mt-2" gutter="24">
        <a-divider
          v-if="row?.description && row.description.trim() !== ''"
          class="m-0 p-0"
          :orientation="row?.orientation || 'left'"
        >
          <span class="font-bold">{{ row.description }}</span>
        </a-divider>
        <a-col v-for="(col, index) in row.fields" :key="index" :span="col?.span || 6" class="mb-2">
          <a-form-item
            :label="col?.label || ''"
            :field="col.field"
            :rules="[{ required: col.required || false, message: '请完善' + col.label }]"
          >
            <a-input
              v-if="enable(col, 'text')"
              v-model="formData.content[col.field]"
              :placeholder="'请输入' + col.label"
              :max-length="col.max || 255"
              show-word-limit
            />
            <a-input-number
              v-if="enable(col, 'number')"
              v-model="formData.content[col.field]"
              :placeholder="'请输入' + col.label"
              :max-length="col.max || 255"
              show-word-limit
            />
            <a-select
              v-else-if="enable(col, 'select')"
              v-model="formData.content[col.field]"
              :placeholder="'请选择' + col.label"
              allow-search
              :options="col.options || []"
              :allow-clear="col?.allowClear || false"
              :multiple="col?.multiple"
              @change="handleSelectChange"
            />
            <a-date-picker
              v-else-if="enable(col, 'datePicker')"
              v-model="formData.content[col.field]"
              :disabled-date="(current) => disabledDate(current, col?.disabledDate)"
            />
            <a-textarea v-else-if="enable(col, 'textarea')" v-model="formData.content[col.field]" />
            <a-range-picker
              v-if="enable(col, 'rangeDate')"
              v-model="formData.content[col.field]"
              :size="size"
              :disabled-date="(current) => disabledDate(current, col?.disabledDate)"
            />
            <a-checkbox-group
              v-else-if="enable(col, 'checkbox')"
              v-model="formData.content[col.field]"
              :max="col?.max || col.options.length"
              :direction="getDir(col)"
            >
              <a-checkbox v-for="(checkbox, checkboxIndex) in col.options" :key="checkboxIndex" :value="checkbox.value">
                {{ checkbox.label }}
              </a-checkbox>
            </a-checkbox-group>
            <a-radio-group v-else-if="enable(col, 'radio')" v-model="formData.content[col.field]">
              <a-radio v-for="(radio, radioIndex) in col.options" :key="radioIndex" :value="radio.value">
                {{ radio.label }}
              </a-radio>
            </a-radio-group>
            <a-button v-else-if="enable(col, 'attachment')" :size="size" @click="handleShowUploadModal(col)">
              <template #icon>
                <IconAttachment v-if="formData.content[col.field]?.length" />
                <icon-share-external v-else />
              </template>
              <span v-if="formData.content[col.field]?.length">{{ '查看附件' }}</span>
              <span v-else>{{ '上传' + col.label }}</span>
            </a-button>
            <richInput v-else-if="enable(col, 'richInput')" v-model="formData.content[col.field]" />
            <uploader
              v-else-if="enable(col, 'uploadArea')"
              v-model="formData.content[col.field]"
              :sub-folder="col?.subfolder || ''"
            />
          </a-form-item>
        </a-col>
        <a-form-item :label="row.component?.label">
          <a-col :span="24">
            <component
              :is="row.component?.inputWidget"
              v-if="row.component?.inputWidget"
              ref="componentRef"
              :component="row.component"
              @update-form-data="handleUpdateFormData"
            />
          </a-col>
        </a-form-item>
      </a-row>
      <a-form-item v-if="false">
        <div class="w-full flex justify-end mr-4 space-x-2">
          <a-button type="primary" html-type="submit">提交</a-button>
          <slot name="action-bar"></slot>
        </div>
      </a-form-item>
    </a-form>
  </a-card>

  <a-modal
    :visible="uploadVisible"
    :on-before-ok="handleUploadPreOk"
    :closable="false"
    title="上传附件"
    @cancel="uploadVisible = false"
  >
    <a-upload
      v-if="uploadVisible"
      :file-list="uploadedList"
      :multiple="false"
      draggable
      :custom-request="handleUploads as any"
      :limit="currentUploadField?.limit"
    >
      <template #upload-item="{ fileItem, index }">
        <div class="h-10 bg-gray-50 rounded mt-1 flex items-center justify-between">
          <div class="flex items-center ml-2">
            <!--<file-type-icon :file="record.name" :thumb-size="{ height: 20 }" :url="record.url || record.udf1" />-->
            <icon-file />
            <span class="ml-2">{{ fileItem?.name }} </span>
          </div>

          <div class="flex items-center mr-4 space-x-4">
            <div v-if="fileItem.percent === 0" class="text-blue-500">正在准备上传...</div>
            <div v-else-if="fileItem.percent < 1">已上传 {{ (fileItem.percent * 100).toFixed(2) }}%</div>
            <div v-else class="text-lime-600">
              <icon-check />
              完成
            </div>
            <icon-delete
              class="cursor-pointer"
              @click="
                () => {
                  uploadedList.splice(index, 1);
                }
              "
            />
          </div>
        </div>
      </template>
    </a-upload>
  </a-modal>
</template>

<style scoped lang="scss"></style>
