<script setup lang="ts">
  import { computed, defineProps, nextTick, onMounted, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import useFormTemplateStore from '../store';

  const props = defineProps({
    component: {
      type: Object,
      default: null,
    },
    // 是否禁用操作
    disable: {
      type: Boolean,
      default: false,
    },
    justView: {
      type: Boolean,
      default: false,
    },
    allowAdd: {
      type: Boolean,
      default: true,
    },
  });

  const size = computed(() => {
    if (props.component?.size) return props.component?.size;
    return 'mini';
  });
  const component = ref(cloneDeep(props.component));

  const data = ref(cloneDeep(props.component?.data || []));

  const handleDel = (record: any, rawIndex: number) => {
    data.value.splice(rawIndex, 1);
  };

  const editVisible = ref(false);
  const oldRecord = ref<any>(null);
  const newRecord = ref<any>(null);
  const currentColumn = ref<any>(null);
  const handleDbClick = (record, column) => {
    if (!component.value?.columns.find((item) => item?.allowDbClick && item.dataIndex === column.dataIndex)) return;
    if (props.disable) {
      Message.warning('查看模式不可修改');
      return;
    }
    oldRecord.value = cloneDeep(record);
    newRecord.value = record;
    currentColumn.value = column;
    editVisible.value = true;
  };

  const getOptionsLabel = (options: any[], val: any): string => {
    if (Array.isArray(val)) {
      return val
        .map((item) => options.find((option) => option.value === item)?.label)
        .filter(Boolean)
        .join(' ');
    }
    return options.find((option) => option.value === val)?.label || '';
  };

  const emits = defineEmits(['updateFormData']);
  const visible = ref(false);
  const store = useFormTemplateStore();

  const disabledDate = (current, range) => {
    if (!range || range?.length === 0) return false;
    const start = range[0];
    const end = range[1];
    return !dayjs(current).isBetween(start, end, 'day', '[]');
  };

  onMounted(async () => {
    const promises = component.value.columns.map(async (row) => {
      if (typeof row.getOptions === 'function') {
        try {
          row.options = await store.handleOptions(row.getOptions, row);
        } catch (e) {
          /**/
        }
      }
    });
    await Promise.all(promises).then(() => {
      visible.value = true;
    });
  });

  watch(
    () => data.value,
    async (newValue) => {
      await nextTick();
      emits('updateFormData', newValue, component.value?.field);
    },
    { deep: true },
  );
  const formRef = ref();
  const handleSubmit = () => {};
  const validateRes = async () => {
    const res = await formRef.value.validate();
    return !(res && Object.keys(res).length > 0);
  };
  defineExpose({ validateRes });
</script>

<template>
  <div v-if="allowAdd" class="flex w-full" :class="component?.class">
    <a-button v-if="!justView" :size="size" class="mb-2" @click="() => data.push({})">
      <icon-plus />
      新增
    </a-button>
  </div>
  <a-form ref="formRef" :model="data" auto-label-width @submit="handleSubmit">
    <a-table
      v-if="visible"
      class="w-full"
      :columns="component?.columns"
      :data="data"
      :size="size"
      column-resizable
      :bordered="{ cell: true }"
      @cell-dblclick="handleDbClick"
    >
      <template #columns>
        <a-table-column v-if="component?.orderVisible" title="序号" width="60" align="center">
          <template #cell="{ rowIndex }">
            <span>{{ rowIndex + 1 }}</span>
          </template>
        </a-table-column>

        <a-table-column
          v-for="(item, index) in component?.columns"
          :key="index"
          :title="item.title"
          :data-index="item.dataIndex"
          :width="item?.width"
        >
          <template #cell="{ record, rowIndex }">
            <div class="flex justify-start items-center h-full w-full p-1">
              <a-form-item
                :field="`${rowIndex}.${item.dataIndex}`"
                :rules="[{ required: item?.required && !justView, message: '请完善' }]"
                no-style
              >
                <div v-if="item.type === 'title'">
                  {{ record[item.dataIndex] || '双击修改' }}
                </div>
                <a-input v-if="item.type === 'text' && !justView" v-model="record[item.dataIndex]" :size="size" />
                <a-input-number
                  v-if="item.type === 'number' && !justView"
                  v-model="record[item.dataIndex]"
                  :size="size"
                  :disabled="disable"
                  :min="item?.min || 0"
                />
                <a-textarea
                  v-if="item.type === 'textarea' && !justView"
                  v-model="record[item.dataIndex]"
                  :size="size"
                  :disabled="disable"
                />
                <a-date-picker
                  v-if="item.type === 'date' && !justView"
                  v-model="record[item.dataIndex]"
                  :size="size"
                  :disabled="disable"
                  :disabled-date="(current) => disabledDate(current, col?.disabledDate)"
                />
                <span v-if="['text', 'number', 'date', 'textarea'].includes(item.type) && justView">
                  {{ record[item.dataIndex] }}
                </span>

                <a-range-picker
                  v-if="item.type === 'rangeDate' && !justView"
                  v-model="record[item.dataIndex]"
                  :size="size"
                  :disabled="disable"
                  :disabled-date="(current) => disabledDate(current, col?.disabledDate)"
                />
                <span v-if="item.type === 'rangeDate' && justView">
                  {{ record[item.dataIndex][0] + ' - ' + record[item.dataIndex][1] }}
                </span>

                <a-select
                  v-if="item.type === 'select' && !justView"
                  v-model="record[item.dataIndex]"
                  :size="size"
                  :options="item.options"
                  :disabled="disable"
                  :multiple="item?.multiple"
                />
                <div v-if="item.type === 'select' && justView && item?.multiple" class="flex space-x-2">
                  <span v-for="(selectValue, selectIndex) in record[item.dataIndex]" :key="selectIndex" :size="size">
                    {{ getOptionsLabel(item.options, selectValue) }}
                  </span>
                </div>
                <div v-else-if="item.type === 'select' && justView && !item?.multiple">
                  {{ getOptionsLabel(item.options, record[item.dataIndex]) }}
                </div>

                <a-checkbox-group
                  v-if="item.type === 'checkbox' && !justView"
                  v-model="record[item.dataIndex]"
                  :max="item?.max || item.options.length"
                  :disabled="disable"
                >
                  <a-checkbox
                    v-for="(checkbox, checkboxIndex) in item?.options"
                    :key="checkboxIndex"
                    :value="checkbox.value"
                  >
                    {{ checkbox.label }}
                  </a-checkbox>
                </a-checkbox-group>
                <div v-if="item.type === 'checkbox' && justView">
                  <span>{{ getOptionsLabel(item.options, record[item.dataIndex]) }}</span>
                </div>

                <div v-if="item.slotName === 'operation'" class="flex justify-center items-center space-x-2">
                  <slot name="operation" :item="record" :row-index="rowIndex" />
                  <a-button
                    :size="size"
                    type="outline"
                    status="danger"
                    :disabled="disable"
                    @click="handleDel(record, rowIndex)"
                    >删除
                  </a-button>
                </div>
              </a-form-item>
            </div>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-form>
  <a-modal v-if="editVisible" v-model:visible="editVisible" title="修改" @cancel="editVisible = false">
    <span>旧数据：</span>
    <a-input v-model="oldRecord[currentColumn.dataIndex]" :size="size" class="mb-3" :disabled="true" />
    <span>新数据：</span>
    <a-input v-model="newRecord[currentColumn.dataIndex]" :size="size" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
