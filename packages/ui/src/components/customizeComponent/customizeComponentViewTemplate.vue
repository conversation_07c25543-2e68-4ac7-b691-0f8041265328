<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import { FormTemplateSchema, SchemaField } from './formTemplateSchema';
  import useFormTemplateStore from './store';
  import AttachmentPreviewModal from '../data-display/attachmentPreviewModal.vue';
  import UploaderModal from '../upload/uploaderModal.vue';

  const props = defineProps({
    formSchema: {
      type: Array as PropType<Array<FormTemplateSchema>>,
      required: false,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });
  const schema = ref<FormTemplateSchema[]>(cloneDeep(props.formSchema));

  const getLabel = (col: SchemaField, val: any): any => {
    if (Array.isArray(val)) {
      const display = col.options.filter((item) => val.includes(item.value));
      const result = [];
      display.forEach((item) => {
        result.push(item.label);
      });
      return result;
    }
    if (Array.isArray(col?.options) && col.options.length > 0) {
      const option = col.options.find((item) => item.value === val);
      return [option?.label];
    }
    return null;
  };
  const content = ref({});
  const visible = ref(false);

  const store = useFormTemplateStore();
  const init = async () => {
    const promises = schema.value.map(async (row) => {
      if (row.component?.inputWidget) {
        row.component.data = content.value?.[row.component.field] || [];
      }
      if (Array.isArray(row.fields)) {
        const fieldPromises = row.fields.map(async (field) => {
          if (field.inputWidgetProps && typeof field.inputWidgetProps.getOptions === 'function') {
            try {
              field.options = await store.handleOptions(field.inputWidgetProps.getOptions, field.inputWidgetProps);
            } catch (error) {
              field.options = [];
            }
          }
        });
        await Promise.allSettled(fieldPromises);
      }
    });
    await Promise.allSettled(promises).then(() => {
      visible.value = true;
    });
  };

  const previewVisible = ref(false);
  const openIndex = ref(0);
  const uploadedList = ref();
  const handlePreview = (files) => {
    previewVisible.value = true;
    uploadedList.value = files;
  };

  onMounted(async () => {
    content.value = { ...props.modelValue?.content };
    if (content.value) await init();
  });
  watch(
    () => props.modelValue,
    async (newVal) => {
      if (newVal?.content) {
        content.value = { ...newVal.content };
        await init();
      }
    },
    { immediate: true },
  );
</script>

<template>
  <a-card v-if="visible" :title="title" :bordered="false">
    <a-descriptions v-for="(item, index) in schema" :key="index" bordered :column="item?.descriptionColumn || 4">
      <template #title>
        <span v-if="!item.component?.inputWidget">{{ item.description }}</span>
      </template>
      <a-descriptions-item
        v-for="(col, colIndex) in item?.fields"
        v-show="!item.component?.inputWidget"
        :key="colIndex"
        :label="col?.label"
      >
        <div v-if="col?.inputType === 'richInput'" v-html="content?.[col.field]" />
        <div v-else-if="col?.inputType === 'uploadArea'">
          <a-button v-if="content[col.field]?.length > 0" size="mini" @click="handlePreview(content[col.field])">
            <icon-attachment />查看附件
          </a-button>
          <span v-else>暂无附件</span>
        </div>
        <div v-else-if="col?.options" class="flex space-x-3">
          <span v-for="(optionSingle, optionIndex) in getLabel(col, content?.[col.field])" :key="optionIndex">
            {{ optionSingle }}
          </span>
        </div>
        <div v-else>
          {{ content?.[col.field] }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item v-if="item.component?.inputWidget">
        <template #label>
          <span class="" style="width: 40px">{{ item?.description }}</span>
        </template>
        <component :is="item.component.inputWidget" :component="item.component" :disable="true" :just-view="true" />
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
  <attachment-preview-modal v-model="previewVisible" :current-file-index="openIndex" :files-list="uploadedList" />
</template>

<style scoped lang="scss"></style>
