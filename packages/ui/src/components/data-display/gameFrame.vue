<script setup lang="ts">
  import { computed } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { getToken } from '@repo/infrastructure/auth';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    resourceId: {
      type: Number,
      required: true,
    },
  });

  const userInfo = useUserStore();

  const gameUrl = computed(() => {
    return `${PROJECT_URLS.GO_PROJECT_API}/resource/digital/game/${props.resourceId}?token=${getToken()}&userId=${userInfo.id}&loginSource=PC`;
  });
</script>

<template>
  <iframe :src="gameUrl" class="game-frame" />
</template>

<style scoped lang="less">
  .game-frame {
    width: 100%;
    height: 100%;
    border: none;
  }
</style>
