<template>
  <a-modal
    v-model:visible="visible"
    :mask-closable="false"
    :on-before-close="handleClose"
    :width="width"
    :title="attachment?.name"
    hide-cancel
    :fullscreen="fullscreen"
    :render-to-body="false"
    @before-open="handleBeforeOpen"
  >
    <template #title>
      <a-space class="justify-between w-full items-center flex-1">
        <div>
          {{ attachment?.name }}
        </div>
        <div>
          <a-button class="mr-12" size="mini" @click="() => (fullscreen = !fullscreen)">
            <template #icon>
              <IconFullscreen v-if="!fullscreen" />
              <IconFullscreenExit v-else />
            </template>
            {{ fullscreen ? '退出全屏' : '全屏' }}
          </a-button>
        </div>
      </a-space>
    </template>
    <div v-if="visible && currentFile" class="preview-dialog-wrapper mx-auto my-2 text-center">
      <div v-if="dataType === 'Image'" class="image-container text-center mx-auto">
        <img :src="attachmentUrl" :alt="attachment.name" class="mx-auto" />
      </div>
      <div v-else-if="dataType === 'Video'" class="text-center" style="margin: 0 auto">
        <video-player :src="attachmentUrl" />
      </div>
      <div v-else-if="dataType === 'PDF'">
        <iframe
          :src="`${attachmentUrl}`"
          width="100%"
          height="100%"
          style="width: 100%; height: 100%; border: none; min-height: 800px"
        ></iframe>
      </div>
      <div v-else-if="dataType === 'Office'">
        <iframe
          :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURI(attachmentUrl)}`"
          width="100%"
          height="100%"
          style="width: 100%; height: 100%; border: none; min-height: 800px"
        ></iframe>
      </div>
      <div v-else class="mt-2 text-center">暂不支持预览</div>

      <div
        v-if="filesList.length > 0 && activeAttachmentIndex > 0"
        class="switch-btn left"
        @click="activeAttachmentIndex -= 1"
      >
        <IconArrowLeft />
      </div>
      <div
        v-if="filesList.length > 0 && activeAttachmentIndex + 1 < filesList.length"
        class="switch-btn right"
        @click="activeAttachmentIndex += 1"
      >
        <IconArrowRight />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { getToken } from '@repo/infrastructure/auth';
  import { VideoPlayer } from '@repo/ui/components';
  import { PROJECT_URLS } from '@repo/env-config';
  import { computed, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    openIndex: {
      type: Number,
      default: 0,
    },
    previewBtnCls: {
      type: String,
      default: 'preview-button',
    },
    imageDisplay: {
      type: Boolean,
      default: false,
    },
    allowDownload: {
      type: Boolean,
      default: false,
    },
    fileType: {
      type: String,
    },
    filesList: {
      type: Array,
      default: () => [],
    },
    currentFileIndex: {
      type: Number,
      default: 0,
    },
    width: {
      type: String,
      default: '70%',
    },
  });

  const activeAttachmentIndex = ref(0);
  const attachment = ref<any>(null);
  const currentFile = ref<any>(null);

  const emit = defineEmits(['update:modelValue']);

  const fullscreen = ref(false);

  const visible = computed({
    get: () => {
      return props.modelValue;
    },
    set: (val) => {
      emit('update:modelValue', val);
    },
  });

  const directUrl = computed(() => {
    return currentFile.value?.url || currentFile.value?.udf1;
  });

  const dataType = computed(() => {
    if (props.fileType) {
      return props.fileType;
    }
    if (!currentFile.value) {
      return '';
    }
    const name = currentFile.value.name?.toLowerCase() || '';
    let dt = 'unknown';
    if (name.endsWith('.png') || name.indexOf('.jp') > 0 || name.endsWith('.gif') || name.endsWith('.webp')) {
      dt = 'Image';
    } else if (name.endsWith('.pdf')) {
      dt = 'PDF';
    } else if (name.indexOf('.doc') > 0 || name.indexOf('.xls') > 0 || name.indexOf('.ppt') > 0) {
      dt = 'Office';
    } else if (
      name.endsWith('.mp4') ||
      name.endsWith('.mov') ||
      name.endsWith('.avi') ||
      name.endsWith('.flv') ||
      name.endsWith('.wmv') ||
      name.endsWith('.m4v')
    ) {
      dt = 'Video';
    }

    return dt;
  });

  const attachmentUrl = computed(() => {
    if (directUrl.value) {
      return directUrl.value;
    }
    let base = `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource`;
    if (dataType.value === 'Image') {
      base = `${base}/image/${attachment.value.id}`;
    } else {
      base = `${base}/goDirect/${attachment.value.id}`;
    }

    const token = getToken();
    base = `${base}?token=${token}&loginSource=PC`;

    return base;
  });

  watch(
    () => activeAttachmentIndex.value,
    (val) => {
      if (!props.filesList?.length) {
        return;
      }

      currentFile.value = props.filesList[val];
      attachment.value = cloneDeep(currentFile.value || {});
    },
  );

  const handleBeforeOpen = async () => {
    currentFile.value = props.filesList[props.currentFileIndex];
    attachment.value = cloneDeep(currentFile.value || {});
    activeAttachmentIndex.value = props.currentFileIndex;
  };

  const handleClose = () => {
    currentFile.value = null;
    visible.value = false;
  };
</script>

<style lang="scss" scoped>
  .preview-button {
    padding: 0;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .image-container {
    img {
      max-width: 100%;
      max-height: 90vh;
    }
  }
  .preview-dialog-wrapper {
    position: relative;
    .switch-btn {
      position: absolute;
      top: 180px;
      z-index: 99;
      padding: 14px;
      color: #fff;
      border: 1px solid #f2f2f2;
      background-color: rgba(0, 0, 0, 0.5);
      font-size: 20px;
      &:hover {
        background-color: rgba(0, 0, 0, 0.8);
      }
      &.left {
        left: 0;
      }
      &.right {
        right: 0;
      }
    }
  }
</style>
