<script setup lang="ts">
  import '@vime/core/themes/light.css';
  import { DefaultUi, Player } from '@vime/vue-next';
  import { computed, ref } from 'vue';
  import { getOssProcessor } from '@repo/infrastructure/upload';

  const props = defineProps({
    src: {
      type: String,
      required: true,
    },
    poster: {
      type: String,
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
  });

  const playerRef = ref(null);

  const ossProcessor = getOssProcessor();

  const computedPoster = computed(() => {
    if (props.poster) {
      return props.poster;
    }

    return ossProcessor.videoCoverUrl(props.src, 600, 400);
  });

  const handlePlay = () => {
    const allPlayers = document.querySelectorAll('video');
    allPlayers.forEach((player) => {
      if (player !== playerRef.value) {
        player.pause();
      }
    });
  };
</script>

<template>
  <div>
    <player ref="player" playsinline>
      <video
        ref="playerRef"
        :poster="computedPoster"
        :autoplay="autoplay"
        :src="src"
        controls
        controlslist="nodownload"
        @play="handlePlay"
      />
      <default-ui />
    </player>
  </div>
</template>

<style scoped lang="less">
  video {
    width: 100%;
    height: 100%;
  }
</style>
