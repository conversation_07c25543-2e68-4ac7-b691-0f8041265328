<script setup lang="ts">
  import { PropType } from 'vue';
  import AvatarDisplay from './avatarDisplay.vue';

  defineProps({
    userList: {
      type: Array as PropType<any[]>,
    },
    maxCount: {
      type: Number,
      default: 5,
    },
  });
</script>

<template>
  <a-avatar-group :max-count="maxCount">
    <avatar-display v-for="user in userList" v-bind="$attrs" :key="user.id" :user-card="true" :user-info="user" />
  </a-avatar-group>
</template>

<style scoped lang="scss"></style>
