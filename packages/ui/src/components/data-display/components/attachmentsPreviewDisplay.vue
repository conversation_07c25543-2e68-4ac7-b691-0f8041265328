<script setup lang="ts">
  import { ref } from 'vue';
  import AttachmentPreviewModal from '../attachmentPreviewModal.vue';

  const props = defineProps({
    raw: {
      type: Array,
      required: true,
    },
    openIndex: {
      type: Number,
      default: 0,
    },
  });

  const visible = ref(false);

  const handlePreview = () => {
    visible.value = true;
  };
</script>

<template>
  <span v-if="raw?.length" @click="handlePreview">
    <slot>
      <a-button size="mini">
        <template #icon>
          <IconAttachment />
        </template>
        查看附件({{ raw?.length || 0 }})
      </a-button>
    </slot>
  </span>

  <attachment-preview-modal v-model="visible" :current-file-index="openIndex" :files-list="raw || []" />
</template>

<style scoped lang="scss"></style>
