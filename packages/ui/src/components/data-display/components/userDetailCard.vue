<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { getRandomBackgroundGradient } from '@repo/infrastructure/constants';
  import { colorful } from '@repo/infrastructure/utils';
  import AvatarOnlyDisplay from './avatarOnlyDisplay.vue';

  const props = defineProps({
    userDetail: {
      type: Object as PropType<any>,
      required: true,
    },
    displayName: {
      type: String,
      default: '',
    },
    userDetailLoading: {
      type: Boolean,
      default: false,
    },
    avatarSrc: {
      type: String,
      default: '',
    },
    cardClassName: {
      type: String,
      default: '',
    },
  });

  const cardAvatarBg = computed(() => {
    if (!props.userDetail.id) {
      return '';
    }
    return getRandomBackgroundGradient(props.userDetail.id);
  });

  const cardAvatarSize = 64;
  const cardAvatarStyle = computed(() => {
    return {
      backgroundColor: colorful(props.displayName),
      width: `${cardAvatarSize}px`,
      height: `${cardAvatarSize}px`,
    };
  });
</script>

<template>
  <div :class="cardClassName">
    <a-spin v-if="userDetailLoading" :loading="userDetailLoading" animation class="w-full p-4">
      <a-skeleton-line :rows="3" class="w-full" />
    </a-spin>
    <div v-else>
      <div :style="{ background: cardAvatarBg }" class="p-4 py-6 w-full text-center">
        <avatar-only-display
          :user-info="userDetail"
          :size="cardAvatarSize"
          :avatar-src="avatarSrc"
          :avatar-style="cardAvatarStyle"
          :display-name="displayName"
          class="!mx-auto !border-white"
          name-class="!p-4"
        />
      </div>
      <div class="p-2 text-xs flex flex-col gap-2">
        <a-space class="items-center">
          <IconHome />
          {{ userDetail.branchOffice?.name }}
        </a-space>
        <a-space class="items-center">
          <IconPhone />
          {{ userDetail.mobile || '无联系方式' }}
          <a-tooltip v-if="userDetail.mobile && !userDetail.certifiedAt" content="尚未认证">
            <IconQuestionCircleFill class="text-red-500" />
          </a-tooltip>
          <a-tooltip v-else-if="userDetail.mobile" content="手机号已认证">
            <IconCheckCircleFill class="text-green-500" />
          </a-tooltip>
        </a-space>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
