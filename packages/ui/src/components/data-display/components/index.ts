import { defineAsyncComponent, markRaw } from 'vue';

const DateDisplay = defineAsyncComponent(() => import('./dateDisplay.vue'));
const ForeignDisplay = defineAsyncComponent(() => import('./foreignDisplay.vue'));
const BooleanDisplay = defineAsyncComponent(() => import('./booleanDisplay.vue'));
const AvatarDisplay = defineAsyncComponent(() => import('./avatarDisplay.vue'));
const AvatarOnlyDisplay = defineAsyncComponent(() => import('./avatarOnlyDisplay.vue'));
const ImagePreviewDisplay = defineAsyncComponent(() => import('./imagePreviewDisplay.vue'));
const AttachmentsPreviewDisplay = defineAsyncComponent(() => import('./attachmentsPreviewDisplay.vue'));
const ListTableDisplay = defineAsyncComponent(() => import('./listTableDisplay.vue'));
const AvatarGroupDisplay = defineAsyncComponent(() => import('./avatarGroupDisplay.vue'));
const UserAvatarListDisplay = defineAsyncComponent(() => import('./userAvatarListDisplay.vue'));
const StringListDisplay = defineAsyncComponent(() => import('./stringListDisplay.vue'));

const RichTextDisplay = defineAsyncComponent(() => import('./richTextDisplay.vue'));
const AttachmentsDisplay = defineAsyncComponent(() => import('./attachmentsDisplay.vue'));

const displayComponents: Record<any, any> = {
  DateDisplay: markRaw(DateDisplay),
  ForeignDisplay: markRaw(ForeignDisplay),
  BooleanDisplay: markRaw(BooleanDisplay),
  AvatarDisplay: markRaw(AvatarDisplay),
  ImagePreviewDisplay: markRaw(ImagePreviewDisplay),
  AttachmentsPreviewDisplay: markRaw(AttachmentsPreviewDisplay),
  ListTableDisplay: markRaw(ListTableDisplay),
  AvatarGroupDisplay: markRaw(AvatarGroupDisplay),
  UserAvatarListDisplay: markRaw(UserAvatarListDisplay),
  StringListDisplay: markRaw(StringListDisplay),
  RichTextDisplay: markRaw(RichTextDisplay),
  AvatarOnlyDisplay: markRaw(AvatarOnlyDisplay),
  AttachmentsDisplay: markRaw(AttachmentsDisplay),
};

export default displayComponents;

export {
  DateDisplay,
  ForeignDisplay,
  BooleanDisplay,
  AvatarDisplay,
  ImagePreviewDisplay,
  AttachmentsPreviewDisplay,
  ListTableDisplay,
  AvatarGroupDisplay,
  UserAvatarListDisplay,
  StringListDisplay,
  RichTextDisplay,
  AvatarOnlyDisplay,
  AttachmentsDisplay,
};
