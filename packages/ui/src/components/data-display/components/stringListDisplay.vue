<template>
  <div class="w-full flex flex-col gap-2">
    <a-space v-for="(item, idx) in raw" :key="item" class="item-center">
      <span>{{ idx + 1 }}、</span>
      <span>{{ item }}</span>
    </a-space>
  </div>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import CrudTable from '../../table/table.vue';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    raw: {
      type: Array as PropType<any[]>,
      default: () => undefined,
    },
  });
</script>

<style scoped></style>
