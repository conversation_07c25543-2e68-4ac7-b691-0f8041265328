<template>
  <slot name="prepend"></slot>
  <crud-table
    size="mini"
    :schema="schema"
    :data="raw"
    :row-selection="false"
    :auto-load="false"
    :pagination="false"
    :show-row-actions="false"
  />
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import CrudTable from '../../table/table.vue';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    raw: {
      type: Array as PropType<any[]>,
      default: () => undefined,
    },
  });

  const schema = computed(() => {
    return {
      api: `/virtual/list-table/${props.schemaField?.key}`,
      listViewProps: {
        pagination: false,
        size: 'mini',
      },
      schemaFieldsMap: props.schemaField?.displayProps?.columns?.reduce(
        (acc, column) => {
          acc[column.key] = column;
          return acc;
        },
        ({} as Record<string, SchemaField>) || {},
      ),
      schemaFields: props.schemaField?.displayProps?.columns || [],
    };
  });
</script>

<style scoped></style>
