<script setup lang="ts">
  import { PropType } from 'vue';

  const props = defineProps({
    userInfo: {
      type: Object as PropType<any>,
      required: true,
    },
    shape: {
      type: String as PropType<'circle' | 'square'>,
      default: 'circle',
    },
    size: {
      type: [String, Number] as PropType<string | number>,
      default: 32,
    },
    showTrigger: {
      type: Boolean,
      default: false,
    },
    avatarSrc: {
      type: String,
      default: '',
    },
    avatarStyle: {
      type: Object,
      default: () => ({}),
    },
    displayName: {
      type: String,
      default: '',
    },
    nameClass: {
      type: String,
      default: 'p-2',
    },
    className: {
      type: String,
      default: '',
    },
  });
</script>

<template>
  <a-avatar
    :size="Number(size)"
    :style="avatarStyle"
    :auto-fix-font-size="!avatarSrc"
    :shape="shape"
    v-bind="$attrs"
    class="avatar flex items-center"
    :class="className"
  >
    <slot name="content" :avatar-src="avatarSrc" :user-info="userInfo" :display-name="displayName">
      <img v-if="avatarSrc" :src="avatarSrc" alt="avatar" />
      <img v-else-if="userInfo.avatar" :src="userInfo.avatar" alt="avatar" />

      <!--// @todo 使用用户id显示头像 -->
      <span v-else :class="nameClass">{{ displayName }}</span>
    </slot>
    <template v-if="showTrigger" #trigger-icon>
      <slot name="trigger-icon"></slot>
    </template>
  </a-avatar>
</template>

<style scoped lang="scss"></style>
