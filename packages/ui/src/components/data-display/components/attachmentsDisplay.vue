<template>
  <div class="attachment-viewer">
    <div v-if="currentAttachment" class="preview-area">
      <div v-if="dataType === 'Image'" class="image-preview">
        <img :src="attachmentUrl" :alt="currentAttachment.name" />
      </div>
      <div v-else-if="dataType === 'Video'" class="video-preview">
        <video-player :src="attachmentUrl" />
      </div>
      <div v-else-if="dataType === 'PDF'" class="pdf-preview">
        <iframe :src="attachmentUrl" frameborder="0"></iframe>
      </div>
      <div v-else-if="dataType === 'Office'" class="office-preview">
        <iframe :src="officePreviewUrl" frameborder="0"></iframe>
      </div>
      <div v-else class="unsupported-preview">
        <p>暂不支持预览此文件类型。</p>
        <p class="file-name">{{ currentAttachment.name }}</p>
        <p v-if="allowDownload" class="download-tip">
          您可以
          <a :href="attachmentUrl" target="_blank" download>点击此处下载</a>
          查看。
        </p>
      </div>

      <div
        v-if="attachments.length > 1 && activeAttachmentIndex > 0"
        class="nav-button nav-button--left"
        @click="activeAttachmentIndex--"
      >
        <IconLeft />
      </div>
      <div
        v-if="attachments.length > 1 && activeAttachmentIndex < attachments.length - 1"
        class="nav-button nav-button--right"
        @click="activeAttachmentIndex++"
      >
        <IconRight />
      </div>
    </div>
    <div v-else class="no-attachment">
      <p>暂无附件可供预览。</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, onMounted } from 'vue';
  import { VideoPlayer } from '@repo/ui/components';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import { IconLeft, IconRight } from '@arco-design/web-vue/es/icon';

  interface AttachmentItem {
    id?: string;
    name: string;
    url?: string;
    udf1?: string;
  }

  const props = defineProps({
    raw: {
      type: Array as () => AttachmentItem[],
      default: () => [],
    },
    allowDownload: {
      type: Boolean,
      default: false,
    },
    explicitFileType: {
      type: String as () => 'Image' | 'Video' | 'PDF' | 'Office' | 'unknown' | undefined,
      default: undefined,
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
  });

  const attachments = ref<AttachmentItem[]>([]);
  const activeAttachmentIndex = ref(props.initialIndex);

  const currentAttachment = computed<AttachmentItem | undefined>(() => {
    return attachments.value[activeAttachmentIndex.value] || undefined;
  });

  const directUrl = computed<string | undefined>(() => {
    return currentAttachment.value?.udf1 || currentAttachment.value?.url;
  });

  const dataType = computed<'Image' | 'Video' | 'PDF' | 'Office' | 'unknown'>(() => {
    if (props.explicitFileType) {
      return props.explicitFileType;
    }
    if (!currentAttachment.value) {
      return 'unknown';
    }
    const name = currentAttachment.value.name?.toLowerCase() || '';
    if (name.match(/\.(png|jpe?g|gif|webp)$/)) {
      return 'Image';
    }
    if (name.endsWith('.pdf')) {
      return 'PDF';
    }
    if (name.match(/\.(doc|docx|xls|xlsx|ppt|pptx)$/)) {
      return 'Office';
    }
    if (name.match(/\.(mp4|mov|avi|flv|wmv|m4v)$/)) {
      return 'Video';
    }
    return 'unknown';
  });

  const attachmentUrl = computed<string>(() => {
    if (directUrl.value) {
      return directUrl.value;
    }
    let baseUrl = `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource`;
    if (dataType.value === 'Image' && currentAttachment.value?.id) {
      baseUrl = `${baseUrl}/image/${currentAttachment.value.id}`;
    } else if (currentAttachment.value?.id) {
      baseUrl = `${baseUrl}/goDirect/${currentAttachment.value.id}`;
    } else {
      return '';
    }

    const token = getToken();
    return `${baseUrl}?token=${token}&loginSource=PC`;
  });

  const officePreviewUrl = computed<string>(() => {
    if (dataType.value === 'Office' && attachmentUrl.value) {
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(attachmentUrl.value)}`;
    }
    return '';
  });

  watch(
    () => props.raw,
    (newRaw) => {
      attachments.value = newRaw || [];
      activeAttachmentIndex.value = attachments.value.length > 0 ? 0 : -1;
    },
    { immediate: true, deep: true },
  );

  watch(activeAttachmentIndex, (newIndex) => {
    if (attachments.value.length === 0) {
      activeAttachmentIndex.value = -1;
      return;
    }
    if (newIndex < 0) {
      activeAttachmentIndex.value = 0;
    } else if (newIndex >= attachments.value.length) {
      activeAttachmentIndex.value = attachments.value.length - 1;
    }
  });

  onMounted(() => {
    attachments.value = props.raw || [];
    if (props.initialIndex >= 0 && props.initialIndex < attachments.value.length) {
      activeAttachmentIndex.value = props.initialIndex;
    } else {
      activeAttachmentIndex.value = attachments.value.length > 0 ? 0 : -1;
    }
  });
</script>

<style scoped lang="scss">
  .attachment-viewer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f0f2f5;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);

    .no-attachment,
    .unsupported-preview {
      text-align: center;
      color: #888;
      font-size: 16px;
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

      .file-name {
        font-weight: bold;
        margin-top: 10px;
        color: #333;
      }

      .download-tip {
        font-size: 14px;
        margin-top: 15px;
        a {
          color: #1890ff;
          text-decoration: none;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .preview-area {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      background-color: #fff;
    }

    .image-preview,
    .video-preview,
    .pdf-preview,
    .office-preview {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      img,
      video,
      iframe {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        display: block;
        margin: auto;
        border: none;
        min-height: 500px;
        width: 100%;
      }
    }

    .nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background-color: rgba(0, 0, 0, 0.4);
      color: #fff;
      font-size: 24px;
      padding: 12px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;
      z-index: 10;

      &:hover {
        background-color: rgba(0, 0, 0, 0.6);
      }

      &--left {
        left: 15px;
      }

      &--right {
        right: 15px;
      }
    }
  }
</style>
```
