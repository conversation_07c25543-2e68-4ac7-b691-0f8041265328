<template>
  <div v-if="raw">
    <img v-if="showImage" alt="" :src="src" v-bind="{ ...imageProps, ...$attrs }" @click="handlePreview" />
    <a-button v-else size="mini" @click="handlePreview">查看图片</a-button>
    <a-modal v-model:visible="previewVisible" :title="imageProps.alt" hide-cancel width="80%" @close="handleClose">
      <img :alt="imageProps.alt" :src="src" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { isNumber } from 'lodash';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    raw: {
      type: [String, Number],
      default: '',
    },
    showImage: {
      type: Boolean,
      default: false,
    },
    imageProps: {
      type: Object,
      default: () => ({
        alt: '图片',
        style: {
          maxWidth: '200px',
          maxHeight: '200px',
        },
      }),
    },
  });

  const src = computed(() => {
    const pattern = /^\d+$/;
    if (pattern.test(props.raw?.toString())) {
      return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/image/${props.raw}`;
    }
    return props.raw;
  });

  const previewVisible = ref(false);

  const handleClose = () => {
    previewVisible.value = false;
  };

  const handlePreview = () => {
    previewVisible.value = true;
  };
</script>

<style scoped></style>
