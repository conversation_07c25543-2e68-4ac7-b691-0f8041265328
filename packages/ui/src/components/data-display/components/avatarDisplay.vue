<script lang="ts" setup>
  import { colorful } from '@repo/infrastructure/utils';
  import { computed, PropType, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { isString } from 'lodash';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import userMoreDetailCard from './userMoreDetailCard.vue';
  import AvatarOnlyDisplay from './avatarOnlyDisplay.vue';
  import UserDetailCard from './userDetailCard.vue';

  const props = defineProps({
    userInfo: {
      type: Object as PropType<any>,
      required: true,
    },
    raw: {
      // idNameVo
      type: [Object, String] as PropType<any>,
      default: () => ({}),
    },
    record: {
      // only in user list
      type: Object as PropType<any>,
      default: () => ({}),
    },
    shape: {
      type: String as PropType<'circle' | 'square'>,
      default: 'circle',
    },
    size: {
      type: [String, Number] as PropType<string | number>,
      default: 32,
    },
    showTrigger: {
      type: Boolean,
      default: false,
    },
    userCard: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String as PropType<'circle' | 'capsule'>,
      default: 'circle',
    },
    userCardFresh: {
      type: Boolean,
      default: true,
    },
    moreInfo: {
      type: Boolean,
      default: true,
    },
    cardClassName: {
      type: String,
      default: '',
    },
  });

  const userStore = useUserStore();
  const userDetailLoading = ref(false);
  const userDetail = ref<any>({});

  const uInfo = computed(() => {
    if (props.raw?.id) {
      return props.raw;
    }
    if (isString(props.raw) && props.record?.id) {
      // user list
      return props.record || {};
    }

    return props.userInfo || {};
  });

  const isMe = computed(() => {
    return uInfo.value?.id && uInfo.value?.id === userStore.userInfo?.id;
  });

  const avatarSrc = computed<any>(() => {
    if (Number(uInfo.value.avatar) > 0) {
      return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/image/${uInfo.value.avatar}?width=100&height=100`;
    }
    return undefined;
  });

  const displayName = computed(() => {
    if (!uInfo.value.name) {
      return 'Guest';
    }

    // 英文 返回 last name
    if (uInfo.value.name.indexOf(' ') > -1) {
      return uInfo.value.name.split(' ')[1];
    }

    // 中文返回最后两个字
    return uInfo.value.name.slice(-2);
  });

  const avatarStyle = computed(() => {
    return {
      backgroundColor: colorful(displayName.value),
      width: `${props.size}px`,
      height: `${props.size}px`,
    };
  });

  const capsuleAvatarSize = 16;
  const capsuleAvatarStyle = computed(() => {
    return {
      backgroundColor: colorful(displayName.value),
      width: `${capsuleAvatarSize}px`,
      height: `${capsuleAvatarSize}px`,
    };
  });

  const handleLoadUserDetail = async (show?: boolean) => {
    if (userDetailLoading.value || !show || !uInfo.value?.id) {
      return;
    }
    userDetailLoading.value = true;
    try {
      const { data } = await request(`/org/companyUser/${uInfo.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      userDetail.value = data || {};
    } finally {
      userDetailLoading.value = false;
    }
  };
  const additionalInfoVisible = ref(false);
</script>

<template>
  <avatar-only-display
    v-if="mode === 'circle' && !userCard && uInfo?.id"
    v-bind="$attrs"
    :user-info="uInfo"
    :size="size"
    :avatar-src="avatarSrc"
    :avatar-style="avatarStyle"
    :display-name="displayName"
    :shape="shape"
    :show-trigger="showTrigger"
  >
    <template v-if="showTrigger" #trigger-icon> <slot name="trigger-icon"></slot> </template>
  </avatar-only-display>

  <a-popover
    v-else-if="mode === 'circle' && userCard && uInfo?.id"
    trigger="click"
    class="w-64 user-card"
    content-class="!p-0 popover-without-top-margin"
    @popup-visible-change="handleLoadUserDetail"
  >
    <template #content>
      <user-detail-card
        :user-detail="userDetail"
        :display-name="displayName"
        :user-detail-loading="userDetailLoading"
        :avatar-src="avatarSrc"
        :card-class-name="cardClassName"
      />
    </template>
    <avatar-only-display
      v-bind="$attrs"
      :user-info="uInfo"
      :size="size"
      :avatar-src="avatarSrc"
      :avatar-style="avatarStyle"
      :display-name="displayName"
      :shape="shape"
      :show-trigger="showTrigger"
    >
      <template v-if="showTrigger" #trigger-icon> <slot name="trigger-icon"></slot> </template>
    </avatar-only-display>
  </a-popover>

  <div v-else-if="mode === 'capsule' && uInfo?.id">
    <a-popover trigger="click" class="w-64 user-card" content-class="!p-0 popover-without-top-margin">
      <a-button
        :type="isMe ? 'outline' : undefined"
        size="mini"
        shape="round"
        class="!px-1 !pr-2 !text-xs !h-6 flex items-center"
        @click="() => handleLoadUserDetail(true)"
      >
        <template #icon>
          <avatar-only-display
            v-bind="$attrs"
            :user-info="uInfo"
            :size="capsuleAvatarSize"
            :avatar-src="avatarSrc"
            :avatar-style="capsuleAvatarStyle"
            :display-name="displayName"
            :shape="shape"
            :show-trigger="showTrigger"
            :name-class="''"
          />
        </template>
        <span class="mt-0.5">{{ uInfo.name }}</span>
      </a-button>
      <template #content>
        <user-detail-card
          :user-detail="userDetail"
          :display-name="displayName"
          :user-detail-loading="userDetailLoading"
          :avatar-src="avatarSrc"
          :card-class-name="cardClassName"
        />
        <div v-if="moreInfo">
          <a-divider :margin="5" />
          <div class="flex justify-start p-2">
            <a-button size="mini" shape="round" class="action-btn" @click="additionalInfoVisible = true">
              <template #icon>
                <IconInfoCircle />
              </template>
              更多信息
            </a-button>
          </div>
        </div>
      </template>
    </a-popover>
  </div>
  <a-modal v-model:visible="additionalInfoVisible" width="60%" :render-to-body="false" :closable="false" draggable>
    <template #title>
      <div class="flex items-center gap-3 text-base text-gray-700">
        <span class="font-semibold">
          {{ userDetail.name }}
        </span>
        <span v-if="userDetail.gender" class="text-xs text-gray-500">
          （{{ userDetail?.gender }}{{ userDetail?.age ? ` · ${userDetail?.age}岁` : '' }}）
        </span>
      </div>
    </template>
    <userMoreDetailCard v-if="additionalInfoVisible" :teacher="userDetail" />
    <template #footer>
      <div class="flex justify-end">
        <a-button size="mini" type="primary" @click="additionalInfoVisible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style lang="less" scoped>
  .avatar {
    cursor: pointer;
    text-shadow: none;

    span {
      margin: 3px;
    }
  }

  .action-btn {
    background-color: #e3f2fd;
    border: 1px dashed #2196f3;
    color: #1565c0;
    transition: all 0.3s ease;
    font-weight: 500;

    &:hover {
      background-color: #5eb8ff;
      color: #ffffff;
      border-color: #1565c0;
      box-shadow: 0 0 6px rgba(33, 150, 243, 0.4);
    }
  }
</style>

<style lang="less">
  .popover-without-top-margin {
    .arco-popover-title {
      display: none;
    }
    .arco-popover-content {
      margin-top: 0 !important;
    }
  }
</style>
