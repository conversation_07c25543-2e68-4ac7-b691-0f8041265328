<template>
  <a-tooltip v-if="raw" :content="fullFormatted">
    <span>{{ val }}</span>
  </a-tooltip>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import dayjs from 'dayjs';
  import Constants from '@repo/infrastructure/constants';

  const props = defineProps({
    raw: {
      type: [String, Number],
      default: '',
    },
    format: {
      type: String,
      default: Constants.DEFAULT_DATE_FORMAT,
    },
    fullFormat: {
      type: String,
      default: Constants.FULL_DATE_FORMAT,
    },
    humanizeDate: {
      type: Boolean,
      default: false,
    },
  });
  const val = ref<any>(props.raw ? dayjs(props.raw).format(props.format) : '');
  const fullFormatted = ref<any>(props.raw ? dayjs(props.raw).format(props.fullFormat) : '');

  if (props.humanizeDate) {
    val.value = dayjs(props.raw).fromNow();
  }
</script>

<style scoped></style>
