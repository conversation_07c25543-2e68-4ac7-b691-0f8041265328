<script setup lang="ts">
  import { PropType, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';

  const props = defineProps({
    teacher: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });
  const schema = ref(null);
  const isReady = ref(false);

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/org/companyUser');
    isReady.value = true;
  });
</script>

<template>
  <a-spin :loading="!isReady" class="w-full">
    <div v-if="isReady && schema" class="w-full">
      <record-detail ref="recordDetailRef" :raw="teacher" :schema="schema" />
    </div>
  </a-spin>
</template>

<style scoped lang="scss"></style>
