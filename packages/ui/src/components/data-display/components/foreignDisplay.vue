<template>
  <div class="data-display-foreign">{{ val }}</div>
</template>

<script setup lang="ts">
  import { PropType, ref, toRaw } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { isArray } from 'lodash';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    raw: {
      type: [Object, Array],
      default: () => undefined,
    },
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const val = ref<any>();
  const labelField = ref<any>(props.schemaField?.foreignField?.labelField || 'name');

  const possibleFields = ['name', 'title', 'subject', 'symbol'];
  const getDisplayValue = (item: Record<string, any>) => {
    if (!item) {
      return '';
    }
    if (typeof props.schemaField?.displayProps?.toDisplay === 'function') {
      const toDisplayValue = props.schemaField.displayProps?.toDisplay(item, props.record);
      // if toDisplay return a Component
      if (!(toDisplayValue instanceof Object)) {
        return toDisplayValue;
      }
    }
    let result = item[labelField.value];
    if (result !== undefined) {
      return result;
    }
    for (let i = 0; i < possibleFields.length; i += 1) {
      const field = possibleFields[i];
      if (item[field] !== undefined) {
        result = item[field];
        break;
      }
    }
    if (result !== undefined) {
      return result;
    }

    if (item.id) {
      return `#${item.id}`;
    }
    return item;
  };

  if (isArray(props.raw)) {
    val.value = props.raw.map((item: any) => getDisplayValue(item)).join(', ');
  } else {
    val.value = getDisplayValue(props.raw as any);
  }
</script>

<style scoped></style>
