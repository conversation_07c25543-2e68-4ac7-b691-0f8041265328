<template>
  <a-tag
    v-if="schemaField?.displayProps?.booleanDisplayOptions?.mode !== 'switch'"
    :color="rawValue ? 'green' : 'red'"
    size="small"
    :class="{ 'cursor-pointer': allowSwitch }"
    @click="() => handleSwitch(!rawValue)"
  >
    <template #icon>
      <IconCheckCircleFill v-if="rawValue" />
      <IconCloseCircleFill v-else />
    </template>
    {{ String(displayValue) }}
  </a-tag>
  <a-switch
    v-else
    type="round"
    size="medium"
    :model-value="props.raw"
    :checked-text="displayValue"
    :unchecked-text="displayValue"
    :checked="props.raw"
    :disabled="!allowSwitch"
    @change="handleSwitch"
  />
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { BooleanDisplayValue, SchemaField } from '@repo/infrastructure/types';
  import { Modal } from '@arco-design/web-vue';
  import { isBoolean } from 'lodash';

  const props = defineProps({
    raw: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
    },
    record: {
      type: Object,
    },
  });

  const emit = defineEmits(['update:raw', 'update:record']);

  const allowSwitch = computed(() => {
    if (typeof props.schemaField?.displayProps?.booleanDisplayOptions?.allowUpdate === 'function') {
      return props.schemaField?.displayProps?.booleanDisplayOptions?.allowUpdate(props.record);
    }
    return props.schemaField?.displayProps?.booleanDisplayOptions?.allowUpdate;
  });

  const rawValue = computed(() => {
    if (typeof props.schemaField?.displayProps?.toDisplay === 'function') {
      return props.schemaField.displayProps.toDisplay(props.raw, props.record);
    }
    return props.raw;
  });

  const booleanDisplayValue: BooleanDisplayValue[] =
    props.schemaField?.displayProps?.booleanDisplay || props.schemaField?.inputWidgetProps?.options || [];

  if (!booleanDisplayValue.length) {
    booleanDisplayValue.push({ label: '是', value: true });
    booleanDisplayValue.push({ label: '否', value: false });
  }

  const displayValue = computed(() => {
    const display = booleanDisplayValue.find((item) => item.value === rawValue.value);

    return display?.label || rawValue.value;
  });

  const handleSwitch = async (val) => {
    if (allowSwitch.value && typeof props.schemaField?.displayProps?.booleanDisplayOptions?.handler === 'function') {
      let confirmMsg = '确定要修改此项吗？';
      if (typeof props.schemaField?.displayProps?.booleanDisplayOptions?.confirmMessage === 'function') {
        confirmMsg = props.schemaField.displayProps.booleanDisplayOptions.confirmMessage(val, props.record);
      } else {
        confirmMsg = props.schemaField?.displayProps?.booleanDisplayOptions?.confirmMessage || confirmMsg;
      }
      Modal.confirm({
        title: '提示',
        content: confirmMsg,
        renderToBody: false,
        onOk: async () => {
          emit('update:raw', val);
          const res = await props.schemaField.displayProps.booleanDisplayOptions.handler(val, props.record);
          if (isBoolean(res)) {
            emit('update:raw', res);
          }
        },
      });
    }
  };
</script>

<style scoped></style>
