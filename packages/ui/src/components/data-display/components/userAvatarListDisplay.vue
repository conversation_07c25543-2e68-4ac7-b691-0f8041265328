<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import { useTeacherStore, useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import { SchemaField } from '@repo/infrastructure/types';
  import { getClientRole } from '@repo/env-config';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import AvatarDisplay from './avatarDisplay.vue';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    shape: {
      type: String as PropType<'circle' | 'square'>,
      default: 'circle',
    },
    size: {
      type: [String, Number] as PropType<string | number>,
      default: 24,
    },
    showTrigger: {
      type: Boolean,
      default: false,
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
  });

  const getOrgNature = () => {
    if (getClientRole() === 'Company') {
      const userStore = useUserStore();
      return userStore.getUserNature();
    }
    const menuStore = useUserMenuStore();
    return menuStore.getCurrentMenuInfo().app?.label;
  };

  const teacherStore = useCommonStore({
    api: '/org/companyUser/allTeachers',
  });
  const teachers = ref([]);
  const tooltipEnabled = props.schemaField?.displayProps?.tooltip?.visible !== false;
  const getContent = props.schemaField?.displayProps?.tooltip?.getContent;

  const maxCount = props.schemaField?.displayProps?.maxCount || 3;

  onMounted(async () => {
    const teachersMap = await teacherStore.getMap();

    teachers.value =
      props.raw?.map((item) => {
        item = {
          ...item,
          ...teachersMap[item.userId || item.id],
        };
        let tooltipContent = '';
        if (tooltipEnabled && typeof getContent === 'function') {
          tooltipContent = getContent(item) || item?.name;
        }
        return {
          ...item,
          tooltipEnabled,
          tooltipContent,
        };
      }) || [];
  });
</script>

<template>
  <div v-if="!raw?.length">
    <slot name="empty"> 无 </slot>
  </div>
  <a-avatar-group
    v-else-if="tooltipEnabled"
    :max-count="maxCount"
    :size="size"
    :auto-fix-font-size="true"
    class="avatar-group-global"
  >
    <a-tooltip v-for="user in teachers" :key="user.id" :content="user.tooltipContent || user.name">
      <avatar-display
        :user-info="user"
        :shape="shape"
        :user-card="true"
        :size="size"
        card-class-name="in-avatar-group"
      />
    </a-tooltip>
  </a-avatar-group>
  <a-avatar-group v-else :max-count="maxCount" :size="size" class="avatar-group-global">
    <avatar-display
      v-for="user in teachers"
      :key="user.id"
      :user-card="true"
      :user-info="user"
      :shape="shape"
      :size="size"
      card-class-name="in-avatar-group"
    />
  </a-avatar-group>
</template>

<style lang="less" scoped>
  .arco-avatar-group {
    display: flex;
  }
</style>

<style lang="less">
  .user-card .in-avatar-group {
    .arco-avatar-text {
      transform: scale(1.5) translateX(-50%) !important;
      span {
        font-weight: 800;
      }
    }
  }
</style>
