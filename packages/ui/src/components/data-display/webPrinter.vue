<script setup lang="ts">
  import { VuePrintNext } from 'vue-print-next';
  import { computed } from 'vue';

  const props = defineProps({
    btnText: {
      type: String,
      default: '打印',
    },
    modalTitle: {
      type: String,
      default: '打印',
    },
    selector: {
      type: String,
      required: true,
    },
    hideSelector: {
      type: String,
      default: '.no-print',
    },
  });

  const id = computed(() => {
    return `${props.selector}`;
  });

  const handlePrint = () => {
    // eslint-disable-next-line no-new
    new VuePrintNext({
      el: `#${id.value}`,
      popTitle: props.modalTitle,
      zIndex: 9999,
      printMode: 'popup',
      hide: props.hideSelector,
    });
  };
</script>

<template>
  <a-button v-bind="$attrs" @click="handlePrint">
    <template #icon>
      <IconPrinter />
    </template>
    {{ btnText }}
  </a-button>
</template>

<style scoped lang="scss"></style>
