<template>
  <a-spin v-if="detailData" :loading="loading" class="w-full">
    <grouping :raw="detailData" :schema="schema" :groups="groups" @refresh="handleRefresh">
      <slot :detail-data="detailData"></slot>
    </grouping>
  </a-spin>
</template>

<script setup lang="ts">
  import { computed, onMounted, PropType, Ref, ref, watch } from 'vue';
  import { isNumber } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Grouping } from '@repo/ui/components/description';
  import { FieldsGroup, Schema, SchemaField } from '@repo/infrastructure/types';
  import { CommonApi } from '@repo/infrastructure/crud';

  const props = defineProps({
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    showFresh: {
      type: Boolean,
      default: true,
    },
  });

  const rawId = computed(() => {
    if (isNumber(props.raw)) {
      return props.raw;
    }
    return props.raw?.id;
  });

  const detailData = ref<any>(props.raw);

  const { loading, setLoading } = useLoading();
  setLoading(false);

  let commonApi: any = null;

  const groups: Ref<FieldsGroup[]> = ref<any>(
    SchemaHelper.getFieldsGrouping(
      props.schema,
      props.schema?.detailViewProps?.fieldsGrouping || [],
      (field: SchemaField) => {
        if (typeof field.visibleInDetail === 'function') {
          return !field.visibleInDetail(detailData.value);
        }
        return field.visibleInDetail === false;
      },
    ),
  );

  const loadData = async () => {
    setLoading(true);
    commonApi
      .fetchOne(
        rawId.value,
        {},
        {
          baseURL: props.schema.baseURL,
        },
      )
      .then((res) => {
        detailData.value = res.data;
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleRefresh = async () => {
    await loadData();
  };

  onMounted(async () => {
    commonApi = CommonApi.getInstance(props.schema);
    if (props.showFresh !== false && (isNumber(props.raw) || props.schema?.detailViewProps?.showFresh)) {
      await loadData();
    }
  });
  watch(
    () => props.raw,
    async (newVal) => {
      if (newVal?.id) {
        await loadData();
      }
    },
  );
  defineExpose({
    loadData,
    setLoading,
    handleRefresh,
    detailData,
  });
</script>

<script lang="ts">
  export default {
    name: 'RecordDetail',
  };
</script>

<style scoped></style>
