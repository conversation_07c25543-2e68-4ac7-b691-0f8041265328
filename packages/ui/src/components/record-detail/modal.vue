<template>
  <a-modal
    id="curd-table-detail-modal"
    v-model:visible="visible"
    :fullscreen="fullscreen"
    :hide-cancel="true"
    :width="schema.detailViewProps?.width || '80%'"
    :mask-closable="schema.detailViewProps?.clickOutsideToClose"
    :render-to-body="false"
    @close="closeModal"
  >
    <template #title>
      <div class="flex-1 flex mr-4 gap-2 justify-between">
        {{ modalTitle }}
        <a-space>
          <a-button size="mini" type="secondary" @click="handlePrint">
            <template #icon>
              <IconPrinter />
            </template>
            打印此页
          </a-button>
          <a-button size="mini" type="secondary" @click="handleToggleFullscreen">
            <template #icon>
              <IconFullscreen v-if="!fullscreen" />
              <IconFullscreenExit v-else />
            </template>
            {{ fullscreen ? '退出全屏' : '全屏' }}
          </a-button>
        </a-space>
      </div>
      <div style="width: 20px"></div>
    </template>
    <div :id="id" class="printable">
      <div v-if="printTitle" class="flex justify-center mb-2 mt-2 text-lg">{{ printTitle }}</div>
      <component
        :is="schema.detailViewProps.component"
        v-if="visible && raw && schema?.detailViewProps?.component"
        :raw="raw"
        :schema="schema"
      />
      <record-detail v-else-if="visible" ref="recordDetailRef" :raw="raw" :schema="schema" />
    </div>
    <template #footer>
      <a-space>
        <row-actions
          v-bind="$attrs"
          :schema="schema"
          :load-data="() => recordDetailRef?.handleRefresh()"
          :row-actions="schema.rowActions"
          :set-loading="(loading: boolean) => recordDetailRef?.setLoading(loading)"
          :record="recordDetailRef?.detailData"
          :invisible-actions="['view']"
          :expose-all="true"
          :module-path="modulePath"
          @row-action="handleRowAction"
        />
        <a-divider direction="vertical" />
        <a-button type="primary" @click="closeModal">关闭</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { Schema } from '@repo/infrastructure/types';
  import CryptoJS from 'crypto-js/crypto-js.js';
  import { VuePrintNext } from 'vue-print-next';
  import RecordDetail from './index.vue';
  import RowActions from '../table/rowActions.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true,
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    modulePath: {
      type: String as PropType<string>,
      required: false,
    },
  });
  const emits = defineEmits(['update:modelValue', 'rowAction']);
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emits('update:modelValue', value),
  });

  const recordDetailRef = ref<any>(null);
  const fullscreen = ref(false);

  const id = computed(() => {
    const apiHash = CryptoJS.MD5(props.schema.api).toString();
    return `record-detail-modal-${apiHash}`;
  });

  const handleToggleFullscreen = () => {
    fullscreen.value = !fullscreen.value;
  };

  const modalTitle = computed(() => {
    if (props.schema?.detailViewProps?.title) {
      if (typeof props.schema.detailViewProps.title === 'function') {
        return props.schema.detailViewProps.title(props.raw);
      }
      return props.schema.detailViewProps.title;
    }
    return '查看详情';
  });

  const printTitle = computed(() => {
    if (props.schema?.detailViewProps?.printTitle) {
      if (typeof props.schema.detailViewProps.printTitle === 'function') {
        return props.schema.detailViewProps.printTitle(props.raw);
      }
      return props.schema.detailViewProps.printTitle;
    }
    return null;
  });

  const closeModal = () => {
    visible.value = false;
  };

  const handleRowAction = (action: any, record: Record<string, any>) => {
    if (action.key === 'delete') {
      closeModal();
    }
    emits('rowAction', action, record, {
      modulePath: props.modulePath,
    });
  };

  const loadData = async () => {
    await recordDetailRef.value?.handleRefresh();
  };

  const handlePrint = () => {
    // eslint-disable-next-line no-new
    new VuePrintNext({
      el: `#${id.value}`,
      popTitle: modalTitle.value,
      zIndex: 9999,
      printMode: 'popup',
      hide: '.no-print',
    });
  };

  defineExpose({
    loadData,
  });
</script>

<script lang="ts">
  export default {
    name: 'RecordDetailModal',
  };
</script>

<style scoped>
  @media screen {
    .print-title {
      display: none;
    }
  }
  @media print {
    .printable {
      width: 110%;
      transform: scale(0.9);
      transform-origin: 0 0;
    }
    .print-title {
      display: inline;
    }

    * {
      page-break-inside: avoid;
    }
  }
</style>
