<template>
  <a-drawer
    v-model:visible="visible"
    :placement="schema.detailViewProps?.drawerPlacement || 'left'"
    :hide-cancel="true"
    :width="schema.detailViewProps?.width || '80%'"
    :mask-closable="schema.detailViewProps?.clickOutsideToClose"
    :render-to-body="false"
    @ok="closeModal"
    @close="closeModal"
  >
    <template #title> {{ modalTitle }}</template>
    <component
      :is="schema.detailViewProps.component"
      v-if="visible && schema?.detailViewProps?.component"
      :raw="raw"
      :schema="schema"
    />
    <record-detail v-else-if="visible" ref="recordDetailRef" :raw="raw" :schema="schema" />

    <template #footer>
      <a-space>
        <row-actions
          v-if="recordDetailRef?.detailData"
          v-bind="$attrs"
          :schema="schema"
          :load-data="() => recordDetailRef.handleRefresh()"
          :row-actions="schema.rowActions"
          :set-loading="(loading: boolean) => recordDetailRef.setLoading(loading)"
          :record="recordDetailRef?.detailData"
          :invisible-actions="['view']"
          :expose-all="true"
          :module-path="modulePath"
          @row-action="handleRowAction"
        />
        <a-divider direction="vertical" />
        <a-button type="primary" @click="closeModal">关闭</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { Schema } from '@repo/infrastructure/types';
  import RecordDetail from './index.vue';
  import RowActions from '../table/rowActions.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true,
    },
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    modulePath: {
      type: String as PropType<string>,
      required: false,
    },
  });

  const recordDetailRef = ref<any>(null);

  const emits = defineEmits(['update:modelValue', 'rowAction']);
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emits('update:modelValue', value),
  });

  const modalTitle = computed(() => {
    if (props.schema?.detailViewProps?.title) {
      if (typeof props.schema.detailViewProps.title === 'function') {
        return props.schema.detailViewProps.title(props.raw);
      }
      return props.schema.detailViewProps.title;
    }

    return '查看详情';
  });

  const closeModal = () => {
    visible.value = false;
  };

  const handleRowAction = (action: any, record: Record<string, any>) => {
    emits('rowAction', action, record, {
      modulePath: props.modulePath,
    });
  };

  const loadData = async () => {
    await recordDetailRef.value.handleRefresh();
  };
  defineExpose({
    loadData,
  });
</script>

<script lang="ts">
  export default {
    name: 'RecordDetailDrawer',
  };
</script>

<style scoped></style>
