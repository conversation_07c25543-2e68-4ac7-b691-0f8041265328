// <script src="http://api.tianditu.gov.cn/api?v=4.0&tk=您的密钥" type="text/javascript"></script>
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';

const tiandiMapInit = async (callback?: () => void) => {
  if (window.T) {
    if (typeof callback === 'function') {
      callback();
    }
    return true;
  }
  const script = document.createElement('script');
  script.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${PROJECT_URLS.TIANDITU_KEY}`;
  script.type = 'text/javascript';

  return new Promise((resolve, reject) => {
    script.onload = () => {
      if (typeof callback === 'function') {
        callback();
      }
      resolve(true);
    };
    script.onerror = () => {
      reject();
    };
    document.body.appendChild(script);
  });
};

const coordinateToAddress = async (lng: number, lat: number, options?: any): Promise<T.GeocoderResult> => {
  if (!lng || !lat) {
    throw new Error('经纬度不能为空');
  }

  const pu = options.PROJECT_URLS || PROJECT_URLS || {};

  // http://api.tianditu.gov.cn/geocoder?postStr={'lon':116.37304,'lat':39.92594,'ver':1}&type=geocode&tk=您的密钥
  const url = `/geocoder?postStr={'lon':${lng},'lat':${lat},'ver':1}&type=geocode&tk=${pu.TIANDITU_KEY}`;
  console.log('url', url);
  console.log('pu', pu);
  const raw = await request(url, {
    baseURL: 'https://api.tianditu.gov.cn',
    withCredentials: false,
    doNotInterceptRequest: true,
    headers: {},
  });
  return raw.result || raw.data?.result;
};

const locationSearch = async (mapInstance: T.Map, keyword: string): Promise<any[]> => {
  if (!keyword?.toString().trim().length) {
    return [];
  }

  return new Promise((resolve, reject) => {
    const localSearcher = new T.LocalSearch(mapInstance, {
      pageCapacity: 10,
      onSearchComplete: (res: any) => {
        if (res.getPois()?.length) {
          resolve(
            res.getPois().map((item: any) => {
              const [lng, lat] = item.lonlat.split(',');
              return {
                label: item.name,
                value: item.lonlat,
                longitude: Number(lng),
                latitude: Number(lat),
                raw: item,
              };
            }),
          );
        } else {
          reject();
        }
      },
    });
    localSearcher.search(keyword);
  });
};

export { tiandiMapInit, coordinateToAddress, locationSearch };
