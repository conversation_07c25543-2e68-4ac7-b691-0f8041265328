import { coordinateToAddress } from './tiandiMap';

const getCurrentLocation = async () => {
  if (!navigator.geolocation) {
    throw new Error('Geolocation is not supported by your browser');
  }

  return new Promise<GeolocationPosition>((resolve, reject) => {
    navigator.geolocation.getCurrentPosition((position) => {
      resolve(position);
    }, reject);
  });
};

const calculateDistance = (currentLocation: any, targetLocation: any) => {
  // 基于googlemap 算法 计算距离
  const rad = (d: number) => {
    return (d * Math.PI) / 180.0;
  };

  const EARTH_RADIUS = 6378137.0; // 地球半径
  const radLat1 = rad(currentLocation.latitude);
  const radLat2 = rad(targetLocation.latitude);
  const a = radLat1 - radLat2;
  const b = rad(currentLocation.longitude) - rad(targetLocation.longitude);
  let s = 2 * Math.asin(Math.sqrt(Math.sin(a / 2) ** 2 + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(b / 2) ** 2));
  s *= EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000.0;

  // 返回米
  return s;
};

export { getCurrentLocation, calculateDistance, coordinateToAddress };
