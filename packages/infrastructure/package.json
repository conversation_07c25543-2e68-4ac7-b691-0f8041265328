{"name": "@repo/infrastructure", "version": "2.1.50", "main": "index.js", "license": "MIT", "exports": {".": "./index.ts", "./iconfont-online-css": "./src/ui/iconfont/iconfont-online.css", "./iconfont": "./src/ui/iconfont/iconfont.js", "./ui": "./src/ui/index.ts", "./ui/styles/*.scss": "./src/ui/styles/*.scss", "./constants": "./src/constants.ts", "./auth": "./src/auth/index.ts", "./schema": "./src/schema/index.ts", "./types": "./src/types/index.ts", "./types/*": "./src/types/*.ts", "./data": "./src/data/index.ts", "./data/*": "./src/data/*.ts", "./openapi": "./src/openapi/api/index.ts", "./openapi/*": "./src/openapi/api/*.ts", "./typings": "./typings.d.ts", "./hooks": "./src/hooks/index.ts", "./store": "./src/store/index.ts", "./store/*": "./src/store/*.ts", "./crud": "./src/crud/index.ts", "./upload": "./src/upload/index.ts", "./utils": "./src/utils/index.ts", "./utils/*": "./src/utils/*.ts", "./locale": "./src/locale/index.ts", "./request": "./src/request/index.ts", "./adapter": "./src/adapter/index.ts", "./customizeComponent/*": "./src/customizeComponent/*.ts", "./customizeComponent/*.vue": "./src/customizeComponent/*.vue", "./electron": "./src/electron/index.ts"}, "dependencies": {"@maxbilbow/ant-path-matcher": "^0.0.2", "@types/ali-oss": "^6.16.11", "@uni-helper/axios-adapter": "^1.5.2", "ali-oss": "^6.20.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "vue-i18n": "^9.9.1", "vue3-sfc-loader": "^0.9.5"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@uni-helper/uni-ui-types": "^0.5.11", "miniprogram-api-typings": "^3.12.2", "xregexp": "^5.1.1"}, "peerDependencies": {"crypto-js": "^4.2.0", "vite": "^6.0.7", "vue": "^3.0.0"}, "type": "module"}