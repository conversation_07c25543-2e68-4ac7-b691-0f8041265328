import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { clearToken, getToken } from '@repo/infrastructure/auth';
import { HttpResponse } from '@repo/infrastructure/types';
import { isString } from 'lodash';
import { getClientRole, PROJECT_URLS } from '@repo/env-config';
import { createUniAppAxiosAdapter } from '@uni-helper/axios-adapter';
import { getDefaultRequestParams, getRequestClientRole, storage } from '../adapter';
import { getElectronApi, isElectron } from '../electron';

export const axiosInstance = axios.create({
  baseURL: PROJECT_URLS.GO_PROJECT_API,
  timeout: 25000,
  // @ts-ignore
  adapter: typeof uni === 'undefined' ? undefined : createUniAppAxiosAdapter(),
});

axiosInstance.interceptors.request.use(
  (config: any) => {
    if (config.doNotInterceptRequest) {
      return config;
    }

    const loginSource = storage.getItem('LoginSource');

    let requestClientRole = getClientRole();
    if (!requestClientRole) {
      requestClientRole = getRequestClientRole() as any;
    }

    config.headers = {
      ...{
        'Accept-Language': 'zh-CN',
        'X-User-Type': 'System',
        'Login-Source': loginSource || 'PC',
        'Request-Client-Role': requestClientRole,
      },
      ...config.headers,
    };
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      // config.headers.UserId = localStorage.getItem(Constants.USER_ID_CACHE_KEY);
    }

    return {
      ...config,
      params: {
        ...getDefaultRequestParams(),
        ...(config.params || {}),
      },
    };
  },
  (error) => {
    // do something
    return Promise.reject(error.response);
  },
);
// add response interceptors
axiosInstance.interceptors.response.use(
  (response: any) => {
    const res = response.data;
    if (isString(res)) {
      return Promise.resolve(res);
    }
    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 200 && response.status !== 200) {
      // Message.error({
      //   content: res.msg || 'Error',
      //   duration: 5 * 1000,
      //   position: 'bottom',
      // });
      console.error('Error: ', res);
      throw new Error(res?.errors || res?.message || 'Error');
      // return Promise.reject(res);
    }

    return res;
  },
  (error) => {
    const res = (error.response || {}) as AxiosResponse<HttpResponse>;
    if (res.status === 401) {
      clearToken();
      if (isElectron) {
        getElectronApi().send('relaunchToLogin');
      } else {
        return Promise.reject(res);
      }
    }
    if (res.status === 403) {
      // const perm = menuService.permissionToLabel(res.data?.errors) || res.data?.message;
      return Promise.reject(res);
    }

    // @ts-ignore
    if (typeof window?.handleRequestError === 'function') {
      // @ts-ignore
      window.handleRequestError(res?.data?.errors || res?.data?.message || error.msg || '[0] 系统开小差了~', res);
      // throw new Error(res?.data?.errors || res?.data?.message || error.msg || '系统开小差了 :(');
      return Promise.reject(res);
    }
    // @ts-ignore
    if (typeof uni !== 'undefined') {
      const msg = res?.data?.errors || res?.data?.message || error.msg || '[1] 系统开小差了~';
      // 中文开头
      if (/^[\u4e00-\u9fa5]/.test(msg)) {
        if (msg.length < 10) {
          // @ts-ignore
          uni.showToast({
            title: `[4]${msg}`,
            icon: 'none',
          });
          return Promise.reject(res);
        }
        // @ts-ignore
        uni.showModal({
          title: '提示',
          content: msg,
          showCancel: false,
        });
      } else {
        // @ts-ignore
        uni.showToast({
          title: '[3] 系统开小差了~',
          icon: 'none',
        });
        return Promise.reject(res);
      }
    }

    console.error(error);
    throw new Error(res?.data?.errors || res?.data?.message || error.msg || '系统开小差了 :(');
  },
);

export function request<T = any, R = AxiosResponse<T>, D = any>(
  url: string | AxiosRequestConfig,
  config?: AxiosRequestConfig,
): Promise<R> {
  config = config || {};
  if (typeof url === 'string') {
    config.url = url;
  } else {
    config = url;
  }

  return axiosInstance.request<T, R, D>({
    ...config,
  });
}
