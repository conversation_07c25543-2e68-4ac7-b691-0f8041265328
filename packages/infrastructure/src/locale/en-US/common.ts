export default {
  'common.ok': 'OK',
  'common.cancel': 'Cancel',
  'common.confirm': 'Confirm',
  'common.delete': 'Delete',
  'common.deleteConfirm': 'Are you sure to delete this {count} item? | Are you sure to delete this {count} items?',
  'common.deleteSuccess': 'Delete successfully',
  'common.deleteFailed': 'Delete failed',
  'common.submit': 'Submit',
  'common.save': 'Save',
  'common.saveSuccess': 'Save successfully',
  'common.saveFailed': 'Save failed',
  'common.add': 'Add',
  'common.edit': 'Edit',
  'common.operation': 'Operation',
  'common.view': 'View',
  'common.search': 'Search',
  'common.quickSearch': 'Quick Search',
  'common.quickSearchBy': 'Quick Search By {0}',
  'common.quickSearchByTips': 'Enter or paste from text or Excel to start quick search',
  'common.enableMultiQuickSearch': 'Use multiple quick search, separated by spaces or commas, or paste from Excel',
  'common.quickSearch.tip': 'Quick search, separated by spaces or commas, or paste from Excel',
  'common.searchAdvanced': 'Search Advanced',
  'common.searchAdvancedCollapse': 'Collapse Advanced Search',
  'common.saveCurrentSearchCondition': 'Save Query',
  'common.reset': 'Reset',
  'common.clear': 'Clear',
  'common.copy': 'Copy',

  'common.copySuccess': 'Copy successfully',
  'common.copyFailed': 'Copy failed',
  'common.copyToClipboard': 'Copy to clipboard',
  'common.refresh': 'Refresh',
  'common.loading': 'Loading...',
  'common.noData': 'No data',
  'common.noMatchData': 'No match data',
  'common.noPermission': 'No permission',
  'common.nextStep': 'Next',
  'common.prevStep': 'Previous',
  'common.returnBack': 'Back',
  'common.others': 'Others',
  'common.parentNode': 'Parent Node',
  'common.export': 'Export',
  'common.exportSuccess': 'Export Success',
  'common.exportFailed': 'Export Failed',
  'common.exporting': 'Exporting...',
  'common.import': 'Import',
  'common.importSuccess': 'Import Success',
  'common.importFailed': 'Import Failed',
  'common.importing': 'Importing...',
  'common.selected': 'Selected',
  'common.selectedItems': 'Selected 1 item | Selected {count} items',
  'common.confirmPlease': 'Please confirm',
  'common.enable': 'Enable',
  'common.disable': 'Disable',
  'common.enabled': 'Enabled',
  'common.disabled': 'Disabled',
  'common.enableSuccess': 'Enable successfully',
  'common.enableFailed': 'Enable failed',
  'common.disableSuccess': 'Disable successfully',
  'common.disableFailed': 'Disable failed',
  'common.enableConfirm': 'Are you sure to enable this {count} item? | Are you sure to enable this {count} item?',
  'common.disableConfirm': 'Are you sure to disable this {count} item? | Are you sure to disable this {count} items?',
  'common.returnBackConfirm': 'Are you sure to return back?',

  'common.form.basicInfo': 'Basic Info',
  'common.form.validator.required': "{0} can't be empty",
  'common.form.validator.unique': '{0} already exists',
  'common.form.validateFailed': 'Please check whether {0} is filled in correctly',

  'common.componentSize': 'Component Size',
  'common.columnSetting': 'Column Setting',
  'common.switchLayout': 'Switch Layout',
  'common.viewDetail': 'View Detail',

  'common.size.large': 'Large',
  'common.size.medium': 'Medium',
  'common.size.small': 'Small',
  'common.size.mini': 'Mini',

  'common.fixedOn': 'Fixed On',
  'common.fixedOn.left': 'Left',
  'common.fixedOn.right': 'Right',

  'common.comparator.Equal': 'Equals To',
  'common.comparator.NotEqual': 'Not Equals To',
  'common.comparator.Like': 'Contains',
  'common.comparator.LikeIn': 'May Contains',
  'common.comparator.NotLike': 'Not Contains',
  'common.comparator.GreaterThan': 'Greater Than',
  'common.comparator.GreaterThanOrEqual': 'Greater Than Or Equals To',
  'common.comparator.LessThan': 'Less Than',
  'common.comparator.LessThanOrEqual': 'Less Than Or Equals To',
  'common.comparator.Between': 'Between',
  'common.comparator.NotBetween': 'Not Between',
  'common.comparator.In': 'In',
  'common.comparator.NotIn': 'Not In',
  'common.comparator.IsNull': 'Is Null',
  'common.comparator.IsNotNull': 'Is Not Null',

  'common.configureGroup.system': 'System',
  'common.configureKey.upload.target': 'Upload Target',
  'common.configureDescription.upload.target':
    'Where the file user uploaded will be stored, configured the cloud storage in your codes.',
};
