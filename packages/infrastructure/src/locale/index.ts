import { createI18n } from 'vue-i18n';
import en from './en-US';
import cn from './zh-CN';

export const LOCALE_OPTIONS = [
  { label: '中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
];

const defaultLocale =
  // localStorage.getItem(Constants.LOCALE_CACHE_KEY) ||
  // import.meta.env.VITE_I18N_LOCALE ||
  'zh-CN';

const i18n = createI18n({
  globalInjection: true,
  locale: defaultLocale,
  fallbackLocale: 'zh-CN',
  silentTranslationWarn: true,
  silentFallbackWarn: true,
  legacy: false,
  allowComposition: true,
  messages: {
    'en-US': en,
    'zh-CN': cn,
  },
});

export { i18n, defaultLocale };
