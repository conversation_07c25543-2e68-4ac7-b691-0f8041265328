import UniappAdapter from './uniapp';

const getStorageAdapter = () => {
  // @ts-ignore
  if (typeof uni !== 'undefined') {
    return UniappAdapter.storage;
  }
  if (typeof localStorage !== 'undefined') {
    return localStorage;
  }

  throw new Error('No storage adapter found');
};

const storage = getStorageAdapter();

const getRequestClientRole = () => {
  // @ts-ignore
  if (typeof uni !== 'undefined') {
    return UniappAdapter.getRequestClientRole();
  }

  return 'Company';
};

const getDefaultRequestParams = () => {
  // @ts-ignore
  if (typeof uni !== 'undefined') {
    return UniappAdapter.getDefaultRequestParams();
  }

  return {};
};

export { storage, getRequestClientRole, getDefaultRequestParams };
