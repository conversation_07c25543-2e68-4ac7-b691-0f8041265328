import { InfrastructureAdapter } from './types';

const UniappAdapter: InfrastructureAdapter = {
  storage: {
    getItem: (key: string) => {
      // @ts-ignore
      return uni?.getStorageSync(key);
    },
    setItem: (key: string, value: string) => {
      // @ts-ignore
      uni?.setStorageSync(key, value);
    },
    removeItem: (key: string) => {
      // @ts-ignore
      uni?.removeStorageSync(key);
    },
    clear: () => {
      // @ts-ignore
      uni?.clearStorageSync();
    },
  },
  getRequestClientRole: () => {
    return 'Company'; // 教师端
  },
  getDefaultRequestParams: () => {
    return {
      // @ts-ignore
      defaultOrgId: uni?.getStorageSync('defaultOrgId') || undefined,
    };
  },
};

export default UniappAdapter;
