import { defineStore } from 'pinia';
// @ts-ignore
import { Component, defineAsyncComponent, ref } from 'vue';
// eslint-disable-next-line import/no-cycle
import openapi from '@repo/infrastructure/openapi';
import { merge } from 'lodash';
import type { Schema, SchemaField, QuickSearchProps, InputWidget } from '../types';
import { DataTypeOf } from '../types';
import { customSchemaMap } from '../schema/customSchema';
// eslint-disable-next-line import/no-cycle
import SchemaHelper from '../schema/schemaHelper';
import useDataStructureStore from './dataStructure';
import { DsConvertOptions } from '../types/schema';

const getDefaultInputWidget = (field: SchemaField): InputWidget | Component => {
  // if field.inputWidget is not a InputWidget then return as a Component
  if (field.inputWidget && typeof field.inputWidget !== 'string') {
    return field.inputWidget as Component;
  }
  const widget: InputWidget = field.inputWidget as InputWidget;
  if (widget) {
    return widget;
  }
  const dataType = DataTypeOf(field.valueType!);
  return dataType?.defaultInputWidget;
};

const possibleFilterableFields = ['name', 'title', 'symbol', 'mobile', 'subject'];

const useSchemaStore = defineStore('schema', () => {
  const schemaList = ref<any>([] as Schema[]);
  const schemaMap = ref<any>({} as Record<string, Schema>);
  const fetchSchemaList = async () => {
    if (schemaList.value.length > 0) {
      return schemaList;
    }
    const { data: schemaListData } = await openapi.schema.getSchemaList();
    schemaList.value = schemaListData as Schema[];
    const data = schemaListData || [];
    data.forEach((schema: API.Schema) => {
      if (!schema.api) {
        return;
      }
      schema.schemaFields?.push({
        key: 'createdBy',
        label: '创建人',
        visibleInForm: false,
        // @ts-ignore
        displayProps: {
          component: 'avatarDisplay',
          mode: 'capsule',
        },
      } as any);
      schema.schemaFields?.push({
        key: 'updatedBy',
        label: '最后修改',
        visibleInForm: false,
        // @ts-ignore
        displayProps: {
          component: 'avatarDisplay',
          mode: 'capsule',
        },
      } as any);
      schema.schemaFields?.push({
        key: 'updatedAt',
        label: '修改于',
        visibleInForm: false,
        // @ts-ignore
        displayProps: {
          component: 'dateDisplay',
        },
      } as any);
      schema.schemaFields?.push({
        key: 'createdAt',
        label: '创建于',
        visibleInForm: false,
        // @ts-ignore
        displayProps: {
          component: 'dateDisplay',
        },
      } as any);
      schema.schemaFieldMap = schema.schemaFields?.reduce(
        (acc, field) => {
          acc[field.key || ''] = field;
          return acc;
        },
        {} as Record<string, SchemaField>,
      );
      schemaMap.value[schema.api] = schema as Schema;
    });

    const dsStore = useDataStructureStore();
    await dsStore.initDataStructure();

    // @ts-ignore
    // @eslint-ignore
    const preloadDataStructure = [
      '/rehabilitation/rehabilitationAssessment',
      '/org/companyUser',
      '/evaluation/evaluationCategory',
      '/asset/assetBorrowApplication',
      '/resourceCenter/fusionSchool',
      '/resourceRoom/student',
      '/resourceRoom/gradeClass',
      '/teacher/schoolGrade',
      '/evaluation/customCriterion',
    ];

    preloadDataStructure.forEach(async (api) => {
      schemaMap.value[api] = await SchemaHelper.getInstanceByDs(api);
    });

    return schemaListData || [];
  };

  const mergeCustomSchema = (raw: Schema, options?: DsConvertOptions): Schema => {
    const { api } = raw;
    const quickSearchProps: QuickSearchProps = {
      enabled: true,
      fields: [],
      multiple: true,
      maxTagCount: 1,
    };
    possibleFilterableFields.forEach((field: string) => {
      if (raw.schemaFieldMap && raw.schemaFieldMap[field]?.filterable) {
        quickSearchProps.fields.push(field);
      }
    });
    if (!quickSearchProps.fields.length) {
      quickSearchProps.enabled = false;
    }

    const schema = merge(
      {
        quickSearchProps,
      },
      raw,
      customSchemaMap[api] || {},
      options?.schemaOverride || {},
    ) as Schema;

    schema.schemaFields = schema.schemaFields?.map((field: SchemaField) => {
      return {
        ...field,
        ...(customSchemaMap[api]?.fieldsMap?.[field.key || ''] || {}),
        ...options?.fieldsOverride?.[field.key || ''],
      };
    });

    const existsFields = schema.schemaFields.map((field) => field.key);

    Object.keys(customSchemaMap[api]?.fieldsMap || {}).forEach((key) => {
      if (!existsFields.includes(key)) {
        schema.schemaFields.push(customSchemaMap[api]!.fieldsMap![key]);
      }
    });

    schema.schemaFieldsMap = schema.schemaFields?.reduce(
      (acc, field) => {
        acc[field.key || ''] = field;
        return acc;
      },
      {} as Record<string, SchemaField>,
    );
    // schema.fieldsMap = schema.schemaFieldMap;

    return schema;
  };

  const getSchemaByApi = (api: string): Schema => {
    const raw = schemaMap.value[api];
    try {
      return mergeCustomSchema(raw);
    } catch (e) {
      console.error(`Failed to get schema by api: ${api}`);
      return raw;
    }
  };

  return {
    schemaList,
    schemaMap,
    mergeCustomSchema,
    getSchemaByApi,
    fetchSchemaList,
  };
});
export default useSchemaStore;

export { getDefaultInputWidget };
