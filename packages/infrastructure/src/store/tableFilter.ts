import { defineStore } from 'pinia';
import { TableFilter, OperatorType, QueryFilter, TableColumn } from '../types';

const useTableFilterStore = defineStore('tableFilter', {
  state: (): any => ({
    filters: {} as Record<string, TableFilter>,
  }),

  getters: {},

  actions: {
    getKey(api: string, scene?: string): string {
      return `${api}${scene ? `-${scene}` : ''}`;
    },
    updateFilter(api: string, filter?: TableFilter, scene?: string) {
      const key = this.getKey(api, scene);
      this.filters[key] = filter || {
        key,
        api,
        scene,
        filters: [],
      };
    },
    updateFilterValue(api: string, column: TableColumn, value?: any, operator?: OperatorType, scene?: string) {
      const filter = this.getFilterItem(api, column, scene);
      filter.value = value;
      if (filter.operator) {
        filter.operator = operator;
      }
      if (!filter.formatValue) {
        filter.formatValue = (val: any) => val;
      }
      const key = this.getKey(api, scene);
      this.filters[key].filters[column.key!] = filter;
    },
    updateOperator(api: string, column: TableColumn, operator?: OperatorType, scene?: string) {
      const filter = this.getFilterItem(api, column, scene);
      filter.operator = operator;
      const key = this.getKey(api, scene);
      this.filters[key].filters[column.key!] = filter;
    },
    getFilter(api: string, scene?: string): TableFilter {
      const key = this.getKey(api, scene);
      return this.filters[key] || this.resetFilter(api, scene);
    },
    getFormattedFilters(api: string, scene?: string): QueryFilter[] {
      const filter = this.getFilter(api, scene);
      filter.filters.forEach((item: QueryFilter) => {
        if (item.operator === 'In' && item.value) {
          item.value = item.value.join(',');
        }
      });
      return [];
    },
    getFilterItem(api: string, column: TableColumn, scene?: string) {
      const filter = this.getFilter(api, scene);
      const { filters } = filter;
      const filterItem = filters[column.key!] || {
        field: column.filterProps?.targetField || column.key!,
        operator: column.filterProps?.defaultOperator || 'Equal',
        formatValue: column.inputWidgetProps?.formatValue,
        virtualSearch: !!column.filterProps?.virtualSearch,
      };
      filters[column.key!] = filterItem;
      this.filters[filter.key].filters = filters;
      return filterItem;
    },
    setFilters(api: string, filters: Record<string, QueryFilter>, scene?: string) {
      const filter = this.getFilter(api, scene);
      filter.filters = filters;
    },
    resetFilter(api: string, scene?: string): void {
      const key = this.getKey(api, scene);
      this.filters[key] = {
        key,
        api,
        scene,
        filters: {},
      };
      return this.filters[key];
    },
  },
});

export default useTableFilterStore;
