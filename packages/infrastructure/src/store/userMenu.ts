import { defineStore } from 'pinia';
import { inject } from 'vue';
import { getUserMenus, UserMenu } from '../data/menu';
import { UNIT_NATURES_MAP, UNIT_NATURES_OPTIONS } from '../constants';
import { SchoolNature } from '../data/schoolTypes';

export type CurrentMenuInfo = {
  app: UserMenu & {
    label: SchoolNature;
  };
  module?: UserMenu;
  subModules?: string[];
};

const availableOrgNature = Object.values(UNIT_NATURES_MAP);

const useUserMenuStore = defineStore({
  id: 'userMenu',
  state: () => ({
    admin: [],
    teacher: [],
  }),
  actions: {
    // setUserMenu(type: 'admin' | 'teacher', userMenus: UserMenu[]) {
    //   this.$patch({ [type]: userMenus });
    // },
    getUserMenus(type: 'admin' | 'teacher') {
      let raw: UserMenu[] = this[type];
      if (!raw?.length) {
        raw = getUserMenus(type);
        this[type] = raw as any;
        // this.setUserMenu(type, raw);
      }

      return raw;
    },
    getCurrentMenuInfo(route?: any): CurrentMenuInfo {
      if (!route) {
        const router: any = inject('router');
        route = router?.currentRoute?.value || router?.currentRoute;
        if (!route) {
          // /manager/roleIndex/moduleIndex/subModules
          const path = window.location.hash;
          const [, , roleIndex, moduleIndex, ...subModules] = path.split('/').filter(Boolean);
          route = {
            params: {
              roleIndex,
              moduleIndex,
              subModules,
            },
          };
        }
      }
      // 这里系统设置用户选项展示说不存在 params
      const { roleIndex, moduleIndex, subModules } = route?.params || {};
      const menus = this.getUserMenus('admin');
      const app: any = menus.find((r) => r.key === roleIndex);
      if (!app) {
        return {} as any;
      }

      const module = app.children?.find((m) => m.key === moduleIndex);
      return {
        app,
        module,
        subModules,
      };
    },

    getCurrentOrgNature(route?: any): string | undefined {
      const menuInfo = this.getCurrentMenuInfo(route);
      return (availableOrgNature.find(
        (nature: any) => nature === menuInfo.app?.label || menuInfo.app?.label.endsWith(nature),
      ) || undefined) as any;
    },

    getCurrentUnitNature(route?: any): string | undefined {
      const menuInfo = this.getCurrentMenuInfo(route);
      return UNIT_NATURES_OPTIONS.find((nature) => nature.value === menuInfo.app?.unitNature)?.value || undefined;
    },

    getCurrentTeacherMenuInfo(route: any): CurrentMenuInfo {
      const menus = this.getUserMenus('teacher');
      const { modules } = route.params;
      if (!modules) {
        return {} as any;
      }
      const [app, module, ...subModules] = modules;

      if (!app) {
        return {} as any;
      }

      const currentApp = menus.find((m) => m.key === app);
      if (!module) {
        return {
          app: currentApp,
        } as any;
      }

      const currentModule = currentApp?.children?.find((m) => m.key === module);

      if (!currentModule && module) {
        subModules.unshift(module);
      }

      return {
        app: currentApp,
        module: currentModule,
        subModules,
      } as any;
    },
  },
});

export default useUserMenuStore;
