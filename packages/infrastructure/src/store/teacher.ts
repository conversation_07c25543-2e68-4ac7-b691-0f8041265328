import { defineStore } from 'pinia';
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '../request';
import useUserMenuStore from './userMenu';

const useSchoolTeacherStore = defineStore('schoolTeacher', {
  state: () => ({
    natureTeachersMap: {},
  }),
  actions: {
    async loadTeachers(orgNature: string) {
      const { data } = await request('/org/companyUser/allTeachers', {
        method: 'GET',
        params: { orgNature, pageSize: 999 },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      this.natureTeachersMap[orgNature] = data;
    },
    async getTeachersListByNature(orgNature: string) {
      if (!this.natureTeachersMap[orgNature]) {
        await this.loadTeachers(orgNature);
      }

      return this.natureTeachersMap[orgNature];
    },
    setTeachersList(orgNature: string, teachers: any) {
      this.natureTeachersMap[orgNature] = teachers;
    },
    async getTeachersList(route?: any, orgNature?: any) {
      if (!orgNature) {
        const menuStore = useUserMenuStore();
        const menuInfo = menuStore.getCurrentMenuInfo(route);
        orgNature = menuInfo?.app?.label;

        if (!orgNature) {
          return [];
        }
      }

      return this.getTeachersListByNature(orgNature);
    },
    async getTeachersMap(route?: any, orgNature?: any) {
      const teachers = await this.getTeachersList(route, orgNature);
      const teachersMap: Record<string, string> = {};

      teachers.forEach((teacher: any) => {
        teachersMap[teacher.id] = teacher;
      });

      return teachersMap;
    },
  },
});

export default useSchoolTeacherStore;
