import { defineStore } from 'pinia';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

const useDataStructureStore = defineStore({
  id: 'dataStructure',
  state: () => ({
    dsMap: {},
  }),
  actions: {
    async initDataStructure(): Promise<any> {
      if (!Object.keys(this.dsMap).length) {
        const res = await request('/common/configure/dataStructure?from=infrastructure', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });

        this.dsMap = res.data || res;
      }
      return this.dsMap;
    },
    async getDataStructure(schemaName: string) {
      await this.initDataStructure();
      return this.dsMap[schemaName];
    },
  },
});

export default useDataStructureStore;
