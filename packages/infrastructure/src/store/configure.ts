import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { defineStore } from 'pinia';

const useConfigureStore = defineStore({
  id: 'configure',
  state: () => ({
    configureMap: {},
  }),
  actions: {
    async load(force?: boolean, callback?: any) {
      if (!Object.keys(this.configureMap).length || force === true) {
        const { data } = await request('/common/configure', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        const configureMap = {};
        data.items.forEach((item) => {
          configureMap[item.configureItem] = item;
        });

        this.$patch({ configureMap });

        if (typeof callback === 'function') {
          callback(data);
        }
      }
    },
    getConfigureItem(name) {
      return this.configureMap[name] ? this.configureMap[name].value : null;
    },
    async updateConfigure(configData) {
      Object.keys(configData).forEach((name) => {
        this.configureMap[name].value = configData[name];
      });

      await request('/common/configure', {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: configData,
      });

      this.$patch({
        configureMap: {
          ...this.configureMap,
        },
      });

      return this.configureMap;
    },
    async updateConfigureItem(name, value) {
      const data = {};
      data[name] = value;
      await request('/common/configure', {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data,
      });

      const item = this.configureMap[name];
      if (item) {
        item.value = value;
      } else {
        this.configureMap[name] = { configureItem: name, value };
      }
      const configureMap = {
        ...this.configureMap,
      };
      configureMap[name] = { ...item, value };
      this.$patch({
        configureMap,
      });

      // Message.success('操作成功');
    },
    getConfigureItemList(name) {
      const raw = this.getConfigureItem(name);
      if (!raw) {
        return [];
      }

      return raw.split(',');
    },
    getConfigureOptions(name): any[] {
      const item = this.getConfigureItemList(name);
      if (!item) {
        return [];
      }

      return item.map((val) => {
        return {
          id: val,
          name: val,
        };
      });
    },
  },
});

export default useConfigureStore;
