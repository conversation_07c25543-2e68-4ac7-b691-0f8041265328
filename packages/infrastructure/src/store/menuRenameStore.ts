import { defineStore } from 'pinia';
import { ref } from 'vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import type { UserMenu } from '@/types/user';

type MenuType = 'teacher' | 'admin';
type RenamedRecord = {
  id?: string;
  teacherMenus?: Record<string, string>;
  adminMenus?: Record<string, string>;
};

const useMenuRenameStore = defineStore('menuRename', () => {
  const renamedRecord = ref<RenamedRecord>({});

  const loadData = async () => {
    try {
      const { data } = await request(`/org/customizedName`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      renamedRecord.value = data?.items[0] || {};
    } catch (error) {
      renamedRecord.value = {};
    }
  };

  const handleRenamedMenu = (
    renameRecord: Record<string, string> | undefined,
    raw: UserMenu[],
    parentPath = '',
  ): UserMenu[] => {
    return raw.map((item) => {
      const currentPath = parentPath ? `${parentPath}/${item.key}` : item.key;
      const oldLabel = item.label;

      // Create a new object to avoid mutating the original
      const newItem = { ...item };

      if (renameRecord?.[currentPath]) {
        newItem.label = renameRecord[currentPath];
        newItem.originalLabel = oldLabel;
      }

      if (item.children) {
        newItem.children = handleRenamedMenu(renameRecord, item.children, currentPath);
      }

      return newItem;
    });
  };

  const getRenamedMenus = (type: MenuType, rawMenus: UserMenu[]): UserMenu[] => {
    const record = renamedRecord.value[type === 'teacher' ? 'teacherMenus' : 'adminMenus'];
    return handleRenamedMenu(record, [...rawMenus]);
  };

  return {
    renamedRecord,
    loadData,
    handleRenamedMenu,
    getRenamedMenus,
  };
});

export default useMenuRenameStore;
