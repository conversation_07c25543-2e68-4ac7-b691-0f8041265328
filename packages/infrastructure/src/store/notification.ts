import { defineStore } from 'pinia';
// eslint-disable-next-line import/no-cycle
import { useUserStore } from '@repo/infrastructure/store';
import { computed, ref } from 'vue';
import { getToken } from '@repo/infrastructure/auth';
import { PROJECT_URLS } from '@repo/env-config';
import openapi from '@repo/infrastructure/openapi';

const useNotificationStore = defineStore('notification', () => {
  const needValidateUser = ref(0);
  const unreadConversation = ref(0);
  const userStore = useUserStore();
  const { userInfo } = userStore;

  const total = computed(() => {
    let count = unreadConversation.value || 0;
    if (userStore.isAnyAuthorized(['system:org:user:verify'])) {
      count += needValidateUser.value || 0;
    }
    return count;
  });

  const refresh = async () => {
    const { data }: any = await openapi.notificationController.getNotifyCount();

    needValidateUser.value = data.needValidateUser;
    unreadConversation.value = data.unreadConversation;
  };
  const types = computed(() => {
    return [
      {
        key: 'needValidateUser',
        label: '待审核用户',
        count: needValidateUser.value,
        link: '/manage/system/org/user',
        visible: userStore.isAnyAuthorized(['system:org:user:verify']),
      },
      {
        key: 'unreadConversation',
        label: '未读消息',
        count: unreadConversation.value,
        link() {
          const url = `${PROJECT_URLS.GO_PROJECT}/?token=${getToken()}&userId=${
            userInfo.id
          }&mobile=${userInfo.mobile}&to=/homeSchoolCommunicate/conversation`;
          window.open(url);
        },
      },
    ];
  });

  return {
    needValidateUser,
    unreadConversation,
    total,
    refresh,
    types,
  };
});

export default useNotificationStore;
