import { defineStore } from 'pinia';
import { User } from '@repo/infrastructure/types';
// @ts-ignore
import { toRaw } from 'vue';
// @ts-ignore
import AntPathMatcher from '@maxbilbow/ant-path-matcher';
import api from '@repo/infrastructure/openapi';
import { clearToken, getToken } from '@repo/infrastructure/auth';
import { ENV, PROJECT_URLS } from '@repo/env-config';
import { isArray } from 'lodash';
import { getElectronApi, isElectron } from '../electron';
import { request } from '../request';

const matcher = AntPathMatcher();

const useUserStore = defineStore('user', {
  state: (): User => ({
    id: 0,
    name: '',
    avatar: '',
    sysRoles: [],
    consultingStatus: '',
    company: {},
    branchOffice: {},
    unreadMessageCount: 0,
  }),
  getters: {
    userInfo(): User {
      return toRaw(this.$state);
    },
  },

  actions: {
    getUserNature() {
      return this.branchOffice?.school?.nature;
    },
    getUserFusionSchool() {
      return this.branchOffice?.school;
    },
    switchRoles() {
      return new Promise((resolve) => {
        // this.role = this.role === 'user' ? 'admin' : 'user';
        // resolve(this.role);
        resolve(null);
      });
    },
    // Set user's information
    setInfo(partial: Partial<User> | User) {
      this.$patch(partial);
      if (isElectron) {
        getElectronApi().send('setUserInfo', partial);
      }
    },

    async refreshUserInfo() {
      const { data } = await request('/org/session/me', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      if (data) {
        this.setInfo(data);
      }
    },

    updateUnreadMessageCount(changes: number) {
      const userInfo = toRaw(this.$state);
      let count = userInfo.unreadMessageCount || 0;
      count += changes;
      if (count < 0) {
        count = 0;
      }
      const newUserInfo = { ...userInfo, unreadMessageCount: count };
      this.setInfo(newUserInfo);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    isModuleAuthorized(module: string) {
      const authorizedModule = this.company?.authorizedModule || [];
      return authorizedModule.indexOf(module) >= 0;
    },

    getCompanyBindingUrl() {
      if (ENV.MODE !== 'production') {
        return PROJECT_URLS.MAIN_PROJECT;
      }
      const company = this.company || {};
      const domain = company?.visitDomain || '';
      if (domain) {
        return `https://${domain}`;
      }
      return PROJECT_URLS.MAIN_PROJECT;
    },

    getMainProjectUrl(path?: string, page?: string) {
      page = page || '/app.html';
      path = path || '';
      const token = getToken();
      const tokenPart = token ? `?token=${token}` : '';
      return `${this.getCompanyBindingUrl()}${page}${tokenPart}#${path}`;
    },

    getGoProjectUrl(path?: string, page?: string) {
      page = page || '/index.html';
      path = path || '';
      const token = getToken();
      const tokenPart = token ? `?token=${token}` : '';
      return `${PROJECT_URLS.GO_PROJECT}${page}${tokenPart}#${path}`;
    },

    isAnyAuthorized(permissions: string[], grantedPermissions?: string[]) {
      return permissions?.some((p) => this.isAuthorized(p, grantedPermissions));
    },

    isAuthorized(permission: string, grantedPermissions?: string[]): boolean {
      // if (this.userInfo?.su) {
      //   return true;
      // }

      if (isArray(permission)) {
        return this.isAnyAuthorized(permission, grantedPermissions);
      }

      if (!grantedPermissions) {
        grantedPermissions = this.userInfo?.authorities;
      }

      permission = permission.replace('*', '');
      return !!(
        grantedPermissions?.includes(permission) ||
        grantedPermissions?.some(
          (p) => p.indexOf(permission) === 0 || matcher.match(p, permission) || p.startsWith(permission),
        )
      );
    },

    logoutCallBack() {
      clearToken();
      window.location.href = `${PROJECT_URLS.MAIN_PROJECT}/?logout=1`;
    },
    // Logout
    async logout() {
      try {
        await api.genericSessionController.logout();
      } finally {
        this.logoutCallBack();
      }
    },
  },
});

export default useUserStore;
