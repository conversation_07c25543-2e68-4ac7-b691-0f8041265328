// @ts-ignore
import { nextTick, ref, toRaw } from 'vue';
// import { Message, PaginationProps } from '@arco-design/web-vue';
import { isArray, isString } from 'lodash';
import { Schema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import axios from 'axios';
import useLoading from '../hooks/loading';
import { QueryFilter, RO } from '../types/table';
import useTableFilterStore from '../store/tableFilter';
import CommonApi from './common';
import { request } from '../request';

const useList = <T extends Record<string, any>>({
  schema,
  api,
  pageSize = 20,
  rawData = [],
  rowKey = 'id',
  defaultQueryParams = {},
  axiosConfig = {},
  dataFormat,
}: {
  schema?: Schema;
  api?: string;
  pageSize?: number;
  rawData?: T[];
  rowKey?: string;
  defaultQueryParams?: Record<string, any>;
  axiosConfig?: any;
  dataFormat?: (raw: any) => any;
}): any => {
  api = api || schema?.requestApi?.list || schema?.api;
  if (!api) {
    throw new Error('请传入 schema 或 api');
    // Message.error('请传入 schema 或 api');
  }

  const isMainProjectApiRequest =
    axiosConfig.baseURL === PROJECT_URLS.MAIN_PROJECT_API || axios.defaults.baseURL === PROJECT_URLS.MAIN_PROJECT_API;

  const { loading, setLoading } = useLoading();
  const filterStore = useTableFilterStore();
  const sorter = ref<any>(isMainProjectApiRequest ? '-id' : 'id desc');
  const selectedItems = ref<any[]>([]);
  // const queryApi = ref<any>({} as any);
  const queryParams = ref<Record<string, any>>({
    ...defaultQueryParams,
  });

  const pagination = ref<any>({
    current: 1,
    pageSize,
    total: rawData?.length || 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [2, 20, 30, 50, 100, 200],
    size: schema?.listViewProps?.size || 'small',
  });

  const listData = ref<T[]>(rawData || []);
  const filtersWithString = ref<Record<string, string[]>>({});

  const handleSelectionChange = (selectedRowKeys: number[]) => {
    selectedItems.value = listData.value.filter((item) => selectedRowKeys.includes(item[rowKey]));
  };
  const loadData = async (options?: Record<string, any>) => {
    setLoading(true);
    const filters = filterStore.getFilter(api);
    const puredFilters = Object.values(filters.filters)
      .map((item: any) => {
        return {
          field: item.field,
          operator: item.operator,
          value: toRaw(item.formatValue ? item.formatValue(item.value) : item.value),
        };
      })
      .filter((item: any) => item.value !== undefined);

    if (puredFilters.length) {
      const stringFilters: Record<string, string[]> = {};
      puredFilters.forEach((item: QueryFilter) => {
        if ((isString(item.value) && item.value?.trim()) || isArray(item.value)) {
          item.field?.split('|').forEach((field) => {
            stringFilters[field] = isArray(item.value)
              ? item.value.filter((itemVal: any) => {
                  return isString(itemVal) && itemVal.trim();
                })
              : [item.value];
          });
        }
      });
      filtersWithString.value = stringFilters;
    } else {
      filtersWithString.value = {};
    }

    const ro: RO = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      sort: sorter.value,
      ...defaultQueryParams,
      filters: isMainProjectApiRequest ? JSON.stringify(puredFilters) : puredFilters,
      ...queryParams.value,
      ...(options || {}),
    };

    try {
      selectedItems.value = [];
      const { data: result } = await request(api, {
        method: 'GET',
        params: ro,
        ...axiosConfig,
      });

      listData.value = [];
      const raw = [...(result?.data?.list || result?.data?.items || result?.list || result?.items || result || [])];
      await nextTick(() => {
        listData.value = raw;
        pagination.value.total = result?.data?.total || result?.total || result?.totalItems || result?.length || 0;
      });
    } finally {
      setLoading(false);
    }

    if (typeof dataFormat === 'function') {
      listData.value = listData.value.map((item: any) => {
        return dataFormat(item);
      });
    }

    return listData;
  };

  const listPageIndex = (rowIndex: number) => {
    return (pagination.value.current! - 1 || 0) * (pagination.value.pageSize! || 0) + rowIndex + 1;
  };

  const handlePageChange = async (page: number) => {
    pagination.value.current = page;
    await loadData();
  };
  const resetPage = () => {
    pagination.value.current = 1;
  };
  const handlePageSizeChange = async (ps: number) => {
    pagination.value.pageSize = ps || pageSize;
    pagination.value.current = 1;
    await loadData();
  };

  const handleSorterChange = async (field: string, direction: string) => {
    if (direction) {
      if (isMainProjectApiRequest) {
        sorter.value = `${direction.toLowerCase() === 'ascend' ? '+' : '-'}${field}`;
      } else {
        sorter.value = `${field} ${direction.toLowerCase() === 'ascend' ? 'asc' : 'desc'}`;
      }
    } else {
      sorter.value = '';
    }

    await loadData();
  };

  const listInit = async () => {
    filterStore.resetFilter(api);
  };

  const listReset = () => {
    listData.value = [];
    pagination.value.current = 1;
    pagination.value.pageSize = pageSize;
    sorter.value = '';
    selectedItems.value = [];
    filterStore.resetFilter(api);
  };

  const refresh = async () => {
    listReset();
    await loadData({});
  };

  // eslint-disable-next-line consistent-return
  return {
    loading,
    sorter,
    selectedItems,
    filterStore,
    listData,
    pagination,
    filtersWithString,
    setLoading,
    queryParams,
    refresh,
    resetPage,
    listPageIndex,
    listReset,
    listInit,
    loadData,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleSorterChange,
  };
};

export default useList;
