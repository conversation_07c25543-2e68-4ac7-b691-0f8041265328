import { Schema, RO, HttpResponse } from '@repo/infrastructure/types';
import axios from 'axios';
import { extend } from 'lodash';
import { PROJECT_URLS } from '@repo/env-config';
import { useSchemaStore } from '../store';
import * as openapi from '../openapi/api';
import { axiosInstance } from '../request';

const controllers = openapi.default || {};

export default class CommonApi<S extends Schema, T> {
  schema: S;

  controller: any;

  constructor(schema: S) {
    this.schema = schema;
  }

  static getInstanceByApi(api: string): CommonApi<any, any> {
    const schema: Schema = useSchemaStore().getSchemaByApi(api);
    const commonApi = new CommonApi(schema);
    const apiName = `../openapi/api/${schema.functionName}Controller.ts`;
    if (controllers[apiName]) {
      commonApi.controller = controllers[apiName];
    }

    return commonApi;
  }

  static getInstance(schema: Schema): CommonApi<any, any> {
    const api = new CommonApi(schema);
    const apiName = `../openapi/api/${schema.functionName}Controller.ts`;
    if (controllers[apiName]) {
      api.controller = controllers[apiName];
    }

    return api;
  }

  getSchemaApi(): string {
    if (!this.schema.api) {
      // Message.error('请配置接口地址');
      throw new Error('请配置接口地址');
    }
    return this.schema.api;
  }

  async fetchList(queryParams?: RO, config?: any) {
    // if (this.controller && this.controller.findAll) {
    //   return this.controller.findAll(queryParams);
    // }
    const defaultParams = {
      page: 1,
      pageSize: 20,
    };

    queryParams = queryParams || ({} as RO);
    if (queryParams?.filters) {
      queryParams.query = JSON.stringify(queryParams.filters);
    } else {
      queryParams.filters = [];
    }

    if (queryParams?.filters && queryParams?.filters.length > 3) {
      queryParams.filters = undefined;
      return axiosInstance.post<HttpResponse<T[]>>(
        `${this.getSchemaApi()}/findAll`,
        extend(defaultParams, queryParams || {}),
      );
    }
    queryParams.filters = undefined;
    return axiosInstance.get<HttpResponse<T[]>>(this.getSchemaApi() || '', {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
      params: extend(defaultParams, queryParams || {}),
    });
  }

  async fetchOne(id: number, queryParams?: Record<string, any>, config?: any) {
    if (this.controller && this.controller.findOne) {
      return this.controller.findOne({
        id,
        ...queryParams,
      });
    }
    return axiosInstance.get<HttpResponse<T>>(`${this.getSchemaApi()}/${id}`, {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
      params: queryParams,
    });
  }

  async createRecord(data: Record<string, any>, config?: any) {
    const res = axiosInstance.post<HttpResponse<T>>(this.getSchemaApi(), data, {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
    });

    return res;
  }

  async updateRecord(data: Record<string, any>, config?: any) {
    const res = (await axiosInstance.put<HttpResponse<T>>(`${this.getSchemaApi()}/${data.id}`, data, {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
    })) as any;

    return res;
  }

  async deleteRecord(id: number, config?: any) {
    return (await axiosInstance.delete<HttpResponse<T>>(`${this.getSchemaApi()}/${id}`, {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
    })) as any;
  }

  async batchDeleteRecord(id: number[], config?: any) {
    return (await axiosInstance.delete<HttpResponse<T>>(`${this.getSchemaApi()}/batch/${id.join(',')}`, {
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
    })) as any;
  }

  async patchRecord(id: number, data: Record<string, any>) {
    return axiosInstance.patch<HttpResponse<T>>(`${this.getSchemaApi()}/${id}`, data);
  }

  async toggleRecordEnabled(id: number, enableOrNot: boolean) {
    return axiosInstance.patch<HttpResponse<T>>(`${this.getSchemaApi()}/${enableOrNot ? 'enable' : 'disable'}/${id}`);
  }

  async batchToggleRecordEnabled(ids: number[], enableOrNot: boolean) {
    return axiosInstance.patch<HttpResponse<T>>(
      `${this.getSchemaApi()}/batch${enableOrNot ? 'Enable' : 'Disable'}/${ids.join()}`,
    );
  }

  async checkUnique(id: any, field: string, value: any, config?: any) {
    return axiosInstance.get<HttpResponse<boolean>>(`${this.getSchemaApi()}/checkUnique`, {
      params: {
        id,
        field,
        value,
      },
      baseURL: this.schema.baseURL || PROJECT_URLS.GO_PROJECT_API,
      ...(config || {}),
    });
  }
}
