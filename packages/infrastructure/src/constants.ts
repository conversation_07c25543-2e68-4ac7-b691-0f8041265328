const constants: {
  LOCALE_CACHE_KEY: string;
  DEFAULT_DATE_FORMAT: string;
  FULL_DATE_FORMAT: string;
  TOKEN_CACHE_KEY: string;
  USER_ID_CACHE_KEY: string;
  FROM_MAIN_PROJECT_PATH_CACHE_KEY: string;
} = {
  LOCALE_CACHE_KEY: 'arco-locale',
  DEFAULT_DATE_FORMAT: 'YYYY-MM-DD',
  FULL_DATE_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  TOKEN_CACHE_KEY: 'token',
  USER_ID_CACHE_KEY: 'userId',
  FROM_MAIN_PROJECT_PATH_CACHE_KEY: 'fromMain',
};

export const DEFAULT_LIST_FIELDS = ['createdAt', 'updatedAt'];

export default constants;

export const { TOKEN_CACHE_KEY, USER_ID_CACHE_KEY } = constants;

export const DISORDER_TYPES = [
  '视力残疾',
  '听力残疾',
  '言语残疾',
  '肢体残疾',
  '智力残疾',
  '精神残疾',
  '多重残疾',
  '孤独症',
  '抽动症',
  '亚斯伯格症',
  '注意力缺陷多动症',
  '学习障碍',
  '情绪行为障碍',
  '学习困难',
  '其他',
];

export const COURSE_CATEGORIES: string[] = ['生活语文', '生活数学', '生活适应'];

export const COURSE_GRADES: string[] = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'];

export const COURSE_PERIODS: string[] = ['上册', '下册'];

export const CONSULTING_STATUS = [
  {
    id: 'GLOBAL_ONLINE',
    name: '全国家长可咨询',
  },
  {
    id: 'ONLINE',
    name: '本单位下家长咨询',
  },
  {
    id: 'OFFLINE',
    name: '家长不可咨询',
  },
];

export const backgroundGradients = [
  'linear-gradient(45deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%)',
  'linear-gradient(to top, #a18cd1 0%, #fbc2eb 100%)',
  'linear-gradient(to top, #fad0c4 0%, #fad0c4 1%, #ffd1ff 100%)',
  'linear-gradient(to top, #fbc2eb 0%, #a6c1ee 100%)',
  'linear-gradient(to top, #fdcbf1 0%, #fdcbf1 1%, #e6dee9 100%)',
  'linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%)',
  'linear-gradient(to right, #43e97b 0%, #38f9d7 100%)',
  'linear-gradient(to right, #4facfe 0%, #00f2fe 100%)',
  'linear-gradient(to top, #30cfd0 0%, #330867 100%)',
  'linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%)',
  'linear-gradient(to top, #9890e3 0%, #b1f4cf 100%)',
  'linear-gradient(to top, #0ba360 0%, #3cba92 100%)',
  'linear-gradient(to top, #a3bded 0%, #6991c7 100%)',
  'linear-gradient(-20deg, #d558c8 0%, #24d292 100%)',
  'linear-gradient(to top, #0fd850 0%, #f9f047 100%)',
];

export const getRandomBackgroundGradient = (seed?: number): string => {
  if (seed !== undefined) {
    return backgroundGradients[seed % backgroundGradients.length];
  }

  return backgroundGradients[Math.floor(Math.random() * backgroundGradients.length)];
};

// Center("资源中心"),
//     Special("特殊教育学校"),
//     Normal("普通教育学校"),
//     Kindergarten("幼儿园"),
//     Vocational("职业教育学校"),
//     Institution("机构"),
//     DisabledPersonsFederation("残疾人联合委员会")

export const UNIT_NATURES = {
  Center: 'Center',
  Special: 'Special',
  Normal: 'Normal',
  Kindergarten: 'Kindergarten',
  Vocational: 'Vocational',
  Institution: 'Institution',
  DisabledPersonsFederation: 'DisabledPersonsFederation',
  SpecialEducationCommittee: 'SpecialEducationCommittee',
  SendEducation: 'SendEducation',
};

export const UNIT_NATURES_OPTIONS = [
  { label: '资源中心', value: 'Center' },
  { label: '特殊教育学校', value: 'Special' },
  { label: '普通教育学校', value: 'Normal' },
  { label: '幼儿园', value: 'Kindergarten' },
  { label: '职业教育学校', value: 'Vocational' },
  { label: '机构', value: 'Institution' },
  { label: '残疾人联合委员会', value: 'DisabledPersonsFederation' },
  { label: '特教专委会', value: 'SpecialEduCommittee' },
  { label: '送教上门', value: 'SendEducation' },
];

const unitNaturesMap = {};
UNIT_NATURES_OPTIONS.forEach((item) => {
  unitNaturesMap[item.value] = item.label;
});
export const UNIT_NATURES_MAP = unitNaturesMap as any;
