import { extend } from 'lodash';
import SvgIcon from './icon/svgIcon.vue';
import Iconfont from './icon/iconfont.vue';

const collapsedNameDisplay = (
  lists: any[],
  options: {
    labelField?: string;
    exposeCount?: number;
    unit?: string;
    separator?: string;
  },
) => {
  const { labelField, exposeCount, unit, separator } = extend(
    {
      labelField: 'name',
      exposeCount: 2,
      unit: '项',
      separator: '、',
    },
    options || {},
  );
  const collapsedNames = lists?.map((item) => item[labelField]) || [];
  if (collapsedNames.length <= exposeCount) {
    return collapsedNames.join(separator);
  }
  return `${collapsedNames.slice(0, exposeCount).join(separator)}等 ${collapsedNames.length} ${unit}`;
};

export { SvgIcon, Iconfont, collapsedNameDisplay };
