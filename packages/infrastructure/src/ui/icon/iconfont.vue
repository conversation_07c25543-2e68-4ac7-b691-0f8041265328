<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps({
    name: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
      default: 16,
    },
  });

  const cls = computed(() => {
    return `iconfont icon-${props.name}`;
  });
</script>

<template>
  <span class="iconfont-wrapper">
    <i :class="cls" :style="{ fontSize: size + 'px' }"></i>
  </span>
</template>

<style scoped lang="scss">
  .iconfont-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
