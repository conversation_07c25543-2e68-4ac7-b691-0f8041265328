<template>
  <div>
    <svg aria-hidden="true" class="svg-icon" v-bind="$attrs">
      <use :xlink:href="iconHref"></use>
    </svg>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue';

  const props = defineProps({
    iconClass: {
      type: String,
      required: true,
    },
  });

  const iconHref = computed(() => {
    return `#${props.iconClass}`;
  });
</script>

<style lang="less" scoped>
  .svg-icon {
    width: 2em;
    height: 2em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
</style>
