import { ref } from 'vue';
// eslint-disable-next-line import/no-cycle
import { clearToken, getToken, setToken } from '../auth';

export type ElectronClientType = 'admin' | 'teacher';

export type ElectronWindowType = 'login' | 'desktop-widget' | 'hover-ball' | 'main';

// @ts-ignore
export const isElectron = window?.electronAPI !== undefined;

export const getElectronApi = (): {
  [key: string]: any;
} => {
  // @ts-ignore
  return window?.electronAPI || {};
};

export const setClientToken = (token: string, expireTime?: any) => {
  setToken(token, expireTime);
  if (isElectron) {
    // @ts-ignore
    getElectronApi().send('setToken', token);
  }
};

export const getClientToken = async () => {
  let token = getToken();
  if (token) {
    return token;
  }

  if (isElectron) {
    // @ts-ignore
    token = await getElectronApi().send('getToken');
    return token;
  }

  return '';
};

export const clearClientToken = () => {
  setToken('');
  clearToken();
};

export const clientRedirectTo = (client: ElectronClientType, token?: string) => {
  if (isElectron) {
    if (token) {
      setClientToken(token);
    }

    // @ts-ignore
    getElectronApi().send('switchClient', client, token);

    return true;
  }

  return false;
};

export const openDevTools = (window?: string, desktopWidgetPath?: string) => {
  if (isElectron) {
    // @ts-ignore
    getElectronApi().send('openDevTools', window, desktopWidgetPath);
  }
};

// handleTriggerDevTools
let clickCount: number = 0;
let lastClickTime: number = 0;
const maxClicks: number = 10;
const timeLimit: number = 3000; // 3秒

// 处理点击事件的函数
export const handleTriggerDevTools = (windowName?: string, desktopWidgetPath?: string) => {
  const currentTime: number = Date.now();

  // 判断是否在时间限制内连续点击
  if (lastClickTime === 0 || currentTime - lastClickTime <= timeLimit) {
    clickCount += 1;
  } else {
    clickCount = 1; // 如果超过3秒，重置计数
  }

  lastClickTime = currentTime;

  // 如果点击次数达到10次，触发警告
  if (clickCount === maxClicks) {
    openDevTools(windowName, desktopWidgetPath);
    clickCount = 0; // 重置点击计数
  }
};

export const setWindowDraggableArea = (suspensionDom: Element, windowName: ElectronWindowType, widgetPath?: string) => {
  const biasX = ref(0);
  const biasY = ref(0);
  const moveEvent = (e: any) => {
    getElectronApi().send('windowMove', e.screenX - biasX.value, e.screenY - biasY.value, windowName, widgetPath);
  };
  const initSuspension = () => {
    suspensionDom.addEventListener('mousedown', (e: any) => {
      if (e.button === 0) {
        biasX.value = e.x;
        biasY.value = e.y;
        document.addEventListener('mousemove', moveEvent);
      }
    });
    suspensionDom.addEventListener('mouseup', () => {
      biasX.value = 0;
      biasY.value = 0;
      document.removeEventListener('mousemove', moveEvent);
    });
  };

  initSuspension();
};

export const removeWindowDraggableArea = (suspensionDom: Element) => {
  suspensionDom.removeEventListener('mousedown', () => {});
  suspensionDom.removeEventListener('mouseup', () => {});
  document.removeEventListener('mousemove', () => {});
};
