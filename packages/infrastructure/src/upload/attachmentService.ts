import { ENV } from '@repo/env-config';
import { AttachmentUploadOptions } from './types';
import { IAttachmentProcessor } from './IAttachmentProcessor';
import AliyunOSSAttachmentProcessor from './impl/AliyunOSSAttachmentProcessor';

const getOssProcessor = (options?: AttachmentUploadOptions): IAttachmentProcessor => {
  const target = options?.saveTarget || ENV.VITE_APP_UPLOAD_TARGET || null;
  switch (target) {
    case 'AliyunOSS':
      return new AliyunOSSAttachmentProcessor(options);
    default:
      return new AliyunOSSAttachmentProcessor(options);
  }
};

export default getOssProcessor;
