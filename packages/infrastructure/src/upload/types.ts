export type AttachmentToUpload = {
  file: File;
  name?: string;
  percentage?: number;
  partSize?: number;
  parallel?: number;
  saveType?: string;
  subFolder?: string;
  handleUploadProgress?: (percentage: number, index?: number) => void;
  handleUploadComplete?: (url: string, index?: number, uploadedResource?: any) => void;
};

export type AttachmentSaveTarget = 'Local' | 'AliyunOSS';

export type AttachmentUploadOptions = {
  saveTarget?: AttachmentSaveTarget;
  autoStart?: boolean;
  limit?: number;
  maxSize?: number;
  minSize?: number;
  handleUploadProgress?: (percentage: number, index?: number) => void;
  handleUploadComplete?: (url: string, index?: number) => void;
  requestHeaders?: Record<string, string>;
};

export type UploadedAttachment = {
  url: string;
  name: string;
};
