import { AttachmentToUpload } from './types';

export interface IAttachmentProcessor {
  // client
  getClient(): any;

  // 前端简单直传
  uploadSimply(fileItem: any, saveDir?: string, options?: any): Promise<any>;

  // 大文件分段上传
  uploadLarge(attachment: AttachmentToUpload): Promise<any>;

  // 多个文件分段上传
  uploadLargeBatch(attachments: AttachmentToUpload[]): Promise<any[]>;

  // 图片缩略图
  thumbUrl(rawUrl: string, width?: number, height?: number, fill?: string): string;

  thumbUrlById(resourceId: number, width?: number, height?: number, fill?: string = 'fill'): string;

  // 视频封面
  videoCoverUrl(rawUrl: string, width: number, height: number, second?: number): string;
}
