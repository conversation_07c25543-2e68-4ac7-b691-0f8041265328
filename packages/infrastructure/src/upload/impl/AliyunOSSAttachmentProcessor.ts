import api from '@repo/infrastructure/openapi';
// import { Message } from '@arco-design/web-vue';
import axios from 'axios';
import OSS from 'ali-oss';
import { request, axiosInstance } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import dayjs from 'dayjs';
import type { IAttachmentProcessor } from '../IAttachmentProcessor';
import { AttachmentToUpload, AttachmentUploadOptions } from '../types';

export default class AliyunOSSAttachmentProcessor implements IAttachmentProcessor {
  parallel?: number = 5;

  partSize?: number = 1024 * 1024 * 3;

  options: AttachmentUploadOptions;

  schema?: string;

  processors: Record<string, string>;

  stsSecure?: boolean;

  client?: OSS;

  constructor(options?: AttachmentUploadOptions) {
    this.options = options || {};
    this.stsSecure = true;
    this.processors = {
      imageResize: 'x-oss-process=image/resize',
      videoSnapshot: 'x-oss-process=video/snapshot',
      videoConvert: 'x-oss-process=video/convert',
    };
  }

  getClient(): any {
    return this.client;
  }

  async uploadSimply(fileItem: any, saveDir?: string, options?: any): Promise<any> {
    let uploadUrl: string;
    let getUrl: string = '';

    const reqFunc = async (): Promise<any> => {
      // const { fileItem } = options;
      const rawFileName = fileItem.name || fileItem.file?.name || '';
      const randomName = `${Date.now()}-${rawFileName}`;
      saveDir = saveDir || `course/cover/${dayjs().format('YYYYMMDD')}/`;
      try {
        const { data: res } = (await api.ossController.getDirectlyUploadUrl(
          {
            objectName: saveDir + randomName,
            contentType: fileItem.file?.type ? fileItem.file?.type : '',
          },
          { ...options },
        )) as any;

        uploadUrl = res.data?.put || res.put || '';
        getUrl = res.data?.get || res.get || '';
      } catch (e: any) {
        // Message.error('获取上传链接失败');
        throw new Error('获取上传链接失败');
        // return e;
      }

      try {
        // const req = axios.create();
        await axiosInstance({
          url: uploadUrl,
          method: 'PUT',
          headers: {
            'Content-Type': fileItem.file?.type,
          },
          data: fileItem.file,
        });
        return getUrl;
      } catch (e: any) {
        throw new Error('上传失败');
      }
    };

    if (this.options.autoStart !== false) {
      await reqFunc();
    }
    return getUrl;
  }

  thumbUrl(rawUrl: string, width?: number, height?: number, fill: string = 'fill'): string {
    width = width || 400;
    height = height || 300;
    return `${rawUrl}?${this.processors.imageResize},m_${fill},w_${width},h_${height}`;
  }

  // eslint-disable-next-line class-methods-use-this
  thumbUrlById(resourceId: number, width?: number, height?: number, fill?: string = 'fill'): string {
    return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/image/${resourceId}?width=${width}&height=${height}`;
  }

  videoCoverUrl(rawUrl: string, width: number, height: number, second?: number): string {
    // rand 1-10
    const randSec = Math.floor(Math.random() * 10) + 1;
    return `${rawUrl}?${this.processors.videoSnapshot},t_${second || randSec},f_jpg,w_${width},h_${height}`;
  }

  async uploadLarge(attachment: AttachmentToUpload): Promise<any> {
    const client = await this.getOssClient();
    return this.uploadPartly(attachment, client);
  }

  async uploadLargeBatch(attachments: AttachmentToUpload[]): Promise<any> {
    const client = await this.getOssClient();
    return Promise.all(attachments.map((attachment) => this.uploadPartly(attachment, client)));
  }

  async getOssClient(): Promise<OSS> {
    const { data: res } = (await api.ossController.getStsToken({
      headers: {
        'Login-Source': window?.loginSource || 'PC',
        ...this.options.requestHeaders,
      },
    })) as any;
    const stsToken = res.data || res;

    this.client = new OSS({
      accessKeyId: stsToken.accessKeyId!,
      accessKeySecret: stsToken.accessKeySecret!,
      stsToken: stsToken.securityToken!,
      bucket: stsToken.bucket!,
      region: stsToken.region!,
      secure: this.stsSecure,
      visitPrefix: stsToken.visitPrefix,
    });

    return this.client;
  }

  // 分片上传
  async uploadPartly(attachment: AttachmentToUpload, client: OSS | any, index?: number): Promise<any> {
    attachment.percentage = 0;
    const { file } = attachment;
    const fileName = file.name
      .replace(/[\\/:*?"<>|+]/g, '')
      .replace(/\s+/g, '')
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5 .-]/g, '')
      .trim();

    // const nameSplit = file.name.split('.');
    const nameSplit = fileName.split('.');
    const ext = nameSplit.splice(-1);
    const savePath = `${attachment.saveType || 'attachment'}/${attachment.subFolder || dayjs().format('YYYYMMDD')}/`;
    const saveName = `${nameSplit.join('.')}_${dayjs().format('YYYYMMDDHHmmss')}.${ext}`;
    const path = `${savePath}${saveName}`;

    if (attachment.percentage < 1 && file.name.indexOf('.') !== -1) {
      try {
        await client.multipartUpload(path, file, {
          parallel: attachment.parallel || this.parallel || 5,
          partSize: attachment.partSize || this.partSize || 1024 * 1024,
          progress: async (percentage: number, checkpoint: any, res: any) => {
            percentage = Number(percentage.toFixed(2));
            attachment.percentage = percentage;
            const onProgress: any = attachment.handleUploadProgress || this.options.handleUploadProgress;
            if (typeof onProgress === 'function') {
              await onProgress(percentage, index, attachment, checkpoint, res);
            }

            const onComplete: any = attachment.handleUploadComplete || this.options.handleUploadComplete;

            if (percentage >= 1 && typeof onComplete === 'function') {
              const visitPrefix =
                client.options.visitPrefix || `https://${client.options.bucket}.${client.options.region}.aliyuncs.com`;

              const visitUrl = `${visitPrefix}/${path}`;
              let uploadedResource;
              try {
                const { data } = await request('/common/uploadedResource/saveOssUpload', {
                  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
                  method: 'post',
                  data: {
                    name: file.name,
                    savePath,
                    saveName,
                  },
                  headers: {
                    'Login-Source': window?.loginSource || 'PC',
                  },
                });
                uploadedResource = data;
              } finally {
                await onComplete(visitUrl, index, uploadedResource);
              }
            }
          },
        });
      } catch (e: any) {
        console.log(e);
      }
    }
  }
}
