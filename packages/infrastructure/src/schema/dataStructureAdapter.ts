import { capitalize, cloneDeep, extend } from 'lodash';
import { defineAsyncComponent } from 'vue';
import { SchemaField } from '../types';
import useDataStructureStore from '../store/dataStructure';
import { customSchemaMap } from './customSchema';
import { DsConvertOptions } from '../types/schema';

export type FieldStructureVo = {
  label: string;
  key: string;
  type?: string;
  foreignKey?: string;
  foreignApi?: string;
  staticStore?: any[];
  maxLength?: number;
  required?: boolean;
  updateable?: boolean;
  value?: any; // default value
  virtual?: boolean;
  systemGenerated?: boolean;
  preload?: boolean;
};
const systemGeneratedFields = [
  'id',
  'createdBy',
  'createdAt',
  'deletedAt',
  'modifiedBy',
  'modifiedAt',
  'deleted',
  'branchOfficeId',
];

const convertDs2Schema = async (api: string): Promise<API.Schema> => {
  const dsStore = useDataStructureStore();
  const dsRaw: FieldStructureVo[] = (await dsStore.getDataStructure(api)) || [];
  const schemaFields = dsRaw?.map((field) => {
    const type = capitalize((field.type || 'String').toLowerCase());
    const foreignField = field.foreignApi && {
      labelField: field.foreignKey,
      api: field.foreignApi,
    };

    let options;
    if (type === 'Enum') {
      options = field.staticStore?.map((item) => ({
        label: item.name || item.label || item.label,
        value: item.id,
        ...item,
      }));
    } else if (field.staticStore) {
      options = field.staticStore;
    }
    const inputWidgetProps = {
      options,
      maxLength: field.maxLength,
    };

    return {
      key: field.key,
      label: field.label,
      dataType: type === 'Text' ? 'String' : type,
      valueType: type === 'Text' ? 'String' : type,
      foreignField,
      inputWidgetProps,
      defaultValue: field.value,
      editable: !field.systemGenerated && systemGeneratedFields.indexOf(field.key) === -1,
      updatable: field.updateable && !field.systemGenerated && systemGeneratedFields.indexOf(field.key) === -1,
      required: field.required,
      readOnly: !field.updateable || field.systemGenerated,
    } as SchemaField;
  });

  schemaFields.push({
    key: 'createdBy',
    label: '创建人',
    visibleInForm: false,
    displayProps: {
      component: 'avatarDisplay',
      mode: 'capsule',
    },
  });
  schemaFields.push({
    key: 'modifiedBy',
    label: '最后修改',
    visibleInForm: false,
    displayProps: {
      component: 'avatarDisplay',
      mode: 'capsule',
    },
  });
  schemaFields.push({
    key: 'modifiedDate',
    visibleInForm: false,
    label: '修改于',
    displayProps: {
      component: 'dateDisplay',
    },
  });

  const temp = api.split('/');
  const customSchema = cloneDeep(customSchemaMap[api] || {});

  return extend({}, customSchema, {
    api,
    moduleName: temp[1],
    functionName: temp[2],
    schemaFields,
  });
};

export { convertDs2Schema };
