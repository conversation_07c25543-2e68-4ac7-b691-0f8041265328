import { DetailViewProps, FormViewProps, ListViewProps, SchemaField } from '@repo/infrastructure/types';
import Schema = API.Schema;

export const schemaFieldsDefaultsConfig: SchemaField = {
  listProps: {
    visible: true,
  },
  inputWidgetProps: {
    showWordLimit: true,
    allowClear: true,
    disabled: false,
    allowUpdate: true,
  },
  unique: false,
  ellipsis: true,
};

export const schemaDefaultsConfig: Schema = {};

export const schemaDetailViewPropsDefaultsConfig: DetailViewProps = {
  title: '查看详情',
  type: 'modal',
  clickOutsideToClose: true,
};

export const schemaListViewPropsDefaultsConfig: ListViewProps = {};

export const schemaFormViewPropsDefaultsConfig: FormViewProps = {};
