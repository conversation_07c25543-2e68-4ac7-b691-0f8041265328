import { CustomSchema } from '../types';

const customSchemaMap = {} as Record<string, CustomSchema>;

// @ts-ignore
const moduleSchemas = import.meta.glob('/src/modules/*/schema/*.ts', {
  // const moduleSchemas = import.meta.glob('../../../../apps/*/src/modules/*/schema/*.ts', {
  // const moduleSchemas = import.meta.glob('@/modules/*/schema/*.ts', {
  import: 'default',
  eager: true,
});

// console.log('module schemas', moduleSchemas);
// const customSchemas = {};

// @ts-ignore
const commonCustomSchemas = import.meta.glob('../schema/module/*.ts', {
  import: 'default',
  eager: true,
});

// console.log('common module schemas', commonCustomSchemas);

// @ts-ignore
const repoComponentsSchemas = import.meta.glob('../../../components/schemas/*.ts', {
  import: 'default',
  eager: true,
});

// console.log('repo components schemas', repoComponentsSchemas);

const all: any[] = [
  ...Object.values(commonCustomSchemas),
  ...Object.values(moduleSchemas),
  ...Object.values(repoComponentsSchemas),
];

all.forEach((module: any) => {
  Object.keys(module).forEach((name: string) => {
    const obj = module[name];
    const currentApi = obj.api;
    if (!currentApi) {
      throw new Error('The first types you defined must have `api` property');
    }
    // custom schema
    customSchemaMap[currentApi] = obj as CustomSchema;
    customSchemaMap[currentApi].fieldsMap = customSchemaMap[currentApi].fieldsMap || {};
  });
});

export { customSchemaMap, commonCustomSchemas };
