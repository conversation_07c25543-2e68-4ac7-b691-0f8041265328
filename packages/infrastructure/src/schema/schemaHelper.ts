import { cloneDeep, extend, isNumber, merge } from 'lodash';
// eslint-disable-next-line import/no-cycle
import { useSchemaStore } from '@repo/infrastructure/store';
import { PROJECT_URLS } from '@repo/env-config';
import type {
  FieldsGroup,
  FieldsGroupingDefine,
  FilterProps,
  QuickSearchProps,
  RouteComposition,
  Schema,
  SchemaField,
} from '../types';
import { DataTypeOf, DataTypes } from '../types';
import {
  schemaDefaultsConfig,
  schemaDetailViewPropsDefaultsConfig,
  schemaFieldsDefaultsConfig,
  schemaFormViewPropsDefaultsConfig,
  schemaListViewPropsDefaultsConfig,
} from './defaults';
import { convertDs2Schema } from './dataStructureAdapter';
import { customSchemaMap } from './customSchema';
import { DsConvertOptions } from '../types/schema';

class SchemaHelper {
  static getInstanceByApi(api: string): Schema {
    const schemaStore = useSchemaStore();
    const schema = schemaStore.getSchemaByApi(api);
    if (!schema) {
      throw new Error(`Schema not found for api: ${api}`);
    }
    if (!schema.baseURL) {
      schema.baseURL = PROJECT_URLS.GO_PROJECT_API;
    }
    return SchemaHelper.getInstance(schema);
  }

  static async getInstanceByDs(api: string, options?: DsConvertOptions): Promise<Schema> {
    const schema: any = await convertDs2Schema(api);
    schema.baseURL = PROJECT_URLS.MAIN_PROJECT_API;
    return SchemaHelper.getInstance(schema as Schema, options);
  }

  static getInstance(schema: Schema, options?: DsConvertOptions): Schema {
    const fieldsMap: Record<string, SchemaField> = {};
    schema.detailViewProps = merge(cloneDeep(schemaDetailViewPropsDefaultsConfig), schema.detailViewProps || {});
    schema.listViewProps = merge(cloneDeep(schemaListViewPropsDefaultsConfig), schema.listViewProps || {});
    schema.formViewProps = merge(cloneDeep(schemaFormViewPropsDefaultsConfig), schema.formViewProps || {});
    const quickSearchProps: QuickSearchProps = schema.quickSearchProps || ({} as QuickSearchProps);
    quickSearchProps.fields = quickSearchProps.fields || [];
    if (schema.interfaces?.includes('HasName')) {
      quickSearchProps.fields.push('name');
    }
    if (schema.interfaces?.includes('HasSymbol')) {
      quickSearchProps.fields.push('symbol');
    }
    // unique quickSearchProps.fields
    quickSearchProps.fields = Array.from(new Set(quickSearchProps.fields));

    schema.quickSearchProps = quickSearchProps;
    const customSchema = customSchemaMap[schema.api];
    schema.schemaFields.forEach((field: SchemaField, index: any) => {
      const filterProps: FilterProps = SchemaHelper.getDefaultFilterProps(field) || {};

      if (
        customSchema &&
        customSchema.fieldsMap &&
        customSchema.fieldsMap[field.key!]?.filterProps?.supportOperators?.length
      ) {
        filterProps.supportOperators = customSchema.fieldsMap[field.key!].filterProps?.supportOperators;
      }
      field = merge(
        cloneDeep(schemaFieldsDefaultsConfig),
        {
          filterProps,
        },
        field,
      );
      if (!field || !field.key) {
        return;
      }
      if (!field.valueType && field.dataType) {
        field.valueType = DataTypes[field.dataType]?.name || 'String';
      }
      if (!field.inputWidget) {
        field.inputWidget = DataTypeOf(field.valueType!)?.defaultInputWidget || DataTypes.String.defaultInputWidget;
      }
      field.dataIndex = field.key;

      field.listProps = field.listProps || {};
      field.listProps.ellipsis = field.listProps.ellipsis !== false;
      // field.listProps.tooltip = field.listProps.tooltip !== false;

      fieldsMap[field.key!] = field;
      schema.schemaFields[index] = field;
    });
    schema.fieldsMap = fieldsMap;

    if (options?.fieldsOverride) {
      Object.keys(options.fieldsOverride).forEach((key) => {
        const idx = schema.schemaFields.findIndex((f) => f.key === key);
        if (idx > -1) {
          if (options.fieldsOverride) {
            schema.schemaFields[idx] = extend(schema.schemaFields[idx], options.fieldsOverride[key]);
          }
          schema.fieldsMap[key] = schema.schemaFields[idx];
        } else if (options.fieldsOverride) {
          schema.schemaFields.push(options.fieldsOverride[key]);
          schema.fieldsMap[key] = options.fieldsOverride[key];
        }
      });
    }

    schema = merge(cloneDeep(schemaDefaultsConfig), schema);

    // console.log(schema);
    const schemaStore = useSchemaStore();
    return schemaStore.mergeCustomSchema(schema, options);
  }

  static getDefaultFilterProps(field: SchemaField): FilterProps {
    const filterProps: FilterProps = {};
    filterProps.inputWidgetProps = {};
    const dataType = DataTypeOf(field.valueType!);
    switch (dataType) {
      case DataTypes.Date:
        filterProps.supportOperators = ['Between', 'NotBetween'];
        filterProps.defaultOperator = 'Between';
        filterProps.inputWidgetProps.showTime = true;
        filterProps.inputWidgetProps.format = 'YYYY-MM-DD HH:mm:ss';
        break;
      case DataTypes.String:
        filterProps.supportOperators = ['Like', 'NotLike', 'Equal', 'NotEqual', 'LikeIn'];
        filterProps.defaultOperator = 'Like';
        break;
      case DataTypes.Number:
      case DataTypes.Long:
      case DataTypes.Integer:
      case DataTypes.Double:
      case DataTypes.Float:
      case DataTypes.BigDecimal:
        filterProps.supportOperators = [
          'Equal',
          'NotEqual',
          'Between',
          'NotBetween',
          'GreaterThan',
          'GreaterThanOrEqual',
          'LessThan',
          'LessThanOrEqual',
        ];
        filterProps.defaultOperator = 'Equal';
        break;
      case DataTypes.Foreign:
        filterProps.supportOperators = ['In', 'NotIn'];
        filterProps.defaultOperator = 'In';
        break;
      default:
        break;
    }

    return filterProps;
  }

  static parseRoutePath(path: string): RouteComposition {
    // parse url path like `/moduleName/funcName` or `/moduleName/funcName/1` or `/moduleName/funcName/edit/1` to RouteComposition
    const pathArray = path.split('/');
    if (pathArray.length < 3) {
      throw new Error('Invalid route path');
    }
    const [module, func, ...others] = pathArray.slice(1);
    const routeComposition: RouteComposition = {
      module,
      func,
    };
    if (others.length > 1) {
      // /edit/id
      routeComposition.action = others[0] as string;
      routeComposition.id = others[1] as any;
    } else if (others.length > 0) {
      // /id
      if (isNumber(others[0])) {
        routeComposition.id = Number(others[0]);
      } else {
        routeComposition.action = others[0] as string;
      }
    }

    return routeComposition;
  }

  static getBreadcrumbItems(schema: Schema, routeComposition: RouteComposition): Array<{ label: string; to?: string }> {
    if (!schema) {
      return [];
    }
    const breadcrumbItems: Array<{ label: string; to?: string }> = [
      {
        label: `module.name.${schema!.moduleName!}`,
        to: `/${schema.moduleName}`,
      },
      {
        label: schema!.label!,
        to: `/${schema.moduleName}/${schema.functionName}`,
      },
    ];
    if (routeComposition.action) {
      breadcrumbItems.push({
        label: `common.${routeComposition.action}`,
      });
    }
    return breadcrumbItems;
  }

  static toDeepValue(record: Record<string, any>, key: string) {
    const keys = key.split('.');
    if (!record) {
      return null;
    }
    let value = record;
    for (let i = 0; i < keys.length; i += 1) {
      value = value[keys[i]];
      if (value === undefined || value === null) {
        break;
      }
    }
    return value;
  }

  static getFieldsGrouping(
    schemaModel: Schema,
    fieldsGrouping: FieldsGroupingDefine[],
    exclude?: (field: SchemaField) => boolean,
  ): FieldsGroup[] {
    const groups: FieldsGroup[] = [];
    const defaultExclude = ['id', 'createdAt', 'deletedAt', 'branchOfficeId'];
    if (fieldsGrouping?.length) {
      const exists: string[] = [];
      fieldsGrouping?.forEach((g) => {
        const group: FieldsGroup = {
          label: g.label as string,
          fields: [],
          columns: g.columns || schemaModel.detailViewProps?.columns || 2,
          colSpan: g.colSpan || schemaModel.formViewProps?.colSpan || 6,
        };
        g.fields.forEach((f) => {
          const field = schemaModel.schemaFields.find((sf: any) => sf.key === f);
          exists.push(f);
          if (
            field &&
            ((typeof exclude === 'function' && !exclude(field)) || !exclude) &&
            defaultExclude.indexOf(f) === -1
          ) {
            group.fields.push(field as SchemaField);
          }
        });
        groups.push(group);
      });
      const otherFields: SchemaField[] = [];
      schemaModel.schemaFields.forEach((f: any) => {
        if (
          !exists.includes(f.key!) &&
          f &&
          ((typeof exclude === 'function' && !exclude(f)) || !exclude) &&
          defaultExclude.indexOf(f.key) === -1
        ) {
          otherFields.push(f as SchemaField);
        }
      });
      groups.push({
        label: 'common.others',
        fields: otherFields,
        columns: schemaModel.detailViewProps?.columns || 2,
        colSpan: schemaModel.formViewProps?.colSpan || 6,
      });
    } else {
      groups.push({
        label: '__DEFAULT__',
        fields: schemaModel.schemaFields.filter((f: any) => {
          return ((typeof exclude === 'function' && !exclude(f)) || !exclude) && defaultExclude.indexOf(f.key) === -1;
        }) as SchemaField[],
        columns: schemaModel.detailViewProps?.columns || 2,
        colSpan: schemaModel.formViewProps?.colSpan || 24,
      });
    }

    return groups;
  }

  static setDeepValue(rawRecord: Record<string, any> | undefined, field: string, val: any) {
    let record = rawRecord;
    if (!record) {
      record = {};
    }
    const keys = field.split('.');
    let obj = record;
    for (let i = 0; i < keys.length - 1; i += 1) {
      if (!obj[keys[i]]) {
        obj[keys[i]] = {};
      }
      obj = obj[keys[i]];
    }
    obj[keys[keys.length - 1]] = val;
    return record;
  }
}

export default SchemaHelper;
