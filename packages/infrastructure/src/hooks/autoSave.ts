import { computed, h, ref } from 'vue';
import dayjs from 'dayjs';

type useAutoSaveConfig = {
  interval?: number;
  handler: () => void;
};

const useAutoSave = (config: useAutoSaveConfig) => {
  config.interval = config.interval || 30000;
  const timer = ref(null);
  const lastSaveTime = ref(Date.now());
  const isSaving = ref(false);
  const lastSaveSuccess = ref(false);
  const started = ref(false);

  const save = async () => {
    if (isSaving.value) {
      return;
    }
    isSaving.value = true;
    try {
      await config.handler();
      lastSaveTime.value = Date.now();
      lastSaveSuccess.value = true;
    } catch (e) {
      lastSaveSuccess.value = false;
      console.error(e);
    } finally {
      isSaving.value = false;
    }
  };

  const start = () => {
    started.value = true;
    timer.value = setTimeout(async () => {
      await save();
      start();
    }, config.interval);
  };

  const stop = () => {
    clearTimeout(timer.value);
    started.value = false;
  };

  const saveMessage = computed(() => {
    if (!started.value) {
      return '自动保存尚未开启';
    }
    if (isSaving.value) {
      return '正在保存...';
    }
    if (lastSaveSuccess.value) {
      return `已于 ${dayjs(lastSaveTime.value).format('HH:mm:ss')} 自动保存`;
    }
    if (started.value) {
      return `自动保存已启用，将于 ${dayjs(Date.now() + config.interval).format('HH:mm:ss')} 自动保存`;
    }
    if (!lastSaveSuccess.value) {
      return `于${dayjs(lastSaveTime.value).format('HH:mm:ss')}保存失败，请尝试手动保存`;
    }
  });

  const saveStatusComponent = h('a-space', {
    children: [
      h('span', {
        style: {
          color: isSaving.value ? 'orange' : lastSaveSuccess.value ? 'green' : 'red',
        },
        children: saveMessage.value,
      }),
    ],
  });

  return {
    lastSaveTime,
    isSaving,
    lastSaveSuccess,
    save,
    start,
    stop,
    saveMessage,
    saveStatusComponent,
  };
};

export default useAutoSave;
