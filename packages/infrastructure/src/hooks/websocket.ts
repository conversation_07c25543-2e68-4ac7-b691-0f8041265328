import { PROJECT_URLS } from '@repo/env-config';
import { getToken } from '@repo/infrastructure/auth';
import { ref } from 'vue';
import { useUserStore } from '../store';

const useWebSocket = () => {
  // const onlineUsersMap = ref<any>({}); // userId : boolean
  const ws = ref<WebSocket | null>(null);
  const timer = ref<any>(null);

  const userStore = useUserStore();

  const heartBeat = () => {
    ws.value?.send('ping');
  };

  const connect = () => {
    const token = getToken();
    const wsUrl = `${PROJECT_URLS.WS_URL}?token=${token}&userId=${userStore.userInfo.id}`;
    ws.value = new WebSocket(wsUrl);
  };

  const init = async () => {
    return new Promise((resolve, reject) => {
      if (ws.value?.readyState !== WebSocket.OPEN) {
        connect();

        if (!ws.value) {
          reject(new Error('WebSocket init failed'));
        }

        ws.value?.addEventListener('open', () => {
          // heart beat
          timer.value = setInterval(() => {
            heartBeat();
          }, 1000 * 30);

          resolve(true);
        });

        ws.value?.addEventListener('close', () => {
          clearInterval(timer.value);
          setTimeout(() => {
            connect();
          }, 5000);
        });
      }

      resolve(true);
    });
  };

  const onConversationMessage = (callback: (msg: API.ImMessage) => void) => {
    ws.value?.addEventListener('message', (event) => {
      if (event.data === 'pong') {
        return;
      }
      const msg = JSON.parse(event.data) as API.ImMessage;
      if (msg.type === 'conversation') {
        callback(msg);
      }
    });
  };

  const onUserOnline = (callback: (sessionId: string) => void) => {
    ws.value?.addEventListener('message', (event) => {
      if (event.data === 'pong') {
        return;
      }
      const msg = JSON.parse(event.data) as API.ImMessage;
      if (msg.type === 'userOnline' && msg.data) {
        callback(msg.data);
      }
    });
  };

  const onUserOffline = (callback: (sessionId: string) => void) => {
    ws.value?.addEventListener('message', (event) => {
      if (event.data === 'pong') {
        return;
      }
      const msg = JSON.parse(event.data) as API.ImMessage;
      if (msg.type === 'userOffline' && msg.data) {
        callback(msg.data);
      }
    });
  };
  return {
    init,
    onConversationMessage,
    onUserOnline,
    onUserOffline,
  };
};

export default useWebSocket;
