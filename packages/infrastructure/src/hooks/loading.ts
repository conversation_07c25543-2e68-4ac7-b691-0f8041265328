// @ts-ignore
import { ref } from 'vue';

export default function useLoading(initValue = false): {
  loading: import('vue').Ref<boolean>;
  setLoading: (value: boolean) => void;
  toggle: () => void;
} {
  const loading = ref<boolean>(initValue);
  const setLoading = (value: boolean) => {
    loading.value = value;
  };
  const toggle = () => {
    loading.value = !loading.value;
  };
  return {
    loading,
    setLoading,
    toggle,
  };
}
