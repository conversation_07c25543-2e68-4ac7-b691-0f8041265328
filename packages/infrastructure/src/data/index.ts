import { teacherPermNodes, adminPermNodes } from './permissions';
import * as menuService from './menu';
import * as schoolTypes from './schoolTypes';
import platformModules from './platformModules';
import { cityData } from './cityData';

export const nationsList =
  '汉族|蒙古族|回族|藏族|苗族|维吾尔族|彝族|壮族|布依族|白族|朝鲜族|侗族|哈尼族|哈萨克族|满族|土家族|瑶族|达斡尔族|东乡族|高山族|景颇族|柯尔克孜族|拉祜族|纳西族|畲族|傣族|黎族|傈僳族|仫佬族|羌族|水族|土族|佤族|阿昌族|布朗族|毛南族|普米族|撒拉族|塔吉克族|锡伯族|仡佬族|保安族|德昂族|俄罗斯族|鄂温克族|京族|怒族|乌孜别克族|裕固族|独龙族|鄂伦春族|赫哲族|基诺族|珞巴族|门巴族|塔塔尔族'.split(
    '|',
  );

export { teacherPermNodes, adminPermNodes, menuService, platformModules, schoolTypes, cityData };
