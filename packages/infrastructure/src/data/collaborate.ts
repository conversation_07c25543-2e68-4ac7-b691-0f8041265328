export type CollaborateModule = 'Assessment';

export type CollaborateAction = 'View' | 'Edit' | 'Owner';

export const CollaborateModule: Record<CollaborateModule, string> = {
  Assessment: '评估',
};

export const collaborateActions: Record<CollaborateAction, string> = {
  View: '仅查看',
  Edit: '可编辑',
  Owner: '所有者',
};

export const collaborateActionOptions = Object.keys(collaborateActions).map((key) => ({
  label: collaborateActions[key as CollaborateAction],
  value: key,
}));
