/* eslint-disable no-use-before-define */
/* eslint-disable no-shadow */
declare namespace API {
  type Attachment = {
    name?: string;
    url?: string;
  };

  type BatchAuthorizeDigitalResourceCourseRo = {
    courseIds: number[];
    expiredAt?: string;
    isCopy?: boolean;
    toOrgId: number;
  };

  type BatchCreateDigitalResourceRo = {
    chapterId?: number;
    courseId?: number;
    resources: DigitalResourceRo[];
    source: ChapterPresentationItemSource;
  };

  type BatchRejectParamsParams = {
    /** ID列表，逗号分隔 */
    ids: string;
  };

  type BranchOfficeVo = {
    allDescendantIds?: number[];
    allDescendantIdsWithSelf?: number[];
    company?: CompanyVo;
    id?: number;
    name?: string;
    orgNature?: string;
  };

  enum CatalogType {
    0 = '0',
    1 = '1',
    2 = '2',
  }

  type Chapter = {
    bo_id?: number;
    chapterStudents?: IdNameVo[];
    children?: Chapter[];
    classTime?: FullTime;
    content?: ChapterContentVo;
    courseId?: number;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    description?: string;
    id?: number;
    lesson?: number[][];
    /** 推送至教案库后ID */
    libraryId?: number;
    name?: string;
    number?: string;
    org_id?: number;
    parent?: Chapter;
    parentId?: number;
    parents?: Chapter[];
    /** 来源 */
    referenceSource?: ChapterLibrary;
    /** ChapterLibrary ID 从外部引用 */
    referenceSourceId?: number;
    sort?: number;
    /** 是否提交至考核 */
    submitted?: boolean;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type ChapterAssessResult = {
    bo_id?: number;
    chapterId?: number;
    createdAt?: string;
    gradeClassId?: number;
    id?: number;
    org_id?: number;
    scores?: TeachingAssessCriteriaVo[];
    student?: IdNameVo;
    studentId?: number;
    totalScore?: number;
    /** 前测后测 */
    type?: ChapterAssessType;
    updatedAt?: string;
  };

  enum ChapterAssessType {
    pre = 'pre',
    after = 'after',
  }

  type ChapterContent = {
    bo_id?: number;
    chapterId?: number;
    classBehaviorRecord?: Record<string, any>;
    createdAt?: string;
    id?: number;
    /** this is the old 备课安排 renamed to 教学设计, 备课安排 is now LessonResourcePrepare */
    lessonPrepare?: IdNameVo[];
    lessonPrepareAttachments?: Attachment[];
    lessonResourcePrepare?: ChapterPresentationItem[];
    org_id?: number;
    teachingAssessCriteria?: TeachingAssessCriteriaVo[];
    teachingPrepare?: IdNameVo[];
    teachingReflection?: IdNameVo[];
    updatedAt?: string;
  };

  type ChapterContentVo = {
    chapterId?: number;
    classBehaviorRecord?: Record<string, any>;
    id?: number;
    lessonPrepare?: IdNameVo[];
    lessonPrepareAttachments?: Attachment[];
    /** this is the old 备课安排 renamed to 教学设计, 备课安排 is now LessonResourcePrepare */
    lessonResourcePrepare?: ChapterPresentationItem[];
    presentationItems?: ChapterPresentationItemVo[];
    teachingAssessCriteria?: TeachingAssessCriteriaVo[];
    teachingDemo?: Attachment[];
    teachingDesign?: string;
    teachingPrepare?: IdNameVo[];
    teachingPresentation?: string;
    teachingReflection?: IdNameVo[];
  };

  type ChapterDuplicateRo = {
    chapterId?: number;
    courseId?: number;
    includeSub?: boolean;
    name?: string;
  };

  type ChapterLibrary = {
    bo_id?: number;
    category?: number;
    content?: ChapterLibraryContent;
    coverImage?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    description?: string;
    grade?: string;
    id?: number;
    name?: string;
    orgNature?: string;
    org_id?: number;
    /** 来源章节id */
    originId?: number;
    period?: string;
    referenceCount?: number;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type ChapterLibraryContent = {
    attachment?: Attachment[];
    bo_id?: number;
    chapterLibId?: number;
    createdAt?: string;
    id?: number;
    lessonPrepare?: IdNameVo[];
    org_id?: number;
    teachingAssessCriteria?: TeachingAssessCriteriaVo[];
    teachingPrepare?: IdNameVo[];
    teachingReflection?: IdNameVo[];
    updatedAt?: string;
  };

  type ChapterPresentationItem = {
    name?: string;
    resourceId?: number;
    slideId?: number;
    type?: CourseResourceType;
  };

  enum ChapterPresentationItemSource {
    Lib = 'Lib',
    User = 'User',
  }

  type ChapterPresentationItemVo = {
    description?: string;
    name?: string;
    resourceId?: number;
    slideId?: number;
    source?: ChapterPresentationItemSource;
    thumb?: string;
    type?: CourseResourceType;
  };

  type ChapterSubmitParamsParams = {
    /** 章节ID */
    id: number;
  };

  type CompanyVo = {
    authorizedModule?: string[];
    id?: number;
    name?: string;
    visitDomain?: string;
  };

  type Conversation = {
    createdAt?: string;
    fromUser?: ConversationUser;
    fromUserId?: string;
    id?: number;
    lastMessage?: string;
    lastMessageRead?: boolean;
    lastTime?: string;
    lastUser?: string;
    latestMessage?: ConversationMessage;
    other?: ConversationUser;
    toUser?: ConversationUser;
    toUserId?: string;
    updatedAt?: string;
  };

  type ConversationMessage = {
    conversationId?: number;
    createdAt?: string;
    date?: string;
    id?: number;
    messages?: MessageItem[];
    updatedAt?: string;
  };

  enum ConversationMessageType {
    text = 'text',
    image = 'image',
    voice = 'voice',
    video = 'video',
  }

  type ConversationUser = {
    avatar?: string;
    bo_id?: number;
    description?: string;
    name?: string;
    online?: boolean;
    org_id?: number;
    phone?: string;
    sessionId?: string;
    userId?: number;
    userType?: ConversationUserType;
  };

  enum ConversationUserType {
    guardian = 'guardian',
    company = 'company',
    system = 'system',
  }

  type Course = {
    bo_id?: number;
    category?: number;
    /** Participants base.IdList    `json:"participants" gorm:"type:json" label:"参与者"` */
    coverImage?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    /** Name        string         `json:"name" gorm:"size:100;not null" filterable:"true" label:"课程名称"` */
    description?: string;
    grade?: string;
    gradePeriod?: string;
    id?: number;
    orgNature?: string;
    org_id?: number;
    period?: string;
    teachingAssessScores?: IdNameVo[];
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  enum CourseResourceType {
    video = 'video',
    audio = 'audio',
    image = 'image',
    game = 'game',
  }

  type CreateQuestionCatalogRo = {
    name?: string;
    orgNature?: string;
    parentId?: number;
  };

  type CreateQuestionnaireFolderRo = {
    catalogType?: CatalogType;
    name?: string;
    orgNature?: string;
    parentId?: number;
  };

  type CreateRo = {
    attachment?: Attachment[];
    chapter?: IdNameVo;
  };

  type CreateTeacherQuestionCatalogRo = {
    catalogId?: number;
    courseId?: number;
    name?: string;
    orgNature?: string;
    parentId?: number;
  };

  type DeleteCatalogParamsParams = {
    /** ID */
    id: number;
  };

  type DeleteChapterAssessmentParamsParams = {
    /** 评价ID */
    id: string;
  };

  type DeleteChapterLibraryParamsParams = {
    /** 教案库ID */
    id: number;
  };

  type DeleteChapterParamsParams = {
    /** 章节ID */
    id: string;
  };

  type DeletedAt = {
    time?: string;
    /** Valid is true if Time is not NULL */
    valid?: boolean;
  };

  type DeleteDigitalChapterParamsParams = {
    /** 章节ID */
    id: string;
  };

  type DeleteQuestionnaireFolderParamsParams = {
    /** ID */
    id: number;
  };

  type DeleteQuestionnaireParamsParams = {
    /** ID */
    id: number;
  };

  type DeleteQuestionParamsParams = {
    /** ID */
    id: number;
  };

  type DeleteRehabChapterAssessmentParamsParams = {
    /** 评价ID */
    id: string;
  };

  type DeleteRehabChapterLibraryParamsParams = {
    /** 教案库ID */
    id: number;
  };

  type DeleteRehabChapterParamsParams = {
    /** 章节ID */
    id: string;
  };

  type DeleteTeacherCatalogParamsParams = {
    /** ID */
    id: number;
  };

  type DeleteTeacherQuestionParamsParams = {
    /** ID */
    id: number;
  };

  type DigitalResource = {
    bo_id?: number;
    chapterId?: number;
    courseId?: number;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    description?: string;
    id?: number;
    mimeType?: string;
    name?: string;
    org_id?: number;
    source?: ChapterPresentationItemSource;
    thumb?: string;
    type?: CourseResourceType;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type DigitalResourceAuthorize = {
    bo_id?: number;
    courseCopyId?: number;
    createdAt?: string;
    expiredAt?: string;
    id?: number;
    isCopy?: boolean;
    org_id?: number;
    resourceCourse?: DigitalResourceCourse;
    resourceCourseId?: number;
    toOrgId?: number;
    updatedAt?: string;
    /** yyyy-MM-dd */
    version?: string;
  };

  type DigitalResourceChapter = {
    bo_id?: number;
    children?: DigitalResourceChapter[];
    courseId?: number;
    createdAt?: string;
    created_by_id?: number;
    deletedAt?: DeletedAt;
    depth?: number;
    depthIndex?: number;
    description?: string;
    id?: number;
    lft?: number;
    name?: string;
    number?: string;
    org_id?: number;
    parent?: DigitalResourceChapter;
    parentId?: number;
    rgt?: number;
    sort?: number;
    updatedAt?: string;
  };

  type DigitalResourceCourse = {
    authorizedOrgId?: number[];
    bo_id?: number;
    /** Name        string         `json:"name" gorm:"size:100;not null" filterable:"true" label:"课程名称"`
         Description string         `json:"description" label:"课程描述"` */
    category?: string;
    coverImage?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    fromOrgId?: number;
    grade?: string;
    id?: number;
    org_id?: number;
    period?: string;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type DigitalResourceRo = {
    chapterId?: number;
    courseId?: number;
    description?: string;
    name?: string;
    thumb?: string;
    type?: CourseResourceType;
    url?: string;
  };

  type DirectlyUploadUrl = {
    get?: string;
    put?: string;
  };

  type ForeignField = {
    api?: string;
    apiBaseUrl?: string;
    foreignIdField?: string;
    labelField?: string;
    preload?: boolean;
    queryParams?: Record<string, any>;
    relationType?: string;
    foreignOptionsHandler?: (rawOptions: any) => any;
  };

  type FullTime = {
    'time.Time'?: string;
  };

  type GenericGetSessionParamsParams = {
    guardianToken?: string;
    hash?: string;
    id?: number;
    loginSource?: 'PC' | 'WeApp' | 'GuardianWeapp';
    roleType?: string;
    token?: string;
    username?: string;
  };

  type GetByVerifyQuestionListParamsParams = {
    /** 目录ID */
    isVerify: number;
  };

  type GetCatalogsParamsParams = {
    /** 父ID */
    parentId?: number;
  };

  type GetChapterAssessmentListParamsParams = {
    /** 章节ID */
    chapterId: string;
    /** 评价类型 */
    type: string;
    /** 班级ID */
    gradeClassId?: number;
    /** 学生ID */
    studentId?: number;
  };

  type GetChapterLibraryDetailParamsParams = {
    /** 教案库ID */
    id: number;
  };

  type GetChapterLibraryListParamsParams = {
    /** 课程名称 */
    name?: string;
    /** 课程科目 */
    category?: string;
    /** 年级 */
    grade?: string;
    /** 上下册 */
    period?: string;
    /** 机构性质 */
    orgNature: string;
    /** 创建者ID */
    createdById?: number;
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
  };

  type GetChapterListCbIdInParamsParams = {
    _ot?: number;
    cbIds?: number[];
    classDate?: string[];
    courseId?: number;
    /** 小周 */
    createdAt?: number;
    gradePeriod?: string;
    page: number;
    pageSize: number;
    period?: string;
    submitted?: boolean;
    teacherId?: number;
    withParents?: boolean;
  };

  type GetChapterListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    classDate?: string[];
    courseId?: number;
    /** 小周 */
    createdAt?: number;
    gradePeriod?: string;
    page: number;
    pageSize: number;
    period?: string;
    submitted?: boolean;
    teacherId?: number;
    withParents?: boolean;
  };

  type GetChapterParamsParams = {
    /** 章节ID */
    id: number;
  };

  type GetChapterTreeParamsParams = {
    /** 课程ID */
    courseId: string;
  };

  type getConversationListParamsParams = {
    /** Page */
    page?: number;
    /** Page Size */
    pageSize?: number;
  };

  type getConversationMessageListParamsParams = {
    /** Conversation ID */
    conversationId?: number;
    /** Page */
    page?: number;
    /** Page Size */
    pageSize?: number;
  };

  type GetCourseListByCbIdParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type GetCourseListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type getDigitalChapterTreeParamsParams = {
    /** 课程ID */
    courseId: string;
  };

  type getDigitalResourceAuthorizedCourseParamsParams = {
    /** 单位ID */
    orgId: number;
  };

  type getDigitalResourceAuthorizedOrgParamsParams = {
    /** 课程ID */
    courseId?: string;
  };

  type GetDigitalResourceListParamsParams = {
    /** 课程id */
    courseId: string;
    /** 章节id */
    chapterId: string;
    /** 资源类型 */
    type?: string;
    /** 资源名称 */
    name?: string;
    /** 每页数量 */
    pageSize?: number;
    /** 页码 */
    pageNumber?: number;
  };

  type getDirectlyUploadUrlParamsParams = {
    /** objectName */
    objectName: string;
    /** contentType */
    contentType: string;
  };

  type getGameResourceHtmlParamsParams = {
    /** 课程id */
    id: number;
  };

  type GetMediaResource302ParamsParams = {
    /** 资源id */
    id: string;
  };

  type GetMyCourseListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type GetMyFormAnswerParamsParams = {
    /** 问卷表单ID */
    formId: number;
  };

  type GetMyRehabCourseListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type getMyVisitReservationListParamsParams = {
    /** Page */
    page?: number;
    /** PageSize */
    pageSize?: number;
  };

  type getOnlineTeachersParamsParams = {
    /** 范围 0 offline, 1 online, 2 global */
    scope?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 页码 */
    pageNumber?: number;
  };

  type GetPublicQuestionnaireFormParamsParams = {
    /** uuid */
    uuid: string;
  };

  type GetQuestionListParamsParams = {
    /** 目录ID */
    catalogId: number;
  };

  type GetQuestionnaireFoldersParamsParams = {
    /** 父ID */
    parentId?: number;
  };

  type GetQuestionnaireListParamsParams = {
    catalogId?: number;
    ids?: number[];
    orgNature?: string;
  };

  type GetQuestionnaireParamsParams = {
    /** ID */
    id: number;
  };

  type GetQuestionnaireRecordTopListParamsParams = {
    /** 问卷表单ID */
    formId: number;
    /** 前几名 */
    top?: number;
  };

  type GetQuestionParamsParams = {
    /** ID */
    id: number;
  };

  type GetRehabChapterAssessmentListParamsParams = {
    /** 章节ID */
    chapterId: string;
    /** 评价类型 */
    type: string;
    /** 班级ID */
    gradeClassId?: number;
    /** 学生ID */
    studentId?: number;
  };

  type GetRehabChapterLibraryListParamsParams = {
    /** 课程名称 */
    name?: string;
    /** 课程科目 */
    category?: string;
    /** 年级 */
    grade?: string;
    /** 上下册 */
    period?: string;
    /** 机构性质 */
    orgNature: string;
    /** 创建者ID */
    createdById?: number;
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
  };

  type GetRehabChapterListCbIdInParamsParams = {
    _ot?: number;
    cbIds?: number[];
    classDate?: string[];
    courseId?: number;
    /** 小周 */
    createdAt?: number;
    gradePeriod?: string;
    page: number;
    pageSize: number;
    period?: string;
    submitted?: boolean;
    teacherId?: number;
    withParents?: boolean;
  };

  type GetRehabChapterListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    classDate?: string[];
    courseId?: number;
    /** 小周 */
    createdAt?: number;
    gradePeriod?: string;
    page: number;
    pageSize: number;
    period?: string;
    submitted?: boolean;
    teacherId?: number;
    withParents?: boolean;
  };

  type GetRehabChapterParamsParams = {
    /** 章节ID */
    id: number;
  };

  type GetRehabChapterTreeParamsParams = {
    /** 课程ID */
    courseId: string;
  };

  type GetRehabCourseListByCbIdParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type GetRehabCourseListParamsParams = {
    _ot?: number;
    cbIds?: number[];
    grade?: string;
    keyword?: string;
    name?: string;
    page: number;
    pageSize: number;
  };

  type getStudentTransferListParamsParams = {
    /** status */
    status?: string;
    /** Page */
    page?: number;
    /** PageSize */
    pageSize?: number;
  };

  type GetTeacherCatalogsParamsParams = {
    /** 父ID */
    parentId?: number;
  };

  type GetTeacherQuestionListParamsParams = {
    /** 目录ID */
    catalogId: number;
    /** 题目ID列表 */
    ids?: string;
  };

  type GetTeacherQuestionParamsParams = {
    /** ID */
    id: number;
  };

  type getTeachingSupervisionListParamsParams = {
    _ot?: number;
    branchOfficeId?: number;
    classDate?: string[];
    keyword?: string;
    onlyFinished?: boolean;
    orgNature?: string;
    page: number;
    pageSize: number;
    period?: string;
    teacherName?: string;
  };

  type Guardian = {
    avatar?: string;
    createdAt?: string;
    /** udf1 => companyId udf2=>boId udf3=>boName */
    currentStudent?: StudentVo;
    id?: number;
    mobile?: string;
    name?: string;
    studentList?: StudentVo[];
    updatedAt?: string;
  };

  type GuardianAnnouncement = {
    bo_id?: number;
    content?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    id?: number;
    indexBanner?: boolean;
    org_id?: number;
    title?: string;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
    visibleIn?: string[];
  };

  type guardianGetConversationParamsParams = {
    /** Conversation ID */
    id?: number;
    /** Teacher ID */
    teacherId?: string;
  };

  type GuardianLoginResultVo = {
    expireAt?: number;
    guardianInfo?: Guardian;
    token?: string;
  };

  type guardianLoginViaWeappParamsParams = {
    /** 微信登录code */
    code: string;
  };

  type GuardianNews = {
    bo_id?: number;
    category?: GuardianNewsCategory;
    categoryId?: number;
    content?: string;
    coverImage?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    id?: number;
    org_id?: number;
    title?: string;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
    visibleIn?: string[];
  };

  type GuardianNewsCategory = {
    bo_id?: number;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    id?: number;
    name?: string;
    org_id?: number;
    sort?: number;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type guardianNewsPushToCarouselParamsParams = {
    /** id */
    id: number;
  };

  type guardianSwitchStudentParamsParams = {
    /** 学生ID */
    studentId: number;
  };

  enum GuardianVisitReservationStatus {
    pending = 'pending',
    approved = 'approved',
    refused = 'refused',
  }

  type GuardianWeappCarousel = {
    bo_id?: number;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    id?: number;
    image?: string;
    link?: string;
    org_id?: number;
    sort?: number;
    title?: string;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
    visibleIn?: string[];
  };

  type guardianWeappInitParamsParams = {
    /** 可见范围 */
    visibleIn?: string;
  };

  type GuardianWeappInitVo = {
    announcementList?: GuardianAnnouncement[];
    carouselList?: GuardianWeappCarousel[];
    newsList?: GuardianNews[];
  };

  type IdNameVo = {
    id?: number;
    name?: string;
    numUdf1?: number;
    numUdf2?: number;
    numUdf3?: number;
    udf1?: string;
    udf2?: string;
    udf3?: string;
  };

  type ImMessage = {
    conversationId?: number;
    conversationMessageType?: ConversationMessageType;
    data?: string;
    link?: string;
    to?: string;
    type?: WebsocketMessageType;
  };

  enum LoginSource {
    PC = 'PC',
    WeApp = 'WeApp',
    GuardianWeapp = 'GuardianWeapp',
  }

  type MessageItem = {
    content?: string;
    sendTime?: string;
    type?: ConversationMessageType;
    userId?: string;
  };

  type MoveNodeRo = {
    fromId?: number;
    /** -1 from 到为 toId 之前, 0 为from到to下一级， 1 为from到 toId 之后 */
    position?: number;
    toId?: number;
  };

  type NotificationCountVo = {
    needValidateUser?: number;
    pendingReservation?: number;
    unreadConversation?: number;
  };

  type orgQueryParamsParams = {
    /** uuid */
    uuid?: string;
    /** ID */
    id?: number;
  };

  type PaginationResult = {
    list?: any;
    page?: number;
    pageSize?: number;
    total?: number;
  };

  type Question = {
    analysisTips?: string;
    bo_id?: number;
    catalogId?: number;
    correctAnswer?: string;
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    options?: QuestionOption[];
    orgNature?: string;
    org_id?: number;
    presetType?: QuestionTypePreset;
    question?: string;
    questionType?: QuestionType;
    score?: number;
    tips?: string;
    typeId?: number;
    updatedAt?: string;
  };

  type QuestionAnswersMap = true;

  type QuestionConfig = {
    required?: boolean;
  };

  type QuestionConfigStruct = true;

  type QuestionLibraryCatalog = {
    bo_id?: number;
    catalogType?: CatalogType;
    children?: QuestionLibraryCatalog[];
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    lft?: number;
    name?: string;
    orgNature?: string;
    org_id?: number;
    parentId?: number;
    rgt?: number;
    updatedAt?: string;
  };

  type Questionnaire = {
    analysisTips?: string;
    bo_id?: number;
    catalogId?: number;
    correctAnswer?: string;
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    options?: QuestionOption[];
    orgNature?: string;
    org_id?: number;
    presetType?: QuestionTypePreset;
    question?: string;
    questionType?: QuestionType;
    score?: number;
    tips?: string;
    typeId?: number;
    updatedAt?: string;
  };

  type QuestionnaireFolder = {
    bo_id?: number;
    catalogType?: CatalogType;
    children?: QuestionnaireFolder[];
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    lft?: number;
    name?: string;
    orgNature?: string;
    org_id?: number;
    parentId?: number;
    rgt?: number;
    updatedAt?: string;
  };

  type QuestionnaireForm = {
    bo_id?: number;
    branchOfficeIds?: number[];
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    description?: string;
    endTime?: FullTime;
    id?: number;
    name?: string;
    org_id?: number;
    publishScope?: QuestionnaireFormScope;
    published?: boolean;
    questionConfig?: QuestionConfigStruct;
    questionIds?: number[];
    questions?: Questionnaire[];
    relatedId?: number;
    scene?: QuestionnaireFormScene;
    startTime?: FullTime;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
    uuid?: string;
  };

  type QuestionnaireFormRecord = {
    answers?: QuestionAnswersMap;
    bo_id?: number;
    captchaVerifyParam?: string;
    certifyId?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    deviceToken?: string;
    fingerPrint?: string;
    formId?: number;
    fullScore?: number;
    id?: number;
    org_id?: number;
    sceneId?: string;
    source?: string;
    submitted?: boolean;
    timeCost?: number;
    totalScore?: number;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  enum QuestionnaireFormScene {
    examination = 'examination',
    investigation = 'investigation',
  }

  enum QuestionnaireFormScope {
    public = 'public',
    protected = 'protected',
    organization = 'organization',
    conference = 'conference',
  }

  type questionnaireRecordRejectParamsParams = {
    /** ID */
    id: number;
  };

  type QuestionOption = {
    content?: string;
    isCorrect?: boolean;
    sequence?: string;
  };

  type QuestionType = {
    bo_id?: number;
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    name?: string;
    org_id?: number;
    presetType?: QuestionTypePreset;
    updatedAt?: string;
  };

  enum QuestionTypePreset {
    singleChoice = 'singleChoice',
    multipleChoice = 'multipleChoice',
    judgement = 'judgement',
    fill = 'fill',
    textInput = 'textInput',
    attachmentInput = 'attachmentInput',
    essay = 'essay',
  }

  type RehabChapter = {
    bo_id?: number;
    children?: RehabChapter[];
    classTime?: FullTime;
    content?: ChapterContentVo;
    courseId?: number;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    description?: string;
    id?: number;
    /** 推送至教案库后ID */
    libraryId?: number;
    name?: string;
    number?: string;
    org_id?: number;
    parent?: RehabChapter;
    parentId?: number;
    parents?: RehabChapter[];
    /** 来源 */
    referenceSource?: ChapterLibrary;
    /** ChapterLibrary ID 从外部引用 */
    referenceSourceId?: number;
    sort?: number;
    /** 是否提交至考核 */
    submitted?: boolean;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type RehabChapterAssessResult = {
    bo_id?: number;
    chapterId?: number;
    createdAt?: string;
    gradeClassId?: number;
    id?: number;
    org_id?: number;
    scores?: TeachingAssessCriteriaVo[];
    student?: IdNameVo;
    studentId?: number;
    totalScore?: number;
    /** 前测后测 */
    type?: ChapterAssessType;
    updatedAt?: string;
  };

  type RehabChapterContent = {
    bo_id?: number;
    chapterId?: number;
    createdAt?: string;
    id?: number;
    lessonPrepare?: IdNameVo[];
    lessonPrepareAttachments?: Attachment[];
    org_id?: number;
    teachingAssessCriteria?: TeachingAssessCriteriaVo[];
    teachingPrepare?: IdNameVo[];
    teachingReflection?: IdNameVo[];
    updatedAt?: string;
  };

  type RehabChapterLibrary = {
    bo_id?: number;
    category?: number;
    content?: RehabChapterLibraryContent;
    coverImage?: string;
    createdAt?: string;
    createdBy?: IdNameVo;
    createdById?: number;
    deletedAt?: DeletedAt;
    description?: string;
    grade?: string;
    id?: number;
    name?: string;
    orgNature?: string;
    org_id?: number;
    /** 来源章节id */
    originId?: number;
    period?: string;
    referenceCount?: number;
    /** 训练形式 */
    trainingForm?: string;
    updatedAt?: string;
    updatedBy?: IdNameVo;
    updatedById?: number;
  };

  type RehabChapterLibraryContent = {
    attachment?: Attachment[];
    bo_id?: number;
    chapterLibId?: number;
    createdAt?: string;
    id?: number;
    lessonPrepare?: IdNameVo[];
    org_id?: number;
    teachingAssessCriteria?: TeachingAssessCriteriaVo[];
    teachingPrepare?: IdNameVo[];
    teachingReflection?: IdNameVo[];
    updatedAt?: string;
  };

  type RehabChapterSubmitParamsParams = {
    /** 章节ID */
    id: number;
  };

  type RehabGetChapterLibraryDetailParamsParams = {
    /** 教案库ID */
    id: number;
  };

  type removeDigitalResourceCourseAuthorizationParamsParams = {
    /** 数字资源课程授权ID */
    id: number;
  };

  type SaveAnonymousFormAnswerParamsParams = {
    /** 问卷表单ID */
    uuid: string;
  };

  type SaveMyFormAnswerParamsParams = {
    /** 问卷表单ID */
    formId: number;
  };

  type Schema = {
    api?: string;
    dependsOnSchemas?: string[];
    editableFields?: string[];
    filterableFields?: string[];
    foreignPreloadFields?: string[];
    functionName?: string;
    label?: string;
    moduleName?: string;
    permActions?: string[];
    permNode?: string;
    readOnlyFields?: string[];
    schemaFieldMap?: Record<string, any>;
    schemaFields?: SchemaField[];
    sortableFields?: string[];
  };

  type SchemaField = {
    dataType?: string;
    defaultValue?: string | ((record?: Record<string, any>) => any);
    editable?: boolean;
    filterable?: boolean;
    foreignField?: ForeignField;
    fuzzySearch?: boolean;
    invisible?: boolean;
    key?: string;
    label?: string;
    readOnly?: boolean;
    required?: boolean;
    sortable?: boolean;
    systemGenerated?: boolean;
    unique?: boolean;
    updatable?: boolean;
    valueType?: string;
  };

  type SessionVo = {
    branchOffice?: BranchOfficeVo;
    company?: CompanyVo;
    id?: number;
    loginSource?: string;
    name?: string;
    roles?: IdNameVo[];
    token?: string;
    username?: string;
  };

  type setLastMessageReadParamsParams = {
    /** Conversation ID */
    conversationId: number;
  };

  type statisticsByAnswerGroupingParamsParams = {
    /** 问卷表单ID */
    formId: number;
  };

  type StsTokenVo = {
    accessKeyId?: string;
    accessKeySecret?: string;
    bucket?: string;
    region?: string;
    securityToken?: string;
    visitPrefix?: string;
  };

  type StudentTransferApplyRo = {
    remark?: string;
    studentId: number;
    toSchoolId: number;
  };

  type StudentTransferOperationRo = {
    id: number;
    remark?: string;
    status: StudentTransferStatus;
  };

  enum StudentTransferStatus {
    pending = 'pending',
    agreed = 'agreed',
    refused = 'refused',
    canceled = 'canceled',
  }

  type StudentVo = {
    branchOfficeId?: number;
    companyId?: number;
    fusionSchoolName?: string;
    grade?: string;
    gradeClassId?: number;
    guardian?: string;
    guardianPhone?: string;
    id?: number;
    name?: string;
  };

  type TeacherQuestion = {
    analysisTips?: string;
    bo_id?: number;
    catalogId?: number;
    correctAnswer?: string;
    courseId?: number;
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    isVerify?: VerifyStatus;
    options?: QuestionOption[];
    orgNature?: string;
    org_id?: number;
    presetType?: QuestionTypePreset;
    question?: string;
    questionRepositoryId?: number;
    questionType?: QuestionType;
    score?: number;
    teacherId?: number;
    tips?: string;
    typeId?: number;
    updatedAt?: string;
  };

  type TeacherQuestionLibraryCatalog = {
    bo_id?: number;
    catalogType?: CatalogType;
    children?: TeacherQuestionLibraryCatalog[];
    courseId?: number;
    createdAt?: string;
    deletedAt?: DeletedAt;
    id?: number;
    lft?: number;
    name?: string;
    orgNature?: string;
    org_id?: number;
    parentId?: number;
    rgt?: number;
    teacherId?: number;
    updatedAt?: string;
  };

  type TeachingAssessCriteriaVo = {
    after?: number;
    children?: TeachingAssessCriteriaVo[];
    id?: string;
    iepTargetIds?: number[];
    name?: string;
    pre?: number;
    questionIds?: number[];
    rehabTargetIds?: number[];
    score?: number;
    students?: IdNameVo[];
    testResult?: Record<string, any>;
  };

  type TeachingSupervisionVo = {
    classHourRequired?: number;
    id?: number;
    originalCount?: number;
    referenceCount?: number;
    school?: IdNameVo;
    submittedCount?: number;
    teacher?: IdNameVo;
    teachingClasses?: IdNameVo[];
    teachingGrades?: IdNameVo[];
    weekScheduleCount?: number;
  };

  type TreeModel = {
    children?: TreeModel[];
    lft?: number;
    name?: string;
    parentId?: number;
    rgt?: number;
  };

  type UpdateCatalogNameParamsParams = {
    /** ID */
    id: number;
  };

  type UpdateChapterContentParamsParams = {
    /** 章节ID */
    id: number;
  };

  type UpdateChapterParamsParams = {
    /** 章节ID */
    id: number;
  };

  type UpdateCommonCourseRo = {
    ids?: number[];
  };

  type UpdateFolderNameParamsParams = {
    /** ID */
    id: number;
  };

  type UpdateRehabChapterContentParamsParams = {
    /** 章节ID */
    id: number;
  };

  type UpdateRehabChapterParamsParams = {
    /** 章节ID */
    id: number;
  };

  type UpdateTeacherCatalogNameParamsParams = {
    /** ID */
    id: number;
  };

  type UpdateVisitReservationStatusRo = {
    id?: number;
    remark?: string;
    status?: GuardianVisitReservationStatus;
  };

  enum VerifyStatus {
    0 = '0',
    1 = '1',
    2 = '2',
  }

  type WeappLoginRo = {
    code?: string;
    encryptedData?: string;
    iv?: string;
  };

  enum WebsocketMessageType {
    conversation = 'conversation',
    notification = 'notification',
    userOnline = 'userOnline',
    userOffline = 'userOffline',
  }
}
