// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** getStudentTransferList GET /student/transfer */
export async function getStudentTransferList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getStudentTransferListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/student/transfer", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** createStudentTransfer POST /student/transfer */
export async function createStudentTransfer(
  body: API.StudentTransferApplyRo,
  options?: { [key: string]: any }
) {
  return request<string>("/student/transfer", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** studentTransferOperate POST /student/transfer/operate */
export async function studentTransferOperate(
  body: API.StudentTransferOperationRo,
  options?: { [key: string]: any }
) {
  return request<string>("/student/transfer/operate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
