// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取章节树 GET /course/chapter */
export async function getChapterTree(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterTreeParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.Chapter[]>("/course/chapter", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取章节 GET /course/chapter/${param0} */
export async function getChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Chapter>(`/course/chapter/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新章节 PUT /course/chapter/${param0} */
export async function updateChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateChapterParamsParams,
  body: API.Chapter,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Chapter>(`/course/chapter/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除章节 DELETE /course/chapter/${param0} */
export async function deleteChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteChapterParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/course/chapter/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 提交章节 PUT /course/chapter/${param0}/submit */
export async function chapterSubmit(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ChapterSubmitParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/course/chapter/${param0}/submit`, {
    method: "PUT",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新章节内容 PATCH /course/chapter/content/${param0} */
export async function updateChapterContent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateChapterContentParamsParams,
  body: API.ChapterContent,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ChapterContent>(`/course/chapter/content/${param0}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 复制章节 POST /course/chapter/duplicate */
export async function chapterDuplicate(
  body: API.ChapterDuplicateRo,
  options?: { [key: string]: any }
) {
  return request<API.Chapter>("/course/chapter/duplicate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取章节列表 GET /course/chapter/list */
export async function getChapterList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>("/course/chapter/list", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查找当前学校的所有教案 GET /course/chapter/listCbIdIn */
export async function getChapterListCbIdIn(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterListCbIdInParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>("/course/chapter/listCbIdIn", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 移动章节 PUT /course/chapter/move-node */
export async function moveChapterNode(
  body: API.MoveNodeRo,
  options?: { [key: string]: any }
) {
  return request<string>("/course/chapter/move-node", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取常用课程列表 GET /course/common-course */
export async function getCommonCourseList(options?: { [key: string]: any }) {
  return request<API.Course[]>("/course/common-course", {
    method: "GET",
    ...(options || {}),
  });
}

/** 更新常用课程 PUT /course/common-course */
export async function updateCommonCourse(
  body: API.UpdateCommonCourseRo,
  options?: { [key: string]: any }
) {
  return request<API.Course[]>("/course/common-course", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取课程列表 GET /course/course/courseListByCbIdIn */
export async function getCourseListByCbId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetCourseListByCbIdParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>("/course/course/courseListByCbIdIn", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取学校课程列表 GET /course/course/getBranchCourseList */
export async function getCourseList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetCourseListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/course/course/getBranchCourseList", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取我的课程列表 GET /course/course/my-course */
export async function getMyCourseList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetMyCourseListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/course/course/my-course", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取游戏资源的html GET /resource/digital/game/${param0} */
export async function getGameResourceHtml(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getGameResourceHtmlParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/resource/digital/game/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
