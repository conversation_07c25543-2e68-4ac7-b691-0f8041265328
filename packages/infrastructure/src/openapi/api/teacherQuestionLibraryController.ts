// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取题库目录列表 GET /teacherQuestionLibrary/catalog */
export async function getTeacherCatalogs(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetTeacherCatalogsParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.TeacherQuestionLibraryCatalog[]>(
    "/teacherQuestionLibrary/catalog",
    {
      method: "GET",
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 创建题库目录 POST /teacherQuestionLibrary/catalog */
export async function createNewTeacherCatalog(
  body: API.CreateTeacherQuestionCatalogRo,
  options?: { [key: string]: any }
) {
  return request<API.TeacherQuestionLibraryCatalog>(
    "/teacherQuestionLibrary/catalog",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      data: body,
      ...(options || {}),
    }
  );
}

/** 更新题库目录名称 PUT /teacherQuestionLibrary/catalog/${param0} */
export async function updateTeacherCatalogName(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateTeacherCatalogNameParamsParams,
  body: API.IdNameVo,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/teacherQuestionLibrary/catalog/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除题库目录 DELETE /teacherQuestionLibrary/catalog/${param0} */
export async function deleteTeacherCatalog(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteTeacherCatalogParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/teacherQuestionLibrary/catalog/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取题库目录回收站 GET /teacherQuestionLibrary/catalog/trash */
export async function getTeacherQuestionCatalogTrash(options?: {
  [key: string]: any;
}) {
  return request<API.TeacherQuestionLibraryCatalog[]>(
    "/teacherQuestionLibrary/catalog/trash",
    {
      method: "GET",
      ...(options || {}),
    }
  );
}

/** 获取题库目录树 GET /teacherQuestionLibrary/catalog/tree */
export async function getTeacherCatalogsTree(options?: { [key: string]: any }) {
  return request<API.TeacherQuestionLibraryCatalog[]>(
    "/teacherQuestionLibrary/catalog/tree",
    {
      method: "GET",
      ...(options || {}),
    }
  );
}

/** 获取题目列表 GET /teacherQuestionLibrary/question */
export async function getTeacherQuestionList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetTeacherQuestionListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.TeacherQuestion[]>("/teacherQuestionLibrary/question", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 保存题目 POST /teacherQuestionLibrary/question */
export async function saveTeacherQuestion(
  body: API.TeacherQuestion,
  options?: { [key: string]: any }
) {
  return request<API.TeacherQuestion>("/teacherQuestionLibrary/question", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取题目 GET /teacherQuestionLibrary/question/${param0} */
export async function getTeacherQuestion(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetTeacherQuestionParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.TeacherQuestion>(
    `/teacherQuestionLibrary/question/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 删除题目 DELETE /teacherQuestionLibrary/question/${param0} */
export async function deleteTeacherQuestion(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteTeacherQuestionParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/teacherQuestionLibrary/question/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
