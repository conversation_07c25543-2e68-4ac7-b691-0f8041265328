// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** get teaching supervision list GET /teacher/teachingSupervision */
export async function getTeachingSupervisionList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTeachingSupervisionListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.TeachingSupervisionVo[]>("/teacher/teachingSupervision", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
