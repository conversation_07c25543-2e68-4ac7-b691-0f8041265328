// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取章节评价列表 GET /course/chapter-assess */
export async function getChapterAssessmentList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterAssessmentListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.ChapterAssessResult[]>("/course/chapter-assess", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 编辑章节评价 POST /course/chapter-assess */
export async function editChapterAssessment(
  body: API.ChapterAssessResult,
  options?: { [key: string]: any }
) {
  return request<API.ChapterAssessResult>("/course/chapter-assess", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除章节评价 DELETE /course/chapter-assess/${param0} */
export async function deleteChapterAssessment(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteChapterAssessmentParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/course/chapter-assess/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量编辑章节评价 POST /course/chapter-assess/batch */
export async function batchEditChapterAssessment(
  body: API.ChapterAssessResult[],
  options?: { [key: string]: any }
) {
  return request<Record<string, any>>("/course/chapter-assess/batch", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
