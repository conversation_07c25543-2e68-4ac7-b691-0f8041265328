// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** ping example do ping GET /generic/session */
export async function genericGetSession(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GenericGetSessionParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.SessionVo>("/generic/session", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 退出登录 DELETE /generic/session */
export async function logout(options?: { [key: string]: any }) {
  return request<any>("/generic/session", {
    method: "DELETE",
    ...(options || {}),
  });
}

/** Get Unread Message Count GET /generic/session/unreadMessageCount */
export async function getUnreadMessageCount(options?: { [key: string]: any }) {
  return request<number>("/generic/session/unreadMessageCount", {
    method: "GET",
    ...(options || {}),
  });
}
