// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 微信小程序绑定手机号 POST /guardian/bindMobile */
export async function guardianBindMobile(
  body: API.WeappLoginRo,
  options?: { [key: string]: any }
) {
  return request<API.Guardian>("/guardian/bindMobile", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取我的信息 GET /guardian/getMyInfo */
export async function guardianGetMyInfo(options?: { [key: string]: any }) {
  return request<API.Guardian>("/guardian/getMyInfo", {
    method: "GET",
    ...(options || {}),
  });
}

/** 微信小程序登录 GET /guardian/loginViaWeapp */
export async function guardianLoginViaWeapp(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.guardianLoginViaWeappParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.GuardianLoginResultVo>("/guardian/loginViaWeapp", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** guardianNewsPushToCarousel PUT /guardian/news/pushToCarousel/${param0} */
export async function guardianNewsPushToCarousel(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.guardianNewsPushToCarouselParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/guardian/news/pushToCarousel/${param0}`, {
    method: "PUT",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 切换学生 PUT /guardian/switchStudent */
export async function guardianSwitchStudent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.guardianSwitchStudentParamsParams,
  options?: { [key: string]: any }
) {
  return request<string>("/guardian/switchStudent", {
    method: "PUT",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步我的孩子 GET /guardian/syncMyChildren */
export async function syncMyChildren(options?: { [key: string]: any }) {
  return request<string>("/guardian/syncMyChildren", {
    method: "GET",
    ...(options || {}),
  });
}

/** getMyVisitReservationList GET /guardian/visitReservation/myList */
export async function getMyVisitReservationList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMyVisitReservationListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/guardian/visitReservation/myList", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** updateGuardianVisitReservationStatus PUT /guardian/visitReservation/updateStatus */
export async function updateGuardianVisitReservationStatus(
  body: API.UpdateVisitReservationStatusRo,
  options?: { [key: string]: any }
) {
  return request<any>("/guardian/visitReservation/updateStatus", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 微信小程序初始化 GET /guardian/weappInit */
export async function guardianWeappInit(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.guardianWeappInitParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.GuardianWeappInitVo>("/guardian/weappInit", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
