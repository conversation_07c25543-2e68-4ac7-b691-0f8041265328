// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取题库目录列表 GET /questionLibrary/catalog */
export async function getCatalogs(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetCatalogsParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.QuestionLibraryCatalog[]>("/questionLibrary/catalog", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建题库目录 POST /questionLibrary/catalog */
export async function createNewCatalog(
  body: API.CreateQuestionCatalogRo,
  options?: { [key: string]: any }
) {
  return request<API.QuestionLibraryCatalog>("/questionLibrary/catalog", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新题库目录名称 PUT /questionLibrary/catalog/${param0} */
export async function updateCatalogName(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateCatalogNameParamsParams,
  body: API.IdNameVo,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionLibrary/catalog/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除题库目录 DELETE /questionLibrary/catalog/${param0} */
export async function deleteCatalog(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteCatalogParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionLibrary/catalog/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取题库目录回收站 GET /questionLibrary/catalog/trash */
export async function getQuestionCatalogTrash(options?: {
  [key: string]: any;
}) {
  return request<API.QuestionLibraryCatalog[]>(
    "/questionLibrary/catalog/trash",
    {
      method: "GET",
      ...(options || {}),
    }
  );
}

/** 获取题库目录树 GET /questionLibrary/catalog/tree */
export async function getCatalogsTree(options?: { [key: string]: any }) {
  return request<API.QuestionLibraryCatalog[]>(
    "/questionLibrary/catalog/tree",
    {
      method: "GET",
      ...(options || {}),
    }
  );
}

/** 获取题目列表 GET /questionLibrary/question */
export async function getQuestionList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.Question[]>("/questionLibrary/question", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 保存题目 POST /questionLibrary/question */
export async function saveQuestion(
  body: API.Question,
  options?: { [key: string]: any }
) {
  return request<API.Question>("/questionLibrary/question", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取题目 GET /questionLibrary/question/${param0} */
export async function getQuestion(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Question>(`/questionLibrary/question/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除题目 DELETE /questionLibrary/question/${param0} */
export async function deleteQuestion(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteQuestionParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionLibrary/question/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量删除题目 DELETE /questionLibrary/question/batch */
export async function batchReject(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.BatchRejectParamsParams,
  options?: { [key: string]: any }
) {
  return request<string>("/questionLibrary/question/batch", {
    method: "DELETE",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量保存题目 POST /questionLibrary/save/questions */
export async function batchSaveQuestions(
  body: API.Question[],
  options?: { [key: string]: any }
) {
  return request<API.Question[]>("/questionLibrary/save/questions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取题目列表 GET /questionLibrary/unverified/questionList */
export async function getByVerifyQuestionList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetByVerifyQuestionListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.Question[]>("/questionLibrary/unverified/questionList", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
