// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取题库目录列表 GET /questionnaire/folder */
export async function getQuestionnaireFolders(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionnaireFoldersParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.QuestionnaireFolder[]>("/questionnaire/folder", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建题库目录 POST /questionnaire/folder */
export async function createNewQuestionnaireFolder(
  body: API.CreateQuestionnaireFolderRo,
  options?: { [key: string]: any }
) {
  return request<API.QuestionnaireFolder>("/questionnaire/folder", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新题库目录名称 PUT /questionnaire/folder/${param0} */
export async function updateFolderName(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateFolderNameParamsParams,
  body: API.IdNameVo,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionnaire/folder/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除题库目录 DELETE /questionnaire/folder/${param0} */
export async function deleteQuestionnaireFolder(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteQuestionnaireFolderParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionnaire/folder/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取题库目录回收站 GET /questionnaire/folder/trash */
export async function getQuestionnaireFolderTrash(options?: {
  [key: string]: any;
}) {
  return request<API.QuestionnaireFolder[]>("/questionnaire/folder/trash", {
    method: "GET",
    ...(options || {}),
  });
}

/** 获取题库目录树 GET /questionnaire/folder/tree */
export async function getQuestionnaireFoldersTree(options?: {
  [key: string]: any;
}) {
  return request<API.QuestionnaireFolder[]>("/questionnaire/folder/tree", {
    method: "GET",
    ...(options || {}),
  });
}

/** 获取题目列表 GET /questionnaire/questionnaire */
export async function getQuestionnaireList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionnaireListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.Questionnaire[]>("/questionnaire/questionnaire", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 保存题目 POST /questionnaire/questionnaire */
export async function saveQuestionnaire(
  body: API.Questionnaire,
  options?: { [key: string]: any }
) {
  return request<API.Questionnaire>("/questionnaire/questionnaire", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取题目 GET /questionnaire/questionnaire/${param0} */
export async function getQuestionnaire(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionnaireParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Questionnaire>(`/questionnaire/questionnaire/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除题目 DELETE /questionnaire/questionnaire/${param0} */
export async function deleteQuestionnaire(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteQuestionnaireParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionnaire/questionnaire/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
