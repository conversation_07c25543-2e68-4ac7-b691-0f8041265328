// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** Get conversation list GET /im/getConversationList */
export async function getConversationList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getConversationListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/im/getConversationList", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Get conversation message list GET /im/getConversationMessageList */
export async function getConversationMessageList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getConversationMessageListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/im/getConversationMessageList", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Get im message type GET /im/getImMessageType */
export async function getImMessageType(options?: { [key: string]: any }) {
  return request<API.ImMessage>("/im/getImMessageType", {
    method: "GET",
    ...(options || {}),
  });
}

/** Get conversation by id GET /im/guardianGetConversation */
export async function guardianGetConversation(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.guardianGetConversationParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.Conversation>("/im/guardianGetConversation", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Set last message read PUT /im/setLastMessageRead */
export async function setLastMessageRead(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.setLastMessageReadParamsParams,
  options?: { [key: string]: any }
) {
  return request<string>("/im/setLastMessageRead", {
    method: "PUT",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
