// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 查询机构 GET /generic/org/query */
export async function orgQuery(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.orgQueryParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.IdNameVo>("/generic/org/query", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
