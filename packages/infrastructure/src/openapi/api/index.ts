// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as course from "./course";
import * as chapterAssessmentController from "./chapterAssessmentController";
import * as chapterLibrary from "./chapterLibrary";
import * as rehabCourse from "./rehabCourse";
import * as rehabChapterAssessmentController from "./rehabChapterAssessmentController";
import * as rehabChapterLibrary from "./rehabChapterLibrary";
import * as notificationController from "./notificationController";
import * as org from "./org";
import * as ossController from "./ossController";
import * as schema from "./schema";
import * as genericSessionController from "./genericSessionController";
import * as guardianController from "./guardianController";
import * as conversationController from "./conversationController";
import * as getOnlineTeachers from "./getOnlineTeachers";
import * as questionLibraryController from "./questionLibraryController";
import * as questionnaireController from "./questionnaireController";
import * as questionnaireRecordController from "./questionnaireRecordController";
import * as questionnaireFormController from "./questionnaireFormController";
import * as resource from "./resource";
import * as studentTransferController from "./studentTransferController";
import * as teachingController from "./teachingController";
import * as teacherQuestionLibraryController from "./teacherQuestionLibraryController";
export default {
  course,
  chapterAssessmentController,
  chapterLibrary,
  rehabCourse,
  rehabChapterAssessmentController,
  rehabChapterLibrary,
  notificationController,
  org,
  ossController,
  schema,
  genericSessionController,
  guardianController,
  conversationController,
  getOnlineTeachers,
  questionLibraryController,
  questionnaireController,
  questionnaireRecordController,
  questionnaireFormController,
  resource,
  studentTransferController,
  teachingController,
  teacherQuestionLibraryController,
};
