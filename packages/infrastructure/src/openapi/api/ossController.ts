// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** Get OSS directly upload url GET /generic/oss/directlyUploadUrl */
export async function getDirectlyUploadUrl(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDirectlyUploadUrlParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.DirectlyUploadUrl>("/generic/oss/directlyUploadUrl", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Get OSS STS token GET /generic/oss/stsToken */
export async function getStsToken(options?: { [key: string]: any }) {
  return request<API.StsTokenVo>("/generic/oss/stsToken", {
    method: "GET",
    ...(options || {}),
  });
}
