// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取教案库列表 GET /course/chapter-library */
export async function getChapterLibraryList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterLibraryListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/course/chapter-library", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建教案库 POST /course/chapter-library */
export async function createChapterLibrary(
  body: API.CreateRo,
  options?: { [key: string]: any }
) {
  return request<API.ChapterLibrary>("/course/chapter-library", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取教案库详情 GET /course/chapter-library/${param0} */
export async function getChapterLibraryDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetChapterLibraryDetailParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ChapterLibrary>(`/course/chapter-library/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除教案库 DELETE /course/chapter-library/${param0} */
export async function deleteChapterLibrary(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteChapterLibraryParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/course/chapter-library/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除教案库 DELETE /course/rehab-chapter-library/${param0} */
export async function deleteRehabChapterLibrary(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteRehabChapterLibraryParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Record<string, any>>(
    `/course/rehab-chapter-library/${param0}`,
    {
      method: "DELETE",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
