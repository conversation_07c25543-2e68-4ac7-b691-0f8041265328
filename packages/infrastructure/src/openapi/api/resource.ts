// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 创建章节 POST /resource/chapter */
export async function createDigitalChapter(
  body: API.DigitalResourceChapter,
  options?: { [key: string]: any }
) {
  return request<API.DigitalResourceChapter>("/resource/chapter", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除章节 DELETE /resource/chapter/${param0} */
export async function deleteDigitalChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteDigitalChapterParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/resource/chapter/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 移动章节 PUT /resource/chapter/move-node */
export async function moveDigitalChapterNode(
  body: API.MoveNodeRo,
  options?: { [key: string]: any }
) {
  return request<string>("/resource/chapter/move-node", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 授权课程资源 POST /resource/course/authorize */
export async function authorizeCourseResourceTo(
  body: API.BatchAuthorizeDigitalResourceCourseRo,
  options?: { [key: string]: any }
) {
  return request<string>("/resource/course/authorize", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取数字资源授权课程 GET /resource/course/authorized */
export async function getDigitalResourceAuthorizedCourse(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDigitalResourceAuthorizedCourseParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.DigitalResourceAuthorize[]>(
    "/resource/course/authorized",
    {
      method: "GET",
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取数字资源授权单位 GET /resource/course/authorized-org */
export async function getDigitalResourceAuthorizedOrg(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDigitalResourceAuthorizedOrgParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.IdNameVo[]>("/resource/course/authorized-org", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 移除数字资源课程授权 DELETE /resource/course/authorized/${param0} */
export async function removeDigitalResourceCourseAuthorization(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.removeDigitalResourceCourseAuthorizationParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/resource/course/authorized/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取数字资源列表 GET /resource/digital */
export async function getDigitalResourceList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetDigitalResourceListParamsParams,
  options?: { [key: string]: any }
) {
  return request<Record<string, any>>("/resource/digital", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取章节树 GET /resource/digital-chapter */
export async function getDigitalChapterTree(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDigitalChapterTreeParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.DigitalResourceChapter[]>("/resource/digital-chapter", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量创建数字资源 POST /resource/digital/batch */
export async function batchCreateDigitalResource(
  body: API.BatchCreateDigitalResourceRo,
  options?: { [key: string]: any }
) {
  return request<API.DigitalResource[]>("/resource/digital/batch", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取媒体资源 GET /resource/digital/media/${param0} */
export async function getMediaResource302(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetMediaResource302ParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/resource/digital/media/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
