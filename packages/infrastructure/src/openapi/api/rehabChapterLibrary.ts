// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取教案库列表 GET /course/rehab-chapter-library */
export async function getRehabChapterLibraryList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterLibraryListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/course/rehab-chapter-library", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建教案库 POST /course/rehab-chapter-library */
export async function rehabCreateChapterLibrary(
  body: API.CreateRo,
  options?: { [key: string]: any }
) {
  return request<API.RehabChapterLibrary>("/course/rehab-chapter-library", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取教案库详情 GET /course/rehab-chapter-library/${param0} */
export async function rehabGetChapterLibraryDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.RehabGetChapterLibraryDetailParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RehabChapterLibrary>(
    `/course/rehab-chapter-library/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
