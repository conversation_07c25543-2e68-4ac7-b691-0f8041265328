// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取在线老师 GET /im/onlineTeachers */
export async function getOnlineTeachers(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getOnlineTeachersParamsParams,
  options?: { [key: string]: any }
) {
  return request<Record<string, any>>("/im/onlineTeachers", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
