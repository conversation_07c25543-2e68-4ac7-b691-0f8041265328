// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 保存匿名问卷表单答案 POST /questionnaire/form/anonymous */
export async function saveAnonymousFormAnswer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.SaveAnonymousFormAnswerParamsParams,
  body: API.QuestionnaireFormRecord,
  options?: { [key: string]: any }
) {
  return request<API.QuestionnaireFormRecord>("/questionnaire/form/anonymous", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取我的问卷表单答案 GET /questionnaire/record/my/${param0} */
export async function getMyFormAnswer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetMyFormAnswerParamsParams,
  options?: { [key: string]: any }
) {
  const { formId: param0, ...queryParams } = params;
  return request<API.QuestionnaireFormRecord>(
    `/questionnaire/record/my/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 保存我的问卷表单答案 POST /questionnaire/record/my/${param0} */
export async function saveMyFormAnswer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.SaveMyFormAnswerParamsParams,
  body: API.QuestionnaireFormRecord,
  options?: { [key: string]: any }
) {
  const { formId: param0, ...queryParams } = params;
  return request<API.QuestionnaireFormRecord>(
    `/questionnaire/record/my/${param0}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    }
  );
}

/** 驳回问卷记录 PUT /questionnaire/record/reject/${param0} */
export async function questionnaireRecordReject(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.questionnaireRecordRejectParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/questionnaire/record/reject/${param0}`, {
    method: "PUT",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 按答案分组统计 GET /questionnaire/record/statistics/answerGrouping/${param0} */
export async function statisticsByAnswerGrouping(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.statisticsByAnswerGroupingParamsParams,
  options?: { [key: string]: any }
) {
  const { formId: param0, ...queryParams } = params;
  return request<Record<string, any>>(
    `/questionnaire/record/statistics/answerGrouping/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 获取问卷记录排行榜 GET /questionnaire/record/top/${param0} */
export async function getQuestionnaireRecordTopList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetQuestionnaireRecordTopListParamsParams,
  options?: { [key: string]: any }
) {
  const { formId: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/questionnaire/record/top/${param0}`, {
    method: "GET",
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
