// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取可用问卷表单 GET /questionnaire/form/available */
export async function getAvailableQuestionnaireForms(options?: {
  [key: string]: any;
}) {
  return request<API.QuestionnaireForm[]>("/questionnaire/form/available", {
    method: "GET",
    ...(options || {}),
  });
}

/** 获取公共问卷表单 GET /questionnaire/form/public/${param0} */
export async function getPublicQuestionnaireForm(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetPublicQuestionnaireFormParamsParams,
  options?: { [key: string]: any }
) {
  const { uuid: param0, ...queryParams } = params;
  return request<API.QuestionnaireForm>(
    `/questionnaire/form/public/${param0}`,
    {
      method: "GET",
      params: {
        ...queryParams,
      },
      ...(options || {}),
    }
  );
}
