// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取章节树 GET /course/rehab-chapter */
export async function getRehabChapterTree(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterTreeParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.RehabChapter[]>("/course/rehab-chapter", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取章节 GET /course/rehab-chapter/${param0} */
export async function getRehabChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RehabChapter>(`/course/rehab-chapter/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新章节 PUT /course/rehab-chapter/${param0} */
export async function updateRehabChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateRehabChapterParamsParams,
  body: API.RehabChapter,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RehabChapter>(`/course/rehab-chapter/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除章节 DELETE /course/rehab-chapter/${param0} */
export async function deleteRehabChapter(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteRehabChapterParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/course/rehab-chapter/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 提交章节 PUT /course/rehab-chapter/${param0}/submit */
export async function rehabChapterSubmit(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.RehabChapterSubmitParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/course/rehab-chapter/${param0}/submit`, {
    method: "PUT",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新章节内容 PATCH /course/rehab-chapter/content/${param0} */
export async function updateRehabChapterContent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.UpdateRehabChapterContentParamsParams,
  body: API.RehabChapterContent,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.RehabChapterContent>(
    `/course/rehab-chapter/content/${param0}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    }
  );
}

/** 复制章节 POST /course/rehab-chapter/duplicate */
export async function rehabChapterDuplicate(
  body: API.ChapterDuplicateRo,
  options?: { [key: string]: any }
) {
  return request<API.RehabChapter>("/course/rehab-chapter/duplicate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取章节列表 GET /course/rehab-chapter/list */
export async function getRehabChapterList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>("/course/rehab-chapter/list", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查找当前学校的所有教案 GET /course/rehab-chapter/listCbIdIn */
export async function getRehabChapterListCbIdIn(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterListCbIdInParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>("/course/rehab-chapter/listCbIdIn", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 移动章节 PUT /course/rehab-chapter/move-node */
export async function moveRehabChapterNode(
  body: API.MoveNodeRo,
  options?: { [key: string]: any }
) {
  return request<string>("/course/rehab-chapter/move-node", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取课程列表 GET /course/rehab-course/courseListByCbIdIn */
export async function getRehabCourseListByCbId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabCourseListByCbIdParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult[]>(
    "/course/rehab-course/courseListByCbIdIn",
    {
      method: "GET",
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取学校课程列表 GET /course/rehab-course/getBranchCourseList */
export async function getRehabCourseList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabCourseListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>(
    "/course/rehab-course/getBranchCourseList",
    {
      method: "GET",
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 获取我的课程列表 GET /course/rehab-course/my-course */
export async function getMyRehabCourseList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetMyRehabCourseListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.PaginationResult>("/course/rehab-course/my-course", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
