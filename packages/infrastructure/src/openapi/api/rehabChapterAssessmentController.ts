// @ts-ignore
/* eslint-disable */
import { request } from "../../request";

/** 获取章节评价列表 GET /course/rehab-chapter-assess */
export async function getRehabChapterAssessmentList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetRehabChapterAssessmentListParamsParams,
  options?: { [key: string]: any }
) {
  return request<API.RehabChapterAssessResult[]>(
    "/course/rehab-chapter-assess",
    {
      method: "GET",
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/** 编辑章节评价 POST /course/rehab-chapter-assess */
export async function editRehabChapterAssessment(
  body: API.RehabChapterAssessResult,
  options?: { [key: string]: any }
) {
  return request<API.RehabChapterAssessResult>("/course/rehab-chapter-assess", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除章节评价 DELETE /course/rehab-chapter-assess/${param0} */
export async function deleteRehabChapterAssessment(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.DeleteRehabChapterAssessmentParamsParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Record<string, any>>(
    `/course/rehab-chapter-assess/${param0}`,
    {
      method: "DELETE",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** 批量编辑章节评价 POST /course/rehab-chapter-assess/batch */
export async function batchEditRehabChapterAssessment(
  body: API.RehabChapterAssessResult[],
  options?: { [key: string]: any }
) {
  return request<Record<string, any>>("/course/rehab-chapter-assess/batch", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
