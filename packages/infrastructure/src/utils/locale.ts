import { capitalize } from 'lodash';
import { i18n } from '../locale';

const translate = (key?: string, arg1?: any, ...arg2: any[]) => {
  if (!key?.trim()) {
    return '';
  }
  const { t } = i18n.global;
  const keyArray = key.split('.');

  const lastKey = keyArray.slice(-1)[0];
  const lastKeyArray = lastKey.split(/(?=[A-Z])/);
  const lastKeyArrayLength = lastKeyArray.length;
  let defaultMessage = capitalize(lastKey);
  if (lastKeyArrayLength > 1) {
    defaultMessage = lastKeyArray.map((item) => capitalize(item)).join(' ');
  }
  let message = t(key, arg1, ...arg2);

  if (message === key || !message.trim()) {
    key = `common.${keyArray.slice(-1)[0]}`;
    message = t(key, arg1, ...arg2);
    if (message === key || !message.trim()) {
      return defaultMessage;
    }
  }
  return message;
};

const translatePlugin = {
  install: (app: any) => {
    app.provide('translate', translate);
  },
};

export { translate, translatePlugin };
