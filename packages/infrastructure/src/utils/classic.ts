const dynamicLoadScript = async (script: string) => {
  return new Promise((resolve, reject) => {
    const scriptElement = document.createElement('script');
    scriptElement.src = script;
    scriptElement.onload = resolve;
    scriptElement.onerror = reject;
    document.head.appendChild(scriptElement);
  });
};

const dynamicLoadScripts = async (scripts: string[], callback?: () => void) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const script of scripts) {
    // eslint-disable-next-line no-await-in-loop
    await dynamicLoadScript(script);
  }

  if (callback) {
    callback();
  }
};

const dynamicLoadStyles = async (styles: string[], callback?: () => void) => {
  const promises = styles.map((style) => {
    return new Promise((resolve, reject) => {
      const styleElement = document.createElement('link');
      styleElement.rel = 'stylesheet';
      styleElement.href = style;
      styleElement.onload = resolve;
      styleElement.onerror = reject;
      document.head.appendChild(styleElement);
    });
  });

  await Promise.all(promises);

  if (callback) {
    callback();
  }
};

const dynamicLoadStaticFiles = async (scripts: string[], styles: string[], callback?: () => void) => {
  await dynamicLoadScripts(scripts);
  await dynamicLoadStyles(styles);

  if (callback) {
    callback();
  }
};

export { dynamicLoadScripts, dynamicLoadStyles, dynamicLoadStaticFiles };
