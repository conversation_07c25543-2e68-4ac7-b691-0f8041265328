import CryptoJS from 'crypto-js';

const keyStr = 'abcdefghijklmnopqrstuvwxyz0123456789';

const availableColors = [
  'orangered',
  'orange',
  'gold',
  'green',
  'cyan',
  'blue',
  'arcoblue',
  'purple',
  'pinkpurple',
  'magenta',
];

const colorful = (name: string): string => {
  name = CryptoJS.MD5(name).toString().toLowerCase();
  const colorIndex = keyStr.indexOf(name[0]) % availableColors.length;
  const typeIndex = (keyStr.indexOf(name[1]) % 5) + 3;
  return `rgb(var(--${availableColors[colorIndex]}-${typeIndex}))`;
};

const parseUrlParams = (search: string): Record<string, string> => {
  const params = search.slice(1).split('&');
  const result: Record<string, string> = {};
  params.forEach((param) => {
    const [key, value] = param.split('=');
    result[key] = value;
  });
  return result;
};

const stripTags = (html: string): string => {
  return html.replace(/<[^>]*>/g, '');
};

const stripTagsAndCut = (html: string, length: number): string => {
  const raw = stripTags(html);
  return raw.length > length ? `${raw.slice(0, length)}...` : raw;
};

// 获取学期名称
const getPeriodsList = (options?: {
  from?: number;
  to?: number;
  beforeYears?: number;
  afterYears?: number;
  wholeIsNeeded?: boolean;
}): string[] => {
  options = options || {};
  const now = new Date();
  if (!options.from) {
    options.from = now.getFullYear() - (options.beforeYears || 1);
  }
  if (!options.to) {
    options.to = now.getFullYear() + (options.afterYears || 1);
  }

  const periods: string[] = [];
  if (options.wholeIsNeeded) {
    periods.push('全部');
  }
  for (let i = options.from; i <= options.to; i += 1) {
    periods.push(`${i}年春季学期`);
    periods.push(`${i}年秋季学期`);
  }

  return periods;
};

const getCurrentPeriod = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();
  return `${year}年${month < 8 ? '春季' : '秋季'}学期`;
};

const checkIdCardAvailable = (idCard: string): boolean => {
  if (idCard?.length !== 18) {
    return false;
  }

  const idCardArr = idCard.split('');
  const idCardWeight = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const idCardCheckCode = '10X98765432';
  let sum = 0;

  for (let i = 0; i < 17; i += 1) {
    sum += Number.parseInt(idCardArr[i], 10) * idCardWeight[i];
  }

  return idCardCheckCode[sum % 11] === idCardArr[17];
};

export {
  colorful,
  availableColors,
  parseUrlParams,
  stripTags,
  stripTagsAndCut,
  getPeriodsList,
  getCurrentPeriod,
  checkIdCardAvailable,
};
