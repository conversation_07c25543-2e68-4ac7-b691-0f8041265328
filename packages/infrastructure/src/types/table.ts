import { SchemaField } from './schema';

export type TableColumn = SchemaField & {
  [prop: string]: any;
};
export type OperatorType =
  | 'Equal'
  | 'NotEqual'
  | 'GreaterThan'
  | 'GreaterThanOrEqual'
  | 'LessThan'
  | 'LessThanOrEqual'
  | 'Like'
  | 'NotLike'
  | 'In'
  | 'NotIn'
  | 'IsNull'
  | 'IsNotNull'
  | 'Between'
  | 'NotBetween';

export type QueryFilter = {
  field?: string;
  operator?: OperatorType;
  value?: any;
  formatValue?: (value: any) => any;

  virtualSearch?: boolean;
  [props: string]: any;
};

export type RO = {
  page?: number;
  pageSize?: number;
  filters?: QueryFilter[] | string;
  sort?: string;
  [prop: string]: any;
};

export type TableFilter = {
  key: string;
  api: string;
  scene?: string;
  filters: Record<string, QueryFilter>;
};
