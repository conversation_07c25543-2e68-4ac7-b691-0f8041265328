// eslint-disable-next-line no-shadow
enum InputWidget {
  Text = 'textInput',
  Textarea = 'textareaInput',
  Number = 'numberInput',
  Select = 'selectInput',
  Radio = 'radioInput',
  CheckBox = 'checkboxInput',
  Date = 'dateInput',
  Time = 'timeInput',
  DateTime = 'datetimeInput',
  DateRange = 'dateRangeInput',
  TimeRange = 'timeRangeInput',
  DateTimeRange = 'datetimeRangeInput',
  Switch = 'switchInput',
  Upload = 'uploadInput',
  RichInput = 'richInput',
  CoverUpload = 'coverUploadInput',
  AreaSelect = 'areaSelectInput',
}

export type DataType = {
  name: string;
  allowForeign?: boolean;
  defaultInputWidget: InputWidget;
  defaultDisplayWidget?: string;
};

type DataTypes = {
  [key: string]: DataType;
};

const DataTypes: DataTypes = {
  String: {
    name: 'String',
    defaultInputWidget: InputWidget.Text,
  },
  Text: {
    name: 'Text',
    defaultInputWidget: InputWidget.Textarea,
  },
  Integer: {
    name: 'Integer',
    defaultInputWidget: InputWidget.Number,
  },
  Number: {
    name: 'Number',
    defaultInputWidget: InputWidget.Number,
  },
  Double: {
    name: 'Double',
    defaultInputWidget: InputWidget.Number,
  },
  Long: {
    name: 'Long',
    defaultInputWidget: InputWidget.Number,
  },
  BigDecimal: {
    name: 'BigDecimal',
    defaultInputWidget: InputWidget.Number,
  },
  Boolean: {
    name: 'Boolean',
    defaultInputWidget: InputWidget.Switch,
  },
  Date: {
    name: 'Date',
    defaultInputWidget: InputWidget.Date,
  },
  Enum: {
    name: 'Enum',
    defaultInputWidget: InputWidget.Select,
  },
  Foreign: {
    name: 'Foreign',
    defaultInputWidget: InputWidget.Select,
    allowForeign: true,
  },
  Collection: {
    name: 'Collection',
    defaultInputWidget: InputWidget.Select,
  },
};

const DataTypeOf = (name: string | DataType): DataType => {
  if (typeof name === 'string') {
    return DataTypes[name];
  }
  return name;
};

export { InputWidget, DataTypes, DataTypeOf };
