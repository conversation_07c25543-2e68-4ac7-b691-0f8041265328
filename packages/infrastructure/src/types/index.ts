import type { InputWidget, DataType } from './dataType';
import { DataTypes, DataTypeOf } from './dataType';
import type {
  Schema,
  SchemaField,
  CustomSchema,
  InputWidgetProps,
  ListViewProps,
  FormViewProps,
  BooleanDisplayValue,
  QuickSearchProps,
  TableRowAction,
  FieldsGroupingDefine,
  FieldsGroup,
  FilterProps,
  RouteComposition,
  TableRowActionView,
  DetailViewProps,
} from './schema';
import type { TableColumn, TableFilter, QueryFilter, OperatorType, RO } from './table';

interface HttpResponse<T = unknown> {
  status: number;
  message: string;
  code: number;
  data: T;
  total?: number;
  success: boolean;
  errors?: string;
}

type User = {
  [props: string]: any;
} & API.SessionVo;

export { DataTypes, DataTypeOf, RO, TableColumn, TableFilter, QueryFilter, OperatorType };

export type {
  HttpResponse,
  InputWidget,
  DataType,
  User,
  Schema,
  SchemaField,
  CustomSchema,
  InputWidgetProps,
  ListViewProps,
  FormViewProps,
  BooleanDisplayValue,
  QuickSearchProps,
  TableRowAction,
  FieldsGroupingDefine,
  FieldsGroup,
  FilterProps,
  RouteComposition,
  TableRowActionView,
  DetailViewProps,
};
