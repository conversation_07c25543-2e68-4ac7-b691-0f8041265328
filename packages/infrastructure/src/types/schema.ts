import { Component, Ref } from 'vue';
import { DataType, InputWidget } from './dataType';

export type QueryDependsOptions = {
  // eg: onSchoolChange(school, record) => {inputWidgetProps: {disabled: false}}
  [field: string]: (
    value: any,
    inputValue?: any,
    record?: Record<string, any>,
    isFormInit?: boolean,
  ) => Record<string, any> | void;
};

export type BooleanDisplayValue = {
  value: boolean;
  label: string;
};

export type BooleanDisplayOptions = {
  mode: 'tag' | 'switch';
  componentProps?: Record<string, any>;
  allowUpdate?: boolean | ((record: Record<string, any>) => boolean);
  confirmMessage?: string | ((value: boolean, record: Record<string, any>) => string);
  handler?: (value: boolean, record: Record<string, any>) => Promise<boolean> | boolean | void;
  refresh?: boolean;
};

export type InputWidgetProps = {
  readonly?: boolean;
  placeholder?: string;
  colSpan?: number;
  valueValidator?: (value: any, cb: any) => Promise<any>;
  allowClear?: boolean;
  allowUpdate?: boolean;
  allowSearch?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  // dynamic visible
  dynamicVisible?: (record: Record<string, any>) => boolean;
  // string
  maxLength?: number;
  showWordLimit?: boolean;
  // select / checkbox / radio
  options?: (string | number | any)[];
  getOptions?: (record: Record<string, any>) => Promise<(string | number | any)[]>;
  // date
  format?: string;
  showTime?: boolean;
  disabledDate?: (currentDate?: Date) => boolean;
  disableTime?: () => boolean;
  formatValue?: (raw: any) => any;
  onValueChange?: (value: any, record: Ref<Record<string, any>>) => void;
  // foreign
  labelField?: string;
  valueField?: string;
  valueType?: string | ((value: any, options?: any) => any);
  // query depends
  queryDepends?: QueryDependsOptions;
  // upload
  maxSize?: number;
  minSize?: number;
  accept?: string;
  [props: string]: any;
};

export type FieldListProps = {
  visible?: boolean;
  sort?: number;
  columnWidth?: number;
  sortable?: boolean;
  fixed?: 'left' | 'right';
  [prop: string]: any;
};

export type TooltipProps = {
  getContent: (record: Record<string, any>) => string;
  visible?: boolean;
};

export type DisplayProps = {
  toDisplay?: (value: any, record?: Record<string, any>) => any | Component;
  detailSpan?: number;
  booleanDisplay?: BooleanDisplayValue[];
  booleanDisplayOptions?: BooleanDisplayOptions;
  component?: string | Component;
  columns?: any[];
  tooltip?: TooltipProps;
  dynamicVisible?: (record: Record<string, any>) => boolean;
  [prop: string]: any;
};

export type FilterProps = {
  defaultOperator?: string;
  supportOperators?: string[];
  targetField?: string;
  inputWidget?: InputWidget | Component | string;
  inputWidgetProps?: InputWidgetProps;
  // 是否使用虚拟字段搜索或快速检索
  virtualSearch?: boolean;
  [prop: string]: any;
};

export type SchemaField = API.SchemaField & {
  valueType?: string | DataType;
  // grid
  listProps?: FieldListProps;
  // input widget props
  inputWidgetProps?: InputWidgetProps;
  inputWidget?: InputWidget | Component | string;
  // display widget props
  displayProps?: DisplayProps;
  // visible props
  visibleInForm?: boolean | (() => boolean);
  visibleInTable?: boolean | (() => boolean);
  visibleInDetail?: boolean | ((record: Record<string, any>) => boolean);
  // other
  isValidated?: (value: any) => boolean;
  defaultValue?: any | (() => Promise<any>);
  // filter
  filterProps?: FilterProps;
  [prop: string]: any;
};

export type ExtraSchemaProperties = {
  fieldsMap: Record<string, SchemaField>;
};

export type FieldsGroupingDefine = {
  label?: string;
  fields: string[];
  columns?: number; // detail columns
  colSpan?: number; // form col span
  [prop: string]: any;
};

export type FieldsGroup = {
  label: string;
  fields: SchemaField[];
  // uses for columns layout, like detail view
  columns?: number;
  // uses for row/col layout, like form view
  colSpan?: number;
};
export type QuickSearchProps = {
  enabled: boolean;
  placeholder?: string;
  fields: string[];
  multiple?: boolean;
  inputWidth?: number;
  maxTagCount?: number;
};

export type DetailViewProps = {
  fullscreen?: boolean;
  type?: 'drawer' | 'modal';
  width?: number | string;
  title?: string | ((record: Record<string, any>) => string);
  printTitle?: string | ((record: Record<string, any>) => string); // 打印的抬头
  showFresh?: boolean;
  showOthers?: boolean;
  clickOutsideToClose?: boolean;
  fieldsGrouping?: FieldsGroupingDefine[];
  columns?: number;
  drawerPlacement?: 'top' | 'right' | 'bottom' | 'left';
  component?: any;
};

export type FormViewProps = {
  layout?: 'horizontal' | 'vertical' | 'inline';
  fieldsGrouping?: FieldsGroupingDefine[];
  colSpan?: number;
  defaultData?: Record<string, any> | (() => Record<string, any>);
  formatData?: (data: Record<string, any>) => Promise<Record<string, any>>;
  fullscreen?: boolean;
  [prop: string]: any;
};

export type ExportProps = {
  enabled: boolean | (() => boolean);
  columns?: string[];
  api?: string;
  handler?: (data: any) => void;
};

export type ImportProps = {
  enabled: boolean | (() => boolean);
  api?: string;
  templateApi?: string;
  template?: string;
  columns?: string[];
  tips?: string;
};

export type ListViewProps = {
  showIndex?: boolean;
  pagination?: boolean;
  visibleColumns?: string[];
  hiddenColumns?: string[];
  filterFields?: string[];
  searchType?: 'column' | 'advance';
  size?: 'mini' | 'small' | 'medium' | 'large';
  recycleBin?: boolean;
  rowActionWidth?: number;
};

export type TableRowAction = {
  key: string;
  size?: 'mini' | 'small' | 'medium' | 'large';
  label?: string; // | ((record: any | Record<string, any>[]) => string);
  collaborate?: 'Edit' | 'Owner'; // need Edit or Owner
  expose?: boolean;
  visible?: ((record: any | Record<string, any>[]) => boolean) | boolean;
  disabled?: ((record: any | Record<string, any>[]) => boolean) | boolean;
  handler?: (record: Record<string, any> | Record<string, any>[], loadData: any, config?: Record<string, any>) => void;
  confirm?: string;
  permNode?: string | string[];
  icon?: string | Component;
  btnProps?: Record<string, any>;
  multiple?: boolean;
  noWord?: boolean;
  hideInRow?: boolean;
};

export type TableRowActionView = {
  expose: TableRowAction[];
  others?: TableRowAction[];
};

export type SchemaPermission = {
  create?: string | string[] | undefined;
  update?: string | string[] | undefined;
  delete?: string | string[] | undefined;
  export?: string | string[] | undefined;
  import?: string | string[] | undefined;
  [prop: string]: string | string[] | undefined;
};

export type CustomSchema = {
  api: string;
  baseURL?: string;
  requestApi?: Record<string, string>;
  modalEdit?: boolean;
  collaborate?: boolean;
  fieldsMap?: Record<string, SchemaField>;
  formViewProps?: FormViewProps;
  detailViewProps?: DetailViewProps;
  listViewProps?: ListViewProps;
  quickSearchProps?: QuickSearchProps;
  inputWidth?: number;
  rowActions?: TableRowAction[];
  disableRowActionHandler?: (record: Record<string, any>, action: TableRowAction) => boolean; // 额外的禁用处理操作
  modulePath?: string;
  importable?: ImportProps;
  exportable?: ExportProps;
  permissions?: SchemaPermission;
};

export type Schema = API.Schema & {
  [prop: string]: any;
  schemaFields: SchemaField[];
  baseURL?: string;
} & ExtraSchemaProperties &
  CustomSchema;

export type RouteComposition = {
  module: string;
  func: string;
  id?: number;
  action?: string;
};

export type DsConvertOptions = {
  fieldsOverride?: Record<string, SchemaField>;
  schemaOverride?: CustomSchema;
};
