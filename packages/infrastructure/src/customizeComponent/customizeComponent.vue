<script setup lang="ts">
  import { watchEffect, computed, onMounted, PropType, provide, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { SchemaHelper } from '../schema';
  import useCustomizeComponentStore, {
    CustomizeComponentModule,
    CustomizeComponentPage,
    GetComponentOptions,
  } from './store';
  import { request } from '../request';

  const props = defineProps({
    options: {
      type: Object as PropType<GetComponentOptions>,
      default: () => ({
        client: 'Web',
      }),
    },
    module: {
      type: String as PropType<CustomizeComponentModule>,
      required: true,
    },
    page: {
      type: String as PropType<CustomizeComponentPage>,
      required: true,
    },
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const ready = ref(false);
  const store = useCustomizeComponentStore();
  const cmp = ref<any>();
  onMounted(async () => {
    const customizeCmp = await store.getComponent(props.module, props.page, props.options);
    if (customizeCmp) {
      cmp.value = customizeCmp;
    }

    ready.value = true;
  });
  const emits = defineEmits(['handleAction', 'update:modelValue']);
  const handleAction = async (action: string, val: any) => {
    switch (action) {
      case 'submit':
        break;
      default:
        break;
    }
    emits('handleAction', val);
  };
  const componentRef = ref<any>(null);
  const handleSubmit = async () => {
    const data = await componentRef.value?.handleSubmit();
    return data;
  };
  const validateRes = async () => {
    return componentRef.value?.validateRes();
  };
  const isDefaultComponent = computed(() => {
    return !cmp.value && ready.value;
  });
  const handlePrint = async () => {
    await componentRef.value.handlePrint();
  };

  const record = computed({
    get: () => props.modelValue,
    set: (val) => {
      emits('update:modelValue', val);
    },
  });

  provide('PROJECT_URLS', PROJECT_URLS);
  provide('request', request);
  provide('SchemaHelper', SchemaHelper);

  const exposed = ref<Record<string, any>>({});

  watchEffect(() => {
    const refVal = componentRef.value;

    if (!refVal || typeof refVal === 'function') {
      exposed.value = {};
      return;
    }

    const keys = Object.keys(refVal);
    const result: Record<string, any> = {};

    keys.forEach((key) => {
      result[key] = refVal[key];
    });

    exposed.value = result;
  });

  defineExpose({
    handleSubmit,
    validateRes,
    handlePrint,
    isDefaultComponent,
    exposed,
    callMethodSafely(methodName: string, ...args: any) {
      const r = componentRef.value;
      if (!r) {
        console.warn('componentRef is not ready.');
        return;
      }
      if (typeof r[methodName] === 'function') {
        // eslint-disable-next-line consistent-return
        return r[methodName](...args);
      }
    },
  });
</script>

<template>
  <component
    v-bind="$attrs"
    :is="cmp"
    v-if="cmp && ready"
    ref="componentRef"
    v-model:record="record"
    foo="bar"
    @handle-action="handleAction"
  />
  <slot v-else-if="ready"></slot>
</template>

<style scoped lang="scss"></style>
