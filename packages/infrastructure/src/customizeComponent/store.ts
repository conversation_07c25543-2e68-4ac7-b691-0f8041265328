import { defineStore } from 'pinia';
import { PROJECT_URLS } from '@repo/env-config';
import { Component, defineAsyncComponent } from 'vue';
import * as Vue from 'vue';
import { loadModule } from 'vue3-sfc-loader';
import { request } from '../request';

export type CustomizeComponentModule =
  | 'SendEducationPlan'
  | 'IEP'
  | 'SendEducationRecord'
  | 'Student'
  | 'SpecialCommitteeTeacherClient'
  | 'ResettlementApplication'
  | 'SpecialCommitteeManagerClient';
export type CustomizeComponentPage = 'Edit' | 'View' | 'Print' | string;
export type CustomizeComponentClient = 'UniAPP' | 'Web';
export type CustomizeComponentConfig = {
  api: string;
  pages: Record<CustomizeComponentPage, Component | undefined>;
};
export type GetComponentOptions = {
  client?: CustomizeComponentClient;
  path?: string;
  moduleCaches?: Record<string, any>;
  loginSource?: string;
  guardianToken?: string;
  query?: Record<string, any>;
};

const customTags: string[] = ['boolean-display'];

const useCustomizeComponentStore = defineStore('customizeComponent', {
  state: (): Record<CustomizeComponentModule, CustomizeComponentConfig> => ({
    IEP: {
      api: '/resourceRoom/individualizedEducationPlan',
      pages: {
        Edit: undefined,
        View: undefined,
        Print: undefined,
      },
    },
    SendEducationPlan: {
      api: '/resourceRoom/sendEducationPlan',
      pages: {
        Edit: undefined,
        View: undefined,
        Print: undefined,
      },
    },
    SendEducationRecord: {
      api: '/resourceRoom/sendEducationRecord',
      pages: {
        Edit: undefined,
        View: undefined,
        Print: undefined,
      },
    },
    Student: {
      api: '/resourceRoom/student',
      pages: {
        Edit: undefined,
        View: undefined,
        Print: undefined,
        ResettlementApply: undefined,
      },
    },
    SpecialCommitteeTeacherClient: {
      api: '/specialCommittee/placementReport',
      pages: {
        Edit: undefined,
        View: undefined,
        Print: undefined,
      },
    },
    ResettlementApplication: {
      api: '/resourceRoom/student',
      pages: {
        View: undefined,
      },
    },
    SpecialCommitteeManagerClient: {
      api: '/specialCommittee/placementReport',
      pages: {
        Print: undefined,
      },
    },
  }),
  actions: {
    async getComponent(
      module: CustomizeComponentModule,
      page: CustomizeComponentPage,
      options?: GetComponentOptions,
    ): Promise<undefined | Component> {
      const client = options?.client || 'Web';
      let component = this[module].pages[page];
      const headers: any = {};
      if (options?.loginSource) {
        headers['Login-Source'] = options.loginSource;
      }
      if (options?.guardianToken) {
        headers['Guardian-Token'] = `Bearer ${options.guardianToken}`;
        headers.Authorization = '';
      }
      if (!component) {
        const { data } = await request('/resourceRoom/centralConfiguration/customizeComponent', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            page,
            client,
            module,
            ...options?.query,
          },
          headers,
        });

        if (!data?.trim()) {
          return undefined;
        }

        // 配置 vue3-sfc-loader
        const sfcLoaderOptions = {
          moduleCache: Object.assign(Object.create(null), {
            vue: Vue,
            ...(options?.moduleCaches || {}),
          }),
          devMode: true,
          isCustomElement: (tag: string) => {
            return customTags.includes(tag);
          },
          async getFile() {
            return {
              getContentData() {
                return data
                  .replace(/x3C/g, '<')
                  .replace(/x3E/g, '>')
                  .replace(/x22/g, '"')
                  .replace(/x27/g, "'")
                  .replace(/x3D/g, '=')
                  .replace(/x2F/g, '/')
                  .replace(/x60/g, '`')
                  .replace(/x3A/g, ':');
              },
            };
          },
          addStyle(textContent: string) {
            // @todo
          },
        };

        const path = `${client}-${module}-${page}.vue`;
        component = defineAsyncComponent(async () => {
          const m = await loadModule(path, sfcLoaderOptions);
          return m;
        });
      }

      this[module].pages[page] = component;

      return component;
    },
  },
});

export default useCustomizeComponentStore;
