import { PROJECT_URLS } from '@repo/env-config';
import { TOKEN_CACHE_KEY, USER_ID_CACHE_KEY } from '../constants';
import { storage } from '../adapter';
// eslint-disable-next-line import/no-cycle
import { useUserStore } from '../store';
import { clientRedirectTo, getElectronApi, isElectron } from '../electron';

const isLogin = () => {
  return !!storage.getItem(TOKEN_CACHE_KEY);
};

const clearToken = () => {
  storage.removeItem(TOKEN_CACHE_KEY);
  storage.removeItem(USER_ID_CACHE_KEY);
};

const getToken = () => {
  const expire = storage.getItem(USER_ID_CACHE_KEY);
  if (expire && new Date().getTime() > Number.parseInt(expire, 10)) {
    clearToken();
    return undefined;
  }
  return storage.getItem(TOKEN_CACHE_KEY);
};
const setToken = (token: string | undefined, expireTime?: any) => {
  if (token === undefined) {
    clearToken();
    return;
  }
  storage.setItem(TOKEN_CACHE_KEY, token);
  if (expireTime) {
    storage.setItem(USER_ID_CACHE_KEY, expireTime);
  }
};

const loginRedirect = (token, expireTime, userInfo, redirectTo?: string) => {
  const courseUrl = `${PROJECT_URLS.GO_PROJECT}/?token=${token}&expire=${expireTime}`;
  const userStore = useUserStore();
  switch (redirectTo) {
    case 'course':
      // @ts-ignore
      if (isElectron) {
        getElectronApi().send('login', token);
      } else {
        window.top.location.href = courseUrl;
      }

      break;
    default:
      if (userStore.isAuthorized('adminAbility:managerClient', userInfo?.authorities || [])) {
        // @ts-ignore
        if (isElectron) {
          getElectronApi().send('login', token);
        } else {
          window.top.location.href = courseUrl;
        }
      } else {
        // @ts-ignore
        window.top.location.href = '/app.html';
      }
      break;
  }
};

export { isLogin, getToken, setToken, clearToken, loginRedirect };
