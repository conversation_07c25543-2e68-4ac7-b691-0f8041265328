/* eslint-disable @typescript-eslint/no-var-requires */

// const { generateService } = require('@umijs/openapi');
import { generateService } from '@umijs/openapi';
import proc from 'child_process';
import fs from 'fs';

generateService({
  schemaPath: 'http://127.0.0.1:5233/swagger/doc.json',
  serversPath: './packages/infrastructure/src/openapi',
  enumStyle: 'enum',
  requestImportStatement: "import { request } from '../../request';",
  hook: {
    customFunctionName: (data: any) => {
      const funcName = data.operationId.split('_')[0];
      return `${funcName}`;
    },
    customTypeName: (data: any) => {
      const typeName = `${data.operationId}_params`;
      return `${typeName}`;
    },
  },
});
setTimeout(() => {
  fs.readFile('./packages/infrastructure/src/openapi/api/typings.d.ts', 'utf8', (readErr: any, data: any) => {
    if (readErr) {
      return console.log(readErr);
    }
    const result = `/* eslint-disable no-use-before-define */\n/* eslint-disable no-shadow */\n${data}`;
    fs.writeFile('./packages/infrastructure/src/openapi/typings.d.ts', result, 'utf8', (writeErr: any) => {
      if (writeErr) console.log(writeErr);
      return null;
    });
    fs.rm('./packages/infrastructure/src/openapi/api/typings.d.ts', (err: any) => {
      if (err) {
        console.log(err);
      }
    });

    proc.exec('git add packages/infrastructure/src/openapi/*');

    return null;
  });
}, 2000);
