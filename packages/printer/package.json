{"name": "@repo/printer", "version": "2.1.50", "private": true, "exports": {".": "./index.ts"}, "dependencies": {"@claviska/jquery-minicolors": "^2.3.6", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/ui": "workspace:*", "@types/jquery": "^3.5.30", "@wtto00/html2canvas": "^1.4.3", "bwip-js": "^4.3.2", "canvg": "^4.0.2", "jquery": "^3.7.1", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "nzh": "^1.0.12", "vue-plugin-hiprint": "^0.0.56"}, "devDependencies": {"vue": "^3.0.0"}, "peerDependencies": {"vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {}}