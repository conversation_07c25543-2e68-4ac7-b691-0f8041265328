<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { hiprint as hiPrint } from 'vue-plugin-hiprint';
  import { getJQuery } from './bootstrap';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    template: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'update:modelValue', 'print']);
  const printTemplate = computed(() => {
    return props.template;
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  watch(
    () => props.modelValue,
    (value) => {
      if (value?.length) {
        // const jQuery = getJQuery();
        // const htmlList = value.map((item) => {
        //   return props.hiPrintTemplate.getHtml();
        // });
        // jQuery('#repo-printer-preview').html(htmlList.join(''));
      }
    },
  );

  const hiPrintTemplate = ref<any>(null);
  watch(
    () => printTemplate.value,
    (template) => {
      hiPrintTemplate.value = new hiPrint.PrintTemplate({
        template,
      });
    },
  );

  const handlePrint = (printData: any) => {
    hiPrintTemplate.value.print(printData);
  };

  onMounted(() => {
    hiPrintTemplate.value = new hiPrint.PrintTemplate({
      template: printTemplate.value,
    });
    // handlePrint();
  });

  defineExpose({
    handlePrint,
  });
</script>

<!--<template>-->
<!--  <a-modal v-model:visible="modalVisible" width="80%" title="打印预览" @ok="handlePrint">-->
<!--    <div id="repo-printer-preview"></div>-->
<!--  </a-modal>-->
<!--</template>-->

<style lang="scss">
  #repo-printer-preview {
    overflow: scroll;
    background: #f2f2f2;
    .hiprint-printPaper {
      margin: 10px auto;
      background: #fff;
    }
    .page-break {
      page-break-after: always;
    }
  }
</style>
