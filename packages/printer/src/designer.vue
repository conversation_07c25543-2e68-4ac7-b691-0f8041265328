<script setup lang="ts">
  import { computed, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue';
  import { IconZoomIn, IconZoomOut } from '@arco-design/web-vue/es/icon';
  import { hiprint as hiPrint } from 'vue-plugin-hiprint';
  import $ from 'jquery';
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import dayjs from 'dayjs';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import UploaderButton from '@repo/ui/components/upload/uploader.vue';
  import panels from './designer/panels';
  import {
    BackgroundPositionOptions,
    BackgroundRepeatOptions,
    BackgroundSizeOptions,
    Papers,
    Scales,
    ZoomInScales,
    ZoomOutScales,
  } from './designer/constants';
  import Preview from './preview.vue';
  import { PrinterInit } from './bootstrap';

  const props = defineProps({
    provider: {
      type: Object,
      required: true,
    },
    modelValue: {
      type: Object,
      required: true,
    },
    typeCode: {
      type: String,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'update:modelValue']);

  const ready = ref(false);
  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const oss = getOssProcessor();
  const jsonConfig = ref<any>(props.modelValue?.content || {});

  const hiPrintTemplate = ref<any>(null);
  const previewRef = ref<any>(null);
  const previewVisible = ref(false);
  const pageScale = ref(1);
  const loading = ref(false);
  const designData = ref<any>({
    paper: Papers[1],
    scale: Scales[5],
    vertical: true,
    background: {},
    ...props.modelValue,
  });
  const testData = computed(() => {
    const data = {};
    props.provider.groups.forEach((group) => {
      group.elements.forEach((element) => {
        if (element.tid && element.field) {
          data[element.field] = element.options?.testData || `{{${element.title}}`;
        }
      });
    });

    return data;
  });

  const currentTemplateBackground = computed(() => {
    return {
      image: designData.value.background?.image,
      size: BackgroundSizeOptions.find((item) => item.value === designData.value.background?.size)?.label,
      repeat: BackgroundRepeatOptions.find((item) => item.value === designData.value.background?.repeat)?.label,
      position: BackgroundPositionOptions.find((item) => item.value === designData.value.background?.position)?.label,
    };
  });

  const init = async () => {
    hiPrint.init({
      // eslint-disable-next-line new-cap
      providers: [props.provider.provider],
    });

    hiPrint.setConfig({});
    hiPrint.setConfig({
      movingDistance: 2.5,
    });

    hiPrintTemplate.value = new hiPrint.PrintTemplate({
      template: props.modelValue.content || panels || {},
      scale: 2,
      settingContainer: '#PrintElementOptionSetting',
      fontList: [
        { title: '微软雅黑', value: 'Microsoft YaHei' },
        { title: '黑体', value: 'STHeitiSC-Light' },
        { title: '思源黑体', value: 'SourceHanSansCN-Normal' },
        { title: '宋体', value: 'SimSun' },
        { title: '华为楷体', value: 'STKaiti' },
      ],
      history: true,
      onDataChanged: (type, json) => {
        designData.value.content = {
          ...designData.value.content,
          ...json,
        };
      },
      onImageChooseClick: async (target) => {
        if (!window.showOpenFilePicker) {
          Modal.confirm({
            title: '提示',
            content: '当前浏览器不支持此处文件上传，请使用 Chrome 内核浏览器如 Chrome、Edge 等',
            okText: '下载 Edge',
            cancelText: '我知道了',
            onOk() {
              window.open('https://www.microsoft.com/zh-cn/edge');
            },
          });
          return;
        }
        const files = await window.showOpenFilePicker({
          types: [
            {
              description: 'Images',
              accept: {
                'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
              },
            },
          ],
          excludeAcceptAllOption: true,
          multiple: false,
        });

        Message.info('上传中...请稍后');
        const file = await files[0].getFile();
        const url = await oss.uploadSimply(
          {
            file,
          },
          `attachments/${dayjs().format('YYYYMMDD')}/`,
        );

        target.refresh(url, {
          // auto: true, // 根据图片宽高自动等比(宽>高?width:height)
          // width: true, // 按宽调整高
          // height: true, // 按高调整宽
          real: true, // 根据图片实际尺寸调整(转pt)
        });
      },
    });

    hiPrintTemplate.value?.design('#hiprint-printTemplate', { grid: true });
    hiPrint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'));

    $('.hiprint-printPaper').append('<div class="printer-designer-background-image"></div>');
  };

  const handlePaperChange = (paper) => {
    const oldPaper = designData.value.paper;
    designData.value.paper = {
      ...paper,
    };
    if (paper.name === 'CUSTOM') {
      designData.value.paper.width = oldPaper.width;
      designData.value.paper.height = oldPaper.height;
    }

    hiPrintTemplate.value?.setPaper(designData.value.paper.width, designData.value.paper.height);
  };

  const handlePreview = () => {
    jsonConfig.value = hiPrintTemplate.value.getJson();
    previewVisible.value = true;
    setTimeout(() => {
      previewRef.value?.handlePrint(testData.value);
      previewVisible.value = false;
    }, 500);
  };

  const handleBackgroundChanged = (urlList: any[]) => {
    designData.value = {
      ...designData.value,
      background: {
        image: urlList[0].url,
        repeat: designData.value.background?.repeat || 'no-repeat',
        size: designData.value.background?.size || 'cover',
        position: designData.value.background?.position || 'center center',
      },
    };
  };

  const handleSaveTemplate = async () => {
    const data = {
      ...props.modelValue,
      ...designData.value,
      content: hiPrintTemplate.value.getJson(),
    };
    if (!data.typeCode && props.typeCode) {
      data.typeCode = props.typeCode;
    }
    loading.value = true;
    try {
      let url = '/common/printTemplate';
      if (props.modelValue.id) {
        url += `/${props.modelValue.id}`;
      }
      const { data: result } = await request(url, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...data,
          content: JSON.stringify(data.content),
        },
        method: props.modelValue.id ? 'PUT' : 'POST',
      });
      emit('update:modelValue', {
        ...result,
        content: JSON.parse(result.content || '{}'),
      });
    } finally {
      loading.value = false;
    }
  };

  onBeforeMount(async () => {
    await PrinterInit();
    ready.value = true;
  });

  watch(
    () => ready.value,
    (value) => {
      if (value) {
        nextTick(() => {
          init();
        });
      }
    },
  );

  watch(
    () => pageScale.value,
    (value) => {
      hiPrintTemplate.value?.zoom(value || 1);
    },
  );

  watch(
    () => props.modelValue,
    (value) => {
      jsonConfig.value = value?.content || {};
      designData.value = {
        ...designData.value,
        ...value,
      };
    },
  );

  watch(
    () => designData.value.background,
    (value) => {
      if (value.image) {
        setTimeout(() => {
          $('.printer-designer-background-image').css({
            backgroundImage: `url(${value.image})`,
            backgroundSize: value.size,
            backgroundRepeat: value.repeat,
            backgroundPosition: value.position,
          });
        }, 1000);
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  defineExpose({
    rotatePaper: () => hiPrintTemplate.value?.rotatePaper(),
    hiPrintTemplate,
  });

  onMounted(() => {
    if (designData.value.background) {
      designData.value.background.visible = true;
    }
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    class="designer-modal-wrapper"
    fullscreen
    title="打印设计器"
    :mask-closable="false"
    :closable="false"
    ok-text="完成"
    :ok-loading="loading"
    :on-before-ok="handleSaveTemplate"
  >
    <template #title>
      <div class="flex w-full items-center">
        <div class="flex-1">打印设计器</div>
        <div class="flex gap-2 items-center">
          <div class="flex-1"> </div>
          <a-tooltip content="仅在设计时使用">
            <uploader-button
              ref="localUploadRef"
              button-text="背景图片"
              save-type="printer-designer-background"
              accept="image/*"
              :sub-folder="`${dayjs().format('YYYYMMDD')}`"
              @uploaded="handleBackgroundChanged"
            >
            </uploader-button>
          </a-tooltip>
          <a-dropdown v-if="currentTemplateBackground.image">
            <a-tooltip content="背景尺寸">
              <a-button>{{ currentTemplateBackground?.size || '背景尺寸' }}</a-button>
            </a-tooltip>
            <template #content>
              <a-doption
                v-for="(item, idx) in BackgroundSizeOptions"
                :key="idx"
                @click="() => (designData.background.size = item.value)"
              >
                {{ item.label }}
              </a-doption>
            </template>
          </a-dropdown>
          <a-dropdown v-if="currentTemplateBackground.image">
            <a-tooltip content="背景重复">
              <a-button>{{ currentTemplateBackground?.repeat || '背景重复' }}</a-button>
            </a-tooltip>
            <template #content>
              <a-doption
                v-for="(item, idx) in BackgroundRepeatOptions"
                :key="idx"
                @click="() => (designData.background.repeat = item.value)"
              >
                {{ item.label }}
              </a-doption>
            </template>
          </a-dropdown>
          <a-dropdown v-if="currentTemplateBackground.image">
            <a-tooltip content="背景位置">
              <a-button>{{ currentTemplateBackground?.position || '背景位置' }}</a-button>
            </a-tooltip>
            <template #content>
              <a-doption
                v-for="(item, idx) in BackgroundPositionOptions"
                :key="idx"
                @click="() => (designData.background.position = item.value)"
              >
                {{ item.label }}
              </a-doption>
            </template>
          </a-dropdown>
          <a-tooltip v-if="currentTemplateBackground.image" content="清除背景图片">
            <a-button status="danger">
              <template #icon>
                <IconDelete />
              </template>
            </a-button>
          </a-tooltip>
          <a-select :model-value="designData.paper.name" size="mini" style="width: 130px">
            <a-option
              v-for="(p, idx) in Papers"
              :key="idx"
              :value="p.name"
              :label="p.label || p.name"
              @click="() => handlePaperChange(p)"
            >
              {{ p.label || p.name }}
            </a-option>
            <template #prefix>纸张</template>
          </a-select>
          <div v-if="designData.paper.name === 'CUSTOM'" class="flex">
            <a-input-number v-model="designData.paper.width" size="mini" placeholder="宽度">
              <template #suffix>mm</template>
            </a-input-number>
            <span class="px-1"> x </span>
            <a-input-number v-model="designData.paper.height" size="mini" placeholder="高度">
              <template #suffix>mm</template>
            </a-input-number>
          </div>
          <a-tooltip content="旋转纸张">
            <a-button size="mini" @click="() => hiPrintTemplate.rotatePaper()">
              <template #icon>
                <IconRotateRight />
              </template>
            </a-button>
          </a-tooltip>
          <div class="flex">
            <a-button size="mini" @click="() => (pageScale = ZoomOutScales(pageScale))">
              <template #icon> <IconZoomOut /> </template>
            </a-button>
            <a-button size="mini" @click="() => (pageScale = 1)"> {{ pageScale * 100 }}% </a-button>
            <a-button size="mini" @click="() => (pageScale = ZoomInScales(pageScale))">
              <template #icon> <IconZoomIn /> </template>
            </a-button>
          </div>

          <a-button size="mini" type="primary" @click="handlePreview">
            <template #icon>
              <IconEye />
            </template>
            打印预览
          </a-button>
        </div>
      </div>
    </template>
    <div class="flex gap-2 designer-wrapper">
      <a-card class="shrink-0 controls">
        <div class="rect-printElement-types hiprintEpContainer">
          <div v-for="(group, idx) in provider.groups" :key="idx">
            <a-divider> {{ group.label }} </a-divider>
            <a-row>
              <a-col v-for="(child, cIdx) in group.elements" :key="cIdx" :span="12" class="drag_item_box">
                <div>
                  <a class="ep-draggable-item" :tid="child.tid">
                    <Component :is="child.icon" v-if="child.icon" />
                    <p class="glyphicon-class">{{ child.title }}</p>
                  </a>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>
      <a-card class="canvas flex-1">
        <div id="hiprint-printTemplate"></div>
      </a-card>
      <a-card class="shrink-0 properties">
        <div id="PrintElementOptionSetting"></div>
      </a-card>
    </div>

    <preview v-if="previewVisible" ref="previewRef" v-model:visible="previewVisible" :template="jsonConfig" />
  </a-modal>
</template>

<style scoped lang="scss">
  .controls {
    width: 200px;
    min-width: 200px;
    flex-basis: 200px;
    max-height: calc(100vh - 140px);
    overflow-y: scroll;
  }
  .properties {
    width: 250px;
    min-width: 250px;
    max-height: calc(100vh - 140px);
    flex-basis: 250px;
    overflow-y: scroll;
  }
  .canvas {
    max-width: calc(100% - 450px);
    max-height: calc(100vh - 140px);
    :deep .arco-card-body {
      max-height: calc(100vh - 142px);
      width: 100%;
      overflow-y: scroll;
      overflow-x: scroll;
    }
  }

  .drag_item_box {
    text-align: center;
    padding: 8px;
    margin: 8px 0;
    font-size: 12px;
    cursor: default;
    user-select: none;
    .arco-icon {
      font-size: 16px;
    }
    &:hover {
      background: #f2f2f2;
    }
  }

  .hiprintEpContainer :deep .arco-divider-text {
    font-size: 10px;
    font-weight: normal;
    text-align: center;
    padding: 0 8px;
    color: rgb(var(--primary-6));
  }
</style>

<style lang="scss">
  .hiprint_rul_wrapper {
    img {
      max-width: 100000px !important;
    }
  }
  .designer-modal-wrapper .arco-modal-body {
    overflow: hidden;
  }

  .hiprint-printPaper {
    position: relative;
    background: none;
    .hiprint-printPaper-content {
      z-index: 3;
    }
    &.design.grid:before {
      background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 3%, rgba(0, 0, 0, 0) 3%),
        linear-gradient(360deg, rgba(0, 0, 0, 0.1) 3%, rgba(0, 0, 0, 0) 3%);
      background-size: 5mm 5mm;
      background-position: left top;
      position: absolute;
      width: 100%;
      height: 100%;
      content: ' ';
      z-index: 2;
    }
    .printer-designer-background-image {
      width: 100%;
      height: 100%;
      content: ' ';
      position: absolute;
      z-index: 1;
    }
  }
</style>
