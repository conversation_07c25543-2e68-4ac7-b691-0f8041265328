import { hiprint as hiPrint, defaultElementTypeProvider } from 'vue-plugin-hiprint';
import $ from 'jquery';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

export type PrintTemplateType = 'ConferenceCertificate' | 'PaperCompetitionAward';

const PrinterInit = async () => {
  const scripts: string[] = [];
  const styles: string[] = [];
  // @ts-ignore
  if (!window.$) {
    // scripts.push('https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js');
    // scripts.push('https://cdn.bootcdn.net/ajax/libs/jquery-minicolors/2.3.6/jquery.minicolors.min.js');
    // scripts.push('/hiPrint/jquery.hiwprint.js');
    scripts.push('https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.min.js');
  }
  if (!window.hiprint) {
    styles.push('/hiPrint/print-lock.css');
    styles.push('/hiPrint/hiprint.css');
    styles.push('https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap-grid.min.css');
  }

  // await ClassicUtils.dynamicLoadStaticFiles(scripts, styles, () => {
  //   window.jQuery = window.$;
  // });
};

const getHiPrint = () => {
  return hiPrint;
};

const getJQuery = () => {
  return $;
};

const getDefaultElementTypeProvider = () => {
  return defaultElementTypeProvider;
};

const loadPrintTemplate = async (type: PrintTemplateType, typeCode?: string) => {
  const { data } = await request('/common/printTemplate', {
    params: {
      type,
      typeCode,
    },
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  });

  if (data.items?.length) {
    return {
      ...data.items[0],
      content: JSON.parse(data.items[0].content),
    };
  }

  return null;
};

export { PrinterInit, getHiPrint, getJQuery, getDefaultElementTypeProvider, loadPrintTemplate };
