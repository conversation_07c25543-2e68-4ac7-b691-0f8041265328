import { hiprint as hiPrint } from 'vue-plugin-hiprint';

export type PrinterProvider = {
  tid?: string;
  field?: string;
  options?: Record<string, any>;
  title?: string;
  label?: string;
  type?: string;
  [key: string]: any;
};

export type PrinterProviderGroup = {
  label: string;
  elements: PrinterProvider[];
};

const defaultProviders: PrinterProviderGroup[] = [
  {
    label: '控件',
    elements: [
      { tid: 'text', title: '静态文字', data: '', type: 'text' },
      { tid: 'image', title: '图片', data: '', type: 'image' },
      { tid: 'longText', title: '长文本', data: '155123456789', type: 'longText' },
      {
        tid: 'table',
        field: 'table',
        title: '表格',
        type: 'table',
        groupFields: ['name'],
        groupFooterFormatter(group, option) {
          return '这里自定义统计脚信息';
        },
        columns: [
          [
            { title: '行号', fixed: true, rowspan: 2, field: 'id', width: 70 },
            { title: '人员信息', colspan: 2 },
            { title: '销售统计', colspan: 2 },
          ],
          [
            { title: '姓名', align: 'left', field: 'name', width: 100 },
            { title: '性别', field: 'gender', width: 100 },
            { title: '销售数量', field: 'count', width: 100 },
            { title: '销售金额', field: 'amount', width: 100 },
          ],
        ],
        editable: true,
        columnDisplayEditable: true, // 列显示是否能编辑
        columnDisplayIndexEditable: true, // 列顺序显示是否能编辑
        columnTitleEditable: true, // 列标题是否能编辑
        columnResizable: true, // 列宽是否能调整
        columnAlignEditable: true, // 列对齐是否调整
        isEnableEditField: true, // 编辑字段
        isEnableContextMenu: true, // 开启右键菜单 默认true
        isEnableInsertRow: true, // 插入行
        isEnableDeleteRow: true, // 删除行
        isEnableInsertColumn: true, // 插入列
        isEnableDeleteColumn: true, // 删除列
        isEnableMergeCell: true, // 合并单元格
      },
    ],
  },
  {
    label: '辅助',
    elements: [
      { tid: 'hline', title: '横线', type: 'hline' },
      { tid: 'vline', title: '竖线', type: 'vline' },
      { tid: 'rect', title: '矩形', type: 'rect' },
      { tid: 'oval', title: '椭圆', type: 'oval' },
      { tid: 'barcode', title: '条形码', type: 'barcode' },
      { tid: 'qrcode', title: '二维码', type: 'qrcode' },
    ],
  },
];

class PrinterElementProvider {
  // @ts-ignore
  addElementTypes: (context: any) => void;
}

const getProviderGroup = (groupName: string, components: PrinterProvider[]) => {
  return new hiPrint.PrintElementTypeGroup(groupName, components);
};

const getPrinterProvider = (name: string, customGroups?: PrinterProviderGroup[]) => {
  const MyPrinterElementProvider: any = PrinterElementProvider;
  customGroups = customGroups || [];
  const groups = [...customGroups, ...defaultProviders].map((group) => {
    return {
      label: group.label,
      elements:
        group.elements?.map((child) => {
          const tid = child.tid || child.field;
          return {
            ...child,
            tid: `${name}.${tid}`,
            type: child.type || 'text',
            title: child.title || child.label,
            options: {
              field: child.field || undefined,
              ...(child.options || {}),
              testData: !child.field ? undefined : child.testData || `{{${child.title || child.label}}}`,
              hideTitle: child.hideTile === undefined ? true : child.hideTile,
            },
          };
        }) || [],
    };
  });
  MyPrinterElementProvider.addElementTypes = (context) => {
    context.addPrintElementTypes(
      name,
      groups.map((group) => getProviderGroup(group.label, group.elements)),
    );
  };

  return {
    provider: MyPrinterElementProvider,
    groups,
  };
};

export { getPrinterProvider, defaultProviders };
