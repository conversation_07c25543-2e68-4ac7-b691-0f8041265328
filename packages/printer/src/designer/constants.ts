export type PageType = {
  name: string;
  width: number;
  height: number;
  label?: string;
};

export const Orientations = [
  { label: '纵向', value: 'portrait' },
  { label: '横向', value: 'landscape' },
];

export const Papers: PageType[] = [
  { name: 'A3', width: 297, height: 420 },
  { name: 'A4', width: 210, height: 297 },
  { name: 'A5', width: 148, height: 210 },
  { name: 'B4', width: 250, height: 353 },
  { name: 'B5', width: 176, height: 250 },
  { name: 'CUSTOM', width: 0, height: 0, label: '自定义' },
];

export const Scales: number[] = [0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.2, 1.5, 2, 3];

export const ZoomInScales = (currentScale: number): number => {
  const index = Scales.findIndex((item) => item === currentScale);
  if (index === -1) return Scales[5];
  return Scales[index + 1] || Scales[index];
};

export const ZoomOutScales = (currentScale: number): number => {
  const index = Scales.findIndex((item) => item === currentScale);
  if (index === -1) return Scales[5];
  return Scales[index - 1] || Scales[index];
};

export const BackgroundRepeatOptions = [
  { label: '不重复', value: 'no-repeat' },
  { label: '重复', value: 'repeat' },
  { label: '横向重复', value: 'repeat-x' },
  { label: '纵向重复', value: 'repeat-y' },
];

export const BackgroundSizeOptions = [
  { label: '自动', value: 'auto' },
  { label: '填充', value: 'cover' },
  { label: '适应', value: 'contain' },
];

export const BackgroundPositionOptions = [
  { label: '左上', value: 'left top' },
  { label: '左下', value: 'left bottom' },
  { label: '居中', value: 'center' },
  { label: '右上', value: 'right top' },
  { label: '右下', value: 'right bottom' },
];
