<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Modal } from '@arco-design/web-vue';
  import WangEditor from './wangEditor.vue';

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    defaultMode: {
      type: String,
      default: 'simple',
    },
    simpleType: {
      type: String,
      default: 'textarea',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const mode = ref(props.defaultMode);
  const content = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  const handleSwitchMode = () => {
    const pureContent = content.value.replace(/<[^>]+>/g, '');
    if (mode.value === 'rich' && pureContent?.trim()) {
      Modal.confirm({
        title: '切换到简单输入模式',
        content: '切换到简单输入模式会清除所有格式，是否继续？',
        onOk: () => {
          mode.value = 'simple';
          content.value = pureContent;
        },
      });
      return;
    }
    if (mode.value === 'rich') {
      mode.value = 'simple';
      content.value = pureContent;
      return;
    }
    mode.value = mode.value === 'simple' ? 'rich' : 'simple';
  };
</script>

<script lang="ts">
  export default {
    name: 'SimpleOrRichEditor',
  };
</script>

<template>
  <div class="relative">
    <a-input
      v-if="mode === 'simple' && simpleType === 'text'"
      v-bind="$attrs"
      v-model.trim="content"
      :type="simpleType"
    />
    <a-textarea
      v-else-if="mode === 'simple' && simpleType === 'textarea'"
      v-bind="$attrs"
      v-model="content"
      class="block"
      :auto-size="{
        minRows: 3,
        maxRows: 10,
      }"
    />
    <wang-editor v-else v-bind="$attrs" v-model.trim="content" :editor-style="{ minHeight: '200px' }" />
    <icon-voice v-if="false" />
    <a-button type="text" class="absolute switch-button" size="mini" @click="handleSwitchMode">
      使用{{ mode === 'simple' ? '高级输入模式' : '简单输入模式' }}
    </a-button>
  </div>
</template>

<style scoped lang="scss">
  .switch-button {
    opacity: 0.3;
    bottom: 0;
    right: 0;
    z-index: 2;
    position: absolute;
    &:hover {
      opacity: 0.8;
    }
  }
</style>
