<script lang="ts" setup>
  import { computed, onBeforeUnmount, PropType, ref, shallowRef } from 'vue';
  import '@wangeditor/editor/dist/css/style.css';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import { IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'default',
    },
    toolbarConfig: {
      type: Object,
      default: () => ({}),
    },
    editorConfig: {
      type: Object,
      default: () => ({
        placeholder: '请输入内容...',
      }),
    },
    toolbarStyle: {
      type: Object,
      default: () => ({
        borderBottom: '1px solid #e2e2e2',
      }),
    },
    excludeKeys: {
      type: Array as PropType<string[]>,
      default: () => ['bgColor', 'fontFamily', 'lineHeight', 'group-indent', 'codeBlock'],
    },
    editorStyle: {
      type: Object,
      default: () => ({}),
    },
    showToolbar: {
      type: Boolean,
      default: true,
    },
  });

  const { setLoading, loading: uploading } = useLoading();
  const uploadPercentage = ref(0);
  const ossProvider: IAttachmentProcessor = getOssProcessor();
  const editorRef = shallowRef();
  const toolbarConfig = computed<Partial<IToolbarConfig>>(() => {
    return {
      excludeKeys: props.excludeKeys || [],
      ...props.toolbarConfig,
    };
  });

  const uploadConfig = {
    uploadImage: {
      customUpload: async (file: File, insertFn: any) => {
        try {
          setLoading(true);
          const url = await ossProvider.uploadSimply({
            file,
          });

          insertFn(url, '', null);
        } catch (error: any) {
          Message.error(`上传失败: ${error.message || '未知错误'}`);
        } finally {
          setLoading(false);
        }
      },
    },
    uploadVideo: {
      customUpload: async (file: File, insertFn: any) => {
        try {
          Message.info('视频上传中，请稍后...');
          setLoading(true);
          const url = await ossProvider.uploadLarge({
            file,
            handleUploadProgress: (progress) => {
              uploadPercentage.value = progress;
            },
            handleUploadComplete: (url) => {
              insertFn(url, '', null);
            },
          });
        } catch (error: any) {
          Message.error(`上传失败: ${error.message || '未知错误'}`);
        } finally {
          setLoading(false);
        }
      },
    },
  };
  const editorConfig = computed<Partial<IEditorConfig>>(() => {
    return {
      ...props.editorConfig,
      MENU_CONF: {
        ...props.editorConfig?.MENU_CONF,
        ...uploadConfig,
      },
    };
  });

  const emit = defineEmits(['update:modelValue']);

  const htmlContent = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  const editorStyleComputed = computed(() => {
    return {
      minHeight: '300px',
      maxHeight: '500px',
      overflowY: 'scroll',
      background: '#fff',
      ...props.editorStyle,
    };
  });

  const handleCreated = (editorInstance: any) => {
    editorRef.value = editorInstance;
  };

  onBeforeUnmount(() => {
    editorRef.value?.destroy();
  });
</script>

<script lang="ts">
  export default {
    name: 'WangEditor',
  };
</script>

<template>
  <div class="wang-editor-wrapper">
    <a-progress v-if="uploading" :percent="uploadPercentage * 100" class="mx-4" />
    <Toolbar
      v-if="showToolbar"
      :style="toolbarStyle"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <Editor
      v-model="htmlContent"
      :style="editorStyleComputed"
      :default-config="editorConfig"
      :mode="mode"
      @on-created="handleCreated"
    />
  </div>
</template>

<style scoped lang="scss">
  .wang-editor-wrapper {
    border: 1px solid #e2e2e2;
    background: #fff;
    &.w-e-full-screen-container {
      height: 100vh;
    }
  }
</style>

<style lang="scss" scoped>
  @use '@repo/infrastructure/ui/styles/richText.scss' as *;
  .wang-editor-wrapper.w-e-full-screen-container {
    z-index: 4000;
    > div:nth-child(2) {
      max-height: unset !important;
    }
  }
  .w-e-text-container {
    line-height: 180%;
  }
</style>
