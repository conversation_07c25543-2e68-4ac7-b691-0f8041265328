<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import SimpleOrRichEditor from './simpleOrRichEditor.vue';
  import WangEditor from './wangEditor.vue';

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    editing: {
      type: Boolean,
      default: false,
    },
    editorModeSwitch: {
      type: Boolean,
      default: false,
    },
    onBeforeEdit: {
      type: Function as PropType<() => Promise<void>>,
      default: async () => {
        return true;
      },
    },
  });

  const emit = defineEmits(['update:modelValue', 'save', 'update:editing', 'startEditing']);
  const inputValue = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });

  /*  const isEmpty = computed(() => {
    // strip tags and trim
    return !inputValue.value?.replace(/<[^>]+>/g, '')?.trim();
  }); */
  const isEmpty = computed(() => {
    const { value } = inputValue;

    if (!value) return true;

    const hasImage = /<img[^>]+>/i.test(value);

    const textContent = value.replace(/<[^>]+>/g, '').trim();

    return !(hasImage || textContent);
  });

  const editingValue = ref(false);

  const handleSave = () => {
    emit('save');
    editingValue.value = false;
  };
</script>

<template>
  <div>
    <div v-if="editingValue">
      <simple-or-rich-editor v-if="editorModeSwitch" v-model="inputValue" v-bind="$attrs" />
      <wang-editor v-else v-model="inputValue" v-bind="$attrs" />
      <a-button size="mini" class="mt-2" type="primary" @click="handleSave">
        <template #icon>
          <IconSave />
        </template>
        保存
      </a-button>
    </div>
    <a-empty
      v-else-if="isEmpty"
      description="暂无内容，双击此处进行添加或修改"
      @dblclick="() => (editingValue = true)"
    />
    <div v-else class="p-3 bg-slate-50 rounded-lg relative display">
      <a-link size="mini" type="text" class="absolute action" @click="() => (editingValue = true)">
        <template #icon>
          <IconEdit />
        </template>
        点击此处或双击文本区域修改
      </a-link>
      <div class="rich-text-wrapper" @dblclick="() => (editingValue = true)">
        <slot :raw="inputValue">
          <div v-html="inputValue" />
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  @use '@repo/infrastructure/ui/styles/richText.scss' as *;
  .display {
    .action {
      position: absolute;
      right: 0;
      top: 0;
      opacity: 0;
    }
    &:hover .action {
      opacity: 0.6;
    }
  }

  .rich-text-wrapper {
    line-height: 180%;
  }
</style>
