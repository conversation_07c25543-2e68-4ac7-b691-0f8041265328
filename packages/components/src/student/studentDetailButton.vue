<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { isString } from 'lodash';
  import StudentDetailCard from './studentDetailCard.vue';

  const props = defineProps({
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    record: {
      // only in student list
      type: Object as PropType<any>,
      default: () => ({}),
    },
    size: {
      type: String as PropType<'mini' | 'small' | 'medium' | 'large'>,
      default: 'mini',
    },
    eager: {
      type: Boolean,
      default: false,
    },
    trigger: {
      type: String as PropType<'click' | 'hover'>,
      default: 'click',
    },
    width: {
      type: Number,
      default: 300,
    },
    enabled: {
      type: Boolean,
      default: true,
    },
    moreInfo: {
      type: Boolean,
      default: false,
    },
  });

  const student = computed(() => {
    if (props.raw?.id) {
      return props.raw;
    }
    if (isString(props.raw) && props.record?.id) {
      // user list
      return props.record;
    }

    return props.raw;
  });

  const visible = ref(false);

  const handlePopChange = async (v: boolean) => {
    visible.value = v;
  };

  // 'red' | 'orangered' | 'orange' | 'gold' | 'lime' | 'green' | 'cyan' | 'blue' | 'arcoblue' | 'purple' | 'pinkpurple' | 'magenta' | 'gray'
  const colors = [
    'orangered',
    'orange',
    'gold',
    'green',
    'cyan',
    'blue',
    'arcoblue',
    'purple',
    'pinkpurple',
    'magenta',
  ];

  const studentColor = computed(() => {
    // 根据ID计算颜色
    return 'arcoblue';
    // return colors[props.raw.id % colors.length];
  });
</script>

<template>
  <a-popover
    v-if="enabled"
    :trigger="trigger"
    :style="{ width: `${props.width}px` }"
    @popup-visible-change="handlePopChange"
  >
    <a-button
      v-bind="$attrs"
      :size="size"
      shape="round"
      class="!px-2 !text-xs !h-6 flex items-center"
      :color="$attrs.color || studentColor"
    >
      <template #icon>
        <IconUser />
      </template>
      <span class="mt-0.5">
        <slot>{{ student?.name }}</slot>
      </span>
    </a-button>
    <template #content>
      <slot v-if="visible" name="card">
        <student-detail-card :student="student" :eager="eager" :more-info="moreInfo" />
      </slot>
    </template>
  </a-popover>
  <span v-else>{{ student?.name }}</span>
</template>

<style scoped lang="scss"></style>
