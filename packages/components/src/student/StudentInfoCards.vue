<script setup lang="ts">
  import { onBeforeUnmount, onMounted, ref, watch, computed, inject } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { Message } from '@arco-design/web-vue';
  import { nationsList } from '@repo/infrastructure/data';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import ImageEditorUpload from '@repo/ui/components/upload/imageEditorUpload.vue';
  import FormSection from './components/FormSection.vue';
  import FormField from './components/FormField.vue';
  import OptionGroup from './components/OptionGroup.vue';
  import {
    navigationItems,
    options,
    reinforcersConfig,
    activityPreferences,
    defaultBaseInfo,
    attachmentColumns,
    healthMedicalModel,
    developHistoryModel,
    timeSchedules,
  } from './formConfig';

  const activeTab = ref('studentBasic');

  const token = inject('token');

  const isMobileMenuOpen = ref(false);

  const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value;
  };

  const closeMobileMenu = () => {
    isMobileMenuOpen.value = false;
  };

  const props = defineProps({
    studentId: {
      type: Number,
    },
    nature: {
      type: String,
      default: null,
    },
  });

  const student = ref<any>({
    fusionSchool: {},
    attachments: [],
    additionalData: {
      assessmentInstitution: '',
      currentAddress: '',
      causeOfDisability: '',
      onlyChild: false,
      baseInfo: {
        ...defaultBaseInfo,
      },
    },
  });

  const fileList = ref<any>([]);
  const rehabilitationInstitutionList = ref<any>([]);
  const committee = ref<number>();
  const homeDeliveryInstitution = ref<number>();

  const familyMembersModel = [
    {
      title: '父亲信息',
      icon: '👨',
      color: 'blue',
      modelPrefix: 'fatherInfo',
    },
    {
      title: '母亲信息',
      icon: '👩',
      color: 'pink',
      modelPrefix: 'motherInfo',
    },
  ];
  const scheduleModel = [
    { label: '做功课 (小时)', model: 'homework' },
    { label: '帮忙家务 (小时)', model: 'chores' },
    { label: '休闲 (小时)', model: 'leisure' },
    { label: '其他 (小时)', model: 'other' },
  ];

  const isPc = computed(() => {
    return window.innerWidth > 1024;
  });
  const headers = computed(() => {
    return {
      'Authorization': `Bearer ${token?.value}`,
      'Request-Client-Role': 'Company',
    };
  });

  const switchTab = (key: string) => {
    activeTab.value = key;
    if (window.innerWidth < 1024) {
      closeMobileMenu();
    }
  };

  const oss: IAttachmentProcessor = getOssProcessor();

  const checkFileSize = (file: File) => {
    if (file.size / 1024 > 1024 * 2) {
      Message.error(`文件大小不能超过 ${(1024 * 2) / 1024}MB`);
      return false;
    }
    return true;
  };
  const handleUp = async (op: any) => {
    const file = op.fileItem;
    const url = await oss.uploadSimply(file, '', {
      headers: headers.value,
    });
    const { name } = file;
    student.value.additionalData.avatar = { url, name };
  };

  const resetAttachment = () => {
    student.value.attachments.forEach((a) => {
      a.udf1 = a.url;
    });
  };

  const handleIdCardInput = (e: string) => {
    student.value.idCardNo = e.replace(/[^0-9Xx]/g, '');
    if (student.value.idCardNo.length > 0 && /^[^0-9]/.test(student.value.idCardNo)) {
      student.value.idCardNo = student.value.idCardNo.substring(1);
    }
  };

  const checkIdCard = (idCard: string): boolean => {
    if (!idCard) return false;
    idCard = idCard.toUpperCase();
    const reg = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
    if (!reg.test(idCard)) return false;

    const year = parseInt(idCard.substring(6, 10), 10);
    const month = parseInt(idCard.substring(10, 12), 10) - 1;
    const day = parseInt(idCard.substring(12, 14), 10);
    const birthday = new Date(year, month, day);
    if (birthday.getFullYear() !== year || birthday.getMonth() !== month || birthday.getDate() !== day) {
      return false;
    }

    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    let sum = 0;
    for (let i = 0; i < 17; i += 1) {
      sum += parseInt(idCard.charAt(i), 10) * factor[i];
    }
    return parity[sum % 11] === idCard.charAt(17);
  };

  const validateIdCard = (): boolean => {
    const idCard = student.value.idCardNo;
    if (!idCard || idCard.length !== 18) {
      Message.error('请输入18位身份证号码');
      return false;
    }
    if (!checkIdCard(idCard)) {
      Message.error('身份证号码格式不正确');
      return false;
    }
    return true;
  };

  const loading = ref(false);
  const handleSave = async () => {
    try {
      Message.clear('top');
      if (student.value.idCardNo) {
        if (!checkIdCard(student.value.idCardNo)) {
          Message.error('身份证号码格式不正确');
          return;
        }
      }

      if (!student.value?.name || !student.value?.fusionSchool?.id) {
        Message.warning('请完整填写后提交');
        return;
      }
      loading.value = true;
      resetAttachment();
      const url = student.value?.id ? `/resourceRoom/student/${student.value.id}` : '/resourceRoom/student';
      const method = student.value?.id ? `put` : 'post';
      const { data } = await request(url, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method,
        headers: headers.value,
        params: {
          additionalData: 'true',
        },
        data: student.value,
      });
      student.value = data;
      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };
  const deepMerge = (target: any, source: any) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const key in source) {
      if (source[key] instanceof Object && !Array.isArray(source[key])) {
        target[key] = deepMerge(target[key] || {}, source[key]);
      } else {
        target[key] = source[key];
      }
    }
    return target;
  };

  const loadStudent = async () => {
    if (!props.studentId) return;
    try {
      const { data: res } = await request(`/resourceRoom/student/${props.studentId}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        headers: headers.value,
        params: {
          additionalData: 'true',
        },
      });

      student.value = deepMerge(
        {
          fusionSchool: {},
          attachments: [],
          additionalData: {
            baseInfo: defaultBaseInfo,
          },
        },
        res,
      );
    } finally {
      /**/
    }
  };
  const boOptions = ref<any>([]);
  const classOptions = ref<any>([]);
  const loadFs = async () => {
    const { data: res } = await request('/resourceCenter/fusionSchool', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      headers: headers.value,
      params: {
        pageSize: 999,
        nature: props.nature,
      },
    });
    boOptions.value = res.items.map((item) => ({ label: item.name, value: item.id, raw: item }));
  };

  const handleAddColumn = () => {
    student.value.attachments.push({
      type: '',
      attachments: [],
    });
  };

  const classLoading = ref(false);
  const boCatch = ref<any>({});
  const loadClass = async (boId: number) => {
    if (!boId) return;
    try {
      classLoading.value = true;
      const { data: res } = await request('/resourceRoom/gradeClass', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        headers: headers.value,
        params: {
          branchOfficeId: boId,
        },
      });
      boCatch[boId] = res.items?.map((item) => ({ label: item.name, value: item.id, raw: item }));
      classOptions.value = boCatch[boId];
    } finally {
      classLoading.value = false;
    }
  };

  const handleDel = (rowIndex: number) => {
    student.value?.attachments.splice(rowIndex, 1);
  };
  onMounted(async () => {
    await Promise.all([loadStudent(), loadFs()]);
    await loadClass(student.value?.fusionSchool?.branchOfficeId);
  });
  onBeforeUnmount(async () => {
    // await handleSave();
  });

  watch(
    () => student.value,
    (newVal: any) => {
      if (newVal?.additionalData?.avatar) fileList.value = [newVal?.additionalData?.avatar];
      rehabilitationInstitutionList.value = student?.value?.rehabilitationInstitutionList?.map((r) => r?.id) || [];
      committee.value = student?.value.committee?.id;
      homeDeliveryInstitution.value = student?.value.homeDeliveryInstitution?.id;
    },
  );
  watch(
    () => student.value.fusionSchool.id,
    async (newVal) => {
      const boId =
        boOptions.value.find((item) => item.value === student.value.fusionSchool.id)?.raw?.branchOfficeId || null;
      await loadClass(boId);
      console.log('bb');
    },
    { deep: true },
  );

  watch(
    () => headers.value,
    async (val) => {
      if (!student.value.id) {
        await Promise.all([loadStudent(), loadFs()]);
        await loadClass(student.value?.fusionSchool?.branchOfficeId);
        console.log('aaa');
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div class="flex h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative w-full">
    <button
      :class="[
        'lg:hidden fixed top-4 right-4 z-50    text-black ',
        '  transition-all duration-200',
        'flex items-center justify-center',
      ]"
      @click="toggleMobileMenu"
    >
      <icon-menu v-if="!isMobileMenuOpen" size="15" />
      <icon-close v-else class="" size="15" />
      <span>菜单</span>
    </button>

    <div
      v-if="isMobileMenuOpen"
      class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300"
      @click="closeMobileMenu"
    />
    <div
      :class="[
        'bg-white shadow-xl border-r border-gray-200 transition-all duration-300 z-40',
        'lg:w-80 lg:flex-shrink-0 lg:relative lg:transform-none',
        'fixed inset-y-0 left-0 w-80 transform',
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
      ]"
    >
      <div class="hidden lg:block p-6 border-b border-gray-100">
        <h2 class="text-xl font-bold text-gray-900">{{ student?.name }}信息管理</h2>
        <p class="text-sm text-gray-500 mt-2">完善学生档案信息</p>
      </div>

      <nav class="p-2 lg:p-4" :class="isPc ? '' : 'mt-4'">
        <ul class="space-y-1 lg:space-y-3">
          <li v-for="(item, index) in navigationItems" :key="item.key">
            <button
              v-if="item.key !== 'attachments' || (isPc && item.key === 'attachments')"
              :class="[
                'w-full flex items-center text-left transition-all  duration-100 group relative',
                'p-2 lg:p-4 border rounded-lg lg:rounded-xl border-white border-dotted',
                activeTab === item.key
                  ? 'shadow-lg lg:transform lg:scale-105 border-gray-500'
                  : 'hover:bg-gray-50 hover:shadow-md text-gray-700',
              ]"
              @click="switchTab(item.key)"
            >
              <div class="flex-1 flex items-center lg:flex-col lg:items-start">
                <div class="flex items-center flex-1">
                  <div
                    :class="[
                      'rounded-lg flex items-center justify-center  transition-all duration-300 flex-shrink-0',
                      'w-8 h-8 text-sm lg:w-10 lg:h-10 lg:text-lg',
                      'mr-3 lg:mr-4',
                      item.color,
                    ]"
                  >
                    {{ item.icon }}
                  </div>
                  <div>
                    <div class="flex-1 min-w-0">
                      <span class="font-semibold text-xs lg:text-sm block truncate">{{ item.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs font-medium px-2 py-1 rounded-full flex-shrink-0 ml-2 bg-gray-100 text-gray-600">
                {{ index + 1 }}/7
              </div>
            </button>
          </li>
        </ul>
      </nav>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 overflow-y-auto content-area lg:ml-0">
      <a-spin class="w-full">
        <!-- Mobile Header -->
        <div class="lg:hidden bg-white border-b border-gray-200 p-4 pt-20 sticky top-0 z-20">
          <h2 class="text-lg font-bold text-gray-900">{{ student?.name }}信息管理</h2>
          <p class="text-sm text-gray-500 mt-1">完善学生档案信息</p>
        </div>

        <div class="p-4 lg:p-8">
          <!-- 学生基础信息 -->
          <FormSection
            v-if="activeTab === 'studentBasic'"
            title="学生基础信息"
            module-number="1/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              <FormField label="姓名" required>
                <a-input v-model="student.name" placeholder="请输入学生姓名" size="large" />
              </FormField>

              <FormField label="性别">
                <a-select v-model="student.gender" placeholder="请选择性别" size="large">
                  <a-option v-for="option in options.gender" :key="option" :value="option">
                    {{ option }}
                  </a-option>
                </a-select>
              </FormField>

              <FormField label="民族">
                <a-select v-model="student.nation" placeholder="请选择民族" size="large" :options="nationsList" />
              </FormField>

              <FormField label="出生日期">
                <a-date-picker v-model="student.birthday" placeholder="年 / 月 / 日" size="large" class="w-full" />
              </FormField>

              <FormField label="身份证号">
                <a-input
                  v-model="student.idCardNo"
                  placeholder="请输入身份证号"
                  size="large"
                  :max-length="18"
                  show-word-limit
                  @input="handleIdCardInput"
                  @blur="validateIdCard"
                />
              </FormField>

              <FormField label="学籍所在学校" required>
                <a-select v-model="student.fusionSchool.id" :options="boOptions" allow-search />
              </FormField>
              <FormField label="学生班级">
                <span
                  v-if="student?.gradeClass?.id && classOptions.map((c) => c.value).includes(student?.gradeClass?.id)"
                >
                  {{ classOptions.find((c) => c.value === student?.gradeClass?.id)?.label }}
                </span>
                <span v-else-if="student?.gradeClass?.id">班级信息不正确</span>
                <span v-else>暂未分班</span>
              </FormField>

              <FormField label="学籍号">
                <a-input v-model="student.symbol" placeholder="请输入学籍号" size="large" />
              </FormField>

              <FormField label="家庭住址">
                <a-input v-model="student.additionalData.currentAddress" placeholder="请输入详细地址" size="large" />
              </FormField>
              <FormField label="送教学生">
                <a-switch v-model="student.hasSendPlan" type="round" />
              </FormField>

              <FormField
                label="学生一寸照片"
                full-width
                description="支持 JPG、PNG 格式，标准一寸照片尺寸 (2.5×3.5cm)，文件大小不超过 2MB"
              >
                <a-upload
                  accept="image/*"
                  :on-before-upload="checkFileSize"
                  :file-list="fileList"
                  :custom-request="handleUp"
                  :limit="1"
                  draggable
                />
                <div class="w-full flex justify-center mb-2 select-none">
                  <imageEditorUpload />
                </div>
              </FormField>
            </div>
          </FormSection>

          <!-- 残疾与鉴定信息 -->
          <FormSection
            v-if="activeTab === 'disabilityAssessment'"
            title="残疾与鉴定信息"
            module-number="2/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="space-y-6">
              <div class="bg-gradient-to-r from-purple-50 to-purple-100/50 border border-purple-200 rounded-xl p-6">
                <h4 class="text-lg font-semibold text-purple-900 mb-4 flex items-center">
                  <span
                    class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm mr-3"
                    >1</span
                  >
                  残疾类型
                </h4>
                <FormField label="残疾类别">
                  <a-select v-model="student.disorders" placeholder="请选择残疾类别" size="large">
                    <a-option v-for="option in options.disabilityCategory" :key="option" :value="option">
                      {{ option }}
                    </a-option>
                  </a-select>

                  <FormField v-if="student.disorders === '视力残疾'" label="视力障碍类型" class="mt-2">
                    <a-select v-model="student.additionalData.baseInfo.vision">
                      <a-option v-for="op in ['全盲', '低视力']" :key="op">
                        {{ op }}
                      </a-option>
                    </a-select>
                  </FormField>

                  <FormField v-if="student.disorders === '智力残疾'" label="IQ值" class="mt-2">
                    <a-input-number v-model="student.additionalData.baseInfo.IQ" placeholder="请输入IQ值" />
                  </FormField>
                  <FormField v-if="student.disorders === '多重残疾'" label="多重障碍说明" class="mt-2">
                    <a-input
                      v-model="student.additionalData.baseInfo.multiDisabilitiesDescription"
                      placeholder="请标注"
                    />
                  </FormField>

                  <div
                    v-if="student.disorders === '听力残疾'"
                    class="flex justify-center items-center mt-2 w-full space-x-2"
                  >
                    <FormField
                      v-for="item in [
                        { label: '左耳分贝值', key: 'leftEar' },
                        { label: '右耳分贝值', key: 'rightEar' },
                      ]"
                      :key="item"
                      full-width
                      :label="item.label"
                      class="w-full"
                    >
                      <a-input v-model="student.additionalData.baseInfo[item.key]" />
                    </FormField>
                  </div>
                </FormField>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                <FormField label="鉴定机构">
                  <a-input
                    v-model="student.additionalData.assessmentInstitution"
                    placeholder="请输入鉴定机构"
                    size="large"
                  />
                </FormField>

                <FormField label="致残原因">
                  <a-input
                    v-model="student.additionalData.causeOfDisability"
                    placeholder="请输入致残原因"
                    size="large"
                  />
                </FormField>

                <FormField label="残疾人证号">
                  <a-input v-model="student.disabilityCertificateNo" placeholder="请输入残疾人证号" size="large" />
                </FormField>

                <FormField label="主要障碍类别">
                  <a-select
                    v-model="student.additionalData.baseInfo.mainDisabilityCategory"
                    placeholder="请选择障碍类别"
                  >
                    <a-option v-for="option in options.obstacleType" :key="option" :value="option">
                      {{ option }}
                    </a-option>
                  </a-select>
                  <a-textarea
                    v-if="student.additionalData.baseInfo.mainDisabilityCategory === '其他'"
                    v-model="student.additionalData.baseInfo.disabilityCategoryDescription"
                    placeholder="请输入障碍类型的描述"
                  />
                </FormField>

                <FormField label="主要障碍程度">
                  <a-select v-model="student.disabilityLevel" placeholder="请选择障碍程度" size="large">
                    <a-option v-for="option in options.disabilityLevel" :key="option" :value="option">
                      {{ option }}
                    </a-option>
                  </a-select>
                </FormField>

                <FormField label="鉴定日期">
                  <a-date-picker
                    v-model="student.additionalData.baseInfo.assessmentDate"
                    placeholder="年 / 月 / 日"
                    size="large"
                    class="w-full"
                  />
                </FormField>

                <FormField label="伴随障碍" full-width>
                  <a-textarea
                    v-model="student.additionalData.baseInfo.accompanyingDisabilities"
                    placeholder="请输入伴随障碍情况"
                    :auto-size="{ minRows: 3 }"
                  />
                </FormField>
              </div>
            </div>
          </FormSection>

          <!-- 家庭成员信息 -->
          <FormSection
            v-if="activeTab === 'family'"
            title="家庭成员信息"
            module-number="3/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="space-y-8">
              <div
                v-for="member in familyMembersModel"
                :key="member.title"
                :class="[`bg-${member.color}-50 `, `border border-${member.color}-200 rounded-xl p-6 mb-6`]"
              >
                <h4 class="text-lg font-semibold text-${member.color}-900 mb-6 flex items-center">
                  <span
                    :class="[
                      `w-8 h-8 bg-${member.color}-500 rounded-full`,
                      `flex items-center justify-center text-white text-sm mr-3`,
                    ]"
                  >
                    {{ member.icon }}
                  </span>
                  {{ member.title }}
                </h4>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                  <FormField label="教育程度">
                    <a-select
                      v-model="student.additionalData.baseInfo[member.modelPrefix].education"
                      placeholder="请选择教育程度"
                      size="large"
                    >
                      <a-option v-for="option in options.education" :key="option" :value="option">
                        {{ option }}
                      </a-option>
                    </a-select>
                  </FormField>

                  <FormField label="职业">
                    <a-input
                      v-model="student.additionalData.baseInfo[member.modelPrefix].occupation"
                      placeholder="请输入职业"
                      size="large"
                    />
                  </FormField>

                  <FormField label="年龄">
                    <a-input
                      v-model="student.additionalData.baseInfo[member.modelPrefix].age"
                      placeholder="请输入年龄"
                      size="large"
                    />
                  </FormField>

                  <FormField label="联系电话">
                    <a-input
                      v-model="student.additionalData.baseInfo[member.modelPrefix].contact"
                      placeholder="请输入联系电话"
                      size="large"
                    />
                  </FormField>
                </div>
              </div>

              <!-- 监护人信息 -->
              <div class="bg-gradient-to-r from-amber-50 to-amber-100/50 border border-amber-200 rounded-xl p-6">
                <h4 class="text-lg font-semibold text-amber-900 mb-6 flex items-center">
                  <span
                    class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center text-white text-sm mr-3"
                    >👤</span
                  >
                  监护人信息
                </h4>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                  <FormField label="监护人姓名">
                    <a-input v-model="student.guardian" placeholder="请输入监护人姓名" size="large" />
                  </FormField>
                  <FormField label="联系电话">
                    <a-input v-model="student.guardianPhone" placeholder="请输入联系电话" size="large" />
                  </FormField>
                  <FormField label="联系地址" full-width>
                    <a-input
                      v-model="student.additionalData.baseInfo.guardianAddress"
                      placeholder="请输入详细地址"
                      size="large"
                    />
                  </FormField>
                </div>
              </div>
            </div>
          </FormSection>

          <!-- 健康与医疗信息 -->
          <FormSection
            v-if="activeTab === 'healthMedical'"
            title="健康与医疗信息"
            module-number="4/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="space-y-6">
              <FormField label="身体检查情况" full-width>
                <a-textarea
                  v-model="student.additionalData.baseInfo.physicalExamStatus"
                  placeholder="请详细描述身体检查情况"
                  :auto-size="{ minRows: 4, maxRows: 6 }"
                />
              </FormField>

              <OptionGroup
                v-for="item in healthMedicalModel"
                :key="item.key"
                v-model="student.additionalData.baseInfo[item.key]"
                :label="item.label"
                :options="item.options"
                :type="item.type"
                :columns="item.columns"
                bordered
              />
            </div>
          </FormSection>

          <!-- 家庭环境与发展信息 -->
          <FormSection
            v-if="activeTab === 'familyEnvironment'"
            title="家庭环境与发展信息"
            module-number="5/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="space-y-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                <FormField label="父母婚姻状况">
                  <a-select
                    v-model="student.additionalData.baseInfo.parentMaritalStatus"
                    placeholder="请选择婚姻状况"
                    size="large"
                  >
                    <a-option v-for="option in options.maritalStatus" :key="option" :value="option">
                      {{ option }}
                    </a-option>
                  </a-select>
                </FormField>

                <FormField label="家庭经济状况">
                  <a-select
                    v-model="student.additionalData.baseInfo.economicStatus"
                    placeholder="请选择经济状况"
                    size="large"
                  >
                    <a-option v-for="option in options.economicStatus" :key="option" :value="option">
                      {{ option }}
                    </a-option>
                  </a-select>
                </FormField>
              </div>

              <OptionGroup
                v-model="student.additionalData.baseInfo.livingConditions"
                label="居住条件 (多选)"
                :options="options.livingConditions"
                type="checkbox"
                :columns="isPc ? 4 : 1"
                bordered
              />

              <OptionGroup
                v-model="student.additionalData.baseInfo.familyStructure"
                label="家庭结构 (多选)"
                :options="options.familyStructure"
                type="checkbox"
                :columns="isPc ? 4 : 2"
                bordered
              />

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <OptionGroup
                  v-model="student.additionalData.onlyChild"
                  label="是否独生子女"
                  :options="options.yesNo"
                  type="radio"
                  :columns="1"
                  bordered
                />

                <OptionGroup
                  v-model="student.additionalData.baseInfo.hasIndependentSpace"
                  label="是否有独立生活/学习空间"
                  :options="options.yesNo"
                  type="radio"
                  :columns="2"
                  bordered
                />
              </div>

              <!--时间安排-->
              <div
                v-for="schedule in timeSchedules"
                :key="schedule.title"
                :class="[`bg-${schedule.color}-50 `, ` border border-${schedule.color}-200 rounded-xl p-6 mb-6`]"
              >
                <h4 class="text-lg font-semibold text-${schedule.color}-900 mb-4 flex items-center">
                  <span
                    :class="[
                      `w-6 h-6 bg-${schedule.color}-500 rounded-full`,
                      `flex items-center justify-center text-white text-sm mr-3`,
                    ]"
                  >
                    {{ schedule.icon }}
                  </span>
                  {{ schedule.title }}
                </h4>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <FormField v-for="field in scheduleModel" :key="field.model" :label="field.label">
                    <a-input-number
                      v-model="student.additionalData.baseInfo[schedule.modelPrefix][field.model]"
                      :min="0"
                      :max="24"
                      size="large"
                      class="w-full"
                    />
                  </FormField>
                </div>

                <div class="mt-4">
                  <FormField label="其他活动说明">
                    <a-input
                      v-model="student.additionalData.baseInfo[schedule.modelPrefix]['description']"
                      placeholder="请说明其他活动内容"
                      size="large"
                    />
                  </FormField>
                </div>
              </div>

              <!-- 发展历史 -->
              <div class="space-y-6">
                <FormField v-for="item in developHistoryModel" :key="item.key" :label="item.label" full-width>
                  <a-textarea
                    v-model="student.additionalData.baseInfo[item.key]"
                    :placeholder="item.placeholder"
                    :auto-size="{ minRows: item.minRows }"
                  />
                </FormField>
              </div>
            </div>
          </FormSection>
          <!-- 强化物与偏好信息 -->
          <FormSection
            v-if="activeTab === 'reinforcers'"
            title="强化物与偏好信息"
            module-number="6/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div class="space-y-6">
              <!-- 偏好选项 -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <OptionGroup
                  v-for="config in reinforcersConfig"
                  :key="config.label"
                  v-model="student.additionalData.baseInfo[config.key]"
                  :label="config.label"
                  :options="config.options"
                  type="checkbox"
                  :columns="isPc ? 4 : 2"
                  bordered
                />
              </div>

              <!-- 活动偏好 -->
              <OptionGroup
                v-model="student.additionalData.baseInfo.activityPreference"
                label="活动或游戏偏好"
                :options="activityPreferences"
                type="checkbox"
                :columns="isPc ? 4 : 2"
                bordered
              />

              <!-- 其他信息 -->
              <div class="bg-gradient-to-r from-violet-50 to-violet-100/50 border border-violet-200 rounded-xl p-6">
                <h4 class="text-lg font-semibold text-violet-900 mb-6 flex items-center">
                  <span
                    class="w-6 h-6 bg-violet-500 rounded-full flex items-center justify-center text-white text-sm mr-3"
                    >📺</span
                  >
                  媒体与娱乐偏好
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField label="电视节目名称">
                    <a-input
                      v-model="student.additionalData.baseInfo.tvPrograms"
                      placeholder="请输入喜欢的电视节目"
                      size="large"
                    />
                  </FormField>

                  <FormField label="音乐类型/节目">
                    <a-input
                      v-model="student.additionalData.baseInfo.musicType"
                      placeholder="请输入喜欢的音乐类型"
                      size="large"
                    />
                  </FormField>

                  <FormField label="电子设备或游戏名称">
                    <a-input
                      v-model="student.additionalData.baseInfo.electronicGames"
                      placeholder="请输入喜欢的游戏"
                      size="large"
                    />
                  </FormField>
                </div>
              </div>

              <FormField label="厌恶刺激" full-width>
                <a-textarea
                  v-model="student.additionalData.baseInfo.aversiveStimuli"
                  placeholder="请描述学生不喜欢或厌恶的刺激、活动或物品"
                  :auto-size="{ minRows: 4 }"
                />
              </FormField>
            </div>
          </FormSection>
          <FormSection
            v-if="activeTab === 'attachments'"
            title="附件信息"
            module-number="7/7"
            :is-completed="true"
            @save="handleSave"
          >
            <div>
              <a-button size="mini" @click="handleAddColumn">
                <template #icon>
                  <icon-plus />
                </template>
                新增
              </a-button>
            </div>
            <a-table :data="student.attachments || []" :columns="attachmentColumns">
              <template #type="{ record }">
                <a-select v-model="record.type" :options="options.studentAttachmentTypeOptions" />
              </template>
              <template #attachments="{ record }">
                <uploader v-model="record.attachments" sub-folder="" />
              </template>
              <template #operate="{ rowIndex }">
                <a-button size="mini" type="outline" status="danger" @click="handleDel(rowIndex)">删除</a-button>
              </template>
            </a-table>
          </FormSection>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<style scoped></style>
