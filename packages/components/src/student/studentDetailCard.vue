<script setup lang="ts">
  import { onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import StudentMoreDetailCard from './studentMoreDetailCard.vue';
  import StudentDetailModal from './components/studentDetailModal.vue';

  const props = defineProps({
    student: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    eager: {
      type: Boolean,
      default: false,
    },
    moreInfo: {
      type: Boolean,
      default: false,
    },
  });

  const studentItem = ref(props.student);
  const loading = ref(false);
  const additionalInfoVisible = ref(false);
  const handleShowModal = () => {
    additionalInfoVisible.value = true;
  };

  onMounted(async () => {
    if (props.eager) {
      loading.value = true;
      try {
        const { data } = await request(`/resourceRoom/student/${props.student.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        studentItem.value = data || props.student || {};
      } catch (e) {
        studentItem.value.id = undefined;
      } finally {
        loading.value = false;
      }
    }
  });
</script>

<template>
  <a-spin v-if="loading" :loading="loading" animation class="w-full">
    <a-skeleton-line :rows="3" class="w-full" />
  </a-spin>

  <div v-else class="flex flex-col gap-4 text-sm text-gray-700">
    <div class="flex gap-4 items-center">
      <div class="w-[22mm] h-[28mm] border bg-gray-100 rounded flex items-center justify-center text-gray-400">
        <img v-if="studentItem.additionalData?.avatar" :src="studentItem.additionalData?.avatar?.url" />
        <span v-else></span>
      </div>

      <div class="flex-1 flex flex-col gap-2">
        <div class="flex items-center gap-2 flex-wrap">
          <a-tag v-if="studentItem.gender === '男'" size="small" color="arcoblue">
            {{ studentItem.name }}
            <IconMan />
          </a-tag>
          <a-tag v-else-if="studentItem.gender === '女'" size="small" color="pinkpurple">
            {{ studentItem.name }}
            <IconWoman />
          </a-tag>
          <span v-else class="font-semibold">{{ studentItem.name }}</span>
          <span v-if="studentItem.age">{{ studentItem.age }}岁</span>
          <span class="text-xs font-bold">{{ studentItem.nation }}</span>
        </div>

        <div class="flex items-center flex-wrap gap-2">
          <span v-if="studentItem.disorders">{{ studentItem.disorders }}</span>
          <a-divider v-if="studentItem.disorders && studentItem.disabilityLevel" direction="vertical" :margin="8" />
          <span v-if="studentItem.disabilityLevel">残疾等级：{{ studentItem.disabilityLevel }}</span>
        </div>

        <dl class="space-y-1">
          <div v-if="studentItem.fusionSchool">
            <dt class="inline font-bold">融合学校：</dt>
            <dd class="inline text-gray-400">{{ studentItem.fusionSchool.name }}</dd>
          </div>
          <div v-if="studentItem.enrollmentYear">
            <dt class="inline font-bold">入学年份：</dt>
            <dd class="inline text-gray-400">{{ studentItem.enrollmentYear }} 年</dd>
          </div>
          <div v-if="studentItem.gradeClass">
            <dt class="inline font-bold">班级：</dt>
            <dd class="inline text-gray-400">{{ studentItem.gradeClass.name }}</dd>
          </div>
          <div v-if="studentItem.hasSendPlan">
            <a-tag color="green" size="small">送教</a-tag>
          </div>
        </dl>
      </div>
    </div>

    <a-divider v-if="moreInfo && studentItem?.id" :margin="5" />

    <div v-if="moreInfo && studentItem?.id" class="flex gap-2 flex-wrap">
      <!--<a-button size="mini" shape="round" type="dashed" class="action-btn">教育评估</a-button>
      <a-button size="mini" shape="round" type="dashed" class="action-btn">IEP</a-button>
      <a-button v-if="studentItem.hasSendPlan" size="mini" shape="round" type="dashed" class="action-btn">
        送教
      </a-button>-->
      <a-button size="mini" shape="round" type="dashed" class="action-btn" @click="handleShowModal">
        <template #icon>
          <IconInfoCircle />
        </template>
        更多信息
      </a-button>
    </div>
  </div>
  <studentDetailModal v-if="additionalInfoVisible" v-model:visible="additionalInfoVisible" :student="studentItem" />
  <!--<a-modal v-model:visible="additionalInfoVisible" width="60%" :render-to-body="false" :closable="false" draggable>
    <template #title>
      <div class="flex items-center gap-3 text-base text-gray-700">
        <span class="font-semibold">
          {{ studentItem.name }}
        </span>
        <span v-if="studentItem.gender" class="text-xs text-gray-500">
          （{{ studentItem.gender }}{{ studentItem.age ? ` · ${studentItem.age}岁` : '' }}）
        </span>
      </div>
    </template>
    <studentMoreDetailCard v-if="additionalInfoVisible" :student="studentItem" />
    <template #footer>
      <div class="flex justify-end">
        <a-button size="mini" type="primary" @click="additionalInfoVisible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>-->
</template>

<style scoped lang="scss">
  .action-btn {
    background-color: #e3f2fd;
    border: 1px dashed #2196f3;
    color: #1565c0;
    transition: all 0.3s ease;
    font-weight: 500;

    &:hover {
      background-color: #5eb8ff;
      color: #ffffff;
      border-color: #1565c0;
      box-shadow: 0 0 6px rgba(33, 150, 243, 0.4);
    }
  }
</style>
