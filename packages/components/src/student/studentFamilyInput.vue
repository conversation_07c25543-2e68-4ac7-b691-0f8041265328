<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const familyMembers = computed({
    get: () => props.modelValue || [],
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const handleAddFamilyMember = () => {
    familyMembers.value = familyMembers.value || [];
    familyMembers.value.push({});
  };

  watch(
    () => familyMembers.value,
    (value) => {
      emit('update:modelValue', value);
    },
    { deep: true, immediate: true },
  );

  onMounted(() => {
    familyMembers.value = props.modelValue || [];
  });
</script>

<template>
  <a-space direction="vertical" class="w-full">
    <a-button type="primary" size="mini" plain @click="handleAddFamilyMember">添加家庭成员</a-button>
    <a-table v-if="familyMembers?.length" :data="familyMembers" size="mini" class="w-full" :pagination="false">
      <template #columns>
        <a-table-column data-index="relationship" title="关系" :width="150">
          <template #cell="{ record }">
            <a-input v-model="record.relationship" size="mini" />
          </template>
        </a-table-column>
        <a-table-column data-index="name" title="姓名" :width="150">
          <template #cell="{ record }">
            <a-input v-model="record.name" size="mini" />
          </template>
        </a-table-column>
        <a-table-column data-index="birthday" title="生日" :width="150">
          <template #cell="{ record }">
            <a-date-picker v-model="record.birthday" size="mini" />
          </template>
        </a-table-column>
        <a-table-column data-index="phone" title="电话" :width="150">
          <template #cell="{ record }">
            <a-input v-model="record.phone" size="mini" />
          </template>
        </a-table-column>
        <a-table-column data-index="remark" title="备注">
          <template #cell="{ record }">
            <a-input v-model="record.remark" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="100">
          <template #cell="{ record }">
            <a-button
              type="primary"
              status="danger"
              size="mini"
              @click="() => familyMembers.splice(familyMembers.indexOf(record), 1)"
            >
              删除
            </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-space>
</template>

<style scoped lang="scss"></style>
