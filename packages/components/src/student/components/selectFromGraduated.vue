<script setup lang="ts">
  import StudentList from '@repo/components/student/studentList.vue';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Modal } from '@arco-design/web-vue';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const props = defineProps({
    studentName: {
      type: String,
    },
  });

  const name = ref(props.studentName);

  const emit = defineEmits(['importToCurrent']);

  const queryParams = {
    schoolNature: menuInfo?.app?.label,
    onlyNotCirculate: '1',
    name: props.studentName,
    _og: '1', // only graduated
  };

  const visibleColumns = [
    'name',
    'symbol',
    'nation',
    'fusionSchool',
    'status',
    'graduatedSetTime',
    'gender',
    'age',
    'birthday',
    'disorders',
  ];

  const schema = ref<any>();
  const studentListRef = ref(null);

  const handleSearch = async () => {
    studentListRef.value?.tableRef().loadData({
      name: name.value,
    });
  };

  onMounted(async () => {
    const studentSchema = await SchemaHelper.getInstanceByDs('/resourceRoom/student');

    schema.value = {
      ...studentSchema,
      importable: {
        enabled: false,
      },
      listViewProps: {
        searchType: 'quick',
      },
      rowActions: [
        // ...studentSchema.rowActions,
        { key: 'edit', visible: false },
        { key: 'delete', visible: false },
        { key: 'archive', visible: false },
        { key: 'cancelGraduation', visible: false },
        {
          key: 'importToCurrent',
          expose: true,
          label: '引入',
          handler(record) {
            Modal.confirm({
              title: '请确认',
              content: `确定引入 ${record.name} 吗？此操作将导入该学生的所有历史档案，请再次确认！`,
              onOk() {
                emit('importToCurrent', record);
              },
            });
          },
        },
      ],
    };
  });
</script>

<template>
  <a-space class="item-center">
    姓名
    <a-input v-model="name" size="mini" placeholder="输入姓名搜索" allow-clear @keydown.enter="handleSearch" />
    <a-button type="primary" @click="handleSearch">
      <template #icon>
        <IconSearch />
      </template>
      搜索
    </a-button>
  </a-space>
  <student-list
    v-if="schema"
    ref="studentListRef"
    class="mt-2"
    :custom-schema="schema"
    :visible-columns="visibleColumns"
    :default-query-params="queryParams"
    :table-action-props="{
      visibleComponents: ['a'],
    }"
    module-path="/manage/specialSchool/student/graduated"
  >
    <template #title> {{ menuInfo?.app?.label || '' }} 毕业生信息库 </template>
  </student-list>
</template>

<style scoped lang="scss"></style>
