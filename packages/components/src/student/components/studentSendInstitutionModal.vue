<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    student: {
      type: Object,
      required: true,
    },
  });
  const emits = defineEmits(['update:visible']);
  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  const fsStore = useCommonStore({
    api: '/resourceCenter/fusionSchool',
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    queryParams: {
      institutionList: 1,
    },
  });
  const institutionOptions = ref<any>([]);
  const homeDeliveryInstitution = ref<number>();

  const handlePreOk = async () => {
    const matchedOption = institutionOptions.value.find((i) => i.value === homeDeliveryInstitution.value);
    try {
      await request(`/resourceRoom/student/${props.student.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: {
          ...props.student,
          homeDeliveryInstitution: matchedOption ? { id: matchedOption.value, name: matchedOption.label } : null,
        },
      });
      Message.success('保存成功');
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    institutionOptions.value = await fsStore.getOptions();
  });
  watch(
    () => props.student,
    (newVal) => {
      homeDeliveryInstitution.value = newVal?.homeDeliveryInstitution?.id;
    },
  );
</script>

<template>
  <a-modal v-model:visible="visible" title="送教机构" :on-before-ok="handlePreOk">
    <a-select
      v-model="homeDeliveryInstitution"
      placeholder="请选择送教机构"
      :options="institutionOptions"
      size="large"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
