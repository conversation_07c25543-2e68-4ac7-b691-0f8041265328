<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  // 表格列定义
  const columns = ref([
    { title: '项目', dataIndex: 'itemName' },
    { title: '得分', dataIndex: 'score' },
    { title: '总分', dataIndex: 'totalScore' },
    { title: '状态', dataIndex: 'state' },
  ]);

  const rowClassName = (val: any) => {
    if (val.state === '异常') {
      return 'status-abnormal';
    }
    if (val.state === '正常') {
      return 'status-normal';
    }
    return 'status-suspicious';
  };
</script>

<template>
  <!--  <a-table :data="props.record.details" :columns="columns" :pagination="false" :row-class="rowClassName" />-->

  <table class="scores-table">
    <thead>
      <tr>
        <th>测试项目</th>
        <th>得分/总分</th>
        <th>状态</th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(section, index) in props.record.details"
        :key="index"
        :class="rowClassName(section)"
        style="border-bottom: 1px rgba(200, 200, 200, 0.68) solid"
      >
        <td
          ><span class="ml-4">{{ section.itemName }}</span></td
        >
        <td>{{ section.score }} / {{ section.totalScore }}</td>
        <td>{{ section.state }}</td>
      </tr>
    </tbody>
  </table>
</template>

<style scoped lang="scss">
  .status-normal {
    margin: 4px;
    background-color: #f6fff7;
    color: #2e7d32;
  }

  .status-suspicious {
    margin: 4px;
    background-color: #fff9f3;
    color: #ef6c00;
  }

  .status-abnormal {
    margin: 4px;
    background-color: #fff4f5;
    color: #c62828;
  }
  .scores-table {
    margin: 0 auto;
    width: 100%;
    border-collapse: collapse;
  }
</style>
