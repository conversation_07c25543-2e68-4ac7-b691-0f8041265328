<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';

  const props = defineProps({
    visible: <PERSON><PERSON><PERSON>,
    studentIds: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:visible']);
  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });
  const selectedCommittee = ref();
  const options = ref();
  const loadBoList = async () => {
    try {
      const { data: res } = await request(`/org/branchOffice/specialCommittee`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      options.value = res.items.map((item) => ({
        label: item.name,
        value: item.id,
        raw: item,
      }));
    } catch (e) {
      console.error(e);
    }
  };
  const handlePreOk = async () => {
    Modal.confirm({
      title: '请确认',
      content: '确定要引入至专委会并修改学生状态为待安置吗？',
      onOk: async () => {
        try {
          const bo = options.value.find((item) => item.value === selectedCommittee.value);
          let idsList: any;
          if (Array.isArray(props.studentIds)) {
            idsList = props.studentIds;
          } else {
            idsList = [props.studentIds?.id];
          }
          const data = {
            committee: {
              id: bo.raw.id,
              name: bo.raw.name,
            },
            studentIds: idsList,
          };
          await request(`/resourceRoom/student/updateCommittee`, {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'put',
            data,
          });
          Message.success('操作成功');
        } catch (e) {
          console.error(e);
        }
      },
    });
  };
  onMounted(async () => {
    await loadBoList();
  });
</script>

<template>
  <a-modal v-model:visible="visible" title="批量引入专委会" ok-text="引入" :on-before-ok="handlePreOk">
    <a-select v-model="selectedCommittee" :options="options" placeholder="请选择专委会" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
