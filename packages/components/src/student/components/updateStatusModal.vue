<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { isArray } from 'lodash';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    records: {
      type: [Array, Object],
      default: () => [],
    },
  });

  const options: any[] = [
    { label: '正常', value: 'Normal' },
    { label: '转出', value: 'TransferOut' },
    { label: '休学', value: 'Suspension' },
    { label: '毕业', value: 'Graduation' },
  ];

  const emit = defineEmits(['update:visible', 'ok']);
  const studentName = computed(() => {
    return isArray(props.records) ? `${props.records.length}名学生` : props.records.name;
  });
  const studentIds = computed(() => {
    return isArray(props.records) ? props.records.map((record: any) => record.id || record) : [props.records.id];
  });
  const status = ref('');
  const statusText = computed(() => {
    return status.value ? options.find((option) => option.value === status.value)?.label : '';
  });
  const loading = ref(false);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val: boolean) {
      emit('update:visible', val);
    },
  });

  const handleSave = () => {
    if (!status.value) {
      Message.error('请选择学生状态');
      return false;
    }
    Modal.confirm({
      title: '请确认',
      content: `确认修改 ${studentName.value} 的状态为 ${statusText.value} 吗？`,
      onOk: async () => {
        loading.value = true;
        try {
          await request(`/resourceRoom/student/batchUpdateStatus/${status.value}`, {
            method: 'PUT',
            data: studentIds.value,
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          });
          Message.success('修改成功');
          emit('ok');
        } finally {
          loading.value = false;
          modalVisible.value = false;
        }
      },
    });

    return false;
  };

  const handleClose = () => {
    modalVisible.value = false;
  };
</script>

<template>
  <a-modal
    v-if="modalVisible"
    v-model:visible="modalVisible"
    :title="`修改 ${studentName} 的状态`"
    :on-before-ok="handleSave"
    :ok-loading="loading"
    @close="handleClose"
  >
    <a-form-item label="学生状态修改为">
      <a-select v-model="status" size="mini" :options="options" />
    </a-form-item>
  </a-modal>
</template>

<style scoped lang="scss"></style>
