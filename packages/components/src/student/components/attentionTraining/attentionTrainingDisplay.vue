<script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import AttentionTrainingStatistics from './attentionTrainingStatistics.vue';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      align: 'center',
      width: 90,
    },
    { title: '日期', dataIndex: 'trainingTime', align: 'center' },
    {
      title: '方格大小',
      dataIndex: 'gridSize',
      align: 'center',
      sortable: { sortDirections: ['ascend', 'descend'] },
    },
    {
      title: '耗时',
      dataIndex: 'finishTime',
      align: 'center',
      sortable: { sortDirections: ['ascend', 'descend'] },
      width: 100,
    },
    { title: '错误次数', dataIndex: 'errorTimes', align: 'center', width: 100 },
    { title: '反向计数', dataIndex: 'inverseCount', slotName: 'inverseCount', align: 'center' },
    { title: '发散计数', dataIndex: 'divergentCount', slotName: 'divergentCount', align: 'center' },
    { title: '打乱数字', dataIndex: 'shuffleSymbols', slotName: 'shuffleSymbols', align: 'center' },
    { title: '转动数字', dataIndex: 'turnSymbols', slotName: 'turnSymbols', align: 'center' },
    { title: '旋转数字', dataIndex: 'spinSymbols', slotName: 'spinSymbols', align: 'center' },
  ]);

  const statisticsData = ref<Record<string, any>[]>([]);

  const data = ref({ assessmentDetails: [], trainingDetails: [] });
  const assessmentOrTraining = ref(false);

  const loadData = async () => {
    try {
      const res = ref();
      if (props.record.attentionTrainingEvaluationCriteria != null) {
        res.value = await request(
          `/resourceRoom/attentionTrainingRecord/getTrainingRecord/${props.record?.student.id}/${props.record?.attentionTrainingEvaluationCriteria.id}`,
          {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'get',
          },
        );
      } else {
        res.value = await request(`/resourceRoom/attentionTrainingRecord/${props.record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
      }

      data.value.assessmentDetails = res.value.data.assessmentDetails;
      data.value.trainingDetails = res.value.data.trainingDetails;

      // 初始化 statisticsData
      statisticsData.value = res.value.data.trainingDetails
        .map((item: any, index: number) => ({
          time: `第${index + 1}次`,
          errors: item.errorTimes,
          finishTime: item.finishTime,
        }))
        .flatMap((item: any) => [
          { time: item.time, type: '错误次数', value: item.errors },
          { time: item.time, type: '反应时间', value: item.finishTime },
        ]);
    } catch (e) {
      /**/
    }
  };
  const changeStatistic = (record: boolean) => {
    statisticsData.value = [];
    if (record) {
      // 评估
      // 初始化 statisticsData
      statisticsData.value = data.value.assessmentDetails
        .map((item: any, index: number) => ({
          time: `第${index + 1}次`,
          errors: item.errorTimes,
          finishTime: item.finishTime,
        }))
        .flatMap((item: any) => [
          { time: item.time, type: '错误次数', value: item.errors },
          { time: item.time, type: '反应时间', value: item.finishTime },
        ]);
    } else {
      statisticsData.value = data.value.trainingDetails
        .map((item: any, index: number) => ({
          time: `第${index + 1}次`,
          errors: item.errorTimes,
          finishTime: item.finishTime,
        }))
        .flatMap((item: any) => [
          { time: item.time, type: '错误次数', value: item.errors },
          { time: item.time, type: '反应时间', value: item.finishTime },
        ]);
    }
  };

  onMounted(async () => {
    await loadData();
    await nextTick();
    // line.render();
  });
</script>

<!--注意力训练详情查看-->
<template>
  <div v-for="(v, k) in data" :key="k">
    <div class="text-center text-slate-400">{{ k === 'assessmentDetails' ? '评估记录' : '训练记录' }}</div>
    <a-table
      :columns="columns"
      :data="v"
      column-resizable
      :bordered="{ cell: true }"
      :pagination="false"
      :scroll="{ y: v.length >= 4 ? '200px' : 'auto' }"
      class="mb-2"
    >
      <template v-for="(c, index) in columns" :key="index" #[c.slotName]="{ record }">
        <div v-if="c.slotName !== 'actions'" class="flex justify-center">
          <div
            v-if="record[c.dataIndex] === false"
            style="width: 20px; height: 20px; border-radius: 10px; border: #ff814b 2px solid; display: inline-block"
          ></div>
          <div
            v-else
            style="width: 20px; height: 20px; border-radius: 10px; border: #37a8ff 10px solid; display: inline-block"
          ></div>
        </div>
      </template>
    </a-table>
  </div>
  <a-switch
    v-model="assessmentOrTraining"
    size="mini"
    unchecked-color="#b6b6b6"
    checked-color="#b6b6b6"
    type="round"
    @change="changeStatistic"
  >
    <template #checked>
      <span class="text-white">评估</span>
    </template>
    <template #unchecked>
      <span class="text-white">训练</span>
    </template>
  </a-switch>

  <!--统计图标-->
  <AttentionTrainingStatistics
    v-if="data.assessmentDetails.length > 0 || data.trainingDetails.length > 0"
    class="mt-5"
    :data="statisticsData"
    style="height: 200px"
  />
</template>

<style scoped lang="scss"></style>
