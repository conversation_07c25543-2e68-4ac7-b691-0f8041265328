<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { Line } from '@antv/g2plot';

  const props = defineProps({
    data: {},
  });

  const dataList = ref(props.data);
  const color = ['#ff2c2c', '#0a6ccd'];

  let line: Line | null = null; // 声明 line 变量

  onMounted(async () => {
    line = new Line('container', {
      data: dataList.value,
      color,
      xField: 'time',
      yField: 'value',
      seriesField: 'type',
      tooltip: {
        shared: true,
        showMarkers: false,
      },
      point: {
        size: 2,
        shape: 'circle',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      legend: {
        position: 'top',
        layout: 'horizontal',
      },
      interactions: [{ type: 'marker-active' }],
    });

    line.render();
  });

  watch(
    () => props.data,
    (newData: any) => {
      if (line) {
        line.changeData(newData); // 使用 changeData 方法更新图表数据
      }
    },
  );
</script>

<template>
  <div id="container" style="height: 300px; width: 100%"></div>
</template>

<style scoped lang="scss"></style>
