<script setup lang="ts">
  import { computed, onMounted, onUnmounted, PropType, ref, watch } from 'vue';
  import { Line } from '@antv/g2plot';

  const props = defineProps({
    record: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  interface ChartDatum {
    index: string;
    correctRate: number;
    reactionTime: number;
    difficulty: string;
    date: string;
  }

  const charts = ref<{
    lineChart1: Line | null;
    lineChart2: Line | null;
  }>({
    lineChart1: null,
    lineChart2: null,
  });

  const correctRateChart = ref<HTMLElement | null>(null);
  const reactionTimeChart = ref<HTMLElement | null>(null);

  // 难度映射
  const getDifficultyText = (difficulty: 'easy' | 'medium' | 'hard'): string => {
    const map: Record<string, string> = {
      easy: '初级',
      medium: '中级',
      hard: '高级',
    };
    return map[difficulty];
  };

  // 计算图表数据
  const chartData = computed<ChartDatum[]>(() => {
    return props.record.trainingRecord.map((record: any, index: number) => ({
      index: `第${index + 1}次`,
      correctRate:
        (record.recordDetails.filter((item: any) => item.correct).length / record.recordDetails.length) * 100,
      reactionTime: record.recordDetails.reduce((sum, item) => sum + (item.reactionTime || 0), 0) / 1000 || 0,
      difficulty: getDifficultyText(props.record.setting.difficulty),
    }));
  });

  // 渲染图表
  const renderCharts = () => {
    if (!props.record.trainingRecord || props.record.trainingRecord.length === 0) return;

    // 正确率趋势图
    if (correctRateChart.value) {
      charts.value.lineChart1?.destroy(); // 销毁旧实例
      charts.value.lineChart1 = new Line(correctRateChart.value, {
        data: chartData.value,
        xField: 'index',
        yField: 'correctRate',
        seriesField: 'difficulty',
        smooth: true,
        point: {
          size: 5,
          shape: 'diamond',
        },
        label: {
          formatter: (datum: ChartDatum) => `${datum.correctRate.toFixed(1)}%`,
        },
        tooltip: {
          formatter: (datum: ChartDatum) => {
            return {
              name: '正确率',
              value: `${datum.correctRate.toFixed(1)}%`,
            };
          },
        },
        yAxis: {
          min: 0,
          max: 100,
          label: {
            formatter: (v: number) => `${v}%`,
          },
        },
        theme: {
          defaultColor: '#4CAF50',
        },
      });
      charts.value.lineChart1.render();
    }

    // 反应时间趋势图
    if (reactionTimeChart.value) {
      charts.value.lineChart2?.destroy(); // 销毁旧实例
      charts.value.lineChart2 = new Line(reactionTimeChart.value, {
        data: chartData.value,
        xField: 'index',
        yField: 'reactionTime',
        seriesField: 'difficulty',
        smooth: true,
        point: {
          size: 5,
          shape: 'diamond',
        },
        label: {
          formatter: (datum: ChartDatum) => `${datum.reactionTime.toFixed(1)}s`,
        },
        tooltip: {
          formatter: (datum: ChartDatum) => {
            return {
              name: '反应时间',
              value: `${datum.reactionTime.toFixed(1)}秒`,
            };
          },
        },
        yAxis: {
          label: {
            formatter: (v: number) => `${v}s`,
          },
        },
        theme: {
          defaultColor: '#2196F3',
        },
      });
      charts.value.lineChart2.render();
    }
  };

  const formatReactionTime = (recordDetails: any[]) => {
    const totalMilliseconds = recordDetails.reduce((sum, it) => sum + (it.reactionTime || 0), 0);
    const totalSeconds = totalMilliseconds / 1000;
    return totalSeconds.toFixed(2); // 保留两位小数
  };
  const correctRate = computed(() => (item: any) => {
    if (!item.recordDetails || item.recordDetails.length === 0) return 0;
    return ((item.recordDetails.filter((one: any) => one.correct).length / item.recordDetails.length) * 100).toFixed(2);
  });
  // 监听 props.record 的变化
  watch(
    () => props.record,
    () => {
      renderCharts();
    },
    { deep: true },
  );

  // 组件挂载时渲染图表
  onMounted(() => {
    renderCharts();
  });

  // 组件卸载时销毁图表
  onUnmounted(() => {
    charts.value.lineChart1?.destroy();
    charts.value.lineChart2?.destroy();
  });
</script>

<template>
  <div class="w-full mt-2">
    <div class="font-bold">正确率趋势</div>
    <div ref="correctRateChart" class="mb-6 rounded bg-gray-50 h-[350px]" />
    <div class="font-bold">平均反应时间趋势</div>
    <div ref="reactionTimeChart" class="mb-6 rounded bg-gray-50 h-[350px]" />
    <div class="font-bold">历史记录</div>
    <div
      v-for="(item, index) in record.trainingRecord"
      :key="index"
      class="rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow duration-300 p-4 mb-3 border border-gray-100"
    >
      <div class="text-gray-700 font-medium mb-2">
        总测试数：<span class="text-blue-600">{{ item.recordDetails?.length }}</span>
      </div>

      <div class="text-gray-700 font-medium mb-2">
        正确率：
        <span
          :class="{
            'text-green-600': correctRate(item) >= 80,
            'text-yellow-600': correctRate(item) >= 50 && correctRate(item) < 80,
            'text-red-600': correctRate(item) < 50,
          }"
        >
          {{ correctRate(item) + '%' }}
        </span>
      </div>

      <div class="text-gray-700 font-medium">
        总反应时间：
        <span class="text-purple-600">{{ formatReactionTime(item.recordDetails) }}秒</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
