<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = ref([
    { title: '环境空间', dataIndex: 'place' },
    { title: '发生时间', dataIndex: 'timeDate' },
    { title: '环境人物', dataIndex: 'evnPeople' },
    { title: '问题行为', dataIndex: 'event' },
    { title: '期望行为', dataIndex: 'hopeEvent' },
    { title: '频率', dataIndex: 'rate', slotName: 'rate' },
  ]);
  const recordAnalysisData = ref([]);
  const records = ref({ ...props.record });
  const loadData = async (id: number) => {
    try {
      if (id) {
        const res = await request(`/resourceRoom/behaviorRecordAnalysisEvent/findByBraId/${id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        recordAnalysisData.value = res.data.items;
      }
    } catch (error) {
      /**/
    }
  };
  onMounted(async () => {
    await loadData(props.record?.id);
  });

  watch(
    () => props.record,
    async (oldVal, newVal) => {
      records.value = { ...newVal };
      if (records.value.id) await loadData(records.value.id);
    },
    { immediate: true },
  );
</script>

<template>
  <a-table
    column-resizable
    :bordered="{ cell: true }"
    :columns="columns"
    :data="recordAnalysisData"
    :pagination="false"
  >
    <template #rate="{ record }">
      <span>{{ record.rate }} {{ record.rateUit === '0' ? '次/天' : '次/周' }}</span>
    </template>
  </a-table>
</template>
