<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = ref([
    { title: '因素类别', dataIndex: 'analysisType', width: '300px' },
    { title: '症解', dataIndex: 'analysisContent' },
  ]);

  const records = ref({ ...props.record });
  const categorys = ref<Record<any, any>>([]);
  const analysisType = ref();
  const loadData = async (id) => {
    if (id) {
      const res = await request(`/resourceRoom/behaviorRecordAnalysisCategory/findByBraId/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      analysisType.value = res.data.items[0].analysisType;
      categorys.value = res.data.items;
      categorys.value = res.data.items.filter(
        (item) => item.braId !== undefined && item.braId !== null && item.braId !== '',
      );
    }
  };

  onMounted(async () => {
    await loadData(props.record?.id);
  });

  watch(
    () => props.record,
    async (oldVal, newVal) => {
      records.value = { ...newVal };
      if (records.value.id) await loadData(records.value.id);
    },
    { immediate: true },
  );
</script>

<template>
  <a-table column-resizable :bordered="{ cell: true }" :columns="columns" :data="categorys" :pagination="false">
  </a-table>
</template>
