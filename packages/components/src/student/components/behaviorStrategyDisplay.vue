<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });
  const records = ref({ ...props.record });

  const strategyColumnss = [
    { title: '目标行为', dataIndex: 'hopeTarget' },
    { title: '策略内容', dataIndex: 'strategyContent', align: 'center' },
    { title: '策略方法', dataIndex: 'strategyMethods', align: 'center' },
    { title: '效果评级', dataIndex: 'effectRating', align: 'center' },
    { title: '时间范围', dataIndex: 'timeRange', slotName: 'range', align: 'center' },
  ];

  const strategies = ref([]);
  const loadData = async (id) => {
    try {
      if (id) {
        const res = await request(`/resourceRoom/behavioralInterventionRecordStrategy/findByBraId/${id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        strategies.value = res.data;
        strategies.value.sort((a, b) => {
          return a.hopeTarget.localeCompare(b.hopeTarget);
        });
      }
    } catch (error) {
      /**/
    }
  };

  onMounted(async () => {
    await loadData(props.record?.id);
  });

  watch(
    () => props.record,
    async (oldVal, newVal) => {
      records.value = { ...newVal };
      if (records.value.id) await loadData(records.value.id);
    },
    { immediate: true },
  );
</script>

<template>
  <a-table
    :data="strategies"
    :columns="strategyColumnss"
    column-resizable
    :bordered="{ cell: true }"
    :pagination="false"
  >
    <template #range="{ record }">
      <span>{{ record.timeRange[0] }} - {{ record.timeRange[1] }}</span>
    </template>
  </a-table>
</template>
