<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { Message } from '@arco-design/web-vue';
  import { IconClose, IconDownload, IconLink } from '@arco-design/web-vue/es/icon';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import { getOrgNature } from '../../utils/utils';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    student: {
      type: Object,
      required: true,
    },
  });

  const emits = defineEmits(['update:visible']);

  const visible = computed({
    get: () => props.visible,
    set: (val) => emits('update:visible', val),
  });

  const token = getToken();

  const localStudent = ref<any>({});

  const qrCodeUrl = computed(() => {
    return `${PROJECT_URLS.MAIN_PROJECT}/module/editStudentInfo.html?studentId=${localStudent.value.id}&token=${token}&nature=${getOrgNature()}`;
  });

  const currentType = ref('link');

  // 复制链接功能
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(qrCodeUrl.value);
      Message.success('链接已复制到剪贴板');
    } catch (error) {
      Message.error('复制失败，请手动复制');
    }
  };

  const lastSendTime = ref(null);

  const handleDownloadQrCode = () => {
    const canvas = document.querySelector('.qr-code-canvas canvas') as HTMLCanvasElement;
    if (canvas) {
      const link = document.createElement('a');
      link.download = `${localStudent.value.name}信息填写二维码.png`;
      link.href = canvas.toDataURL();
      link.click();
      Message.success('二维码下载成功');
    } else {
      Message.error('下载失败，请稍后重试');
    }
  };

  const handleClose = () => {
    visible.value = false;
  };
  const handleInit = () => {
    localStudent.value = { ...props.student };
    currentType.value = 'link';
  };
  watch(
    () => props.student,
    () => {
      lastSendTime.value = null;
    },
  );
</script>

<template>
  <a-modal
    v-model:visible="visible"
    width="800px"
    :closable="false"
    :title="false"
    :footer="false"
    :body-style="{ padding: 0 }"
    @before-open="handleInit"
  >
    <div class="bg-white">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span class="text-lg font-medium text-gray-800">学生信息填写二维码</span>
        </div>
        <a-button type="text" size="small" class="default-btn" @click="handleClose">
          <template #icon>
            <IconClose />
          </template>
        </a-button>
      </div>

      <div class="p-6 space-y-6">
        <!-- 学生信息区域 -->
        <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <h3 class="text-base font-medium text-gray-800 mb-3">学生信息</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-blue-600 font-medium">姓名：</span>
              <span class="text-gray-700">{{ student?.name || '未填写' }}</span>
            </div>
            <div>
              <span class="text-blue-600 font-medium">性别：</span>
              <span class="text-gray-700">{{ student?.gender || '未填写' }}</span>
            </div>
            <div>
              <span class="text-blue-600 font-medium">学校：</span>
              <span class="text-gray-700">{{ student?.fusionSchool?.name || '未填写' }}</span>
            </div>
            <div>
              <span class="text-blue-600 font-medium">学籍号：</span>
              <span class="text-gray-700">{{ student?.symbol || '未填写' }}</span>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6">
          <!-- 扫码填写区域 -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 class="text-base font-medium text-gray-800 mb-4">扫码填写</h3>
            <div class="text-center">
              <div class="qr-code-canvas inline-block p-4 bg-white rounded-lg shadow-sm">
                <vue-qrcode :value="qrCodeUrl" :size="200" />
              </div>
              <p class="text-sm text-gray-600 mt-3 mb-4">家长可扫描此二维码填写学生相关信息</p>
              <div class="flex justify-center space-x-2">
                <a-button type="outline" size="medium" class="default-btn" @click="handleDownloadQrCode">
                  <template #icon>
                    <IconDownload />
                  </template>
                  下载二维码
                </a-button>
                <a-button
                  type="outline"
                  size="medium"
                  :class="currentType === 'link' ? 'active-btn' : 'default-btn'"
                  @click="handleCopyLink"
                >
                  <template #icon>
                    <IconLink />
                  </template>
                  复制链接
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 class="text-base font-medium text-gray-800 mb-4">使用说明</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 class="font-medium text-gray-700 mb-2">家长需要填写的内容：</h4>
              <ul class="space-y-1 text-gray-600">
                <li>• 家长基本信息和联系方式</li>
                <li>• 补充学生未完成的信息模块</li>
                <li>• 包括家庭成员、健康医疗等信息</li>
                <li>• 孩子的兴趣爱好和强化物信息</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium text-gray-700 mb-2">注意事项：</h4>
              <ul class="space-y-1 text-gray-600">
                <li>• 支持手机端填写，操作简便</li>
                <li>• 只需填写系统中缺失的信息</li>
                <li>• 填写过程中可随时保存</li>
                <li>• 如有疑问可联系班主任老师</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
  .default-btn {
    border: 1px #d8d8d8 solid;
    border-radius: 4px;
    padding: 6px 12px;
    color: #393939;
    &:hover {
      background-color: rgba(181, 181, 181, 0.22);
      color: #393939;
      border: #6a6a6a solid 1px;
    }
  }
  .active-btn {
    border: 1px #d8d8d8 solid;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: black;
    color: white;
    &:hover {
      border: 1px #d8d8d8 solid;
      border-radius: 4px;
      padding: 6px 12px;
      background-color: black;
      color: white;
    }
  }
</style>
