<script setup lang="ts">
  interface Props {
    label: string;
    options: string[];
    modelValue: string | string[];
    type?: 'radio' | 'checkbox' | undefined;
    columns?: number;
    bordered?: boolean;
  }

  interface Emits {
    (e: 'update:modelValue', value: string | string[]): void;
  }

  defineProps<Props>();
  defineEmits<Emits>();
</script>

<template>
  <div :class="['option-group', bordered ? 'bordered' : '']">
    <label class="option-label">{{ label }}</label>

    <!-- Radio Group -->
    <a-radio-group
      v-if="type === 'radio'"
      :model-value="modelValue as string"
      class="option-container"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <div
        :class="[
          'grid gap-2 lg:gap-3',
          columns === 2
            ? 'grid-cols-1 sm:grid-cols-2'
            : columns === 3
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
              : columns === 4
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
                : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
        ]"
      >
        <a-radio v-for="option in options" :key="option" :value="option" class="option-item">
          {{ option }}
        </a-radio>
      </div>
    </a-radio-group>

    <!-- Checkbox Group -->
    <a-checkbox-group
      v-else
      :model-value="modelValue as string[]"
      class="option-container"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <div
        :class="[
          'grid gap-3',
          columns === 2
            ? 'grid-cols-2'
            : columns === 3
              ? 'grid-cols-3'
              : columns === 4
                ? 'grid-cols-4'
                : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
        ]"
      >
        <a-checkbox v-for="option in options" :key="option" :value="option" class="option-item">
          {{ option }}
        </a-checkbox>
      </div>
    </a-checkbox-group>
  </div>
</template>

<style scoped>
  .option-group {
    @apply space-y-3;
  }

  .option-group.bordered {
    @apply border border-gray-200 rounded-lg p-4 bg-gray-50/50;
  }

  .option-label {
    @apply block text-sm font-semibold text-gray-700;
  }

  .option-container {
    @apply mt-3;
  }

  :deep(.option-item) {
    @apply bg-white border  rounded-lg p-3 hover:border-gray-300 hover:shadow-sm transition-all duration-200;
  }

  :deep(.option-item .arco-radio),
  :deep(.option-item .arco-checkbox) {
    @apply font-medium text-gray-700;
  }

  :deep(.option-item.arco-radio-checked),
  :deep(.option-item.arco-checkbox-checked) {
    @apply border-blue-500 bg-blue-50 text-blue-700;
  }

  :deep(.arco-radio-button),
  :deep(.arco-checkbox-button) {
    @apply w-4 h-4;
  }
</style>
