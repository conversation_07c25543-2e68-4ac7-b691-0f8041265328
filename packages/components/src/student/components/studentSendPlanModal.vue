<script setup lang="ts">
  import ModalWithFullscreen from '@repo/ui/components/common/modalWithFullscreen.vue';

  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  // import SendEduRecord from '@repo/components/sendEduPlan/sendEduRecord.vue';

  const props = defineProps({
    student: {
      type: Object,
      required: true,
    },
  });
  const schema = ref(null);

  const queryParams = computed(() => {
    return {
      sort: '-id',
      studentId: props.student?.id,
    };
  });
  const recordVisible = ref(false);
  const currentPlan = ref(null);

  const handleRowAction = (action, row) => {
    if (action.key === 'records') {
      currentPlan.value = row;
      recordVisible.value = true;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationPlan');
  });
</script>

<template>
  <modal-with-fullscreen v-bind="$attrs" :fullscreen="true" hide-cancel ok-text="关闭">
    <template #title>
      <div v-if="student?.id"> {{ student.name }} 的送教计划 </div>
    </template>
    <table-with-modal-form
      v-if="schema && $attrs.visible"
      module-name="送教计划"
      :schema="schema"
      :default-query-params="queryParams"
      :visible-columns="[
        'period',
        'student',
        'dateRange',
        'planTeacher',
        'finished',
        'personInCharge',
        'collaborators',
        'submitStatus',
      ]"
      @row-action="handleRowAction"
    />

    <!--<send-edu-record v-if="recordVisible && currentPlan" v-model:visible="recordVisible" :plan="currentPlan" />-->
  </modal-with-fullscreen>
</template>

<style scoped lang="scss"></style>
