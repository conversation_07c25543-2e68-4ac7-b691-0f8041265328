<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    title: string;
    subtitle?: string;
    icon?: string;
    iconColor?: string;
    moduleNumber?: string;
    isCompleted?: boolean;
  }

  defineProps<Props>();
  const emits = defineEmits(['save']);
  const handleBack = () => {
    window.history.back();
  };
  const isPc = computed(() => {
    return window.innerWidth > 1024;
  });
  const handleSave = () => {
    emits('save');
  };
</script>

<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="bg-gradient-to-r from-gray-50 to-white px-4 lg:px-6 py-4 border-b border-gray-100">
      <div class="flex justify-between items-start lg:items-center">
        <div class="flex items-start lg:items-center space-x-3 lg:space-x-4 flex-1">
          <div class="flex-1 min-w-0">
            <h3 class="text-lg lg:text-xl font-bold text-gray-900 leading-tight">{{ title }}</h3>
            <p v-if="subtitle" class="text-sm text-gray-500 mt-1">{{ subtitle }}</p>
            <p v-else-if="moduleNumber" class="text-sm text-gray-500 mt-1">
              模块 {{ moduleNumber }} - 请填写完整信息后保存
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4 lg:p-6">
      <slot />
    </div>

    <!-- Footer -->
    <div class="px-4 lg:px-6 pb-4 lg:pb-6">
      <div class="flex justify-center lg:justify-end">
        <slot name="footer">
          <a-button type="primary" size="large" shape="round" @click="handleSave">
            <template #icon>
              <icon-save />
            </template>
            保存模块
          </a-button>
          <a-button v-if="isPc" size="large" shape="round" class="ml-2 back-btn" @click="handleBack">
            <template #icon>
              <icon-undo />
            </template>
            返回
          </a-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .back-btn {
    background-color: rgba(0, 0, 0, 0.79);
    color: white;
    &:hover {
      background-color: rgba(39, 39, 39, 0.74);
      color: white;
    }
  }
</style>
