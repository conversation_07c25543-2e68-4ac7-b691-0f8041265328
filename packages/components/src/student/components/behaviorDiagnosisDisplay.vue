<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });
  const records = ref({ ...props.record });

  const analyzeColumns = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      width: 100,
      align: 'center',
    },
    { title: '诊断内容', dataIndex: 'diagnoseContent' },
    { title: '诊断结果', dataIndex: 'isCompleted', slotName: 'isCompleted', width: 140, align: 'center' },
  ]);

  /* 诊断结果 */
  const diagnosisResults = ref<Array<any>>();
  /* 诊断类容 */
  const BehaviorDiagnoseDict = ref<Record<string, any>>();
  const loadData = async () => {
    try {
      const res = await request('/resourceRoom/BehaviorDiagnoseDict', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      BehaviorDiagnoseDict.value = res.data.items.sort((a, b) => a.id - b.id);
    } catch (error) {
      /**/
    }
  };

  const loadData2 = async (id: number) => {
    try {
      if (id) {
        const res = await request(`/resourceRoom/BehaviorRecordAnalysisEventDiagnose/findByBraId/${id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        BehaviorDiagnoseDict.value = res.data.items.sort((a, b) => a.id - b.id);
      }
    } catch (error) {
      /**/
    }
  };

  const hasProblem = (record: any, rowIndex: number): any => {
    if (record.diagnoseResult === '1' && [1, 2].includes(record.diagnoseId))
      return {
        color: 'red',
      };
    if (record.diagnoseResult === '0' && [3, 4, 5, 6, 7, 8, 9].includes(record.diagnoseId))
      return {
        color: 'red',
      };
    return {};
  };
  onMounted(async () => {
    await loadData2(props.record?.id);
  });
  watch(
    () => props.record,
    async (oldVal, newVal) => {
      records.value = { ...newVal };
      if (records.value.id) await loadData2(records.value.id);
    },
    { immediate: true },
  );
</script>

<template>
  <a-table
    :data="BehaviorDiagnoseDict"
    :columns="analyzeColumns"
    :pagination="false"
    column-resizable
    :bordered="{ cell: true }"
  >
    <template #isCompleted="{ record, rowIndex }">
      <a-table-column>
        <span v-if="record.diagnoseResult === '1'" :style="hasProblem(record, rowIndex)">是</span>
        <span v-else :style="hasProblem(record, rowIndex)">否</span>
      </a-table-column>
    </template>
  </a-table>
</template>
