<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { debouncedWatch } from '@vueuse/core';
  import { request } from '@repo/infrastructure/request';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import { Modal } from '@arco-design/web-vue';
  import SelectFromGraduated from './selectFromGraduated.vue';

  const props = defineProps({
    modelValue: {
      type: String,
      required: true,
    },
    record: {
      type: Object as PropType<any>,
      required: true,
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
    },
  });

  const queryGraduatedLoading = ref(false);
  const sameNameInGraduated = ref([]);
  const showImportFromGraduated = ref(false);

  const emit = defineEmits(['update:modelValue', 'update:record']);
  const referFromGraduatedTip = computed(() => {
    return sameNameInGraduated.value.length ? `在毕业生流转库中找到同名学生，点击引入` : '从毕业生流转库引入';
  });

  const studentName = computed({
    get() {
      return props.modelValue;
    },
    set(val: string) {
      emit('update:modelValue', val);
    },
  });

  const handleShowImportFromGraduated = () => {
    showImportFromGraduated.value = true;
  };

  const handleImportFromGraduated = (student: any) => {
    studentName.value = student.name;
    const importedStudent = {
      ...props.record,
      ...student,
      id: undefined,
      fusionSchool: undefined,
      fusionSchoolId: undefined,
      fusionSchoolName: undefined,
      gradeClass: undefined,
      graduated: false,
      graduatedSetTime: undefined,
      gotoAfterGraduation: undefined,
      latestTransferRecord: undefined,
      sourceStudentId: student.id,
    };

    emit('update:record', importedStudent);
    showImportFromGraduated.value = false;

    Modal.success({
      title: '引入成功',
      content: '已成功引入毕业生基本信息，您可以修改其基本信息；学生的历史档案将在保存后自动导入',
    });
  };

  const queryStudentGraduated = async (name: string) => {
    queryGraduatedLoading.value = true;
    if (!name) {
      return;
    }
    try {
      const { data } = await request('/resourceRoom/student', {
        params: {
          name,
          _og: '1',
          onlyNotCirculate: '1',
          pageSize: 1,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      sameNameInGraduated.value = data.items;
    } finally {
      queryGraduatedLoading.value = false;
    }
  };

  const clientRole = getClientRole();

  debouncedWatch(
    studentName,
    async (val) => {
      await queryStudentGraduated(val);
    },
    { debounce: 300 },
  );
</script>

<template>
  <div v-if="!record.id && clientRole === 'Company'" class="w-full flex gap-2">
    <div class="flex-1">
      <a-tooltip :content="record.guid ? '引入的学生不能修改姓名' : '输入姓名检索是否在毕业流转库中存在'">
        <a-input
          v-model="studentName"
          :disabled="record.guid"
          v-bind="{
            ...$attrs,
            ...(schemaField.inputWidgetProps || {}),
          }"
        />
      </a-tooltip>
    </div>
    <a-tooltip
      :content="referFromGraduatedTip"
      :popup-visible="sameNameInGraduated.length && !record.guid && studentName"
    >
      <a-button :loading="queryGraduatedLoading" @click="handleShowImportFromGraduated">
        <template #icon>
          <IconSearch />
        </template>
      </a-button>
    </a-tooltip>

    <a-modal v-model:visible="showImportFromGraduated" title="从毕业生流转库引入" fullscreen>
      <select-from-graduated
        v-if="showImportFromGraduated"
        :student-name="studentName"
        @import-to-current="handleImportFromGraduated"
      />
    </a-modal>
  </div>
  <div v-else>
    <a-input v-model="studentName" />
  </div>
</template>

<style scoped lang="scss"></style>
