<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { isArray } from 'lodash';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    records: {
      type: [Array, Object],
      default: () => [],
    },
    rows: {
      type: Array,
      default: () => [],
    },
  });
  const emit = defineEmits(['update:visible', 'ok']);

  const items = computed(() => {
    if (!isArray(props.records)) {
      return [props.records];
    }
    return props.rows;
  });

  const studentName = computed(() => {
    return items.value.map((item) => item.name).join('、');
  });
  const studentIds = computed(() => {
    return isArray(props.records) ? props.records.map((record: any) => record.id || record) : [props.records.id];
  });

  const loading = ref(false);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val: boolean) {
      emit('update:visible', val);
    },
  });

  const handleSave = () => {
    Modal.confirm({
      title: '请再次确认',
      content: `确认修改 ${studentName.value} 为毕业吗？`,
      onOk: async () => {
        loading.value = true;
        try {
          await request(`/resourceRoom/student/graduation/${studentIds.value.join()}`, {
            method: 'PUT',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          });
          Message.success('修改成功');
          emit('ok');
        } finally {
          loading.value = false;
          modalVisible.value = false;
        }
      },
    });

    return false;
  };

  const handleClose = () => {
    modalVisible.value = false;
  };
</script>

<template>
  <a-modal
    v-if="modalVisible"
    v-model:visible="modalVisible"
    title="学生毕业"
    :on-before-ok="handleSave"
    :ok-loading="loading"
    @close="handleClose"
  >
    <strong>
      修改 <span class="text-primary">{{ studentName }}</span> 为毕业
    </strong>
    <div class="mt-2">
      <div>* 如误操作您有48小时可在毕业生信息库中撤回此操作</div>
      <div>* 毕业学生的所有信息将不在直接展示</div>
      <div>* 毕业学生的基本资料、所有相关档案将不能被修改</div>
      <div>* 所选学生信息进入毕业生信息库，可流转至其他学校</div>
      <div>* 请谨慎操作</div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
