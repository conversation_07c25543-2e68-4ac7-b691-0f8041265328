<script setup lang="ts">
  import { onMounted, PropType, ref } from 'vue';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import dayjs from 'dayjs';
  import RehabilitationPlanDetail from '../../institution/rehabilitationPlanDetail.vue';

  const props = defineProps({
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const detailVisible = ref(false);
  const currentRow = ref<Record<string, any>>({});
  const data = ref<any>([]);
  const options = ref<any>([]);
  const selectTime = ref(null);
  const getRehabilitationPlan = async () => {
    await request('/rehabilitation/rehabilitationPlan/findByStudent', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        studentId: props?.student.id,
      },
    }).then((res) => {
      data.value = res.data.items;
      res.data.items.forEach((item: any, index: number) => {
        if (Array.isArray(item.dateRange) && item.dateRange.length > 0) {
          const startFormatted = dayjs(item.dateRange[0]).format('YYYY-MM-DD');
          const endFormatted = dayjs(item.dateRange[1]).format('YYYY-MM-DD');
          const formattedRange = `${startFormatted} 至 ${endFormatted}`;
          options.value.push({
            label: formattedRange,
            value: item.id,
          });
        } else {
          options.value.push({
            label: `第${index + 1}份`,
            value: item.id,
          });
        }
      });
      // eslint-disable-next-line prefer-destructuring
      currentRow.value = res.data.items[0];
      selectTime.value = options.value[0]?.value;
    });
  };
  const schema = ref(null);
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationPlan');
    await getRehabilitationPlan();
  });
  const handleChange = (val: number) => {
    currentRow.value = data.value.find((item: any) => item.id === val);
  };
</script>

<template>
  <a-card>
    <template #title>
      <div class="w-full flex justify-between">
        <span>{{ student.name + '康复计划' }}</span>
        <div class="flex justify-between space-x-2">
          <a-select
            v-model="selectTime"
            placeholder="请选择计划"
            size="mini"
            class="max-w-72"
            :options="options"
            @change="handleChange"
          />
          <a-button v-if="currentRow?.id" type="outline" size="mini" @click="detailVisible = true">计划明细</a-button>
        </div>
      </div>
    </template>
    <record-detail v-if="currentRow?.id" ref="recordDetailRef" :show-fresh="false" :raw="currentRow" :schema="schema" />
    <a-empty v-else />
  </a-card>
  <rehabilitation-plan-detail
    v-if="detailVisible"
    v-model:visible="detailVisible"
    :plan="currentRow"
    :just-view="true"
  />
</template>

<style scoped lang="scss"></style>
