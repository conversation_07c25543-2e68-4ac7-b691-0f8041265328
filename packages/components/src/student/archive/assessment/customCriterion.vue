<script setup lang="ts">
  import { CrudTable } from '@repo/ui/components/table';
  import { onMounted, PropType, ref, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getCurrentPeriod, getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import Analysis from '@repo/components/assessment/analysis.vue';
  import { cloneDeep } from 'lodash';
  import { getOrgNature } from '../../../utils/utils';
  import { Message } from '@arco-design/web-vue'
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import {request} from "@repo/infrastructure/request";

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const schema = ref(null);
  const tableRef = ref(null);
  const periods = getPeriodsList();
  const criterionList = ref([]);
  const criterionStore = useCommonStore({
    api: '/evaluation/customCriterion',
    queryParams: {
      orgNature: getOrgNature(),
    },
  });
  const clientRole = getClientRole();

  const chartVisible = ref(false);
  const currentItem = ref(null);

  const formData = ref({
    customCriterionId: undefined,
    period: undefined,
  });

  const defaultQueryParams = ref({ sort: '-id', student: props.student.id, ...formData.value });

  const columns = [
    'criterionName',
    'timesLabel',
    'finished',
    'score',
    'evaluator',
    'evaluationDate',
    'collaborators',
    'submitStatus',
  ];

  const handleShowAnalysis = (record) => {
    currentItem.value = record;
    chartVisible.value = true;
  };
  const handleReject = async()=>{
    if(Array.isArray(tableRef.value?.selectedItems) && tableRef.value?.selectedItems.length > 0){
      await request('/evaluation/customCriterionResult/batchReject', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data:tableRef.value?.selectedItems,
      });
      tableRef.value?.loadData()
    }else Message.warning("请先选择驳回项")
  }

  onMounted(async () => {
    const rawSchema = await SchemaHelper.getInstanceByDs('/evaluation/customCriterionResult');
    schema.value = {
      ...cloneDeep(rawSchema),
      rowActions: [
        ...rawSchema.rowActions.map((item) => {
          return {
            key: item.key,
            visible: false,
          };
        }),
        { key: 'view', visible: false },
        { key: 'edit', visible: false },
        { key: 'delete', visible: false },
        { key: 'viewDetail', label: '查看详情', handler: handleShowAnalysis },
      ],
    };
    criterionList.value = await criterionStore.getOptions();
  });

  watch(
    () => formData.value,
    () => {
      if (!tableRef.value) {
        return;
      }
      tableRef.value.queryParams = {
        ...defaultQueryParams.value,
        ...tableRef.value?.queryParams?.value,
        ...formData.value,
      };
      tableRef.value?.loadData();
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>

<template>
  <a-card v-if="schema" :bordered="false" size="mini">
    <template #title>
      <a-space class="items-center"> {{ student.name }} 课标评估 </a-space>
    </template>
    <template #extra>
      <a-space>
        <a-select
          v-model="formData.customCriterionId"
          placeholder="选择评估科目"
          :options="criterionList"
          size="mini"
          allow-clear
          class="ml-4"
        />
        <a-select v-model="formData.period" placeholder="选择评估学期" :options="periods" size="mini" allow-clear />
        <div class="w-40">
          <a-input-number v-model="formData.times" size="mini" mode="button" :min="1" :max="20" allow-clear>
            <template #prefix>第</template>
            <template #suffix>次</template>
          </a-input-number>
        </div>
        <a-popconfirm @ok="handleReject" content="确认驳回？" type="warning" v-if="clientRole==='Manager'">
        <a-button size="mini" status="danger" type="outline"><icon-close />驳回</a-button>
        </a-popconfirm>
      </a-space>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="defaultQueryParams"
      :visible-columns="columns"
    >
    </crud-table>

    <analysis v-if="chartVisible" v-model:visible="chartVisible" :result-id="currentItem?.id" />
  </a-card>
</template>

<style scoped lang="scss"></style>
