<script setup lang="ts">
  // import TeacherLessonSchedule from '@repo/components/teacher/teacherLessonSchedule.vue';
  import { computed, PropType, ref, onMounted } from 'vue';
  import { useTeacherStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';

  const props = defineProps({
    student: {
      type: Object as PropType<any>,
      required: true,
    },
    mode: {
      type: String as PropType<'generation' | 'timetable'>,
      default: 'generation',
    },
  });

  const weekdays = ['周一', '周二', '周三', '周四', '周五'];

  const size = 'mini';
  const listTimetable = ref();

  const result = ref<Record<string, { conditions: any; assignments: any[] }>>({});

  const teacherGrid = ref<number[][]>([]); // 使用 ref 包裹数组
  const lessonGrid = ref<number[][]>([]);

  const handleResult = (list: any[]) => {
    teacherGrid.value = [];
    lessonGrid.value = [];

    list.forEach((item) => {
      const { x, y } = item.coordinate;
      if (!teacherGrid.value[x]) {
        teacherGrid.value[x] = [];
        lessonGrid.value[x] = [];
      }
      teacherGrid.value[x][y] = item.teacherId;
      lessonGrid.value[x][y] = item.lessonId;
    });
  };

  const selectPeriod = ref();

  const handleTimeTable = (list: any[]) => {
    const gradId = props.student?.gradeClass.grade.id;
    const gradClassId = props.student?.gradeClass.id;
    list.forEach((item) => {
      if (!result.value[item.period]) result.value[item.period] = { conditions: null, assignments: [] };

      item.gradeConditions.forEach((condition) => {
        if (condition.gradeId === gradId) {
          result.value[item.period].conditions = condition;
        }
      });
      item.assignments.forEach((assignment) => {
        if (assignment.gradeId === gradId && assignment.gradeClassId === gradClassId) {
          result.value[item.period].assignments.push(assignment);
        }
      });
    });
    const key = Object.keys(result.value)[0];
    selectPeriod.value = key;
    handleResult(result.value[key]?.assignments);
  };

  const options = computed(() => {
    return Object.keys(result.value).map((item) => ({
      label: item,
      value: item,
    }));
  });

  const handleChange = (val: string) => {
    if (val) handleResult(result.value[val]?.assignments);
  };

  const schoolCourseStore = useSchoolCourseStore();
  const allLessons = ref<any[]>([]);

  const allLessonsMap = computed(() => {
    return allLessons.value.reduce((acc, lesson) => {
      acc[lesson.id] = lesson;
      return acc;
    }, {});
  });

  const teachersMap = ref<any>({});
  const initMap = async () => {
    try {
      const teacherRes = await request('/org/companyUser', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          branchOffice: props.student?.branchOfficeId,
        },
      });
      teacherRes.data.items.forEach((item: any) => {
        teachersMap.value[item.id] = item.name;
      });
      allLessons.value = await schoolCourseStore.getSchoolCourses();
    } finally {
      /**/
    }
  };

  const loadData = async () => {
    try {
      const res: any = await request('/teacher/timetable/getStudentAllTable', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
        params: {
          gradId: props.student?.gradeClass.grade.id,
          schoolId: props.student?.gradeClass.grade.school.id,
        },
      });
      listTimetable.value = res.data;
      handleTimeTable(res.data);
    } finally {
      /**/
    }
  };

  const getTdBgClass = (periodIndex, idx) => {
    const teacherId = teacherGrid.value[periodIndex]?.[idx];
    const lessonId = lessonGrid.value[periodIndex]?.[idx];
    if (!teacherId && !lessonId) {
      return 'bg-red-100';
    }
    const a = teachersMap.value[teacherGrid.value[periodIndex][idx]];
    const b = allLessonsMap.value[lessonGrid.value[periodIndex][idx]];
    if (!a || !b) {
      return 'bg-red-100';
    }

    if (!teacherId || !lessonId) {
      return 'bg-red-50';
    }
    return '';
  };

  onMounted(async () => {
    Message.loading('加载中...');
    await initMap();
    await loadData();
  });
</script>

<template>
  <a-card>
    <template #title>
      <div>
        <span>{{ student.name + '的课表' }}</span>
        <a-select
          v-model="selectPeriod"
          class="max-w-72 mr-2 ml-5"
          :size="size"
          placeholder="请选择学期"
          :options="options"
          @change="handleChange"
        ></a-select>
      </div>
    </template>
    <div class="flex justify-center items-center">
      <table
        v-if="teacherGrid.length > 0"
        class="border-collapse border border-slate-400 w-full max-w-7xl text-center mt-2"
      >
        <thead>
          <tr>
            <th class="border border-slate-300 bg-slate-100 p-4"> 节次 / 周</th>
            <th v-for="d in weekdays" :key="d" class="border border-slate-300 bg-slate-100 p-4">{{ d }}</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(period, periodIndex) in result['2023年春季学期']?.conditions.dayPeriodSequence"
            :key="periodIndex"
          >
            <th class="border border-slate-300 p-4">
              {{ period.name }}
            </th>
            <td
              v-for="(type, idx) in period.types"
              :key="idx"
              style="width: 17%"
              class="border border-slate-300 p-4"
              :class="getTdBgClass(periodIndex, idx)"
            >
              <div v-if="teacherGrid[periodIndex]" class="cursor-move" :draggable="true">
                <div class="text-gray-500 cursor-pointer">
                  {{ teachersMap[teacherGrid[periodIndex][idx]] || '未定义' }}
                </div>
                <div class="mt-2 cursor-pointer">
                  {{ allLessonsMap[lessonGrid[periodIndex][idx]]?.name || '无课程' }}
                </div>
              </div>
              <div v-else>本节课排课失败</div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </a-card>
</template>

<style scoped lang="scss"></style>
