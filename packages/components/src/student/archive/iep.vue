<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { VuePrintNext } from 'vue-print-next';
  import IepTargetsModal from '../../iep/iepTargetsModal.vue';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const periods = ref([]);
  const currentPeriod = ref(null);
  const iepData = ref(null);
  const loading = ref(false);
  const schema = ref(null);
  const targetEditVisible = ref(false);
  const clientRole = getClientRole();

  const handlePeriodChange = async (id) => {
    loading.value = true;
    try {
      const { data } = await request(`/resourceRoom/individualizedEducation/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      iepData.value = data;
    } finally {
      loading.value = false;
    }
  };

  const handleReject = async () => {
    loading.value = true;
    try {
      await request(`/resourceRoom/individualizedEducation/${iepData.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...iepData.value,
          submitStatus: 'Rejected',
        },
      });

      periods.value = periods.value.filter((item) => item.value !== currentPeriod.value);

      if (periods.value.length > 0) {
        currentPeriod.value = periods.value[0].value;
        await handlePeriodChange(currentPeriod.value);
      } else {
        currentPeriod.value = null;
        iepData.value = null;
      }
    } finally {
      loading.value = false;
    }
  };

  const printId = computed(() => {
    return `student-archive-iep-${iepData.value?.id}`;
  });

  const handlePrint = () => {
    new VuePrintNext({
      el: `#${printId.value}`,
      zIndex: 9999,
      printMode: 'popup',
      hide: '.no-print',
    });
  };

  onMounted(async () => {
    loading.value = true;
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/individualizedEducation');
    const { data } = await request(`/resourceRoom/individualizedEducation/studentPeriods/${props.student.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    periods.value = data?.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });

    if (data.length > 0) {
      currentPeriod.value = data[0].id;

      await handlePeriodChange(data[0].id);
    } else {
      loading.value = false;
    }
  });

  const resetData = () => {
    iepData.value = null;
    periods.value = [];
    currentPeriod.value = null;
  };
  const initData = async (id: number) => {
    loading.value = true;
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/individualizedEducation');
    const { data } = await request(`/resourceRoom/individualizedEducation/studentPeriods/${id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    resetData();
    periods.value = data?.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });

    if (data.length > 0) {
      currentPeriod.value = data[0].id;

      await handlePeriodChange(data[0].id);
    } else {
      loading.value = false;
    }
  };
  watch(
    () => props.student.id,
    async (newVal) => {
      await initData(newVal);
    },
  );
</script>

<template>
  <a-spin class="w-full" :loading="loading">
    <a-card size="mini" :bordered="false" :title="`${student?.name} 个别化教育计划`">
      <template #extra>
        <a-space class="no-print">
          <a-select
            v-model="currentPeriod"
            :options="periods"
            size="mini"
            placeholder="请选择学期"
            @change="handlePeriodChange"
          />

          <a-popconfirm
            v-if="clientRole === 'Manager'"
            content="确定要驳回此条数据吗？"
            type="warning"
            @ok="handleReject"
          >
            <a-button v-if="iepData?.id" size="mini" status="danger" type="outline">
              <template #icon>
                <IconClose />
              </template>
              驳回
            </a-button>
          </a-popconfirm>

          <a-button :disabled="!iepData?.id" size="mini" type="outline" @click="() => (targetEditVisible = true)">
            <template #icon>
              <IconCheckSquare />
            </template>
            计划目标
          </a-button>

          <a-button v-if="iepData?.id" size="mini" type="secondary" @click="handlePrint">
            <template #icon>
              <IconPrinter />
            </template>
            打印此页
          </a-button>
        </a-space>
      </template>

      <div v-if="!currentPeriod">
        <a-empty v-if="periods.length" description="请选择学期以查看个别化教育计划" />
        <a-empty v-else description="暂无个别化教育计划" />
      </div>

      <div v-else-if="iepData?.id && !loading" :id="`student-archive-iep-${iepData?.id}`" class="printable">
        <record-detail ref="recordDetailRef" :show-fresh="false" :raw="iepData" :schema="schema" />
      </div>
    </a-card>

    <iep-targets-modal v-if="targetEditVisible" v-model="targetEditVisible" :iep="iepData" :editable="false" />
  </a-spin>
</template>

<style scoped lang="scss">
  @media screen {
    .print-title {
      display: none;
    }
  }
  @media print {
    .printable {
      width: 110%;
      transform: scale(0.9);
      transform-origin: 0 0;
    }
    .print-title {
      display: inline;
    }

    * {
      page-break-inside: avoid;
    }
  }
</style>
