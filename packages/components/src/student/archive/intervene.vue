<script setup lang="ts">
  import { onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import StatisticalAnalysis from '../components/statisticalAnalysis.vue';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const size = 'mini';
  const columns = [
    { title: '目标行为', dataIndex: 'targetBehavior' },
    { title: '地点', dataIndex: 'place' },
    { title: '时间', dataIndex: 'timeDate' },
    { title: '执行情况', dataIndex: 'executeInfo' },
    { title: '目标行为', dataIndex: 'happenTimes', slotName: 'happenTimes' },
    { title: '问题行为', dataIndex: 'hopeHappenTimes', slotName: 'hopeHappenTimes' },
  ];
  const startTime = ref<string>(null);
  const endTime = ref<string>(null);
  const data = ref();
  const initData = async (id: number) => {
    if (id)
      await request(`/resourceRoom/behavioralInterventionRecord/findByStudent`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          StudentId: id,
          startTime: startTime.value,
          endTime: endTime.value,
        },
      }).then((res) => {
        data.value = res.data.items;
      });
  };
  const handleChange = async (val: Array<string>) => {
    if (Array.isArray(val)) {
      [startTime.value, endTime.value] = val;
      await initData(props.student?.id);
    } else {
      startTime.value = null;
      endTime.value = null;
    }
  };

  onMounted(async () => {
    await initData(props.student?.id);
  });
</script>

<template>
  <a-card :title="student?.name + '行为干预'" :bordered="false">
    <a-descriptions :column="1" :bordered="true">
      <a-descriptions-item label="干预记录">
        <a-range-picker :size="size" value-format="YYYY-MM-DD" class="mb-2" @change="handleChange" />
        <a-table :columns="columns" :data="data"></a-table>
      </a-descriptions-item>
      <a-descriptions-item label="统计分析">
        <statistical-analysis :student="student" />
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<style scoped lang="scss"></style>
