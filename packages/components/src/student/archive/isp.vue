<script setup lang="ts">
  import { onMounted, PropType, ref, nextTick } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  // import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import _ from 'lodash';
  import recordDetail from '@repo/ui/components/record-detail/index.vue';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const record = ref<any>({});
  const recordDetailVisible = ref(true);
  const data = ref();
  const period = ref<any>([]);
  const defaultSelect = ref(null);
  const getIsp = async () => {
    await request('/resourceRoom/individualizedSupportPlan/findByStudent', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        StudentId: props.student?.id,
      },
    }).then((res) => {
      data.value = res.data.items;
      // eslint-disable-next-line prefer-destructuring
      record.value = res.data.items[0];
      defaultSelect.value = record.value?.id;
      res.data.items.forEach((item: any) => {
        period.value.push({
          label: item.gradePeriod,
          value: item.id,
        });
      });
    });
  };
  const schema = ref(null);
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/individualizedSupportPlan');
    await getIsp();
  });
  const handleChange = (val: string) => {
    recordDetailVisible.value = false;
    record.value = data.value.find((item: any) => item.id === val);
    nextTick(() => {
      recordDetailVisible.value = true;
    });
  };
</script>

<template>
  <a-card :title="student.name + '支持计划'">
    <template #extra>
      <a-select v-model="defaultSelect" placeholder="请选择学期" class="max-w-64" @change="handleChange">
        <a-option v-for="item in period" :key="item.value" :value="item.value">{{ item.label }}</a-option>
      </a-select>
    </template>
    <record-detail
      v-if="!_.isEqual(record, {}) && record && recordDetailVisible"
      ref="recordDetailRef"
      :show-fresh="false"
      :raw="record"
      :schema="schema"
    />
    <a-empty v-else />
  </a-card>
</template>

<style scoped lang="scss"></style>
