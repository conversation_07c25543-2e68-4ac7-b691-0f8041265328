<script setup lang="ts">
  import { PropType, watch, ref, onMounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import BehaviorAnalysisDisplay from '../components/behaviorAnalysisDisplay.vue';
  import BehaviorDiagnosisDisplay from '../components/behaviorDiagnosisDisplay.vue';
  import BehaviorEventDisplay from '../components/behaviorEventDisplay.vue';
  import BehaviorStrategyDisplay from '../components/behaviorStrategyDisplay.vue';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const record = ref();
  const defualt = ref(null);
  const eventOptions = ref<any>([]);
  const initData = async (id: number) => {
    // 加载行为分析的所有事件
    if (id) {
      await request(`/resourceRoom/behaviorRecordAnalysis/findByStudent/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      }).then((res) => {
        res.data.items.forEach((item: any) => {
          eventOptions.value.push({
            label: item.event,
            value: item.id,
            keep: item,
          });
        });
        record.value = eventOptions.value[0]?.keep;
        defualt.value = record.value;
      });
    }
  };
  const handelChange = (val) => {
    record.value = val;
  };
  onMounted(async () => {
    await initData(props?.student.id);
  });

  watch(
    () => props.student.id,
    async (newVal) => {
      await initData(newVal);
    },
  );
</script>

<template>
  <a-card :title="student.name + '行为记录'" :bordered="false">
    <template #extra>
      <a-select v-model="defualt" placeholder="请选择行为" size="mini" class="min-w-32" @change="handelChange">
        <a-option v-for="item in eventOptions" :key="item.value" :value="item.keep">{{ item.label }}</a-option>
      </a-select>
    </template>
    <div v-if="record?.id" class="">
      <a-descriptions :column="4" :bordered="true" class="mb-2">
        <a-descriptions-item label="姓名">{{ student.name }} </a-descriptions-item>
        <a-descriptions-item label="性别">{{ student.gender }} </a-descriptions-item>
        <a-descriptions-item label="年龄">{{ student.age }}</a-descriptions-item>
        <a-descriptions-item label="当前事件">{{ record?.event }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions size="medium" :column="1" :bordered="true">
        <a-descriptions-item label=" 行为事件"> <behavior-event-display :record="record" /> </a-descriptions-item>
        <a-descriptions-item label="诊断信息"><behavior-diagnosis-display :record="record" /></a-descriptions-item>
        <a-descriptions-item label="症解"><behavior-analysis-display :record="record" /> </a-descriptions-item>
        <a-descriptions-item label="策略"> <behavior-strategy-display :record="record" /> </a-descriptions-item>
      </a-descriptions>
    </div>
    <a-empty v-else />
  </a-card>
</template>

<style scoped lang="scss"></style>
