<script setup lang="ts">
  import { onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import TeachingArchiveModal from './studentLessonPlan/teachingArchiveModal.vue';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';

  const props = defineProps({
    student: {
      type: Object as PropType<any>,
      required: true,
    },
    myTeachers: {
      type: Array,
    },
  });
  const size = 'mini';
  const detailVisible = ref(true);
  const currentCourse = ref<number | null>(null);
  const store = useSchoolCourseStore();
  const courseMap = ref({});

  const options = ref<any>([]);

  const curseList = ref<any>([]);
  const teachers = ref([]);
  const cbIds = ref([]);
  const course = ref(null);
  const period = ref(null);
  const courseOptions = ref<any>([]);

  const handlePeriodChange = (val: string) => {
    course.value = null;
    courseOptions.value = [];
    curseList.value.forEach((item, index: number) => {
      if (item.gradePeriod === val) {
        courseOptions.value.push({
          label: courseMap.value[item.category]?.name || `课程${index}`,
          value: item.id,
        });
      }
    });
  };
  const handleCourseChange = (val: number) => {
    if (val) currentCourse.value = val;
  };

  const initSelect = (list: any[]) => {
    const date = new Date();
    const currentYear = String(date.getFullYear());
    const res = list.find((item) => item.gradePeriod.indexOf(currentYear) > -1);
    if (res) {
      period.value = res?.gradePeriod;
      handlePeriodChange(res?.gradePeriod);
      course.value = res?.id;
      currentCourse.value = course.value;
    } else {
      period.value = null;
      course.value = null;
    }
  };

  const loadDta = async () => {
    try {
      const res = await request('/org/companyUser', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          branchOffice: props.student?.branchOfficeId,
        },
      });
      teachers.value = res.data.items;
      cbIds.value = res.data.items.map((item: any) => item.id);
      const result = await request('/course/course/courseListByCbIdIn', {
        baseURL: PROJECT_URLS.GO_PROJECT_API,
        method: 'get',
        params: {
          page: 1,
          pageSize: 999,
          cbIds: cbIds.value,
        },
      });
      curseList.value = result.data.list;
      const set = new Set();
      result.data.list.forEach((item) => {
        set.add(item.gradePeriod);
      });
      set.forEach((gradePeriod) => {
        options.value.push({
          label: gradePeriod,
          value: gradePeriod,
        });
      });
      initSelect(curseList.value);
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    Message.loading('加载中...');
    courseMap.value = await store.getSchoolCoursesMap();
    await loadDta();
  });
</script>

<template>
  <a-card :bordered="false">
    <template #title>
      <div>
        <span class="mr-5">{{ student.name }}教学档案</span>
        <a-select
          v-model="period"
          :size="size"
          placeholder="请选择学期"
          :options="options"
          class="max-w-64 mr-2"
          @change="handlePeriodChange"
        />
        <a-select
          v-model="course"
          :size="size"
          placeholder="请选择科目"
          :options="courseOptions"
          class="max-w-64"
          @change="handleCourseChange"
        />
      </div>
    </template>
    <div>
      <teaching-archive-modal v-if="currentCourse" v-model="detailVisible" :course-id="currentCourse" />
      <a-empty v-else />
    </div>
  </a-card>
</template>

<style scoped lang="scss"></style>
