<script setup lang="ts">
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    student: {
      type: Object,
      required: true,
    },
  });
</script>

<template>
  <a-card :title="`${student.name} 基本档案`" :bordered="false" size="mini">
    <record-detail ref="recordDetailRef" :raw="student" :schema="studentSchema" />
  </a-card>
</template>

<style scoped lang="scss"></style>
