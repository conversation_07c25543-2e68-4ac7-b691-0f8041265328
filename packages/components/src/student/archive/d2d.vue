<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import _, { isEqual } from 'lodash';

  // import SendEduRecord from '../../sendEduPlan/sendEduRecord.vue';
  import Detail from '../../teacher/detail.vue';

  const props = defineProps({
    student: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const schema = ref(null);
  const data = ref<any>([]);
  const raw = ref<Record<string, any>>({});
  const recordVisible = ref(false);
  const periodOptions = ref<any>([]);
  const period = ref(null);
  const loadSendPlanSimple = async () => {
    await request(`resourceRoom/sendEducationPlan/findByStudent`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        studentId: props.student?.id,
        period: period.value,
      },
    }).then((res) => {
      if (res.data.items?.length > 0) {
        data.value = res.data.items;
        // eslint-disable-next-line prefer-destructuring
        raw.value = data.value[0];
        period.value = raw.value.period;
        res.data.items.forEach((item: any) => {
          periodOptions.value.push({
            label: item.period,
            value: item.period,
          });
        });
      }
    });
  };
  const detailVisible = ref(false);
  const records = ref({});
  const loadDocument = async () => {
    // 加载学生的全部送教计划
    const api = props.student?.id
      ? `/resourceRoom/d2dEducation/findLastPlanByStudent/${props.student?.id}`
      : '/resourceRoom/d2dEducation';
    await request(api, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      data.value = res.data.items;
      // eslint-disable-next-line prefer-destructuring
      records.value = data.value[0];
      period.value = records.value?.period;
      res.data.items.forEach((item: any) => {
        periodOptions.value.push({
          label: item.period,
          value: item.period,
        });
      });
      detailVisible.value = true;
    });
  };
  const configuration = ref<any>({});
  const viewInArchive = ref(false);
  const loadConfiguration = async () => {
    await request(`/resourceRoom/centralConfiguration/findByCompany`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      configuration.value = res.data;
      if (configuration.value.sendVersion === 1) {
        loadSendPlanSimple();
      } else {
        loadDocument();
      }
    });
  };
  const handleChange = (val: string) => {
    const res = data.value.find((item: any) => item.period === val);
    if (configuration.value?.sendVersion === 1) raw.value = res;
    else records.value = res;
  };
  const flush = () => {
    /**/
  };
  const ready = ref(false);
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationPlan');
    await loadConfiguration();
    ready.value = true;
  });

  watch(
    () => props.student.id,
    async (newVal) => {
      await loadConfiguration();
    },
  );
</script>

<template>
  <div v-if="ready">
    <a-card
      v-if="!isEqual(raw, {}) && configuration?.sendVersion === 1"
      :title="student.name + '送教计划'"
      :bordered="false"
    >
      <a-select v-model="period" placeholder="请选择学期" class="max-w-64 mb-2 mr-2" @change="handleChange">
        <a-option v-for="item in periodOptions" :key="item.value">{{ item.label }}</a-option>
      </a-select>
      <a-button @click="recordVisible = true">送教记录</a-button>
      <a-descriptions :column="4" :bordered="true" class="mb-10">
        <a-descriptions-item label="学生">{{ raw?.student?.name }}</a-descriptions-item>
        <a-descriptions-item label="年龄">{{ raw?.age }}</a-descriptions-item>
        <a-descriptions-item label="性别">{{ raw?.gender }}</a-descriptions-item>
        <a-descriptions-item label="性别">{{ raw?.student?.disorders }}</a-descriptions-item>
        <a-descriptions-item label="学期">{{ raw?.period }}</a-descriptions-item>
        <a-descriptions-item label="制定计划教师">{{ raw?.personInCharge }}</a-descriptions-item>
        <a-descriptions-item label="学校">{{ raw?.school }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions :column="1" :bordered="true" class="mb-10">
        <a-descriptions-item label="送教措施">
          {{ raw?.sendEducationMeasures }}
        </a-descriptions-item>
        <a-descriptions-item label="送教计划">
          {{ raw?.sendEducationMeasures }}
        </a-descriptions-item>
      </a-descriptions>
      <a-descriptions :column="1" :bordered="true" class="mb-10">
        <a-descriptions-item label="附件列表">
          <AttachmentsPreviewDisplay v-if="raw.attachments?.length" :raw="raw.attachments" class="ml-10" />
        </a-descriptions-item>

        <a-descriptions-item label="详情内容">
          <div v-html="raw.detailContent" />
        </a-descriptions-item>
      </a-descriptions>
      <a-descriptions :column="3" :bordered="true">
        <a-descriptions-item label="创建时间">
          {{ raw.createdDate.split(' ')[0] }}
        </a-descriptions-item>
        <a-descriptions-item label="已完成">
          <icon-check-circle v-if="raw.finished" class="text-green-600" style="color: green" />
          <icon-close-circle v-else class="text-red-500" style="color: red" />
        </a-descriptions-item>
        <a-descriptions-item label="分享者">
          <a-tag v-for="item in raw.collaborators" :key="item.id" class="rounded" size="mini">{{ item.name }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ raw.createdBy.name }}
        </a-descriptions-item>
        <a-descriptions-item label="最后修改于">
          {{ raw.modifiedDate.split(' ')[0] }}
        </a-descriptions-item>
      </a-descriptions>
      <send-edu-record v-if="raw && recordVisible" v-model:visible="recordVisible" :plan="raw" />
    </a-card>
    <div v-else-if="configuration?.sendVersion === 2">
      <detail
        v-if="detailVisible"
        v-model="detailVisible"
        :visible="detailVisible"
        :record="records"
        :student="student"
        :view-in-archive="viewInArchive"
        @update:model-value="flush"
      >
        <template #select>
          <a-select
            v-model="period"
            placeholder="请选择学期"
            class="max-w-64 mb-2 mr-2"
            size="mini"
            @change="handleChange"
          >
            <a-option v-for="item in periodOptions" :key="item.value">{{ item.label }}</a-option>
          </a-select>
        </template>
      </detail>
    </div>
    <a-empty v-else />
  </div>
</template>

<style scoped lang="scss"></style>
