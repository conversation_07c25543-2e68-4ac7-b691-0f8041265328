<script setup lang="ts">
  import { computed, defineAsyncComponent, onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS, getClientRole } from '@repo/env-config';
  import { IconUser } from '@arco-design/web-vue/es/icon';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { getOrgNature } from '../utils/utils';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    studentId: {
      type: Number,
    },
    baseInfo: {
      type: Object as PropType<Record<string, any>>,
    },
  });

  const emit = defineEmits(['update:visible']);

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const splitSize = ref(200);
  const studentSchema = ref<any>();
  const student = ref<Record<string, any>>(props.baseInfo || {});
  const currentArchiveComponent = ref<any>(null);
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });
  const menusMap = {
    baseInfo: {
      title: '基本档案',
      cmp: defineAsyncComponent(() => import('./archive/baseInfo.vue')),
    },
    assessment: {
      title: '评估档案',
      cmp: defineAsyncComponent(() => import('./archive/assessment.vue')),
    },
    iep: {
      title: '个别化教育计划',
      cmp: defineAsyncComponent(() => import('./archive/iep.vue')),
    },
    isp: {
      title: '支持计划',
      cmp: defineAsyncComponent(() => import('./archive/isp.vue')),
    },
    d2d: {
      title: '送教计划',
      cmp: defineAsyncComponent(() => import('./archive/d2d.vue')),
    },
    rehabilitationPlan: {
      title: '康复计划',
      cmp: defineAsyncComponent(() => import('./archive/rehabilitationPlan.vue')),
    },
    record: {
      title: '行为记录',
      cmp: defineAsyncComponent(() => import('./archive/record.vue')),
    },
    intervene: {
      title: '行为干预',
      cmp: defineAsyncComponent(() => import('./archive/intervene.vue')),
    },
    studentLessonPlan: {
      title: '学生教案',
      cmp: defineAsyncComponent(() => import('./archive/studentLessonPlan.vue')),
    },
    classSchedule: {
      title: '班级课表',
      cmp: defineAsyncComponent(() => import('./archive/classSchedule.vue')),
    },
  };

  const orgNature = getOrgNature();
  const isSpecialSchool = computed(() => {
    return orgNature === '特殊教育学校';
  });

  const clientRole = getClientRole();
  const isManager = computed(() => {
    return clientRole === 'Manager';
  });
  const loadStudent = async (studentId?: number) => {
    const { data } = await request(`/resourceRoom/student/${studentId || props.studentId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    student.value = data;
  };

  const handleMenuSelect = (key) => {
    currentArchiveComponent.value = menusMap[key];
  };

  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const queryParams = (): any => {
    let params = {};
    if (isManager.value) params = { ...params, schoolNature: menuInfo.app.label };
    return params;
  };
  const studentList = ref();

  const gradeList = ref<any>([]);
  const classList = ref([]);
  const currentGrade = ref();
  const currentClass = ref();
  const currentStudent = ref();

  const studentOptions = ref([]);

  const loadStudentList = async () => {
    const { data: res } = await request('/resourceRoom/student', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: { ...queryParams() },
    });
    studentList.value = res.items;
    studentOptions.value = res.items.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
    const gradeMap = new Map();
    res.items.forEach((item) => {
      const gradeKey = item.gradeClass?.grade?.id;
      if (gradeKey && !gradeMap.has(gradeKey))
        gradeMap.set(gradeKey, { label: item.gradeClass?.grade?.name, value: item.gradeClass?.grade?.id });
    });
    gradeList.value = Array.from(gradeMap.values());
  };

  const classAndStudentByGradeCatch = new Map();

  const handleGrade = async (val: number) => {
    currentClass.value = null;
    currentStudent.value = null;
    if (classAndStudentByGradeCatch.has(val)) {
      classList.value = classAndStudentByGradeCatch.get(val).classOptions;
      return;
    }
    const sameGradeStudent = studentList.value.filter((item) => item.gradeClass?.grade?.id === val);
    if (sameGradeStudent) {
      const classMap = new Map();
      sameGradeStudent.forEach((item) => {
        const classKey = item.gradeClass?.id;
        if (classKey && !classMap.has(classKey))
          classMap.set(classKey, { label: item.gradeClass?.className, value: item.gradeClass?.id });
      });
      classAndStudentByGradeCatch.set(val, {
        classOptions: Array.from(classMap.values()),
        sameGradeStudent,
      });
      classList.value = Array.from(classMap.values());
      return;
    }
    classList.value = [];
  };

  const handleClass = async (val: number) => {
    currentStudent.value = null;
    studentOptions.value = [];
    const sameClassStudent = classAndStudentByGradeCatch
      .get(currentGrade.value)
      .sameGradeStudent.filter((item) => item.gradeClass?.id === val);
    studentOptions.value = sameClassStudent.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  };

  const handleStudentChange = async (val: number) => {
    await loadStudent(val);
  };

  onMounted(async () => {
    studentSchema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/student');
    if (props.studentId) {
      await loadStudent();
    }
    await loadStudentList();
    currentArchiveComponent.value = menusMap.baseInfo;
  });
</script>

<template>
  <a-modal v-model:visible="visible" fullscreen hide-cancel ok-text="关闭">
    <template #title>
      <div class="flex items-center space-x-5 w-full">
        <span class="flex-shrink-0"> {{ student.gradeClass?.name }} {{ student.name }} 的数字档案 </span>
        <a-select
          v-if="isManager && isSpecialSchool"
          v-model="currentGrade"
          placeholder="请选择年级"
          allow-search
          class="!w-60"
          :size="splitSize"
          :options="gradeList"
          @change="handleGrade"
        />
        <a-select
          v-if="isManager && isSpecialSchool"
          v-model="currentClass"
          placeholder="请选择班级"
          allow-search
          class="!w-60"
          :size="splitSize"
          :options="classList"
          @change="handleClass"
        />
        <a-select
          v-model="currentStudent"
          placeholder="请选择学生"
          allow-search
          class="!w-40"
          :size="splitSize"
          :options="studentOptions"
          @change="handleStudentChange"
        />
      </div>
    </template>
    <a-split v-model:size="splitSize" class="split-wrapper">
      <template #first>
        <a-menu :accordion="true" :default-selected-keys="['baseInfo']" @menu-item-click="handleMenuSelect">
          <a-menu-item key="baseInfo">
            <IconUser />
            基本档案
          </a-menu-item>
          <a-menu-item key="assessment">
            <IconCalendar />
            评估档案
          </a-menu-item>
          <a-sub-menu key="plan">
            <template #title>
              <IconCalendar />
              教育计划
            </template>
            <a-menu-item key="iep">个别化教育计划</a-menu-item>
            <a-menu-item key="isp">支持计划</a-menu-item>
            <a-menu-item key="d2d">送教计划</a-menu-item>
            <a-menu-item key="rehabilitationPlan">康复计划</a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="implement">
            <template #title>
              <IconUser />
              教育实施
            </template>
            <a-menu-item key="studentLessonPlan">学生教案</a-menu-item>
            <a-menu-item key="classSchedule">班级课表</a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="behavior">
            <template #title>
              <IconCalendar />
              行为档案
            </template>
            <a-menu-item key="record">行为记录</a-menu-item>
            <a-menu-item key="intervene">行为干预</a-menu-item>
          </a-sub-menu>
          <a-sub-menu v-if="false" key="rehabilitation">
            <template #title>
              <IconCalendar />
              康复记录
            </template>
            <a-menu-item key="rehabilitationAssessment">康复评估</a-menu-item>
          </a-sub-menu>
        </a-menu>
      </template>
      <template #second>
        <component
          :is="currentArchiveComponent.cmp"
          v-if="currentArchiveComponent && studentSchema"
          :student-schema="studentSchema"
          :student="student"
        />
        <a-empty v-else description="加载中..." />
      </template>
    </a-split>
  </a-modal>
</template>

<style scoped lang="scss">
  .split-wrapper {
    height: calc(100vh - 165px);
  }
</style>
