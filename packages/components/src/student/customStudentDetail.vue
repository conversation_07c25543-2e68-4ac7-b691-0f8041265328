<script setup lang="ts">
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { PropType, provide } from 'vue';
  import { Schema } from '@repo/infrastructure/types';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import DevStudentView from '../development/dev-student-view.vue';

  const props = defineProps({
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  provide('RecordDetail', RecordDetail);
</script>

<template>
  <customize-component
    v-if="raw"
    :current-record="raw"
    :raw="raw"
    :schema="schema"
    module="Student"
    page="View"
    model-value=""
  >
    <record-detail ref="recordDetailRef" :raw="raw" :schema="schema" />
    <!--    <dev-student-view :raw="raw" :schema="schema" />-->
  </customize-component>
</template>

<style scoped lang="scss"></style>
