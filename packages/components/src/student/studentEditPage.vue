<script setup lang="ts">
  import { CardForm } from '@repo/ui/components/form';
  import { inject, onMounted, provide, ref, toRaw } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getOrgNature } from '@repo/components/utils/utils';
  import StudentInfoCards from './StudentInfoCards.vue';

  const props = defineProps({
    studentId: {
      type: String,
      default: '',
    },
  });

  const editData = ref({});
  const router: any = inject('router');
  const route = toRaw(router.currentRoute.value || router.currentRoute);

  const id = ref(route?.params.id || route?.query.id);

  const schema = ref<any>(null);
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/student');
  });
</script>

<template>
  <div class="h-full w-full">
    <!-- 使用新的学生信息卡片组件 -->
    <student-info-cards v-if="schema" :student-id="id" :nature="getOrgNature()" />

    <!-- 原有的表单组件（如果需要保留） -->
    <card-form
      v-if="schema && false"
      v-model="editData"
      :raw-id="studentId"
      size="mini"
      :schema="schema"
      module-name="学生信息"
    />
  </div>
</template>

<style scoped lang="scss"></style>
