<script setup lang="ts">
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, PropType, ref, watch } from 'vue';
  import GradeClassSelect from './gradeClassSelect.vue';
  import { getOrgNature } from '../utils/utils';

  const props = defineProps({
    useGradeClassFilter: {
      type: Boolean,
      default: false,
    },
    gradeClass: {
      type: Object,
      default: null,
    },
    student: {
      type: Object,
      default: null,
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    autoDisableSelected: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String as PropType<'mini' | 'small' | 'medium' | 'large'>,
      default: 'small',
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    selectAllVisible: {
      type: Boolean,
      default: false,
    },
  });

  const { loading: searchLoading, setLoading: setSearchLoading } = useLoading();
  const dataList = ref<any[]>([]);
  const rawDataList = ref<any[]>([]);
  const params = ref<Record<string, any>>({
    ...props.defaultQueryParams,
    page: 1,
    pageSize: 100,
  });
  const currentStudent = ref<any>(props.student || {});
  const currentGradeClass = ref<any>(props.gradeClass || {});

  const orgNature = getOrgNature();

  const emit = defineEmits(['change', 'gradeClassChange', 'selectAll', 'cancel']);

  const handleSearch = async (value) => {
    setSearchLoading(true);
    try {
      params.value['name|symbol'] = `%${value}%`;
      const { data } = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          ...params.value,
          orgNature,
        },
      });
      rawDataList.value = data.items || [];
      dataList.value = data.items?.map((item) => {
        return {
          id: item.id,
          name: item.name,
        };
      });
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSelect = (student) => {
    const raw = rawDataList.value.find((item) => item.id === student.id) || student;
    currentStudent.value = student;
    emit('change', student.id, student, raw);
  };

  const isSelectAll = ref(false);
  const handleSelectAll = () => {
    isSelectAll.value = !isSelectAll.value;
    if (isSelectAll.value) {
      emit('selectAll', rawDataList.value);
    } else {
      emit('cancel');
    }
  };

  const handleGradeClassChange = async (gradeClassId, gradeClass) => {
    if (gradeClassId === -1) {
      params.value.gradeClass = null;
      params.value.fromSubgroup = true;
    } else {
      params.value.gradeClass = gradeClassId;
      params.value.fromSubgroup = null;
    }

    currentGradeClass.value = gradeClass;
    await handleSearch('');
    emit('gradeClassChange', gradeClassId, gradeClass);
  };

  defineExpose({
    handleSearch,
  });

  onMounted(async () => {
    if (props.useGradeClassFilter && props.gradeClass?.id) {
      await handleGradeClassChange(props.gradeClass?.id, props.gradeClass);
    }

    if (props.autoLoad) {
      await handleSearch('');
    }
  });

  watch(
    () => props.gradeClass,
    async (value) => {
      if (props.useGradeClassFilter && value?.id) {
        currentGradeClass.value = value;
        await handleGradeClassChange(value.id, value);
      }
    },
    { deep: true },
  );

  watch(
    () => props.student,
    async (value) => {
      if (value?.id) {
        currentStudent.value = value;
      }
    },
    { deep: true },
  );
</script>

<template>
  <div class="flex gap-2 flex-1">
    <grade-class-select
      v-if="useGradeClassFilter"
      placeholder="请先选择班级"
      :model-value="currentGradeClass?.id"
      clearable
      :size="size"
      @change="handleGradeClassChange"
    />
    <a-select
      :size="size"
      v-bind="{
        placeholder: '选择或搜索学生',
        ...$attrs,
      }"
      :model-value="currentStudent?.id"
      :loading="searchLoading"
      :filter-option="false"
      allow-search
      @search="handleSearch"
    >
      <a-option
        v-for="item in dataList"
        :key="item.id"
        :value="item.id"
        :label="item.name"
        :extra="selectedIds.includes(item.id) ? '已选择' : ''"
        :disabled="autoDisableSelected && selectedIds.includes(item.id)"
        @click="() => handleSelect(item)"
      />
      <template #footer>
        <div class="flex justify-end items-center">
          <a-button v-if="selectAllVisible" type="text" size="small" @click="handleSelectAll">
            {{ isSelectAll ? '取消全选' : '全选' }}
          </a-button>
        </div>
      </template>
    </a-select>
  </div>
</template>

<style scoped lang="scss"></style>
