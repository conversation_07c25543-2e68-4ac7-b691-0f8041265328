<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const myClasses = ref<any[]>([]);
  const currentClass = ref<any>(undefined);

  const emit = defineEmits(['change']);

  const currentClassInfo = computed(() => {
    return myClasses.value?.find((item) => item.id === currentClass.value);
  });

  const loadMyClasses = async () => {
    const { data } = await request('/resourceRoom/gradeClass', {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
      },
    });
    myClasses.value = data.items || [];
  };

  const handleClassChange = (value) => {
    emit('change', value, currentClassInfo.value);
  };

  onMounted(async () => {
    await loadMyClasses();
  });
</script>

<template>
  <a-select
    v-model="currentClass"
    v-bind="{ size: 'mini', placeholder: '选择班级', ...$attrs }"
    @change="handleClassChange"
  >
    <a-option :value="-1">分组</a-option>
    <a-option value="" label="全部班级"></a-option>
    <a-option v-for="item in myClasses" :key="item.id" :value="item.id">
      {{ item.name }}
    </a-option>
  </a-select>
</template>

<style scoped lang="scss"></style>
