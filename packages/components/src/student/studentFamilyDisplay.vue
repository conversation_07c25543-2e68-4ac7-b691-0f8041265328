<script setup lang="ts">
  import { PropType } from 'vue';

  defineProps({
    raw: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });
</script>

<template>
  <a-table v-if="raw?.length" :bordered="false" :data="raw" size="mini" class="w-full" :pagination="false">
    <template #columns>
      <a-table-column data-index="relationship" title="关系" :width="150" />
      <a-table-column data-index="name" title="姓名" :width="150" />
      <a-table-column data-index="birthday" title="生日" :width="150" />
      <a-table-column data-index="phone" title="电话" :width="150" />
      <a-table-column data-index="remark" title="备注" />
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
