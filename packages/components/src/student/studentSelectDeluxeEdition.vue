<script setup lang="ts">
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, PropType, ref, watch } from 'vue';
  import cloneDeep from 'lodash/cloneDeep';
  import GradeClassSelect from './gradeClassSelect.vue';
  import { getOrgNature } from '../utils/utils';
  import SubGroupSelect from './subGroupSelect.vue';

  const props = defineProps({
    useGradeClassFilter: {
      type: Boolean,
      default: false,
    },
    useSubgroupFilter: {
      type: Boolean,
      default: false,
    },
    gradeClass: {
      type: Object,
      default: null,
    },
    student: {
      type: Object,
      default: null,
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
    limitSelectRange: {
      type: Array,
      default: () => [],
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    maxTagCount: {
      type: Number,
      default: 2,
    },
    autoDisableSelected: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String as PropType<'mini' | 'small' | 'medium' | 'large'>,
      default: 'small',
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const { loading: searchLoading, setLoading: setSearchLoading } = useLoading();
  const dataList = ref<any[]>([]);
  const rawDataList = ref<any[]>([]);
  const params = ref<Record<string, any>>({
    ...props.defaultQueryParams,
    page: 1,
    pageSize: 100,
  });
  const currentStudent = ref<any>(props.student || {});
  const currentStudentIds = ref<any>([]);
  const currentGradeClass = ref<any>(props.gradeClass || {});

  const orgNature = getOrgNature();

  const emit = defineEmits(['change', 'gradeClassChange', 'subgroupChange', 'changeMulti']);

  const handleFilterByLimit = () => {
    if (props.limitSelectRange.length) {
      dataList.value = cloneDeep(dataList.value.filter((item) => props.limitSelectRange.includes(item.id)));
    }
  };
  const handleSearch = async (value) => {
    setSearchLoading(true);
    try {
      params.value['name|symbol'] = `%${value}%`;
      const { data } = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          ...params.value,
          orgNature,
        },
      });
      rawDataList.value = data.items || [];
      dataList.value = data.items?.map((item) => {
        return {
          id: item.id,
          name: item.name,
        };
      });
      handleFilterByLimit();
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSelect = (student: any) => {
    if (!props.multiple) {
      // 单选
      const raw = rawDataList.value.find((item) => item.id === student.id) || student;
      currentStudent.value = student;
      emit('change', student.id, student, raw);
    } else {
      // 多选
      if (!currentStudentIds.value.includes(student.id)) {
        currentStudentIds.value.push(student.id);
      } else {
        currentStudentIds.value.splice(currentStudentIds.value.indexOf(student.id), 1);
      }
      const raw = rawDataList.value.filter((item) => currentStudentIds.value.includes(item.id)) || [];
      emit('changeMulti', currentStudentIds, raw);
    }
  };
  const isSelectedAll = ref(true);
  const handleGradeClassChange = async (gradeClassId, gradeClass) => {
    isSelectedAll.value = true;
    currentStudentIds.value = [];
    if (gradeClassId === -1) {
      params.value.gradeClass = null;
      params.value.fromSubgroup = true;
    } else {
      params.value.gradeClass = gradeClassId;
      params.value.fromSubgroup = null;
    }

    currentGradeClass.value = gradeClass;
    await handleSearch('');
    handleFilterByLimit();
    emit('gradeClassChange', gradeClassId, gradeClass);
  };

  const handleGroupChange = async (val, currentGroupInfo) => {
    isSelectedAll.value = true;
    currentStudentIds.value = [];
    if (val && currentGroupInfo?.studentList) {
      const { studentList } = currentGroupInfo;
      const currentGroupStudentIds = studentList.map((item) => item?.id);
      await handleSearch('');
      dataList.value = cloneDeep(dataList.value.filter((item) => currentGroupStudentIds.includes(item.id)));
      handleFilterByLimit();
      emit('subgroupChange', val, currentGroupInfo);
    } else await handleSearch('');
  };

  defineExpose({
    handleSearch,
  });

  onMounted(async () => {
    if (props.useGradeClassFilter && props.gradeClass?.id) {
      await handleGradeClassChange(props.gradeClass?.id, props.gradeClass);
    }

    if (props.autoLoad) {
      await handleSearch('');
    }
  });

  watch(
    () => props.gradeClass,
    async (value) => {
      if (props.useGradeClassFilter && value?.id) {
        currentGradeClass.value = value;
        await handleGradeClassChange(value.id, value);
      }
    },
    { deep: true },
  );

  watch(
    () => props.student,
    async (value) => {
      if (value?.id) {
        currentStudent.value = value;
      }
    },
    { deep: true },
  );

  /*---------------------------------*/
  const filterTypes = [
    { label: '按班级选', key: 'byGradeClass' },
    { label: '按分组选', key: 'bySubGroup' },
    { label: '取消过滤', key: 'cancel' },
  ];
  const filterType = ref({
    label: '过滤方式',
    key: null,
  });

  const handleSelectAll = () => {
    isSelectedAll.value = !isSelectedAll.value;
    if (!isSelectedAll.value) {
      currentStudentIds.value = dataList.value.map((item) => item.id);
      emit('changeMulti', currentStudentIds.value, dataList.value);
    } else {
      currentStudentIds.value = [];
      emit('changeMulti', [], []);
    }
  };
  const useGradeClassFilter = ref(props.useGradeClassFilter);
  const useSubgroupFilter = ref(props.useSubgroupFilter);
  const dropdownVisible = ref(false);
  const handlePopupVisibleChange = (val: boolean) => {
    dropdownVisible.value = val;
  };
  const handleDropDownSelect = async (val: any) => {
    currentStudentIds.value = [];
    isSelectedAll.value = true;
    switch (val) {
      case 'byGradeClass':
        useGradeClassFilter.value = true;
        useSubgroupFilter.value = false;
        break;
      case 'bySubGroup':
        useSubgroupFilter.value = true;
        useGradeClassFilter.value = false;
        break;
      case 'cancel':
        useSubgroupFilter.value = false;
        useGradeClassFilter.value = false;
        break;
      default:
        break;
    }
    params.value.gradeClass = null;
    params.value.fromSubgroup = null;
    await handleSearch('');
  };
  watch(
    () => props.limitSelectRange,
    async () => {
      await handleSearch('');
    },
    { deep: true },
  );
</script>

<template>
  <div class="flex gap-2 flex-1 justify-start items-center">
    <grade-class-select
      v-if="useGradeClassFilter"
      placeholder="请先选择班级"
      :model-value="currentGradeClass?.id"
      clearable
      :size="size"
      @change="handleGradeClassChange"
    />
    <subGroupSelect v-if="useSubgroupFilter" @group-change="handleGroupChange" />
    <a-select
      :size="size"
      v-bind="{
        placeholder: '选择或搜索学生',
        ...$attrs,
      }"
      :model-value="currentStudentIds"
      :loading="searchLoading"
      :filter-option="false"
      allow-search
      :multiple="multiple"
      :max-tag-count="maxTagCount"
      @search="handleSearch"
    >
      <a-option
        v-for="item in dataList"
        :key="item.id"
        :value="item.id"
        :label="item.name"
        :extra="selectedIds.includes(item.id) ? '已选择' : ''"
        :disabled="autoDisableSelected && selectedIds.includes(item.id)"
        @click="() => handleSelect(item)"
      />
      <template #footer>
        <div class="p-2 bg-gray-100 flex justify-between space-x-4 items-center">
          <div class="flex justify-start space-x-4">
            <a-dropdown
              position="bl"
              trigger="hover"
              @popup-visible-change="handlePopupVisibleChange"
              @select="handleDropDownSelect"
            >
              <a-button size="mini">
                {{ filterType.label }}
                <template #icon>
                  <icon-double-right v-if="!dropdownVisible" />
                  <icon-double-down v-else />
                </template>
              </a-button>
              <template #content>
                <a-doption v-for="item in filterTypes" :key="item.key" :value="item.key">{{ item.label }}</a-doption>
              </template>
            </a-dropdown>
          </div>
          <a-button v-if="multiple" size="mini" type="text" @click="handleSelectAll">
            {{ isSelectedAll ? '全选' : '取消全选' }}
          </a-button>
        </div>
      </template>
    </a-select>
  </div>
</template>

<style scoped lang="scss"></style>
