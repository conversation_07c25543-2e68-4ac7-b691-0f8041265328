<script setup lang="ts">
  import { BooleanDisplay } from '@repo/ui/components/data-display/components';
  import { onMounted, PropType, ref, watch } from 'vue';
  import useSchoolCourseStore from '../../store/schoolCourseStore';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const teachingSubjectsMap = ref<any>({});
  const listData = ref<any[]>(props.raw || []);
  const courseStore = useSchoolCourseStore();

  watch(
    () => {
      return {
        raw: props.raw,
        teachingSubjectsMap: teachingSubjectsMap.value,
      };
    },
    () => {
      listData.value = props.raw.map((item) => {
        return {
          ...item,
          courses: item.courses?.map((courseId) => teachingSubjectsMap.value[courseId]?.name),
        };
      });
    },
    { deep: true, immediate: true },
  );

  onMounted(async () => {
    teachingSubjectsMap.value = await courseStore.getSchoolCoursesMap();
  });
</script>

<template>
  <a-table :data="listData" :bordered="false" :pagination="false">
    <template #columns>
      <a-table-column title="教师" data-index="name" />
      <a-table-column title="班主任" data-index="fixed">
        <template #cell="{ record }">
          <boolean-display :raw="record.fixed" />
        </template>
      </a-table-column>
      <a-table-column title="备注" data-index="remark" />
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
