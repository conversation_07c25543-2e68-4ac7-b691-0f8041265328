<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import { Message } from '@arco-design/web-vue';

  const myGroups = ref<any[]>([]);
  const currentSubgroup = ref<any>(undefined);

  const emit = defineEmits(['groupChange']);

  const currentClassInfo = computed(() => {
    if (currentSubgroup.value === -1) return null;
    return myGroups.value?.find((item) => item.id === currentSubgroup.value);
  });

  const { subgroupIds } = useUserStore().userInfo;

  const loadMySubGroups = async () => {
    const { data } = await request('/resourceRoom/subgroup/findByIds', {
      method: 'put',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      data: subgroupIds,
    });
    myGroups.value = data.items || [];
  };

  const handleSubgroupChange = (value) => {
    emit('groupChange', value, currentClassInfo.value);
  };

  onMounted(async () => {
    if (subgroupIds.length > 0) await loadMySubGroups();
    else Message.warning('没有可选分组');
  });
</script>

<template>
  <a-select
    v-model="currentSubgroup"
    v-bind="{ size: 'mini', placeholder: '请选择分组', ...$attrs }"
    @change="handleSubgroupChange"
  >
    <a-option :value="-1" label="全部分组"></a-option>
    <a-option v-for="item in myGroups" :key="item.id" :value="item.id">
      {{ item.name }}
    </a-option>
  </a-select>
</template>

<style scoped lang="scss"></style>
