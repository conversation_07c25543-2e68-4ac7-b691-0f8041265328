<script setup lang="ts">
  import { getQuestionnaireList } from '@repo/infrastructure/openapi/questionnaireController';
  import { computed, PropType, ref } from 'vue';
  import { debouncedWatch } from '@vueuse/core';
  import presetTypes, { answerAvailable } from '../questionLibrary/presets';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
    questionIds: {
      type: Array,
      default: () => [],
    },
    answer: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String as PropType<'resultView' | 'default'>,
      default: 'default',
    },
  });

  const emit = defineEmits(['update:questionIds', 'update:answer']);

  const currentQuestionIds = computed({
    get: () => props.questionIds,
    set: (value) => emit('update:questionIds', value),
  });

  const loading = ref(false);
  const questionsList = ref<any[]>([]);
  const questionAnswers = computed({
    get: () => props.answer,
    set: (value) => emit('update:answer', value),
  });

  const questionConfig = props.form.questionConfig || {};

  const answersNotAvailable = ref<number[]>([]);

  const sortedQuestionList = computed(() => {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    return questionsList.value.sort(
      (a: any, b: any) => currentQuestionIds.value.indexOf(a.id) - currentQuestionIds.value.indexOf(b.id),
    );
  });

  const getUsageCmp = computed(() => (question) => {
    const { presetType } = question;
    return presetTypes.find((item) => item.presetType === presetType)?.answerCmp;
  });

  const checkAnswerIsAvailable = () => {
    answersNotAvailable.value = [];
    sortedQuestionList.value.forEach((question, idx) => {
      if (!questionConfig[question.id]?.required) {
        return;
      }
      const res = answerAvailable(idx, question, questionAnswers.value[question.id]);
      if (!res) {
        answersNotAvailable.value.push(idx);
      }
    });
  };

  const loadQuestionnaires = async (ids) => {
    if (props.form.published) {
      questionsList.value = props.form.questions || [];
      return;
    }

    if (!ids.length) {
      questionsList.value = [];
      return;
    }

    try {
      loading.value = true;

      const { data } = await getQuestionnaireList({
        ids,
      });

      questionsList.value = data;
      currentQuestionIds.value = ids;

      checkAnswerIsAvailable();
    } finally {
      loading.value = false;
    }
  };

  const descriptionIsEmpty = computed(() => {
    const htmlWithoutTags = props.form?.description?.replace(/<[^>]*>?/gm, '');
    return htmlWithoutTags?.trim()?.length === 0;
  });

  debouncedWatch(() => props.questionIds, loadQuestionnaires, { immediate: true, deep: true, debounce: 500 });
  debouncedWatch(
    () => questionAnswers.value,
    () => {
      checkAnswerIsAvailable();
    },
    { immediate: true, deep: true, debounce: 300 },
  );

  defineExpose({
    answersNotAvailable,
  });
</script>

<template>
  <a-spin :loading="loading" class="w-full">
    <div
      v-if="!descriptionIsEmpty"
      class="p-4 bg-slate-50 border border-slate-200 rounded-lg"
      v-html="form?.description"
    >
    </div>
    <div v-for="(question, idx) in sortedQuestionList" :key="idx" class="mb-2 mt-3 pb-2 border-b border-slate-200">
      <div class="header flex gap-2 py-1 text-base">
        <span class="flex-1">
          <strong v-if="questionConfig[question.id]?.required" class="text-red-500 mr-1">*</strong>
          <strong> 第{{ idx + 1 }}题、 </strong>
          <span v-html="question.question" />
        </span>
        <small v-if="question.score">({{ question.score }}分)</small>
      </div>
      <component
        :is="getUsageCmp(question)"
        v-model="questionAnswers[question.id]"
        :disabled="mode === 'resultView'"
        :question="question"
        :mode="mode"
      />
    </div>
  </a-spin>
</template>

<style scoped lang="scss"></style>
