<script setup lang="ts">
  import { getQuestionnaireList } from '@repo/infrastructure/openapi/questionnaireController';
  import { computed, PropType, ref } from 'vue';
  import { debouncedWatch } from '@vueuse/core';
  import { VueDraggable } from 'vue-draggable-plus';
  import presetTypes from '../questionLibrary/presets';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
    questionIds: {
      type: Array,
      default: () => [],
    },
    questionConfig: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:questionIds', 'update:questionConfig']);

  const currentQuestionIds = computed({
    get: () => props.questionIds,
    set: (value) => emit('update:questionIds', value),
  });

  const loading = ref(false);
  const questionsList = ref<any[]>([]);
  const questionAnswers = ref<any>({});
  const questionConfigs = computed({
    get: () => props.questionConfig,
    set: (value) => emit('update:questionConfig', value),
  });

  const sortedQuestionList = computed(() => {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    return questionsList.value.sort(
      (a, b) => currentQuestionIds.value.indexOf(a.id) - currentQuestionIds.value.indexOf(b.id),
    );
  });

  const getUsageCmp = computed(() => (question) => {
    const { presetType } = question;
    return presetTypes.find((item) => item.presetType === presetType)?.answerCmp;
  });

  const loadQuestionnaires = async (ids) => {
    if (!ids.length) {
      questionsList.value = [];
      return;
    }

    try {
      loading.value = true;

      const { data } = await getQuestionnaireList({
        ids,
      });

      questionsList.value = data;
      currentQuestionIds.value = ids;
    } finally {
      loading.value = false;
    }
  };

  debouncedWatch(() => props.questionIds, loadQuestionnaires, { immediate: true, deep: true, debounce: 500 });
</script>

<template>
  <a-spin :loading="loading" class="w-full">
    <div class="p-4 bg-slate-50 border border-slate-200 rounded-lg" v-html="form?.description"> </div>
    <vue-draggable v-model="currentQuestionIds" handle=".sort-handle" :animation="200" ghost-class="ghost">
      <div v-for="(question, idx) in sortedQuestionList" :key="idx" class="mb-2 mt-3 pb-2 border-b border-slate-200">
        <div class="header flex gap-2 py-1 text-base">
          <span class="flex-1">
            <strong v-if="questionConfig[question.id]?.required" class="text-red-500 mr-1">*</strong>
            <strong> 第{{ idx + 1 }}题、 </strong>
            <span v-html="question.question" />
          </span>
          <a-space v-if="questionConfigs[question.id]" class="items-center">
            <a-tooltip content="必填">
              <a-switch v-model="questionConfigs[question.id].required" size="small" type="round" />
            </a-tooltip>
            <a-button size="mini" class="cursor-move sort-handle">
              <template #icon>
                <IconDragDotVertical />
              </template>
            </a-button>
          </a-space>
        </div>
        <component :is="getUsageCmp(question)" v-model="questionAnswers[question.id]" :question="question" />
      </div>
    </vue-draggable>
  </a-spin>
</template>

<style scoped lang="scss"></style>
