<script setup lang="ts">
  import { computed, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { getQuestionnaireList } from '@repo/infrastructure/openapi/questionnaireController';
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';
  import { questionnaireFormScopeOptions } from '../../constants';

  const props = defineProps({
    raw: {
      type: Boolean,
      required: true,
    },
    record: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['refresh', 'update:record']);

  const additionalData = [
    {
      label: '填写人',
      key: 'authorName',
    },
    {
      label: '填写人单位',
      key: 'boName',
    },
    {
      label: '填写人电话',
      key: 'authorPhoneNumber',
    },
  ];

  const publishVisible = ref(false);
  const formData = ref({ ...props.record });
  const saveLoading = ref(false);
  const conferenceList = ref<any[]>([]);
  const allBranchOfficeList = ref<any[]>([]);

  const hasResourceRoom = ref<any>(null);
  const nature = ref<any>(null);

  const boStore = useCommonStore({
    api: '/org/branchOffice/simpleList',
  });
  const currentBo = ref(null);
  const publicLink = computed(() => {
    return `${PROJECT_URLS.MAIN_PROJECT}/module/questionnaireForm.html?formId=${formData.value.uuid}`;
  });

  const selectedBos = computed(() => {
    return allBranchOfficeList.value.filter((item) => formData.value.branchOfficeIds?.includes(item.value));
  });

  const buttonDisabled = computed(() => {
    if (!formData.value?.questionIds?.length) {
      return true;
    }

    const now = new Date();
    const { endTime } = formData.value;

    return now > new Date(endTime);
  });

  const scopesMap = questionnaireFormScopeOptions.reduce((acc, cur) => {
    acc[cur.value] = cur.label;
    return acc;
  }, {});

  const loadConference = async () => {
    const { data } = await request('/resourceCenter/conference/available', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    conferenceList.value = data || [];
  };

  const handleLoadBranchOffices = async () => {
    allBranchOfficeList.value = await boStore.getOptions();
    boListOption.value = await boStore.getOptions();
  };

  const handleShowPublish = async () => {
    publishVisible.value = true;
  };

  const loadQuestionnaires = async (ids) => {
    if (!ids.length) {
      return [];
    }
    const { data } = await getQuestionnaireList({
      ids,
    });

    return (data || []).sort((a, b) => ids.indexOf(a.id) - ids.indexOf(b.id));
  };

  const handleSave = async () => {
    saveLoading.value = true;

    const questions = await loadQuestionnaires(formData.value.questionIds || []);

    formData.value = {
      ...formData.value,
      published: true,
      questions,
    };
    try {
      await request(`/questionnaire/form/${props.record.id}`, {
        method: 'PUT',
        data: formData.value,
      });

      Message.success('保存成功');

      emit('refresh');
      emit('update:record', { ...formData.value });

      publishVisible.value = false;

      return true;
    } catch (e) {
      Message.error('保存失败');
      return false;
    } finally {
      saveLoading.value = false;
    }
  };

  const handleCancelPublish = async () => {
    saveLoading.value = true;

    formData.value = {
      ...formData.value,
      published: false,
      questions: [],
    };
    try {
      await request(`/questionnaire/form/${props.record.id}`, {
        method: 'PUT',
        data: formData.value,
      });

      Message.success('保存成功');

      emit('refresh');
      emit('update:record', { ...formData.value });

      publishVisible.value = false;

      return true;
    } catch (e) {
      Message.error('保存失败');
      return false;
    } finally {
      saveLoading.value = false;
    }
  };

  const handleBoSelect = (value) => {
    const selected = formData.value.branchOfficeIds || [];
    if (!selected.includes(value)) {
      formData.value = {
        ...formData.value,
        branchOfficeIds: [...selected, value],
      };
    }
  };

  const handleBoRemove = (value) => {
    const selected = formData.value.branchOfficeIds || [];
    const index = selected.indexOf(value);
    if (index > -1) {
      selected.splice(index, 1);
      formData.value = {
        ...formData.value,
        branchOfficeIds: selected,
      };
    }
  };
  const boListOption = ref<any[]>([]);
  const loadBoList = async () => {
    try {
      const { data: res } = await request('/org/branchOffice/simpleList', {
        method: 'get',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          nature: nature.value,
          hasResourceRoom: hasResourceRoom.value,
        },
      });
      boListOption.value = res?.map((school) => ({ label: school?.name, value: school?.id }));
    } finally {
      /**/
    }
  };
  const getOptionByNature = async (key: string) => {
    switch (key) {
      case 'kindergarten':
        nature.value = UNIT_NATURES_MAP.Kindergarten;
        hasResourceRoom.value = null;
        break;
      case 'vocationalEducation':
        nature.value = UNIT_NATURES_MAP.Vocational;
        hasResourceRoom.value = null;
        break;
      case 'specialSchool':
        nature.value = UNIT_NATURES_MAP.Special;
        hasResourceRoom.value = null;
        break;
      case 'compulsoryWithResourceRoom':
        nature.value = UNIT_NATURES_MAP.Normal;
        hasResourceRoom.value = true;
        break;
      case 'compulsoryWithoutResourceRoom':
        nature.value = UNIT_NATURES_MAP.Normal;
        hasResourceRoom.value = false;
        break;
      default:
        break;
    }
    await loadBoList();
  };

  const handleAdditionalChanged = (val: any) => {
    console.log(val, '---');
  };
  watch(
    () => props.record,
    (value) => {
      formData.value = { ...value };
    },
    { immediate: true },
  );

  watch(
    () => formData.value.publishScope,
    async (value) => {
      if (value === 'conference' && !conferenceList.value.length) {
        await loadConference();
      } else if (value === 'organization' && !allBranchOfficeList.value.length) {
        await handleLoadBranchOffices();
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div>
    <a-button
      size="mini"
      :status="record.published ? 'success' : 'danger'"
      :disabled="buttonDisabled"
      @click="handleShowPublish"
    >
      {{ raw ? scopesMap[record.publishScope] : '未发布' }}
    </a-button>
    <!--need add a details classification-->
    <a-modal
      v-model:visible="publishVisible"
      title="问卷表单发布"
      :ok-loading="saveLoading"
      :on-before-ok="handleSave"
      :width="700"
    >
      <a-form size="mini" auto-label-width>
        <a-form-item label="发布范围">
          <a-space class="justify-between">
            <a-radio-group v-model="formData.publishScope" type="button" size="mini">
              <a-radio
                v-for="scope in questionnaireFormScopeOptions.filter((item) => item.value !== 'organization')"
                :key="scope.value"
                :value="scope.value"
                :label="scope.value"
              >
                {{ scope.label }}
              </a-radio>
              <a-dropdown trigger="hover">
                <a-radio value="organization">部分单位发布</a-radio>
                <template #content>
                  <a-doption value="kindergarten" @click="getOptionByNature('kindergarten')">幼儿园</a-doption>
                  <a-doption value="vocationalEducation" @click="getOptionByNature('vocationalEducation')"
                    >职业学校
                  </a-doption>
                  <a-doption value="specialSchool" @click="getOptionByNature('specialSchool')">特殊教育学校</a-doption>
                  <a-doption value="compulsoryWithResourceRoom" @click="getOptionByNature('compulsoryWithResourceRoom')"
                    >普通学校（无资源教室）
                  </a-doption>
                  <a-doption
                    value="compulsoryWithoutResourceRoom"
                    @click="getOptionByNature('compulsoryWithoutResourceRoom')"
                    >普通学校（有资源教室）
                  </a-doption>
                </template>
              </a-dropdown>
            </a-radio-group>

            <a-popconfirm v-if="formData.published" content="确定要取消发布吗？" class="w-48" @ok="handleCancelPublish">
              <a-button size="mini" status="danger">
                <template #icon>
                  <IconClose />
                </template>
                取消发布
              </a-button>
            </a-popconfirm>
          </a-space>
        </a-form-item>
        <a-form-item v-if="formData.publishScope === 'conference'" label="关联会议">
          <a-select v-model="formData.relatedId">
            <a-option v-for="conference in conferenceList" :key="conference.id" :value="conference.id">
              {{ conference.subject }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item v-else-if="formData.publishScope === 'public'" label="发布链接">
          <a-space class="flex-1" direction="vertical">
            <div class="">
              <div>请复制以下链接或使用右侧二维码，将问卷表单分享给需要填写的人员。</div>
              <small>您也可以复制链接后，使用二维码美化工具自行制作二维码</small>
            </div>
            <a-textarea :model-value="publicLink" />
            <div id="formAdditionalData" class="w-full">
              <a-checkbox-group v-model="formData.additionalInfo" @change="handleAdditionalChanged">
                <a-checkbox v-for="item in additionalData" :key="item.key" :value="item.key">
                  {{ item.label }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </a-space>
          <vue-qrcode :value="publicLink" class="mx-auto my-2" />
        </a-form-item>
        <a-form-item v-else-if="formData.publishScope === 'protected'" label="发布范围">
          全单位内所有人员可见
        </a-form-item>
        <a-form-item v-else-if="formData.publishScope === 'organization'" label="选择单位">
          <a-space direction="vertical" class="w-full">
            <div>
              <a-select v-model="currentBo" :options="boListOption" allow-search @change="handleBoSelect" />
            </div>
            <a-space class="flex-wrap">
              <a-tag
                v-for="bo in selectedBos"
                :key="bo.value"
                class="mb-2"
                color="blue"
                size="mini"
                closable
                @close="handleBoRemove(bo.value)"
              >
                {{ bo.label }}
              </a-tag>
            </a-space>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
