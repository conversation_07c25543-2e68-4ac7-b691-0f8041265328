<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { useRoute, useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';

  import useSchoolCourseStore from '../../store/schoolCourseStore';

  const props = defineProps({
    teacher: {
      type: Object as PropType<any>,
      required: true,
    },
    courseAssignments: {
      type: Array,
      default: () => [],
    },
    task: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    gradeClassesMap: {
      type: Object as PropType<any>,
    },
    mode: {
      type: String as PropType<'mini' | 'full' | 'teacher'>,
      default: 'mini',
    },
    highlightToday: {
      type: Boolean,
      default: true,
    },
  });

  const schoolCourseStore = useSchoolCourseStore();
  const gradeClasses = ref([]);
  const classesMap = ref({});
  const lessonsMap = ref({});
  const teacherSchedule = ref<any[]>([]);
  const { loading, setLoading } = useLoading();
  const currentTask = ref(props.task);
  const todayWeekIndex = new Date().getDay() || 7;

  const morning = ref();
  const afternoon = ref();

  const maxMorningCount = ref(
    currentTask.value?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.morningCount), 0) || morning.value,
  );
  const maxAfternoonCount = ref(
    currentTask.value?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.afternoonCount), 0) || afternoon.value,
  );

  const totalDayPeriods = computed(() => {
    return maxMorningCount.value + maxAfternoonCount.value;
  });

  const getGradeClasses = async () => {
    if (props.gradeClassesMap) {
      classesMap.value = props.gradeClassesMap;
      return;
    }
    const { data } = await request('/resourceRoom/gradeClass/findAllGradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
      },
    });

    gradeClasses.value = data.items || [];
    classesMap.value = data.items.reduce((acc, gradeClass) => {
      acc[gradeClass.id] = gradeClass;
      return acc;
    }, {});
  };

  const getLessons = async () => {
    lessonsMap.value = await schoolCourseStore.getSchoolCoursesMap();
  };

  const handleScheduleFormatted = (assignments: any[]) => {
    assignments = assignments.filter((item) => item.teacherId === props.teacher.id);
    const schedule: any[] = Array.from(
      {
        length: totalDayPeriods.value,
      },
      () => Array.from({ length: 5 }, () => []),
    );
    assignments.forEach((item) => {
      const gradeClass = classesMap.value[item.gradeClassId];
      const lesson = lessonsMap.value[item.lessonId];
      if (!gradeClass || !lesson) {
        return;
      }

      const dayPeriodIndex = item.dayPeriod === 0 ? item.classIndex : item.classIndex + maxMorningCount.value;
      const { weekdayIndex } = item;

      schedule[dayPeriodIndex][weekdayIndex].push({
        ...item,
        gradeClass,
        lesson,
      });
    });

    teacherSchedule.value = schedule;
  };
  const date2Period = (date?: Date | string): string => {
    const now = date ? new Date(date) : new Date();
    const month = now.getMonth() + 1;
    const isAutumn = month >= 9 || month <= 1; // 2
    const year = now.getFullYear() - (isAutumn && month >= 1 && month <= 2 ? 1 : 0);
    const season = isAutumn ? '秋' : '春';
    return `${year}年${season}季学期`;
  };
  const loadData = async () => {
    await getGradeClasses();
    if (props.courseAssignments?.length && props.task) {
      handleScheduleFormatted(props.courseAssignments);
      return;
    }

    if (props.mode === 'teacher') {
      const { data } = await request(`/teacher/timetable/my/${date2Period(new Date())}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      if (!data || !data.assignments?.length) {
        return;
      }

      currentTask.value = data;

      maxMorningCount.value = currentTask.value?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.morningCount),
        0,
      );
      maxAfternoonCount.value = currentTask.value?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.afternoonCount),
        0,
      );
      handleScheduleFormatted(data.assignments || []);
    } else {
      const { data: t } = await request('/teacher/lessonSchedule', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          enabled: true,
        },
      });

      if (!t.items?.length) {
        return;
      }

      currentTask.value = t.items?.pop();

      const { data } = await request('/teacher/courseAssignment', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          teacherId: props.teacher.id,
          pageSize: 9999,
          taskId: currentTask.value.id,
        },
      });

      handleScheduleFormatted(data.items);
    }
  };

  const cardClass =
    'w-auto h-60 absolute shadow right-[-85px] top-[-245px] transform translate-x-1 translate-y-1 rounded z-[2000] overflow-hidden';
  const lessonCardClass =
    'rounded-lg bg-white border shadow-md p-2 pl-4 pr-4 flex-1 space-y-2 box-border hover:border-blue-300';
  const lessonClass =
    'w-full h-12 flex items-center justify-between bg-white border border-gray-200 mb-3 px-4 rounded-lg shadow-sm hover:shadow-lg transition-shadow';
  const highlightStyle = {
    border: '1px solid rgb(var(--arcoblue-6))',
  };
  const length = 16;
  const selectedDate = ref<Date | null>(new Date());

  const hiddenCard = () => {
    selectedDate.value = null;
  };
  const getWeekDay = (day: number) => {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekdays[day];
  };

  const teachingPlanList = ref<any[]>();

  const getPickerRange = (date: Date, type?: string) => {
    const now = new Date(date);
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const formatDate = (time) => {
      const year = time.getFullYear();
      const month = String(time.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以下个月份需要加1
      const day = String(time.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    if (type != null) {
      return formatDate(now);
    }
    const firstDayFormatted = formatDate(firstDay);
    const lastDayFormatted = formatDate(lastDay);
    return [firstDayFormatted, lastDayFormatted];
  };

  const loadTeachingPlan = async (date?: Date) => {
    try {
      const { data: res } = await request(`/course/chapter/getChapterListBySchedule`, {
        method: 'PUT',
        params: {
          classDate: getPickerRange(date || new Date()),
          period: date2Period(new Date()),
        },
      });
      teachingPlanList.value = res.list;
    } finally {
      /**/
    }
  };
  const router = useRouter();
  const route = useRoute();

  const timetable = ref();
  const loadClass = async (date?: string | Date) => {
    try {
      const { data } = await request(`/teacher/timetable/my/${date2Period(date)}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      if (!data || !data.assignments?.length) return;

      morning.value = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.morningCount), 0);
      afternoon.value = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.afternoonCount), 0);
      timetable.value = data;
    } finally {
      /**/
    }
  };

  const handleChange = async (date: Date) => {
    hiddenCard();
    await loadTeachingPlan(date);
    await loadClass(date);
  };
  const currentChapter = ref();

  const haseLessonPlan = (date: Date) => {
    const key = getPickerRange(date, 'format');
    if (teachingPlanList.value?.length) {
      return teachingPlanList.value?.find((item) => {
        return item?.classDate === key;
      });
    }
    return false;
  };
  const getCurrentChapter = (date: Date) => {
    selectedDate.value = date;
    const key = getPickerRange(date, 'format');
    if (teachingPlanList.value?.length) {
      currentChapter.value = teachingPlanList.value?.find((item) => {
        return item?.classDate === key;
      });
    }
  };
  const getInfo = (i: number) => {
    return currentChapter.value?.chapters.find((lesson) => lesson?.lesson.some((item) => item.includes(i)));
  };
  const allMyCourseByPeriod = ref();
  const loadCourse = async () => {
    try {
      const { data: res } = await request('/course/course/my-course', {
        method: 'get',
        params: {
          page: 1,
          pageSize: 20,
        },
      });

      allMyCourseByPeriod.value = res.list.reduce((acc, item) => {
        const { gradePeriod } = item;
        if (!acc[gradePeriod]) {
          acc[gradePeriod] = [];
        }
        acc[gradePeriod].push(item);
        return acc;
      }, {});
    } finally {
      /**/
    }
  };
  const routerToMyTeaching = (chapter: any, lessonId?: number) => {
    if (!lessonId) return;
    let chapterInfo;
    if (chapter?.courseId && chapter?.id) {
      chapterInfo = {
        courseId: chapter?.courseId,
        chapterId: chapter?.id,
        chapterNumber: chapter?.number,
      };
    } else {
      const key = date2Period(selectedDate.value);
      if (allMyCourseByPeriod.value?.[key]) {
        // 对应课程
        const result = allMyCourseByPeriod.value?.[key].find((item) => item.category === lessonId);
        if (result?.id)
          chapterInfo = {
            chapterInfo: result?.id,
            courseId: result?.id,
          };
        else {
          Message.warning('当前学期未创建这门学科教案');
          return;
        }
      }
    }
    // courseId
    if (!chapterInfo?.chapterInfo) {
      Message.warning('暂无教案，请先创建课程');
      return;
    }
    router.push({
      path: `/teacher/teaching/teachingImpl/course/mission`,
      query: {
        ...route.query,
        morningCount: morning.value || 4,
        afternoonCount: afternoon.value || 4,
        ...chapterInfo,
      },
    });
  };

  onMounted(async () => {
    setLoading(true);
    try {
      // await getGradeClasses();
      await Promise.all([getLessons(), loadCourse(), loadClass(), loadData(), loadTeachingPlan()]);
    } finally {
      setLoading(false);
    }
  });

  watch(() => props.teacher, loadData);
</script>

<template>
  <div class="flex justify-between">
    <a-date-picker hide-trigger class="w-3/4" style="overflow: visible" @picker-value-change="handleChange">
      <template #cell="{ date }">
        <div class="arco-picker-date mb-5 relative" @click="getCurrentChapter(date)">
          <!--@click="haseLessonPlan(date) ? handleClickDate(date) : hiddenCard()"-->

          <div v-if="selectedDate === date.getTime()" :class="cardClass">
            <div class="relative w-full h-full bg-white rounded-lg shadow-lg border">
              <div class="w-full h-6 flex items-center bg-blue-500 justify-between px-4 rounded-t-lg shadow-sm">
                <span class="font-bold text-white">{{ getWeekDay(date.getDay()) }}</span>
                <a-popover :content="`${'显示' + getWeekDay(date.getDay()) + '全部'}`">
                  <icon-filter style="color: white" />
                </a-popover>
                <icon-close class="hover:text-red-800 cursor-pointer closeStyle" @click.stop="hiddenCard" />
              </div>
              <div class="w-full h-full pt-4 px-4 pb-6 overflow-auto custom-scrollbar">
                <div
                  v-for="(chapter, index) in haseLessonPlan(date)?.chapters"
                  :key="index"
                  class="flex mb-4 pb-4"
                  :class="{ 'border-b': length > 1 }"
                >
                  <span class="mr-1 text-gray-700 font-bold">{{ `${index + 1}、` }}</span>
                  <div :class="lessonCardClass">
                    <div class="text-xs font-bold text-black">{{ chapter?.name || '-' }}</div>
                    <div
                      v-for="(round, classIndex) in chapter?.lesson"
                      :key="classIndex"
                      class="bg-gray-50 px-1 text-xs rounded text-gray-700"
                    >
                      <div v-if="round.length > 0">
                        {{ classIndex > 0 ? '下午:' : '上午:' }}
                        第
                        <span v-for="(i, a) in round" :key="a">
                          {{ `${i + 1}${a === round.length - 1 ? '' : '、'}` }}
                        </span>
                        节
                      </div>
                    </div>
                    <div
                      v-if="!chapter.lesson || (!chapter?.lesson[0].length && !chapter?.lesson[1].length)"
                      class="text-xs text-gray-400"
                    >
                      暂未设置
                    </div>
                    <div class="text-xs">{{ haseLessonPlan(date)?.classDate }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="arco-picker-date-value"
            style="width: 30px; height: 30px; border-radius: 50%"
            :data-date="date.getTime()"
            :style="haseLessonPlan(date) ? highlightStyle : {}"
          >
            <span class="text-xl">{{ date.getDate() }}</span>
          </div>
        </div>
      </template>
    </a-date-picker>
    <div class="w-1/4 h-full bg-gradient-to-b from-gray-100 to-gray-50 p-4 ml-4 rounded-xl shadow-lg">
      <!-- 顶部提示 -->
      <div class="flex items-center justify-between mb-4">
        <span>{{ getPickerRange(selectedDate, 'format') }}</span>
        <a-popover content="✔: 教案已提交。 ×: 教案未提交" placement="bottom">
          <icon-question-circle class="text-gray-500 cursor-pointer hover:text-gray-700 transition-colors" />
        </a-popover>
      </div>

      <!-- 上午部分 -->
      <div class="text-lg font-semibold text-blue-600 mb-4 border-b border-gray-300 pb-2 flex items-center">
        <icon-sun class="mr-2 text-yellow-500" /> 上午
      </div>
      <div
        v-for="i in maxMorningCount"
        :key="i"
        :class="lessonClass"
        class="cursor-pointer"
        @click="routerToMyTeaching(getInfo(i), teacherSchedule[i - 1][selectedDate.getDay() - 1]?.[0]?.lessonId)"
      >
        <span class="text-sm font-medium text-gray-800">{{ `第${i}节: ` }}</span>
        <span v-if="teacherSchedule[i - 1][selectedDate?.getDay() - 1]?.[0]?.lesson?.name" class="flex items-center">
          {{ teacherSchedule[i - 1][selectedDate.getDay() - 1][0]?.lesson?.name }}
          <span v-if="getInfo(i)" class="text-sm text-gray-600 font-medium">
            / {{ getInfo(i)?.name }}
            <icon-check-circle v-if="getInfo(i)?.submitted" style="color: green" class="ml-2" />
            <icon-close-circle v-else style="color: red" class="ml-2" />
          </span>
          <span v-else>
            <a-popover content="点击去备课">
              <icon-send size="mini" style="color: skyblue" class="ml-2" />
            </a-popover>
          </span>
        </span>
        <span v-else class="text-sm text-gray-400">未安排</span>
      </div>

      <!-- 下午部分 -->
      <div class="text-lg font-semibold text-blue-600 mt-8 mb-4 border-b border-gray-300 pb-2 flex items-center">
        <icon-moon class="mr-2 text-purple-500" /> 下午
      </div>
      <div
        v-for="i in maxAfternoonCount"
        :key="i"
        :class="lessonClass"
        class="cursor-pointer"
        @click="
          routerToMyTeaching(
            getInfo(i),
            teacherSchedule[i + maxMorningCount - 1][selectedDate.getDay() - 1][0]?.lessonId,
          )
        "
      >
        <span class="text-sm font-medium text-gray-800">{{ `第${i}节: ` }}</span>
        <span
          v-if="teacherSchedule[i + maxMorningCount - 1][selectedDate?.getDay() - 1]?.[0]?.lesson?.name"
          class="flex items-center"
        >
          {{ teacherSchedule[i + maxMorningCount - 1][selectedDate.getDay() - 1][0]?.lesson?.name }}
          <span v-if="getInfo(i)" class="text-sm text-gray-600 font-medium">
            / {{ getInfo(i)?.name }}
            <icon-check-circle v-if="getInfo(i)?.submitted" style="color: green" class="ml-2" />
            <icon-close-circle v-else style="color: red" class="ml-2" />
          </span>
          <span v-else>
            <a-popover content="点击去备课">
              <icon-send size="mini" style="color: skyblue" class="ml-2" />
            </a-popover>
          </span>
        </span>
        <span v-else class="text-sm text-gray-400">未安排</span>
        <!--        <span class="text-sm font-medium text-gray-800">{{ `第${i}节: ` }}</span>
        <span v-if="getInfo(i)" class="flex items-center">
          <span class="text-sm text-gray-600 font-medium">
            {{ `${lessonsMap?.[getInfo(i)?.category].name} / ` }}{{ getInfo(i)?.name }}
          </span>
          <icon-check-circle v-if="getInfo(i)?.submitted" style="color: green" class="ml-2" />
          <icon-close-circle v-else style="color: red" class="ml-2" />
        </span>
        <span v-else class="text-sm text-gray-400">未安排</span>-->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .closeStyle {
    color: white;
  }

  .closeStyle:hover {
    color: red;
  }

  .custom-scrollbar {
    overflow-y: scroll;
  }

  .custom-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .custom-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
</style>
