<script setup lang="ts">
  import { computed, nextTick, onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import useSchoolCourseStore from '../../store/schoolCourseStore';

  const props = defineProps({
    teacher: {
      type: Object as PropType<any>,
      required: true,
    },
    courseAssignments: {
      type: Array,
      default: () => [],
    },
    task: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    gradeClassesMap: {
      type: Object as PropType<any>,
    },
    mode: {
      type: String as PropType<'mini' | 'full' | 'teacher'>,
      default: 'mini',
    },
    highlightToday: {
      type: Boolean,
      default: true,
    },
  });

  const schoolCourseStore = useSchoolCourseStore();
  const gradeClasses = ref([]);
  const classesMap = ref({});
  const lessonsMap = ref({});
  const teacherSchedule = ref<any[]>([]);
  const { loading, setLoading } = useLoading();
  const currentTask = ref(props.task);
  const todayWeekIndex = new Date().getDay() || 7;

  const maxMorningCount = ref(
    currentTask.value?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.morningCount), 0),
  );
  const maxAfternoonCount = ref(
    currentTask.value?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.afternoonCount), 0),
  );

  const totalDayPeriods = computed(() => {
    return maxMorningCount.value + maxAfternoonCount.value;
  });

  const getGradeClasses = async () => {
    if (props.gradeClassesMap) {
      classesMap.value = props.gradeClassesMap;
      return;
    }
    const { data } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
      },
    });

    gradeClasses.value = data.items || [];
    classesMap.value = data.items.reduce((acc, gradeClass) => {
      acc[gradeClass.id] = gradeClass;
      return acc;
    }, {});
  };

  const getLessons = async () => {
    lessonsMap.value = await schoolCourseStore.getSchoolCoursesMap();
  };

  const handleScheduleFormatted = (assignments: any[]) => {
    assignments = assignments.filter((item) => item.teacherId === props.teacher.id);
    const schedule: any[] = Array.from(
      {
        length: totalDayPeriods.value,
      },
      () => Array.from({ length: 5 }, () => []),
    );
    assignments.forEach((item) => {
      const gradeClass = classesMap.value[item.gradeClassId];
      const lesson = lessonsMap.value[item.lessonId];
      if (!gradeClass || !lesson) {
        return;
      }

      const dayPeriodIndex = item.dayPeriod === 0 ? item.classIndex : item.classIndex + maxMorningCount.value;
      const { weekdayIndex } = item;

      schedule[dayPeriodIndex][weekdayIndex].push({
        ...item,
        gradeClass,
        lesson,
      });
    });

    teacherSchedule.value = schedule;
  };

  const loadData = async () => {
    if (props.courseAssignments?.length && props.task) {
      handleScheduleFormatted(props.courseAssignments);
      return;
    }

    if (props.mode === 'teacher') {
      const { data } = await request('/teacher/timetable/my', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      if (!data || !data.assignments?.length) {
        return;
      }

      currentTask.value = data;

      maxMorningCount.value = currentTask.value?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.morningCount),
        0,
      );
      maxAfternoonCount.value = currentTask.value?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.afternoonCount),
        0,
      );

      handleScheduleFormatted(data.assignments || {});
    } else {
      const { data: t } = await request('/teacher/lessonSchedule', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          enabled: true,
        },
      });

      if (!t.items?.length) {
        return;
      }

      currentTask.value = t.items?.pop();

      const { data } = await request('/teacher/courseAssignment', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          teacherId: props.teacher.id,
          pageSize: 9999,
          taskId: currentTask.value.id,
        },
      });

      handleScheduleFormatted(data.items);
    }
  };
  const weekdays = ['一', '二', '三', '四', '五'];

  const cardClass =
    'w-auto h-60 absolute shadow right-[-85px] top-[-245px] transform translate-x-1 translate-y-1 rounded z-[2000] overflow-hidden';
  const length = 16;
  const selectedDate = ref(null);
  const currentDefaultDate = ref(new Date());
  const handleClickDate = (date: Date) => {
    // selectedDate.value = date.getTime();
  };
  const hiddenCard = () => {
    selectedDate.value = null;
  };
  const getWeekDay = (day: number) => {
    const weekdayList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekdayList[day];
  };
  const handleChange = (date) => {
  };
  const teachingPlanList = ref<Record<string, any>>();
  const loadTeachingPlan = async () => {};

  onMounted(async () => {
    setLoading(true);
    try {
      await getGradeClasses();
      await getLessons();
      await loadData();
    } finally {
      setLoading(false);
    }
  });

  watch(() => props.teacher, loadData);
</script>

<template>
  <!--{{ currentDefaultDate.getDate() }}-->
  <a-date-picker v-if="false" hide-trigger class="w-full" style="overflow: visible" @picker-value-change="handleChange">
    <template #cell="{ date }">
      <div class="arco-picker-date mb-5 relative" @click="handleClickDate(date)">
        <div v-if="selectedDate === date.getTime()" :class="cardClass">
          <div class="relative w-full h-full bg-white rounded-lg shadow-lg border">
            <div class="w-full h-6 flex items-center bg-blue-500 justify-between px-4 rounded-t-lg shadow-sm">
              <span class="font-bold text-white">{{ getWeekDay(date.getDay()) }}</span>
              <a-popover :content="`${'显示' + getWeekDay(date.getDay()) + '全部'}`">
                <icon-filter style="color: white" />
              </a-popover>
              <icon-close class="hover:text-red-800 cursor-pointer closeStyle" @click.stop="hiddenCard" />
            </div>
            <div class="w-full h-full pt-4 px-4 pb-6 overflow-auto custom-scrollbar">
              <div v-for="i in length" :key="i" class="flex mb-4 pb-4" :class="{ 'border-b': length > 1 }">
                <span class="mr-1 text-gray-700 font-bold">{{ `${i}、` }}</span>
                <div
                  class="rounded-lg bg-white border shadow-md p-2 pl-4 pr-4 flex-1 space-y-2 box-border hover:border-blue-300"
                >
                  <div class="text-xs font-bold text-black">生活语文</div>
                  <div class="flex text-xs justify-start text-gray-500 space-x-2">
                    <span v-for="j in 2" :key="j" class="bg-gray-50 px-1 text-xs rounded text-gray-700">
                      第{{ j }}节
                    </span>
                  </div>
                  <div class="text-xs">{{ `${'2024-01-10'}` }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="arco-picker-date-value"
          style="width: 30px; height: 30px; border-radius: 50%"
          :data-date="date.getTime()"
        >
          <span class="text-xl">{{ date.getDate() }}</span>
        </div>
      </div>
    </template>
  </a-date-picker>

  <a-skeleton v-if="loading" :animation="true">
    <a-skeleton-line :rows="5" />
  </a-skeleton>
  <div v-else-if="teacherSchedule?.length && totalDayPeriods > 0">
    <table class="border-collapse border border-slate-400 w-full text-center">
      <thead>
        <tr>
          <th class="border border-slate-300 px-2 py-4">
            <div class="cursor-pointer text-blue-500">
              {{ teacher.name }}
            </div>
          </th>
          <th
            v-for="d in 5"
            :key="d"
            class="border border-slate-300 bg-slate-100 p-0"
            :class="{
              'text-blue-600': d === todayWeekIndex && highlightToday,
            }"
            style="width: 17%"
          >
            周{{ weekdays[d - 1] }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="dpi in totalDayPeriods" :key="dpi">
          <th class="border border-slate-300">
            <div v-if="mode === 'mini'">
              {{ dpi }}
            </div>
            <div v-else>
              {{ dpi > maxMorningCount ? '下午' : '上午' }}
              第{{ dpi > maxMorningCount ? dpi - maxMorningCount : dpi }}节
            </div>
          </th>
          <td v-for="wdi in 5" :key="wdi" style="width: 17%" class="border border-slate-300 p-2">
            <div
              v-if="teacherSchedule[dpi - 1][wdi - 1]?.length"
              :class="{
                'text-red-500': teacherSchedule[dpi - 1][wdi - 1]?.length > 1,
                'text-blue-600': wdi === todayWeekIndex && highlightToday,
              }"
            >
              <div v-for="(ts, idx) in teacherSchedule[dpi - 1][wdi - 1]" :key="idx">
                <small>
                  {{ ts.gradeClass?.name }}
                </small>
                <div>
                  {{ ts.lesson?.name }}
                </div>
              </div>
            </div>
            <div v-else class="text-gray-400">
              <small>-</small>
              <div>无</div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <a-empty v-else description="暂无课表" />
</template>

<style scoped lang="scss">
  .closeStyle {
    color: white;
  }

  .closeStyle:hover {
    color: red;
  }

  .custom-scrollbar {
    overflow-y: scroll; /* 使容器在垂直方向可滚动 */
  }

  /* 隐藏滚动条 */
  .custom-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .custom-scrollbar {
    -ms-overflow-style: none; /* IE 10+ */
    scrollbar-width: none; /* Firefox */
  }
</style>
