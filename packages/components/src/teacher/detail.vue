<template>
  <a-card
    v-if="d2dEducation && ready"
    :visible="visible"
    :closable="false"
    fullscreen
    :on-before-ok="handlePreOk"
    cancel-text="返回"
  >
    <template v-if="currentTab" #title>
      <div class="flex justify-between w-full mr-10 ml-10">
        <div>
          <span>
            {{ d2dEducation?.student.gradeClass?.name }}
            {{ d2dEducation?.student.name }} {{ d2dEducation?.period }}的送教档案
          </span>
          <span>
            <a-tag size="mini" color="orange" type="warning">制定计划教师： {{ d2dEducation?.createdBy.name }}</a-tag>
            <a-tag v-if="d2dEducation?.personInCharge" size="mini" color="orange" type="warning" class="ml-2">
              送教负责人： {{ d2dEducation?.personInCharge }}
            </a-tag>
          </span>
        </div>
        <div class="flex justify-end">
          <slot name="select"></slot>
          <a :href="getDocUrl()" download class="mr-12">
            <a-button
              v-if="currentTab.attachment?.udf1 || currentTab.name === 'records'"
              :size="size"
              type="outline"
              @click="
                () => {
                  if (!getDocUrl()) Message.warning('当前没有可下载的模版');
                }
              "
            >
              <icon-to-bottom />
              文件模版
            </a-button>
          </a>
        </div>
      </div>
    </template>
    <div v-if="currentTab" class="page-wrapper -mt-2">
      <div class="flex justify-between content-wrapper">
        <a-card class="left">
          <template #title>
            <icon-file />
            档案导航
          </template>
          <div
            v-for="tab in tabs"
            :key="tab.name"
            class="left-item"
            :class="{
              active: currentTab.name === tab.name,
              divider: tab === '-',
            }"
          >
            <a-divider v-if="tab === '-'" />
            <span v-else :title="tab.label" @click="() => handleChangeTab(tab)">
              <i v-if="tab.icon" :class="tab.icon"></i>
              <span v-else class="index">{{ tab.index }}. </span>
              {{ tab.label }}
              <icon-double-right />
            </span>
          </div>
        </a-card>
        <div class="main-content" style="height: 80vh">
          <d2d-education-record-edit
            v-if="d2dEducation && editRecordVisible"
            ref="editForm"
            v-model="editRecordVisible"
            :is-archive="true"
            :visible="editRecordVisible"
            :d2d-education="d2dEducation"
            :data="currentRecord"
            :record-flush="recordFlush"
            :del-img-forbid="true"
          />
          <document-template-editing
            v-else
            :current-tab="currentTab"
            :data-type="currentTab.attachment?.udf1.split('.').pop().toLowerCase()"
          />
        </div>
      </div>
    </div>
  </a-card>
  <a-empty v-else description="暂无数据..." />
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, PropType } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useRoute } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';

  import D2dEducationRecordEdit from './d2dEducationRecordEdit.vue';
  import DocumentTemplateEditing from './documentTemplateEditing.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
    student: {
      type: Object as PropType<any>,
    },
  });
  const emits = defineEmits(['update:modelValue']);

  // Props and state
  const d2dEducationId = ref<number | null>(null);
  const d2dEducation = ref<any>(null);
  const currentTab = ref<any>(null);
  const tabs = ref<any[]>([]);
  const docTemplates = ref<any[]>([]);
  const editRecordVisible = ref(false);
  const currentRecord = ref<any>({});
  const editForm = ref(null);

  const route = useRoute();
  const size = 'mini';
  const teacherList = ref<any>([]);
  const teacherOptions = ref<any>([]);
  const recordFlush = ref(false); // 子组件监听，只要修改了记录就刷新

  const handlePreOk = async () => {
    emits('update:modelValue', false);
  };
  // Load the education plan
  const loadPlan = async () => {
    if (d2dEducationId.value) {
      const { data } = await request(`/resourceRoom/d2dEducation/${d2dEducationId.value}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      d2dEducation.value = data;
    }
  };

  const initTabs = async () => {
    docTemplates.value = d2dEducation.value?.docTemplates || [];
    tabs.value = [
      ...docTemplates.value.map((item, index) => ({
        index: index + 1,
        label: item.name,
        name: `d2d-education-document-${index}`,
        type: 'document',
        id: index,
        attachment: item.attachment,
        val: item.id,
      })),
      '-',
      {
        icon: 'el-icon-edit',
        label: '送教记录',
        name: 'records',
      },
    ];
    // eslint-disable-next-line prefer-destructuring
    currentTab.value = tabs.value[0];
  };
  const loadTeacher = async () => {
    await request('/org/companyUser', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    }).then((res) => {
      teacherList.value = res.data.items;
      res.data.items.forEach((item, index) => {
        teacherOptions.value.push({
          label: item.name,
          value: item.id,
        });
      });
    });
  };

  const handleChangeTab = (tab: any) => {
    currentTab.value = tab;
    if (tab.name === 'records') editRecordVisible.value = true;
    else editRecordVisible.value = false;
  };

  const getDocUrl = (): string => {
    if (currentTab.value.attachment?.udf1) return currentTab.value.attachment?.udf1;
    if (currentTab.value.name === 'records') return editForm.value?.currentTab?.attachment?.udf1;
    return '';
  };

  const init = async (id: number) => {
    if (!id) return;
    d2dEducationId.value = id;
    await loadPlan();
    await initTabs();
    // await loadTeacher();
    // await Promise.all([loadPlan, initTabs, loadTeacher]);

    if (route?.params?.docId) {
      const docId = Number(route?.params?.docId);
      const tab = tabs.value.find((item) => item.id === docId);
      if (tab) {
        currentTab.value = tab;
      }
    }

    if (route?.path.includes('/record')) {
      currentTab.value = tabs.value.find((item) => item.name === 'records');
    } else {
      // eslint-disable-next-line prefer-destructuring
      currentTab.value = tabs.value[0];
    }
  };

  const loadLastPlan = async () => {
    if (props.record?.id) return;
    const { id } = props.student;
    if (!id) return;

    try {
      const { data } = await request(`/resourceRoom/d2dEducation/findLastPlanByStudent/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      d2dEducation.value = data.items[0];
      d2dEducationId.value = data.items[0]?.id;
    } finally {
      /**/
    }
  };
  const ready = ref(false);
  onMounted(async () => {
    await loadLastPlan();
    await init(props.record?.id || d2dEducationId.value);
    ready.value = true;
  });
  watch(
    () => props.record,
    async (newVal) => {
      await init(newVal?.id);
    },
  );
  watch(
    () => props.student.id,
    async (newVal) => {
      await loadLastPlan();
      await init(props.record?.id || d2dEducationId.value);
    },
  );
</script>

<style lang="scss" scoped>
  .page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .content-wrapper {
    margin: 8px;
    //height: calc(100% - 170px);
    flex: 1;
    align-items: flex-start;
  }

  .container {
    margin: 16px;
  }

  .left {
    max-width: 280px;
    height: auto;
    z-index: 2;

    .left-item {
      cursor: pointer;
      padding: 4px 16px 4px 0;
      text-align: left;
      border-radius: 4px 0 0 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      border: 1px solid #fff;
      border-right: none;
      white-space: nowrap;
      position: relative;

      .el-divider {
        margin: 8px 0;
      }

      &.active {
        font-weight: bold;
      }

      &.divider {
        cursor: default;
        padding: 0;
      }

      &:hover {
      }

      i.right {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 16px;
        height: 16px;
        margin: auto;
      }
    }
  }

  .main-content {
    flex: 1;
    margin-left: 8px;
    position: relative;
    height: 100%;
  }

  .header-append {
    font-size: 12px;
    margin-left: 16px;
    font-weight: normal;
    padding-top: 10px;
  }
</style>
