<script setup lang="ts">
  defineProps({
    raw: {
      type: Array,
      default: () => [],
    },
  });
</script>

<template>
  <a-table size="mini" :pagination="false" :data="raw">
    <template #columns>
      <a-table-column title="学期" data-index="name" />
      <a-table-column title="开始时间" data-index="startTime" />
      <a-table-column title="结束时间" data-index="endTime" />
      <a-table-column title="是否假期" data-index="holiday">
        <template #cell="{ record }">
          {{ record.holiday ? '是' : '否' }}
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
