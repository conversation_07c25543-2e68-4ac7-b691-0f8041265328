<script setup lang="ts">
  const props = defineProps({
    raw: {
      type: String,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
  });
</script>

<template>
  <div class="items-center flex gap-2 flex-1 w-full">
    <div>{{ raw }}</div>
    <a-tooltip v-if="raw && !record.certifiedAt" content="尚未认证">
      <IconQuestionCircleFill class="text-red-500" />
    </a-tooltip>
    <a-tooltip v-else-if="raw" content="手机号已认证">
      <IconCheckCircleFill class="text-green-500" />
    </a-tooltip>
  </div>
</template>

<style scoped lang="scss"></style>
