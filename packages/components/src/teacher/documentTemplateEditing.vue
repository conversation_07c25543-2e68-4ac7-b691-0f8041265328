<script setup lang="ts">
  import { ref, computed, defineEmits, PropType } from 'vue';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    currentTab: {
      type: Object as PropType<any>,
    },
    dataType: {
      type: String,
      default: 'Office',
    },
    topNav: {
      type: Boolean,
      default: true,
    },
  });
  const previewVisible = ref(true);
  const handleUpload = () => {};
  // handle Url try to fix doc can't open
  const handleUrl = (url): any => {
    // return encodeURIComponent(url);
    return encodeURI(url);
  };
  const isImage = computed(() => {
    return [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'svgz',
      'bmp',
      'tiff',
      'tif',
      'ico',
      'cur',
      'avif',
      'apng',
      'heic',
      'heif',
    ].includes(props.dataType.toLowerCase());
  });

  const emits = defineEmits(['replace', 'download']);

  const handlePopEmits = (event: 'replace' | 'download') => {
    if (event) {
      emits(event);
    }
  };
</script>

<template>
  <div v-if="topNav" class="w-full px-2 py-2 mb-2 flex justify-end space-x-2">
    <a-button size="mini" type="outline" @click="handlePopEmits('replace')">
      <template #icon>
        <icon-swap />
      </template>
      替换当前附件
    </a-button>
    <a :href="currentTab.attachment?.udf1" download target="_blank">
      <a-button size="mini" type="outline" @click="handlePopEmits('download')">
        <template #icon>
          <icon-download />
        </template>
        附件下载
      </a-button>
    </a>
  </div>
  <div v-if="!currentTab.attachment?.udf1" class="h-full flex items-center justify-center relative">
    <a-empty class="-mt-10" description="暂无附件" />
  </div>
  <div v-else class="h-full border rounded">
    <div v-if="dataType === 'pdf'">
      <iframe
        :src="`${currentTab.attachment.udf1}`"
        width="100%"
        height="100%"
        style="width: 100%; height: 100%; border: none"
        v-bind="$attrs"
      ></iframe>
    </div>
    <div v-else-if="isImage">
      <div class="image-container text-center mx-auto max-w-[1300px] h-[80vh] overflow-auto">
        <img :src="currentTab.attachment.udf1" :alt="currentTab.attachment.name" class="mx-auto" />
      </div>
    </div>
    <div v-else class="h-full">
      <iframe
        :src="`https://view.officeapps.live.com/op/embed.aspx?src=${handleUrl(currentTab.attachment.udf1)}`"
        width="100%"
        height="100%"
        style="width: 100%; height: 100%; border: none"
        v-bind="$attrs"
      ></iframe>
    </div>
  </div>
</template>

<style scoped lang="scss">
  ::-webkit-scrollbar {
    height: 1px;
    width: 1px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #d3d3d3;
    border-radius: 10px;
    width: 1px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  * {
    scrollbar-width: thin;
    scrollbar-color: #d3d3d3 transparent;
  }
</style>
