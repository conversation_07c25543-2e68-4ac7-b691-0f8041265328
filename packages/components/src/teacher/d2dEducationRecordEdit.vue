<template>
  <div v-if="recordVisible && records.length > 0">
    <div
      v-for="(item, index) in records"
      :key="item.id"
      class="mt-3 rounded-lg transition-all duration-200 border-2 bg-gray-100 border-gray-200"
      :class="{
        '!bg-blue-100/80 !border-blue-300': currentItem?.id === item.id,
      }"
      @click="() => (currentItem = item)"
    >
      <div class="p-4">
        <div class="flex items-start justify-between">
          <div class="flex items-baseline">
            <span class="font-bold text-lg text-gray-800 mr-2">{{ index + 1 }}.</span>
            <div class="flex flex-wrap items-center gap-2">
              <span
                v-for="teacher in item.sendTeachers"
                :key="teacher.id"
                class="px-2 py-1 bg-blue-200 text-blue-800 rounded-full text-xs"
              >
                {{ teacher.name }}
              </span>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <span class="text-gray-500 text-sm">{{ item.date }}</span>
            <a-tag size="mini" :color="'#10b981'" class="font-medium"> {{ item.type }} {{ item.classHour }}课时 </a-tag>
          </div>
        </div>

        <!-- 次信息 -->
        <div class="mt-2 ml-8 flex gap-1 text-xs">
          <span class="bg-gray-50 px-2 py-1 rounded-full text-gray-600 border border-sky-200">
            {{ d2dEducation?.period }}
          </span>
          <span class="bg-gray-50 px-2 py-1 rounded-full text-gray-600 border border-sky-200">
            第{{ item.recordSequence?.sequenceInPeriod }}次
          </span>
        </div>

        <!-- 简述 -->
        <div class="flex items-center mt-2">
          <div class="w-8 h-[1px] bg-gray-300"></div>
          <span class="px-3 text-gray-600">简述</span>
          <div class="flex-1 h-[1px] bg-gray-300"></div>
        </div>

        <div class="mt-3 ml-8 p-3 bg-white rounded-lg">
          <p class="text-gray-600 text-sm">{{ item.description || '暂无描述' }}</p>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-3 flex justify-end space-x-2">
          <a-button size="mini" type="dashed" status="warning" @click="showParentSignature(item)"> 家长签字 </a-button>
          <a-button
            size="mini"
            type="outline"
            class="text-gray-600 hover:text-blue-600 border-gray-300 hover:border-blue-300"
            @click.stop="handleEditAttachment(item)"
          >
            <template #icon><icon-file-text /></template>
            <span>{{ !editEnable ? '查看' : '文档编辑' }}</span>
          </a-button>

          <a-button
            v-if="!isArchive"
            size="mini"
            type="outline"
            :disabled="!editEnable"
            class="text-gray-600 hover:text-yellow-600 border-gray-300 hover:border-yellow-300"
            @click.stop="handleEdit(item)"
          >
            <template #icon><icon-edit /></template>
            修改
          </a-button>

          <a-button
            v-if="!isArchive"
            size="mini"
            type="outline"
            status="danger"
            :disabled="!editEnable"
            @click.stop="handleDel(item)"
          >
            <template #icon><icon-delete /></template>
            删除
          </a-button>
        </div>
      </div>
    </div>
  </div>
  <a-tabs
    v-else-if="currentItem?.docTemplates.length > 0 && currentItem?.id != null"
    :active-key="currentTab.id"
    :default-active-key="currentTab.id"
    class="h-full"
    @change="
      (val) => {
        currentTab = currentItem?.docTemplates.find((item) => val === item.id);
        if (val === -1) currentTab = { id: -1, attachment: {} };
        emits('updateTemplateId', val, currentTab.attachment?.udf1);
      }
    "
  >
    <template #extra>
      <div v-if="!recordVisible" class="mb-2 flex justify-end space-x-2">
        <a-button
          v-if="currentTab?.id !== -1 && !isArchive"
          type="outline"
          size="mini"
          :disabled="!editEnable"
          @click="replace()"
        >
          <template #icon>
            <icon-swap />
          </template>
          替换附件
        </a-button>
        <a-button v-else-if="!isArchive" type="outline" size="mini" :disabled="!editEnable" @click="uploadImg()">
          <template #icon>
            <icon-upload />
          </template>
          上传图片
        </a-button>
        <a-button
          v-if="currentTab?.id !== -1 && !isArchive"
          type="outline"
          size="mini"
          :disabled="!editEnable"
          @click="handlePopEmits('download')"
        >
          <template #icon>
            <icon-download />
          </template>
          下载模版
        </a-button>
        <a-button size="mini" type="dashed" @click="handleBack">
          <template #icon>
            <icon-backward />
          </template>
          返回记录
        </a-button>
      </div>
    </template>
    <a-tab-pane v-for="tab in currentItem?.docTemplates" :key="tab.id" :title="tab.name" class="h-full">
      <document-template-editing
        v-if="tab.attachment?.udf1"
        :current-tab="tab"
        :top-nav="false"
        style="min-height: calc(100vh - 210px)"
        :data-type="tab.attachment?.udf1?.split('.')?.pop()?.toLowerCase()"
      />
      <a-empty v-else class="mt-20" description="暂无附件，请先上传" />
    </a-tab-pane>
    <a-tab-pane :key="-1">
      <template #title>现场照片</template>
      <!-- 图片 -->
      <div v-if="currentItem.attachments.length > 0" class="flex flex-wrap gap-4 p-2 overflow-auto h-[90vh]">
        <div
          v-for="(img, index) in currentItem.attachments"
          :key="index"
          class="group flex-none w-[200px] h-[300px] rounded-md border overflow-hidden hover:shadow shadow-lg flex items-center justify-center bg-white relative"
        >
          <div
            v-if="editEnable"
            class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer z-[10]"
          >
            <icon-close-circle style="color: red" @click="handleDelImg(currentItem.id, img.udf1)" />
          </div>

          <a-image :src="img.udf1" :preview="true" class="max-w-full max-h-full object-contain">
            <!--<template #preview-actions>-->
            <!--  <a-image-preview-action name="删除" @click="handleDelImg(currentItem.id, img.udf1)">-->
            <!--    <icon-delete style="color: red" />-->
            <!--  </a-image-preview-action>-->
            <!--</template>-->
          </a-image>
        </div>
      </div>
      <a-empty v-else description="暂无照片" class="mt-20" />
    </a-tab-pane>
  </a-tabs>
  <div v-else>
    <div class="flex justify-end gap-2">
      <a-button size="mini" @click="handleBack">返回记录</a-button>
    </div>
    <a-empty class="courser-pointer" description="暂无数据" @dblclick="() => (recordVisible = true)" />
  </div>

  <a-modal
    :visible="replaceAttachmentVisible"
    :closable="false"
    :on-before-ok="handleReplacePreOk"
    @cancel="handleReplaceCancel"
  >
    <uploader
      v-if="true"
      v-model="fileList"
      :multiple="currentUploadType === 'img'"
      sub-folder=""
      :accept="currentUploadType === 'img' ? '.jpg,.jpeg,.png' : '*'"
      @update:model-value="handleUpload"
    />
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, h } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import DocumentTemplateEditing from './documentTemplateEditing.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
    },
    d2dEducation: {
      type: Object,
    },
    recordFlush: {
      type: Boolean,
    },
    isArchive: {
      type: Boolean,
      default: false,
    },
    editEnable: {
      type: Boolean,
      default: false,
    },
    delImgForbid: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['editInfo', 'editAttachment', 'updateTemplateId', 'download']);

  const records = ref<any>([]);
  const fileList = ref<any>([]);
  const uploadedList = ref<any>([]);

  const currentItem = ref<any>();
  const currentTab = ref<any>({
    id: -1,
    attachment: {},
  });
  const currentUploadType = ref('attachment');
  const recordVisible = ref(true);

  const replaceAttachmentVisible = ref(false);

  const loadData = async () => {
    await request(`/resourceRoom/d2dEducationRecord/findByD2dEduId/${props.d2dEducation?.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    }).then((res) => {
      records.value = res.data.items.sort(
        (a, b) => a.recordSequence.sequenceInPeriod - b.recordSequence.sequenceInPeriod,
      );
      // eslint-disable-next-line prefer-destructuring
      currentItem.value = records.value[0];

      // control button description
      if (currentItem.value?.docTemplates?.length > 0) {
        const docTemplate = currentItem.value?.docTemplates[0];
        currentTab.value.id = docTemplate?.id;
        emits('updateTemplateId', docTemplate.id, docTemplate.attachment?.udf1);
      }
    });
  };
  const isRightType = ref(true);

  const handleUpdate = async (files: any) => {
    if (!isRightType.value) {
      Message.clear('top');
      Message.warning('请上传正确格式的文件');
      return;
    }
    // 上传图片
    if (currentUploadType.value === 'img') {
      files.forEach((item) => {
        currentItem.value.attachments.push({
          name: item.name,
          udf1: item.url,
          id: item.id,
        });
      });
    } else if (files.length === 1) {
      currentItem.value?.docTemplates.forEach((item) => {
        if (item.id === currentTab.value.id) {
          item.attachment = {
            name: files[0].name,
            udf1: files[0].url,
            id: files[0]?.id,
          };
        }
      });
    } else {
      Message.warning('只能上传并单个文件替换');
    }
  };

  const handleUploads = async (options: any) => {
    if (!options?.fileItem?.file) return;
    if (
      !['jpg', 'jpeg', 'bmp', 'png']?.includes(options.fileItem.name.split('.').pop().toLowerCase()) &&
      currentUploadType.value === 'img'
    ) {
      Message.warning('请上传如下格式的图片 jpg,jpeg,bmp,png');
      isRightType.value = false;
      return;
    }
    const formDataSet = new FormData();
    formDataSet.set('type', currentUploadType.value === 'attachment' ? 'Wps' : 'Other');
    formDataSet.set('file', options.fileItem.file);
    formDataSet.set('fileName', options.fileItem.name);
    try {
      await request('/common/uploadedResource/uploadAndGetDirectUrl', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: formDataSet,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((res) => {
        uploadedList.value.push({
          name: res.data.name,
          url: res.data.directVisitUrl,
          id: res.data.id,
        });
      });
    } finally {
      /**/
    }
  };

  const handleDelImg = async (id, url) => {
    if (props.delImgForbid) return;
    Modal.confirm({
      title: '删除！',
      content: '确认删除当前图片？',
      onOk: () => {
        currentItem.value.attachments = currentItem.value.attachments.filter((item) => item.udf1 !== url);
        request(`/resourceRoom/d2dEducationRecord/${currentItem.value.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
          data: currentItem.value,
        }).then((res) => {
          Message.success('操作成功');
        });
      },
    });
  };
  const handleUpload = async () => {};
  const handleReplacePreOk = async () => {
    await handleUpdate(fileList.value);
    // await handleUpdate(uploadedList.value);
    await request(`/resourceRoom/d2dEducationRecord/${currentItem.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: currentItem.value,
    }).then(async (res) => {
      // await loadData();
      uploadedList.value = [];
      fileList.value = [];
      Message.success('上传成功');
      replaceAttachmentVisible.value = false;
    });
  };
  const handleReplaceCancel = () => {
    replaceAttachmentVisible.value = false;
  };
  const upload = (item) => {
    currentItem.value = item;
    replaceAttachmentVisible.value = true;
  };
  const replace = () => {
    currentUploadType.value = 'attachment';
    replaceAttachmentVisible.value = true;
  };
  const uploadImg = () => {
    currentUploadType.value = 'img';
    replaceAttachmentVisible.value = true;
  };
  const handleBack = () => {
    recordVisible.value = true;
    currentTab.value = {
      id: -1,
      attachment: {},
    };
  };
  const handleDel = async (item) => {
    Modal.warning({
      title: '提示',
      content: `确认删除该条${item?.type || ''}记录`,
      onOk: async () => {
        await request(`/resourceRoom/d2dEducationRecord/${item.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'delete',
        }).then(() => {
          Message.success('删除成功');
          loadData();
        });
      },
    });
  };

  const showParentSignature = (record: any) => {
    Modal.info({
      title: '家长签字',
      content: h('img', {
        src: record?.parentSignature?.udf1,
        style: { maxWidth: '100%' },
        alt: '家长签字详情',
      }),
      width: 700,
      maskClosable: true,
    });
  };

  const handleEdit = async (item: any) => {
    emits('editInfo', item);
  };

  const handleEditAttachment = (item: any) => {
    currentItem.value = item;
    recordVisible.value = false;
  };

  const handlePopEmits = async (event: string) => {
    try {
      const { data: response } = await request(`/document/docTemplate/${currentTab.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      const url = response.attachment?.udf1;
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', currentTab.value.name || response.attachment?.name || '');
      document.body.appendChild(link);
      link.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      emits(event);
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  onMounted(async () => {
    await loadData();
  });
  watch(
    () => props.recordFlush,
    async () => {
      await loadData();
    },
  );
  watch(
    () => replaceAttachmentVisible.value,
    () => {
      isRightType.value = true;
    },
  );
  defineExpose({
    currentItem,
    currentTab,
  });
</script>

<style lang="scss" scoped>
  * {
    user-select: none;
  }
</style>
