<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: String,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
  });
</script>

<template>
  <div class="items-center flex gap-2 flex-1 w-full">
    <!--disabled-->
    <a-input v-bind="$attrs" :model-value="modelValue" class="flex-1">
      <template #append>
        <a-tooltip v-if="modelValue && !record.certifiedAt" content="尚未认证">
          <IconQuestionCircleFill class="text-red-500" />
        </a-tooltip>
        <a-tooltip v-else-if="modelValue" content="手机号已认证">
          <IconCheckCircleFill class="text-green-500" />
        </a-tooltip>
      </template>
    </a-input>
  </div>
</template>

<style scoped lang="scss"></style>
