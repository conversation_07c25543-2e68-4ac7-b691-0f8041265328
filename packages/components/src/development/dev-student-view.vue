<script lang="ts" setup>
  import { PropType, inject } from 'vue';

  const props = defineProps({
    schema: {
      type: Object as PropType<any>,
      required: true,
    },
    raw: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    teachersMap: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const RecordDetail = inject('RecordDetail') as any;
  const AvatarDisplay = inject('AvatarDisplay') as any;
</script>

<template>
  <record-detail ref="recordDetailRef" :raw="raw" :schema="schema">
    <div v-if="raw.status === 'WaitingResettlement'">
      <a-descriptions title="安置信息" :column="4" bordered>
        <a-descriptions-item label="评测时间">{{ raw.additionalData?.assessmentDate || '未设置' }}</a-descriptions-item>
        <a-descriptions-item label="评测专家">
          <div v-if="!raw.additionalData?.assessmentExperts?.length">未设置</div>
          <div v-else class="flex gap-2 flex-wrap">
            <avatar-display
              v-for="expert in raw.additionalData?.assessmentExperts"
              :key="expert"
              :size="24"
              :user-card="true"
              mode="capsule"
              :user-info="teachersMap[expert]"
            />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="家长已确认">
          {{ raw.additionalData?.guardianConfirmedResettlement ? '是' : '否' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </record-detail>
</template>
