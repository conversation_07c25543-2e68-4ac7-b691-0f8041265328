/**
 * CBCL评分计算工具
 */

/**
 * 计算活动能力得分
 * @param {Object} data
 * @returns {Number}
 */
function calculateActivityScore(data) {
  let score = 0;

  // I(1) 体育活动列表
  if (data.sportsActivities?.list) {
    if (data.sportsActivities.list.length === 0) {
      score += 0;
    } else if (data.sportsActivities.list.length === 1) {
      score += 0;
    } else if (data.sportsActivities.list.length === 2) {
      score += 1;
    } else {
      score += 2;
    }
  }

  // I(2)和I(3) 体育活动时间和水平
  let sportSubScore = 0;
  let sportCount = 0;

  if (data.sportsActivities?.timeSpent !== null) {
    sportSubScore += data.sportsActivities.timeSpent;
    sportCount += 1;
  }

  if (data.sportsActivities?.level !== null) {
    sportSubScore += data.sportsActivities.level;
    sportCount += 1;
  }

  if (sportCount > 0) {
    score += sportSubScore / sportCount;
  }

  // II(1) 爱好列表
  if (data.hobbies?.list) {
    if (data.hobbies.list.length === 0) {
      score += 0;
    } else if (data.hobbies.list.length === 1) {
      score += 0;
    } else if (data.hobbies.list.length === 2) {
      score += 1;
    } else {
      score += 2;
    }
  }

  // II(2)和II(3) 爱好时间和水平
  let hobbySubScore = 0;
  let hobbyCount = 0;

  if (data.hobbies?.timeSpent !== null) {
    hobbySubScore += data.hobbies.timeSpent;
    hobbyCount += 1;
  }

  if (data.hobbies?.level !== null) {
    hobbySubScore += data.hobbies.level;
    hobbyCount += 1;
  }

  if (hobbyCount > 0) {
    score += hobbySubScore / hobbyCount;
  }

  // IV(2) 工作质量
  if (data.jobs?.quality !== null) {
    score += data.jobs.quality;
  }

  // 活动能力总分
  return score;
}

/**
 * 计算社交情况得分
 * @param {Object} data
 * @returns {Number}
 */
function calculateSocialScore(data) {
  let score = 0;

  // III 组织
  if (data.organizations?.list) {
    if (data.organizations.list.length === 0) {
      score += 0;
    } else if (data.organizations.list.length === 1) {
      score += 0;
    } else if (data.organizations.list.length === 2) {
      score += 1;
    } else {
      score += 2;
    }
  }

  // III(2) 活跃程度
  if (data.organizations?.activeness !== null) {
    score += data.organizations.activeness;
  }

  // V(1) 朋友数量
  if (data.friends?.count !== null) {
    if (data.friends.count === 0) {
      score += 0;
    } else if (data.friends.count === 1) {
      score += 0;
    } else if (data.friends.count === 2) {
      score += 1;
    } else {
      score += 2;
    }
  }

  // V(2) 与朋友在一起的频率
  if (data.friends?.frequency !== null) {
    score += data.friends.frequency;
  }

  // VI 相处情况
  if (data.relationship) {
    let relationshipSum = 0;
    let relationshipCount = 0;

    // a, b, c 三项的平均分
    if (data.relationship.siblings !== null) {
      relationshipSum += data.relationship.siblings;
      relationshipCount += 1;
    }

    if (data.relationship.otherChildren !== null) {
      relationshipSum += data.relationship.otherChildren;
      relationshipCount += 1;
    }

    if (data.relationship.parents !== null) {
      relationshipSum += data.relationship.parents;
      relationshipCount += 1;
    }

    if (relationshipCount > 0) {
      score += relationshipSum / relationshipCount;
    }

    // d 项单独计分
    if (data.relationship.selfPlay !== null) {
      score += data.relationship.selfPlay;
    }
  }

  return score;
}

/**
 * 计算学校情况得分
 * @param {Object} data
 * @returns {Number}
 */
function calculateSchoolScore(data) {
  let score = 0;

  // 如果未上学，返回0分
  if (!data.academics?.inSchool) {
    return 0;
  }

  // VII(1) 学科成绩
  if (data.academics?.subjects) {
    const { subjects } = data.academics;
    let subjectSum = 0;
    let subjectCount = 0;

    // 基本科目
    if (subjects.reading !== null) {
      subjectSum += subjects.reading;
      subjectCount += 1;
    }

    if (subjects.writing !== null) {
      subjectSum += subjects.writing;
      subjectCount += 1;
    }

    if (subjects.math !== null) {
      subjectSum += subjects.math;
      subjectCount += 1;
    }

    if (subjects.phonetics !== null) {
      subjectSum += subjects.phonetics;
      subjectCount += 1;
    }

    // 其他科目
    if (subjects.others && subjects.others.length > 0) {
      subjects.others.forEach((subject) => {
        if (subject.score !== null) {
          subjectSum += subject.score;
          subjectCount += 1;
        }
      });
    }

    if (subjectCount > 0) {
      score += subjectSum / subjectCount;
    }
  }

  // VII(2) 特殊班级
  if (data.academics?.specialClass !== null) {
    score += data.academics.specialClass ? 0 : 1;
  }

  // VII(3) 留级
  if (data.academics?.retained !== null) {
    score += data.academics.retained ? 0 : 1;
  }

  // VII(4) 学习问题
  if (data.academics?.problems !== null) {
    score += data.academics.problems ? 0 : 1;
  }

  return score;
}

/**
 * 获取正常范围
 * @param {String} gender 性别
 * @param {Number} age 年龄
 * @returns {Object} 正常范围
 */
function getNormalRanges(gender, age) {
  // 根据性别和年龄段选择相应的正常范围
  if (age >= 6 && age <= 11) {
    if (gender === 'male') {
      return {
        activity: { min: 3, max: 9 },
        social: { min: 3, max: 8 },
        school: { min: 2, max: 5.5 },
      };
    }
    return {
      activity: { min: 2.5, max: 9 },
      social: { min: 3.5, max: 8 },
      school: { min: 3, max: 6 },
    };
  }
  if (age >= 12 && age <= 16) {
    if (gender === 'male') {
      return {
        activity: { min: 3, max: 9 },
        social: { min: 3.5, max: 8.5 },
        school: { min: 2, max: 5.5 },
      };
    }
    return {
      activity: { min: 3, max: 9 },
      social: { min: 3, max: 8.5 },
      school: { min: 3, max: 6 },
    };
  }
  // 4-5岁范围（简化处理）
  return {
    activity: { min: 2.5, max: 9 },
    social: { min: 3, max: 8 },
    school: { min: 2, max: 5.5 },
  };
}

/**
 * 获取分数的百分位
 * @param {Number} score 得分
 * @param {Object} range 正常范围
 * @returns {String} 百分位描述
 */
function getPercentile(score, range) {
  if (score < range.min) {
    return '<2%';
  }
  if (score > range.max) {
    return '>69%';
  }
  // 简化的百分位计算
  const percentage = Math.round(((score - range.min) / (range.max - range.min)) * 67) + 2;
  return `${percentage}%`;
}

/**
 * 获取分数状态
 * @param {Number} score 得分
 * @param {Object} range 正常范围
 * @returns {String} 状态描述
 */
function getStatus(score, range) {
  if (score < range.min) {
    return '可能异常';
  }
  return '正常';
}

/**
 * 计算社会能力得分
 * @param {Object} socialAbilityData 社会能力数据
 * @param {String} gender 性别 "male"或"female"
 * @param {Number} age 年龄
 * @returns {Object} 社会能力各维度得分
 */
export function calculateSocialAbilityScores(socialAbilityData, gender, age) {
  // 活动能力得分计算
  const activityScore = calculateActivityScore(socialAbilityData);

  // 社交情况得分计算
  const socialScore = calculateSocialScore(socialAbilityData);

  // 学校情况得分计算
  const schoolScore = calculateSchoolScore(socialAbilityData);

  // 获取正常范围
  const normalRanges = getNormalRanges(gender, age);

  return {
    activityScore: {
      score: activityScore,
      percentile: getPercentile(activityScore, normalRanges.activity),
      status: getStatus(activityScore, normalRanges.activity),
    },
    socialScore: {
      score: socialScore,
      percentile: getPercentile(socialScore, normalRanges.social),
      status: getStatus(socialScore, normalRanges.social),
    },
    schoolScore: {
      score: schoolScore,
      percentile: getPercentile(schoolScore, normalRanges.school),
      status: getStatus(schoolScore, normalRanges.school),
    },
    totalScore: activityScore + socialScore + schoolScore,
  };
}

/**
 * 获取因子分的百分位
 * @param {Number} score 因子分
 * @param {Object} range 正常范围
 * @returns {String} 百分位描述
 */
function getFactorPercentile(score, range) {
  if (score <= range.min) {
    return '<69%';
  }
  if (score > range.max) {
    return '>98%';
  }
  // 简化的百分位计算
  const percentage = Math.round(((score - range.min) / (range.max - range.min)) * 29) + 69;
  return `${percentage}%`;
}

/**
 * 获取因子分状态
 * @param {String} percentile 百分位
 * @returns {String} 状态描述
 */
function getFactorStatus(percentile) {
  if (percentile === '>98%') {
    return '异常';
  }
  return '正常';
}

/**
 * 获取因子定义
 * @param {String} gender 性别
 * @param {Number} age 年龄
 * @returns {Array} 因子定义列表
 */
function getFactorDefinitions(gender, age) {
  // 根据性别和年龄段选择相应的因子定义
  if (age >= 6 && age <= 11) {
    if (gender === 'male') {
      return [
        {
          name: '分裂样',
          items: [11, 29, 30, 40, 47, 50, 59, 70, 75],
          normalRange: { min: 1, max: 5 },
        },
        {
          name: '抑郁',
          items: [12, 14, 18, 31, 32, 33, 34, 35, 45, 50, 52, 71, 88, 89, 91, 103, 112],
          normalRange: { min: 3, max: 13 },
        },
        {
          name: '交往不良',
          items: [13, 65, 69, 71, 75, 80, 86, 103],
          normalRange: { min: 2, max: 6 },
        },
        {
          name: '强迫性',
          items: [9, 13, 17, 46, 47, 50, 54, 66, 76, 80],
          normalRange: { min: 3, max: 9 },
        },
        {
          name: '体诉',
          items: [49, 51, 54, 56, 56, 56, 56, 56, 56, 77],
          normalRange: { min: 0, max: 4 },
        },
        {
          name: '社交退缩',
          items: [25, 34, 38, 42, 48, 64, 102, 111],
          normalRange: { min: 1, max: 6 },
        },
        {
          name: '多动',
          items: [1, 8, 10, 13, 17, 20, 41, 61, 62, 64],
          normalRange: { min: 3, max: 11 },
        },
        {
          name: '攻击性',
          items: [3, 7, 16, 19, 22, 23, 25, 27, 37, 43, 48, 57, 68, 86, 87, 88, 89, 90, 93, 94, 95, 97, 104],
          normalRange: { min: 9, max: 20 },
        },
        {
          name: '违纪',
          items: [21, 22, 23, 39, 43, 67, 72, 81, 82, 90, 101, 106],
          normalRange: { min: 0, max: 6 },
        },
      ];
    }
    return [
      {
        name: '体诉',
        items: [36, 46, 50, 51, 54, 56, 56, 56, 56, 56, 56, 56, 80, 102, 112],
        normalRange: { min: 1, max: 7 },
      },
      {
        name: '分裂样',
        items: [5, 11, 30, 31, 32, 40, 51, 52, 99, 112],
        normalRange: { min: 0, max: 5 },
      },
      {
        name: '交往不良',
        items: [13, 42, 65, 69, 71, 75, 80, 86, 87, 88, 89, 102, 103],
        normalRange: { min: 3, max: 13 },
      },
      {
        name: '不成熟',
        items: [1, 11, 14, 19, 64, 108, 109],
        normalRange: { min: 0, max: 5 },
      },
      {
        name: '强迫性',
        items: [7, 9, 17, 31, 66, 83, 84, 85, 104],
        normalRange: { min: 1, max: 6 },
      },
      {
        name: '敌意性',
        items: [1, 12, 20, 21, 25, 33, 34, 35, 37, 38, 48, 62, 64, 111],
        normalRange: { min: 3, max: 9 },
      },
      {
        name: '违纪',
        items: [20, 21, 23, 39, 43, 61, 67, 72, 81, 82, 101, 105, 106],
        normalRange: { min: 0, max: 8 },
      },
      {
        name: '攻击性',
        items: [3, 10, 16, 19, 22, 27, 34, 37, 41, 45, 57, 68, 86, 87, 88, 89, 90, 93, 94, 95, 97, 104],
        normalRange: { min: 9, max: 22 },
      },
      {
        name: '多动',
        items: [1, 8, 10, 23, 41, 44, 45, 61, 62, 74],
        normalRange: { min: 1, max: 10 },
      },
    ];
  }
  if (age >= 12 && age <= 16) {
    if (gender === 'male') {
      return [
        {
          name: '焦虑强迫',
          items: [9, 12, 14, 27, 29, 30, 31, 32, 33, 34, 35, 45, 47, 50, 52, 71, 76, 100, 112],
          normalRange: { min: 3, max: 14 },
        },
        {
          name: '体诉',
          items: [56, 56, 56, 56, 56, 56, 80, 84, 85, 96],
          normalRange: { min: 0, max: 4 },
        },
        {
          name: '分裂样',
          items: [17, 29, 40, 47, 70, 75, 77, 80, 86, 88, 102, 103, 111],
          normalRange: { min: 0, max: 4 },
        },
        {
          name: '抑郁退缩',
          items: [42, 54, 65, 69, 71, 75, 80, 86, 88, 102, 103, 111],
          normalRange: { min: 3, max: 11 },
        },
        {
          name: '不成熟',
          items: [1, 8, 10, 11, 13, 17, 25, 38, 48, 58, 62, 64, 80, 83, 98],
          normalRange: { min: 2, max: 9 },
        },
        {
          name: '违纪',
          items: [22, 23, 26, 39, 43, 63, 67, 69, 81, 82, 90, 101, 105],
          normalRange: { min: 2, max: 12 },
        },
        {
          name: '攻击性',
          items: [3, 10, 16, 19, 22, 27, 34, 37, 41, 45, 57, 68, 86, 87, 88, 89, 90, 93, 94, 95, 97, 104],
          normalRange: { min: 6, max: 22 },
        },
        {
          name: '残忍',
          items: [15, 16, 20, 21, 25],
          normalRange: { min: 0, max: 4 },
        },
      ];
    }
    return [
      {
        name: '体诉',
        items: [36, 49, 50, 51, 54, 56, 56, 56, 56, 56, 56, 56, 80, 102, 112],
        normalRange: { min: 1, max: 7 },
      },
      {
        name: '分裂样',
        items: [5, 11, 30, 31, 32, 40, 51, 52, 99, 112],
        normalRange: { min: 0, max: 5 },
      },
      {
        name: '交往不良',
        items: [13, 42, 65, 69, 71, 75, 80, 86, 87, 88, 89, 102, 103, 111, 112],
        normalRange: { min: 3, max: 13 },
      },
      {
        name: '不成熟',
        items: [1, 11, 14, 19, 64, 108, 109],
        normalRange: { min: 0, max: 5 },
      },
      {
        name: '强迫性',
        items: [7, 9, 17, 31, 66, 83, 84, 85, 104],
        normalRange: { min: 1, max: 6 },
      },
      {
        name: '敌意性',
        items: [1, 12, 20, 21, 25, 33, 34, 35, 37, 38, 48, 62, 64, 111],
        normalRange: { min: 3, max: 9 },
      },
      {
        name: '违纪',
        items: [20, 21, 23, 39, 43, 61, 67, 72, 81, 82, 101, 105, 106],
        normalRange: { min: 0, max: 8 },
      },
      {
        name: '攻击性',
        items: [3, 10, 16, 19, 22, 27, 34, 37, 41, 45, 57, 68, 86, 87, 88, 89, 90, 93, 94, 95, 97, 104],
        normalRange: { min: 9, max: 22 },
      },
      {
        name: '多动',
        items: [1, 8, 10, 23, 41, 44, 45, 61, 62, 74],
        normalRange: { min: 1, max: 10 },
      },
    ];
  }
  // 默认返回一个简化版本（适用于4-5岁）
  return [
    {
      name: '分裂样',
      items: [11, 29, 30, 40, 47, 50, 59, 70, 75],
      normalRange: { min: 1, max: 5 },
    },
    {
      name: '抑郁',
      items: [12, 14, 18, 31, 32, 33, 34, 35, 45, 50, 52, 71, 88, 89, 91, 103, 112],
      normalRange: { min: 3, max: 13 },
    },
    {
      name: '注意力问题',
      items: [1, 8, 10, 13, 17, 41, 45, 61, 62],
      normalRange: { min: 2, max: 10 },
    },
    {
      name: '攻击性',
      items: [3, 16, 19, 20, 21, 22, 23, 27, 37, 57, 68, 86, 87, 88, 93, 94, 95, 97],
      normalRange: { min: 8, max: 20 },
    },
  ];
}

/**
 * 计算行为问题因子分
 * @param {Array} behaviorProblemsData 行为问题数据
 * @param {String} gender 性别 "male"或"female"
 * @param {Number} age 年龄
 * @returns {Array} 行为问题因子分
 */
export function calculateBehaviorFactorScores(behaviorProblemsData, gender, age) {
  // 因子分定义
  const factors = getFactorDefinitions(gender, age);

  // 计算每个因子的分数
  const factorScores = factors.map((factor) => {
    // 计算因子分数 - 把因子包含的所有项目分数相加
    const score = factor.items.reduce((sum, itemId) => {
      // 数组索引从0开始，而项目ID从1开始，所以需要减1
      return sum + (behaviorProblemsData[itemId - 1] || 0);
    }, 0);

    // 获取正常范围
    const { normalRange } = factor;

    // 计算百分位和状态
    const percentile = getFactorPercentile(score, normalRange);
    const status = getFactorStatus(percentile);

    return {
      factor: factor.name,
      score,
      percentile,
      status,
      items: factor.items,
    };
  });

  return factorScores;
}

export default {
  calculateSocialAbilityScores,
  calculateBehaviorFactorScores,
};
