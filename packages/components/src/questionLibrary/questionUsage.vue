<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import presetTypes from './presets';
  import QuestionAnswerPreviewSimple from './questionAnswerPreviewSimple.vue';

  const props = defineProps({
    question: {
      type: Object,
      required: true,
    },
    sequence: {
      type: String,
      required: true,
    },
    answer: {
      type: [Object, Array, Number, String] as PropType<any>,
      required: true,
    },
    criteria: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const emit = defineEmits(['update:answer']);

  const usageCmp = computed(() => {
    const { presetType } = props.question;
    return presetTypes.find((item) => item.presetType === presetType)?.answerCmp;
  });

  const questionAnswer = computed({
    get: () => props.answer,
    set: (value) => {
      emit('update:answer', value);
    },
  });
</script>

<template>
  <div class="mb-2 mt-3 pb-2 border-b border-slate-200">
    <div class="header flex gap-2 py-1 text-base">
      <span class="flex-1">
        <strong> {{ sequence }}、 </strong>
        <span v-html="question.question" />
      </span>
      <small v-if="criteria?.preTestResult && criteria?.preTestResult[question.id]" class="w-16">
        前测:
        <question-answer-preview-simple :answer="criteria?.preTestResult[question.id]" />
      </small>
      <small class="w-14">({{ question.score }}分)</small>
    </div>
    <component :is="usageCmp" v-model="questionAnswer" :question="question" />
  </div>
</template>

<style scoped lang="scss"></style>
