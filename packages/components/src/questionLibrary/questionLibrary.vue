<script setup lang="ts">
  import { IconFolderAdd, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon';
  import { useList } from '@repo/infrastructure/crud';
  import { onMounted, computed, ref, watch, PropType } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import {
    deleteCatalog,
    deleteQuestion,
    getQuestionList,
    updateCatalogName,
  } from '@repo/infrastructure/openapi/questionLibraryController';
  import { Message, Modal } from '@arco-design/web-vue';
  import { usePrompt } from '@repo/ui/components';
  import NewFolderModal from './components/newFolderModal.vue';
  import Breadcrumb from './components/breadcrumb.vue';
  import LibraryContentItem from './components/libraryContentItem.vue';
  import useQuestionLibraryStore from './store';
  import { getPresetTypeDisplay } from './presets';
  import ProcessQuestionModal from './components/processQuestionModal.vue';

  const props = defineProps({
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    defaultEditData: {
      type: Object,
      default: () => ({}),
    },
    requestApi: {
      type: String,
      default: '/questionLibrary/catalog',
    },
    onGetQuestionList: {
      type: Function,
      default: getQuestionList,
    },
    onUpdateCatalogName: {
      type: Function,
      default: updateCatalogName,
    },
    onDeleteCatalog: {
      type: Function,
      default: deleteCatalog,
    },
    onDeleteQuestion: {
      type: Function,
      default: deleteQuestion,
    },
    onCreateNewCatalog: {
      type: Function,
      required: false,
    },
    catalogType: {
      type: Number,
      required: true,
    },
    isInject: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  });

  const { loading: folderLoading, setLoading: setFolderLoading } = useLoading();
  const folderTree = computed(() => listData.value);
  const currentFolder = ref<any>({});
  const currentSubFolders = ref<any[]>([]);
  const currentSubFiles = ref<any[]>([]);
  const libraryStore = useQuestionLibraryStore();
  const { prompt } = usePrompt();
  const currentFolderContents = computed(() => {
    return [
      ...currentSubFolders.value.map((item: any) => {
        return {
          ...item,
          type: 'folder',
          children: undefined,
        };
      }),
      ...currentSubFiles.value.map((item: any) => {
        return {
          ...item,
          type: 'file',
        };
      }),
    ];
  });

  const { loadData, listInit, listData, loading } = useList({
    api: `${props.requestApi}/tree`,
    defaultQueryParams: {
      ...props.defaultQueryParams,
      catalogType: props.catalogType,
    },
  });

  const rawFoldsMap = ref({});
  const treeProps = ref({
    key: 'id',
    title: 'name',
    children: 'children',
  });
  const createNewFolderVisible = ref(false);

  const emit = defineEmits(['goEditQuestion']);

  // 递归 flat folderTree => rawFoldsMap
  const flatFolderTree = (data: any) => {
    data.forEach((item: any) => {
      rawFoldsMap.value[item.id] = item;
      if (item.children) {
        flatFolderTree(item.children);
      }
    });
  };

  const handleGoFolder = async (data: any) => {
    if (data.rgt > 0) {
      currentSubFiles.value = [];
      currentSubFolders.value = [];
      currentFolder.value = data || {
        id: 0,
        name: '根目录',
      };

      await loadFolderContent();
    } else {
      emit('goEditQuestion', data);
    }
  };

  const breadCrumbs = computed(() => {
    const bc: any[] = [];
    if (currentFolder.value.id === 0) {
      return bc;
    }

    // 根据左右值找到所有父级目录
    const parentIds: number[] = [];
    Object.values(rawFoldsMap.value)
      .sort((a: any, b: any) => {
        return a.lft - b.lft;
      })
      .forEach((item: any) => {
        if (item.lft < currentFolder.value.lft && item.rgt > currentFolder.value.rgt) {
          parentIds.push(item.id);
        }
      });

    if (parentIds?.length) {
      parentIds.forEach((id: number) => {
        if (id === 0 || !rawFoldsMap.value[id]) {
          return;
        }
        bc.push({
          ...rawFoldsMap.value[id],
        });
      });
    }

    bc.push({
      ...currentFolder.value,
      last: true,
    });
    return bc;
  });

  watch(
    () => breadCrumbs.value,
    (value) => {
      libraryStore.setBreadcrumbs(value);
      libraryStore.setCatalogId(currentFolder.value.id);
    },
  );

  const loadFolderTree = async () => {
    rawFoldsMap.value = {};
    await loadData();
    flatFolderTree(folderTree.value);
  };

  const loadFolderContent = async () => {
    currentSubFolders.value = [];
    currentSubFiles.value = [];
    setFolderLoading(true);
    try {
      const { data } = await props.onGetQuestionList({
        catalogId: currentFolder.value.id,
        ...props.defaultQueryParams,
      });
      currentSubFolders.value = rawFoldsMap.value[currentFolder.value.id]?.children || [];
      currentSubFiles.value = data || [];
    } finally {
      setFolderLoading(false);
    }
  };

  const handleRefresh = async (refreshAll?: boolean) => {
    if (refreshAll) {
      await loadFolderTree();
    }

    await loadFolderContent();
  };

  const handleGoEditQuestion = (item?: any) => {
    emit('goEditQuestion', item);
  };

  const handleEditFolderName = async (item: any) => {
    const name = await prompt({
      title: '修改文件夹名称',
      placeholder: '请输入新的文件夹名称',
      raw: item.name,
    });

    if (!name) {
      Message.error('文件夹名称不能为空');
    }

    try {
      setFolderLoading(true);
      await props.onUpdateCatalogName(
        {
          id: item.id,
        },
        {
          name,
        },
      );
      await handleRefresh(true);

      Message.success('修改成功');
    } finally {
      setFolderLoading(false);
    }
  };

  const handleDelete = async (item: any) => {
    let msg = '';
    if (item.type === 'folder') {
      msg = `确定删除文件夹 ${item.name} 吗？其子文件夹及试题将会被一同删除`;
    } else {
      msg = `确定删除这个试题吗？`;
    }

    Modal.confirm({
      title: '提示',
      content: msg,
      okButtonProps: {
        status: 'danger',
      },
      onOk: async () => {
        try {
          setFolderLoading(true);
          if (item.type === 'folder') {
            await props.onDeleteCatalog({
              id: item.id,
            });
          } else {
            await props.onDeleteQuestion({
              id: item.id,
            });
          }
          await handleRefresh(true);
        } finally {
          setFolderLoading(false);
        }
      },
    });
  };

  const processModalVisible = ref(false);
  const handleProcess = async () => {
    processModalVisible.value = true;
  };

  onMounted(async () => {
    await listInit();
    await loadFolderTree();

    await handleGoFolder(folderTree.value[0] || {});
  });
</script>

<script lang="ts">
  export default {
    name: 'QuestionLibrary',
  };
</script>

<template>
  <div class="question-library-wrapper mx-auto flex gap-2">
    <a-spin class="w-80" :loading="loading">
      <a-card :title="props.record?.scene === 'investigation' ? '问卷目录' : '题库目录'" class="h-full" size="small">
        <a-empty v-if="!folderTree.length" description="暂无数据"></a-empty>
        <a-tree
          v-else
          size="small"
          :data="folderTree[0].children"
          :checkable="false"
          :default-expanded-keys="[currentFolder?.id, folderTree[0].id]"
          :field-names="treeProps"
          auto-expand-parent
          selectable
          :default-expand-selected="true"
          :selected-keys="[currentFolder.id, folderTree[0].id]"
          @select="(keys: any, e: any) => handleGoFolder(e.node)"
        >
          <template #icon>
            <IconFolder />
          </template>
        </a-tree>
      </a-card>
    </a-spin>
    <div class="flex flex-col gap-2 flex-1">
      <a-card size="small">
        <breadcrumb :bread-crumbs="breadCrumbs" @node-click="handleGoFolder" @handle-process="handleProcess" />
      </a-card>
      <a-spin :loading="folderLoading" class="w-full">
        <a-card class="files-wrapper" size="small">
          <template #title>
            <div class="flex justify-between">
              <a-space>
                <a-button
                  v-if="currentFolder.parentId"
                  icon="el-icon-top"
                  plain
                  size="mini"
                  @click="
                    () => handleGoFolder(currentFolder.parentId ? rawFoldsMap[currentFolder.parentId] : folderTree[0])
                  "
                >
                  <template #icon>
                    <IconArrowUp />
                  </template>
                  返回上级
                </a-button>
                <a-button v-if="!isInject" size="mini" type="primary" @click="() => handleGoEditQuestion()">
                  <template #icon>
                    <IconPlus />
                  </template>
                  {{ props.record?.scene === 'investigation' ? '创建问卷' : '创建试题' }}
                </a-button>
                <a-button v-if="!isInject" size="mini" type="outline" @click="() => (createNewFolderVisible = true)">
                  <template #icon>
                    <IconFolderAdd />
                  </template>
                  新建文件夹
                </a-button>
              </a-space>
              <a-space>
                <a-button-group>
                  <a-button size="mini" @click="() => loadFolderContent()">
                    <template #icon>
                      <IconRefresh />
                    </template>
                    刷新
                  </a-button>
                </a-button-group>
              </a-space>
            </div>
          </template>

          <div class="folder-contents">
            <a-table
              v-if="currentFolderContents?.length"
              :data="currentFolderContents"
              stripe
              size="small"
              :pagination="false"
            >
              <template #columns>
                <a-table-column key="name" title="名称" data-index="name">
                  <template #cell="{ record }">
                    <library-content-item :item="record" @item-click="() => handleGoFolder(record)" />
                  </template>
                </a-table-column>
                <a-table-column key="type" title="类型" data-index="preset" :width="120">
                  <template #cell="{ record }">
                    <span v-if="record.lft > 0"></span>
                    <span v-else>{{ getPresetTypeDisplay(record.presetType) }}</span>
                  </template>
                </a-table-column>
                <a-table-column
                  v-if="props.record?.scene !== 'investigation'"
                  key="score"
                  title="分数"
                  data-index="score"
                  :width="100"
                >
                  <template #cell="{ record }">
                    <span v-if="record.type === 'folder'"></span>
                    <span v-else>{{ record.score }}</span>
                  </template>
                </a-table-column>
                <a-table-column title="操作" :width="200">
                  <template #cell="{ record }">
                    <div class="actions-wrapper flex gap-2">
                      <slot :record="record" name="prepend-actions"></slot>
                      <div v-if="!isInject" class="actions">
                        <a-button
                          v-if="record.type === 'folder'"
                          type="text"
                          size="mini"
                          @click="() => handleEditFolderName(record)"
                          >修改</a-button
                        >
                        <a-button v-else type="text" size="mini" @click="() => handleGoEditQuestion(record)"
                          >修改</a-button
                        >
                        <a-button type="text" size="mini" @click="() => handleDelete(record)">删除</a-button>
                      </div>
                    </div>
                  </template>
                </a-table-column>
              </template>
            </a-table>
            <a-empty v-else description="该目录下暂无内容"></a-empty>
          </div>
        </a-card>
      </a-spin>
    </div>

    <new-folder-modal
      v-model="createNewFolderVisible"
      :create-new-catalog-method="onCreateNewCatalog"
      :default-edit-data="defaultEditData"
      :parent-folder="currentFolder"
      :breadcrumb="breadCrumbs"
      :catalog-type="catalogType"
      @refresh="() => handleRefresh(true)"
    />
    <ProcessQuestionModal
      v-if="processModalVisible"
      v-model="processModalVisible"
      :folder-tree="folderTree[0]?.children"
      :default-query-params="defaultQueryParams"
    />
  </div>
</template>

<style scoped lang="scss">
  .question-library-wrapper {
    :deep .arco-table-cell-inline-icon {
      margin-right: 5px;
    }
  }

  .actions {
    display: none;
  }
  .actions-wrapper {
    width: 100%;
    height: 24px;
  }
  .arco-table-tr {
    &:hover {
      .actions {
        display: flex;
      }
    }
  }
</style>
