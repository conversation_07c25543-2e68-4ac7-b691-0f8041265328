<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import Uploader from '@repo/ui/components/upload/uploader.vue';

  const props = defineProps({
    question: {
      type: Object,
      required: true,
    },
    sequence: {
      type: String,
      required: true,
    },
    modelValue: {
      type: String as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const result = computed({
    get: () => {
      return JSON.parse(props.modelValue || '[]');
    },
    set: (value) => {
      emit('update:modelValue', JSON.stringify(value || []));
    },
  });
</script>

<template>
  <uploader v-model="result" v-bind="$attrs" :sub-folder="`question-form/${question.id}`" />
</template>

<style scoped lang="scss"></style>
