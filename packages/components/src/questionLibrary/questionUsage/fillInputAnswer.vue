<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { SimpleOrRichEditor } from '@repo/rich-editor';

  const props = defineProps({
    question: {
      type: Object,
      required: true,
    },
    sequence: {
      type: String,
      required: true,
    },
    modelValue: {
      type: Array as PropType<any[]>,
      required: true,
    },
    mode: {
      type: String as PropType<'resultView' | 'default'>,
      default: 'edit',
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const result = ref<any>(props.modelValue || {});

  onMounted(() => {
    if (!Object.keys(result.value)?.length) {
      const res = {};
      props.question.options.forEach((o) => {
        res[o.sequence] = '';
      });
      result.value = res;
    }
  });

  watch(
    () => result.value,
    (value) => {
      emit('update:modelValue', value);
    },
  );
</script>

<template>
  <div v-for="(o, i) in question.options" :key="i">
    <div class="flex gap-1 my-2">
      <a-tooltip v-if="mode === 'resultView' && o.isCorrect" :content="o.correctAnswer || o.content">
        <div class="w-12">第{{ o.sequence }}空</div>
      </a-tooltip>
      <div v-else class="w-12">第{{ o.sequence }}空</div>
      <a-input v-model="result[o.sequence]" size="mini" :disabled="$attrs.disabled" />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
