<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { SimpleOrRichEditor } from '@repo/rich-editor';

  const props = defineProps({
    question: {
      type: Object,
      required: true,
    },
    sequence: {
      type: String,
      required: true,
    },
    modelValue: {
      type: String as PropType<any>,
      required: true,
    },
    mode: {
      type: String as PropType<'resultView' | 'default'>,
      default: 'edit',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const result = computed({
    get: () => {
      return props.modelValue;
    },
    set: (value) => {
      emit('update:modelValue', value);
    },
  });
</script>

<template>
  <simple-or-rich-editor v-model="result" />
  <div v-if="mode === 'resultView'" class="mt-2"> 正确答案： {{ question.correctAnswer }} </div>
</template>

<style scoped lang="scss"></style>
