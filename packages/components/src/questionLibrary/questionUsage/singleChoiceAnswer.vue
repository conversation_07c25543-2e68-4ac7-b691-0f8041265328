<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    question: {
      type: Object,
      required: true,
    },
    sequence: {
      type: String,
      required: true,
    },
    modelValue: {
      type: [Object, Array, Number, String] as PropType<any>,
      required: true,
    },
    mode: {
      type: String as PropType<'resultView' | 'default'>,
      default: 'edit',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const result = computed({
    get: () => {
      return props.modelValue;
    },
    set: (value) => {
      emit('update:modelValue', value);
    },
  });
</script>

<template>
  <a-radio-group v-model="result" size="mini" direction="vertical" v-bind="$attrs">
    <a-radio v-for="(o, i) in question.options" :key="i" :value="o.sequence">
      <div class="flex gap-1 flex-1" :class="{ 'text-green-400': mode === 'resultView' && o.isCorrect }">
        <strong>
          <IconCheckCircleFill v-if="mode === 'resultView' && o.isCorrect" />
          {{ o.sequence }}
        </strong>
        <div v-html="o.content" />
      </div>
    </a-radio>
  </a-radio-group>
</template>

<style scoped lang="scss"></style>
