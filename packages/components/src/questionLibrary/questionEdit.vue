<script setup lang="ts">
  import { computed, nextTick, onMounted, PropType, ref } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { saveQuestion, getQuestion } from '@repo/infrastructure/openapi/questionLibraryController';
  import presetTypes, { VerifyStatus } from './presets';
  import useQuestionLibraryStore from './store';
  import Breadcrumb from './components/breadcrumb.vue';

  const props = defineProps({
    editId: {
      type: Number,
      required: false,
    },
    defaultEditData: {
      type: Object,
      default: () => ({}),
    },
    handleSaveQuestion: {
      type: Function as PropType<(data: any) => Promise<any>>,
      default: saveQuestion,
    },
    handleGetQuestion: {
      type: Function as PropType<(data: any) => Promise<any>>,
      default: getQuestion,
    },
    manageType: {
      type: String,
      default: 'examination',
    },
  });

  const answerRequired = computed(() => {
    return props.manageType === 'examination';
  });
  const editData = ref<Record<string, any>>({
    score: 1,
    ...props.defaultEditData,
  });

  const customInputTypes = ref<Record<string, any>[]>([]);
  const dirty = ref(false);
  const libraryStore = useQuestionLibraryStore();
  const { loading, setLoading } = useLoading();
  const questionValid = ref<boolean | string>(false);

  const activeQuestionType = ref('');
  const activeType = ref<any>(null);
  const currentComponent = ref<any>(null);

  const emit = defineEmits(['goBack']);

  const answerRequiredByTestType = computed(() => {
    if (answerRequired.value) {
      return questionValid.value !== true;
    }
    return !['attachmentInput', 'textInput', 'fill', 'judgement', 'multipleChoice', 'singleChoice'].includes(
      editData.value.presetType,
    );
  });

  const handleGoBack = () => {
    emit('goBack');
  };

  const handleTypeChange = (type: any) => {
    const typeChange = () => {
      const pureQuestion = editData.value.question?.trim().replace(/<[^>]+>/g, '');
      editData.value.presetType = type.presetType;
      if (type.id) {
        editData.value.typeId = type.id;
      }
      if (!pureQuestion) {
        editData.value.question = '';
      }
      if (type.presetType === 'fill') {
        editData.value.options = [];
      } else if (type.presetType === 'judgement') {
        editData.value.options = [
          { content: '正确', isCorrect: true },
          { content: '错误', isCorrect: false },
        ];
      }
      dirty.value = false;

      activeQuestionType.value = type.presetType;
      activeType.value = type;
      currentComponent.value = type.component;
    };

    // 选择类题型 不更新
    if (type.presetType?.includes('Choice') && editData.value.presetType?.includes('Choice')) {
      typeChange();
    } else if (dirty.value) {
      Modal.confirm({
        title: '切换题型',
        content: '切换题型会清除除问题外的所有已填写内容，是否继续？',
        onOk: () => {
          typeChange();
        },
      });
    } else {
      typeChange();
    }
  };

  const handleStain = () => {
    dirty.value = true;
  };

  const dataInit = async () => {
    if (props.editId) {
      const { data } = await props.handleGetQuestion({
        id: props.editId,
      });
      editData.value = {
        ...data,
      };

      activeQuestionType.value = data.typeId || data.presetType;
      activeType.value = customInputTypes.value.find((item) => item.id === data.typeId);
      currentComponent.value = presetTypes.find((item) => item.presetType === data.presetType)?.component;
    } else {
      editData.value = {
        ...props.defaultEditData,
      };
      activeQuestionType.value = 'singleChoice';
      activeType.value = customInputTypes.value.find((item) => item.id === editData.value.typeId);
      currentComponent.value = presetTypes.find((item) => item.presetType === editData.value.presetType)?.component;

      dirty.value = false;
      await nextTick(() => {
        handleTypeChange(presetTypes[0]);
      });
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    const formData = { ...editData.value };
    if (formData.options?.length) {
      formData.options = formData.options.map((o: any, index: number) => {
        o.sequence = o.sequence.toString();
        return o;
      });
    }
    const data: any = {
      catalogId: libraryStore.currentCatalogId,
      ...formData,
      isVerify: 1,
      questionRepositoryId: null,
    };
    data.typeId = data.typeId || undefined;
    try {
      await props.handleSaveQuestion(data);

      Message.success('保存成功');
      await dataInit();
    } finally {
      setLoading(false);
    }
  };

  onMounted(async () => {
    await dataInit();
  });

  defineExpose({
    editData,
    handleSubmit,
  });
</script>

<script lang="ts">
  export default {
    name: 'QuestionEdit',
  };
</script>

<template>
  <a-spin class="w-full" :loading="loading">
    <div class="question-edit-wrapper mx-auto flex flex-col gap-2">
      <a-card>
        <template #title>
          <div class="flex gap-2 justify-between">
            <breadcrumb :bread-crumbs="libraryStore.breadcrumbs" :process-btn-visible="false" />
            <slot name="extra">
              <a-space>
                <a-button
                  :disabled="answerRequiredByTestType && questionValid !== true"
                  type="primary"
                  :loading="loading"
                  size="mini"
                  @click="handleSubmit"
                >
                  <template #icon>
                    <IconCheck />
                  </template>
                  保存并继续出题
                </a-button>
                <a-button size="mini" @click="handleGoBack">
                  <template #icon>
                    <IconArrowLeft />
                  </template>
                  返回题库
                </a-button>
              </a-space>
            </slot>
          </div>
        </template>
        <div class="flex gap-4 flex-col">
          <div class="flex gap-4 items-center justify-between">
            <div class="question-types flex gap-4">
              <a-button
                v-for="qt in presetTypes"
                :key="qt.presetType"
                shape="round"
                size="mini"
                :disabled="editData.id"
                :type="activeQuestionType === qt.presetType ? 'outline' : 'secondary'"
                @click="() => handleTypeChange(qt)"
              >
                {{ qt.name }}
              </a-button>
            </div>
            <div class="actions flex gap-2">
              <a-button size="mini">
                <template #icon> <IconPlus /> </template>
                自定义题型
              </a-button>
            </div>
          </div>

          <div v-if="customInputTypes?.length" class="question-types flex gap-4">
            <a-button
              v-for="qt in customInputTypes"
              :key="qt.id"
              status="success"
              shape="round"
              size="medium"
              :disabled="editData.id"
              :type="activeQuestionType === qt.id ? 'outline' : 'secondary'"
              @click="() => handleTypeChange(qt)"
            >
              {{ qt.name }}
            </a-button>
          </div>
        </div>
      </a-card>

      <a-card :title="activeType?.name">
        <template #title>
          <div class="flex justify-between items-center gap-4">
            <div> {{ activeType?.name }}班级 </div>
            <a-space>
              <div v-if="dirty && questionValid !== true" class="text-sm text-right text-red-400">
                <IconExclamationPolygonFill />
                {{ questionValid }}
              </div>
              <a-space v-if="answerRequired" class="text-sm">
                得分
                <a-input-number
                  v-model="editData.score"
                  size="mini"
                  class="w-32"
                  :min="0.5"
                  mode="button"
                  :step="0.5"
                />
              </a-space>
            </a-space>
            <!--<a-button type="secondary" @click="dataInit">-->
            <!--  <template #icon>-->
            <!--    <IconRefresh />-->
            <!--  </template>-->
            <!--  重置-->
            <!--</a-button>-->
          </div>
        </template>
        <component
          :is="currentComponent"
          v-model="editData"
          v-model:question-valid="questionValid"
          :answer-required="answerRequired"
          @stain="handleStain"
        />
      </a-card>
    </div>
  </a-spin>
</template>

<style scoped lang="scss">
  .question-edit-wrapper {
    width: 700px;
  }
</style>
