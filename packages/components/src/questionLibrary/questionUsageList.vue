<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { getTeacherQuestionList } from '@repo/infrastructure/openapi/teacherQuestionLibraryController';
  import { Message } from '@arco-design/web-vue';
  import QuestionUsage from './questionUsage.vue';

  const props = defineProps({
    courseId: {
      type: Number,
      required: true,
    },
    questionIds: {
      type: Array as PropType<number[]>,
      required: true,
    },
    result: {
      type: Object as PropType<Record<number, any>>,
      required: true,
    },
    criteria: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const emit = defineEmits(['update:result']);
  const questions = ref<Record<string, any>[]>([]);

  const testResult = computed({
    get: () => props.result || {},
    set: (value) => {
      emit('update:result', value);
    },
  });

  const loadQuestions = async () => {
    if (!props.questionIds?.length) {
      Message.error('题目不能为空');
    }
    const { data } = await getTeacherQuestionList({
      ids: props.questionIds?.join(','),
      courseId: props.courseId,
    });

    questions.value = data;
  };

  const handleAnswerUpdate = (id: number, value: any) => {
    testResult.value = {
      ...testResult.value,
      [id]: value,
    };
  };

  onMounted(async () => {
    await loadQuestions();
  });
</script>

<template>
  <div v-for="(q, i) in questions" :key="i">
    <question-usage
      :answer="testResult[q.id]"
      :sequence="`第${i + 1}题`"
      :question="q"
      :criteria="criteria"
      @update:answer="(val) => handleAnswerUpdate(q.id, val)"
    />
  </div>
</template>

<style scoped lang="scss"></style>
