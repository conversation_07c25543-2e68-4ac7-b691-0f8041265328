import { defineStore } from 'pinia';

const useQuestionLibraryStore = defineStore({
  id: 'questionLibrary',
  state: (): {
    breadcrumbs: any[];
    currentCatalogId: number;
  } => ({
    breadcrumbs: [],
    currentCatalogId: 0,
  }),
  actions: {
    setBreadcrumbs(breadcrumbs: any[]) {
      this.breadcrumbs = breadcrumbs;
    },
    setCatalogId(currentCatalogId: number) {
      this.currentCatalogId = currentCatalogId;
    },
  },
});

export default useQuestionLibraryStore;
