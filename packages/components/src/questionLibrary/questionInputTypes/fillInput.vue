<script setup lang="ts">
  import { SimpleOrRichEditor } from '@repo/rich-editor';
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import { debounce } from 'lodash';
  import InputOptions from '../components/inputOptions.vue';

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    answerRequired: {
      type: Boolean,
    },
  });

  const answerRequired = computed(() => {
    return props.answerRequired;
  });

  const emit = defineEmits(['update:modelValue', 'update:questionValid', 'stain']);
  const defaultOptions = [{ content: '' }, { content: '' }];

  const handleStain = () => {
    emit('stain');
  };

  const formData = ref({
    question: '',
    score: 1,
    tips: '',
    analysisTips: '',
    options: defaultOptions,

    ...props.modelValue,
  });

  const dirty = ref(false);

  const validate = () => {
    const pureQuestion = formData.value.question?.trim().replace(/<[^>]+>/g, '');
    if (!pureQuestion) {
      emit('update:questionValid', '请输入问题内容');
      return false;
    }
    if (formData.value.options.length < 1) {
      emit('update:questionValid', '至少需要一个填空');
      return false;
    }
    if (formData.value.options.some((item) => !item.content?.trim())) {
      emit('update:questionValid', '请确认已填写所有填空答案');
      return false;
    }
    emit('update:questionValid', true);
    return true;
  };

  const identifyFillLength = debounce(() => {
    const { question } = formData.value;
    const fillLength = question.match(/_{3,}/g)?.length || 0;
    if (fillLength !== formData.value.options.length) {
      formData.value.options = Array.from({ length: fillLength }, (_, index) => ({
        content: formData.value.options[index]?.content || '',
        sequence: index + 1,
      }));
      nextTick(() => {
        emit('stain');
      });
    }
  }, 300);

  onMounted(() => {
    watch(
      () => formData.value,
      (value, oldValue?: any) => {
        if (!dirty.value) {
          dirty.value = true;
        } else {
          emit('stain');
        }

        validate();
        identifyFillLength();
        emit('update:modelValue', value);
      },
      { deep: true, immediate: true },
    );
  });
</script>

<script lang="ts">
  export default {
    name: 'FillInput',
  };
</script>

<template>
  <a-form layout="vertical" :model="formData">
    <a-form-item>
      <template #label>
        <IconQuestionCircleFill />
        填空问题
        <small>
          <IconTag />
          使用多个英文下划线(超过2个)表示填空
        </small>
      </template>
      <simple-or-rich-editor v-model="formData.question" type="textarea" class="flex-1" />
    </a-form-item>
    <a-form-item v-if="answerRequired" label="选项">
      <template #label>
        <IconPenFill />
        填空内容答案
      </template>
      <input-options
        v-model="formData.options"
        :exists-correct="false"
        index-type="number"
        :options-mutated="true"
        @stain="handleStain"
      />
    </a-form-item>
  </a-form>
</template>

<style scoped lang="scss"></style>
