<script setup lang="ts">
  import { SimpleOrRichEditor } from '@repo/rich-editor';
  import { computed, onMounted, ref, watch } from 'vue';
  import InputOptions from '../components/inputOptions.vue';

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    answerRequired: {
      type: Boolean,
    },
  });

  const emit = defineEmits(['update:modelValue', 'update:questionValid', 'stain']);
  const defaultOptions = [
    { content: '', isCorrect: true, sequence: 'A' },
    { content: '', isCorrect: false, sequence: 'B' },
  ];

  const handleStain = () => {
    emit('stain');
  };

  const formData = ref({
    question: '',
    score: 1,
    tips: '',
    analysisTips: '',
    options: defaultOptions,

    ...props.modelValue,
  });

  const dirty = ref(false);

  const validate = () => {
    const pureQuestion = formData.value.question?.trim().replace(/<[^>]+>/g, '');
    if (!pureQuestion) {
      emit('update:questionValid', '请输入问题内容');
      return false;
    }
    if (formData.value.options.some((item) => !item.content?.trim())) {
      emit('update:questionValid', '请确认已填写所有选项内容');
      return false;
    }
    if (formData.value.options.filter((item) => item.isCorrect).length !== 1) {
      emit('update:questionValid', '必须有且只有一个正确答案');
      return false;
    }
    if (formData.value.options.length < 2) {
      emit('update:questionValid', '至少需要两个选项');
      return false;
    }
    emit('update:questionValid', true);
    return true;
  };

  onMounted(() => {
    watch(
      () => formData.value,
      (value) => {
        if (!dirty.value) {
          dirty.value = true;
        } else {
          emit('stain');
        }
        emit('update:modelValue', value);
        validate();
      },
      { deep: true, immediate: true },
    );
  });
</script>

<script lang="ts">
  export default {
    name: 'SingleChoiceInput',
  };
</script>

<template>
  <a-form layout="vertical" :model="formData">
    <a-form-item label="单选问题">
      <template #label>
        <IconQuestionCircleFill />
        单选问题
      </template>
      <simple-or-rich-editor v-model="formData.question" type="textarea" class="flex-1" />
    </a-form-item>
    <a-form-item label="选项">
      <template #label>
        <IconPenFill />
        单选选项
        <small> (点击序号切换正确答案) </small>
      </template>
      <input-options v-model="formData.options" @stain="handleStain" :answerRequired="answerRequired"/>
    </a-form-item>
  </a-form>
</template>

<style scoped lang="scss"></style>
