<script setup lang="ts">
  import { SimpleOrRichEditor } from '@repo/rich-editor';
  import { computed, onMounted, ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    answerRequired: {
      type: Boolean,
    },
  });

  const answerRequired = computed(() => {
    return props.answerRequired;
  });
  const emit = defineEmits(['update:modelValue', 'update:questionValid', 'stain']);

  const handleStain = () => {
    emit('stain');
  };

  const formData = ref({
    question: '',
    correctAnswer: '',

    ...props.modelValue,
  });

  const dirty = ref(false);
  const validate = () => {
    const pureQuestion = formData.value.question?.trim().replace(/<[^>]+>/g, '');
    if (!pureQuestion) {
      emit('update:questionValid', '请输入问题内容');
      return false;
    }
    if (!formData.value.correctAnswer?.trim()) {
      emit('update:questionValid', '请输入正确答案');
      return false;
    }
    emit('update:questionValid', true);
    return true;
  };

  onMounted(() => {
    watch(
      () => formData.value,
      (value) => {
        if (!dirty.value) {
          dirty.value = true;
        } else {
          handleStain();
        }
        validate();
        emit('update:modelValue', value);
      },
      { deep: true, immediate: true },
    );
  });
</script>

<script lang="ts">
  export default {
    name: 'ShortAnswerInput',
  };
</script>

<template>
  <a-form layout="vertical" :model="formData">
    <a-form-item>
      <template #label>
        <IconQuestionCircleFill />
        文本题问题
      </template>
      <simple-or-rich-editor v-model="formData.question" type="textarea" default-mode="rich" class="flex-1" />
    </a-form-item>
    <a-form-item v-if="answerRequired">
      <template #label>
        <IconPenFill />
        正确答案
      </template>
      <simple-or-rich-editor v-model="formData.correctAnswer" type="textarea" class="flex-1" />
    </a-form-item>
  </a-form>
</template>

<style scoped lang="scss"></style>
