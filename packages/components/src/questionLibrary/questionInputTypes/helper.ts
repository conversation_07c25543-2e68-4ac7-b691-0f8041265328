import presetTypes from '../presets';

const englishChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const chineseChars = '一二三四五六七八九十';
const cnNumberClassic = '零壹贰叁肆伍陆柒捌玖拾';

export type OptionIndexType = 'number' | 'alphabet' | 'cnNumber' | 'cnNumberClassic';

const getOptionIndex = (index: number, type?: OptionIndexType): string => {
  switch (type) {
    case 'cnNumber':
      return chineseChars[index];
    case 'cnNumberClassic':
      return cnNumberClassic[index];
    case 'number':
      return `${index + 1}`;
    case 'alphabet':
    default:
      return englishChars[index];
  }
};

const getOptionTypeDisplay = (raw: any): string => {
  if (raw.typeId && raw.questionType) {
    return raw.questionType.name;
  }

  return presetTypes.find((item) => item.presetType === raw.presetType)?.name || '';
};

export { getOptionIndex, getOptionTypeDisplay };
