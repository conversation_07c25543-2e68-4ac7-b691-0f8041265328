<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { getPresetTypeDisplay, VerifyStatus } from '../presets';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    folderTree: {
      type: Object,
    },
    defaultQueryParams: {
      type: Object,
    },
  });
  const emit = defineEmits(['update:modelValue', 'refresh']);
  const treeProps = ref({
    key: 'id',
    title: 'name',
    children: 'children',
  });
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });

  const selectedKeys = ref([]);

  const rowSelection = ref({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  const folderModalVisible = ref(false);
  const handlePass = () => {
    folderModalVisible.value = true;
  };
  const dataList = ref([]);

  const currentFolder = ref<any>(null);
  const handleSetFolder = (node: any) => {
    currentFolder.value = node;
  };

  const loadData = async () => {
    try {
      const { data: res } = await request('/questionLibrary/unverified/questionList', {
        params: {
          isVerify: false,
          ...props.defaultQueryParams,
        },
      });
      dataList.value = res;
    } finally {
      /**/
    }
  };
  const handleSubmit = async () => {
    if (!currentFolder.value?.id) {
      Message.warning('请选择题库目录');
      return;
    }
    const newDataList = dataList.value.map((item: any) => ({
      ...item,
      catalogId: currentFolder.value?.id || 0,
      isVerify: VerifyStatus.Approved,
    }));
    try {
      await request('/questionLibrary/save/questions', {
        method: 'post',
        data: newDataList,
      });
      await loadData();
      Message.success('保存成功');
    } finally {
      /**/
    }
  };
  const handleBatchDelete = async () => {
    try {
      await request('/questionLibrary/batch', {
        method: 'put',
        params: {
          ids: selectedKeys.value.join(','),
        },
      });
      await loadData();
      Message.success('驳回成功');
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    await loadData();
  });
</script>

<template>
  <a-modal v-model:visible="visible" fullscreen title="教师端题目入库审核">
    <div class="w-full flex justify-start space-x-2 mb-2 pr-8">
      <a-button type="outline" size="mini" @click="handlePass">
        <icon-check />
        通过
      </a-button>
      <a-popconfirm content="确认驳回?" type="warning" @ok="handleBatchDelete">
        <a-button type="outline" status="danger" size="mini">
          <icon-close />
          驳回
        </a-button>
      </a-popconfirm>
    </div>
    <a-table
      v-model:selected-keys="selectedKeys"
      :data="dataList"
      :row-selection="rowSelection"
      row-key="id"
      :bordered="{ cell: true }"
    >
      <template #columns>
        <a-table-column title="序号" width="80" align="center">
          <template #cell="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="名称" data-index="question">
          <template #cell="{ record }">
            {{ record.question }}
          </template>
        </a-table-column>
        <a-table-column title="类型" data-index="presetType">
          <template #cell="{ record }"> {{ getPresetTypeDisplay(record.presetType) }}</template>
        </a-table-column>
        <a-table-column title="分数" data-index="score">
          <template #cell="{ record }"> {{ record?.score }}</template>
        </a-table-column>
        <a-table-column title="操作" align="center">
          <template #cell>
            <a-button type="text">查看</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
  <a-modal v-model:visible="folderModalVisible" title="请选择题库目录" :on-before-ok="handleSubmit">
    <a-tree
      size="small"
      :data="folderTree"
      :checkable="false"
      :field-names="treeProps"
      auto-expand-parent
      selectable
      :default-expand-selected="true"
      @select="(keys: any, e: any) => handleSetFolder(e.node)"
    >
      <template #icon>
        <IconFolder />
      </template>
    </a-tree>
  </a-modal>
</template>

<style scoped lang="scss"></style>
