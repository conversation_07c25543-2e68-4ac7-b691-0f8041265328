<script setup lang="ts">
  import { PropType } from 'vue';
  import { getClientRole } from '@repo/env-config';

  defineProps({
    breadCrumbs: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    processBtnVisible: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['nodeClick', 'handleProcess']);

  const handleGoFolder = (item: any) => {
    emit('nodeClick', item);
  };
  const handleProcess = () => {
    emit('handleProcess', true);
  };
  const clientRole = getClientRole();
</script>

<script lang="ts">
  export default {
    name: 'Breadcrumb',
  };
</script>

<template>
  <div class="flex justify-between">
    <a-breadcrumb v-bind="$attrs">
      <a-breadcrumb-item v-for="(item, index) in breadCrumbs" :key="index" class="cursor-pointer">
        <span @click="() => handleGoFolder(item)">{{ item.name }}</span>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <a-button v-if="clientRole === 'Manager' && processBtnVisible" type="outline" size="mini" @click="handleProcess">
      题目审核
    </a-button>
  </div>
</template>

<style scoped lang="scss"></style>
