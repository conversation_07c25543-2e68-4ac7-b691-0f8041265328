<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { createNewCatalog } from '@repo/infrastructure/openapi/questionLibraryController';
  import { Message } from '@arco-design/web-vue';
  import Breadcrumb from './breadcrumb.vue';

  const props = defineProps({
    parentFolder: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    breadcrumb: {
      type: Array,
      default: () => [],
    },
    defaultEditData: {
      type: Object,
      default: () => ({}),
    },
    createNewCatalogMethod: {
      type: Function,
      default: createNewCatalog,
    },
    catalogType: {
      type: Number,
      required: true,
    },
  });

  const { loading, setLoading } = useLoading();
  const emit = defineEmits(['update:modelValue', 'refresh']);

  const formData = ref({
    name: '',
    ...props.defaultEditData,
  });

  const visible = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });

  const handleSubmit = async () => {
    setLoading(true);
    if (!formData.value.name) {
      Message.error('请输入文件夹名称');
      setLoading(false);
      return;
    }
    try {
      await props.createNewCatalogMethod({
        ...formData.value,
        parentId: props.parentFolder.id,
        catalogType: props.catalogType,
      });

      Message.success('创建新文件夹成功');

      visible.value = false;
      emit('refresh');
    } finally {
      setLoading(false);
    }
  };
</script>

<template>
  <a-modal v-model:visible="visible" title="新建题库文件夹">
    <a-alert>
      <div class="gap-2 flex items-center">
        <div>当前目录</div>
        <breadcrumb :bread-crumbs="breadcrumb" />
      </div>
    </a-alert>
    <a-form :model="formData" class="mt-4">
      <a-form-item label="文件夹名称">
        <a-input v-model.trim="formData.name" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="() => (visible = false)">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<style scoped></style>
