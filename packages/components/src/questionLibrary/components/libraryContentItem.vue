<script setup lang="ts">
  import { PropType } from 'vue';
  import FileTypeIcon from '@repo/ui/components/icon/fileTypeIcon.vue';
  import { stripTagsAndCut } from '@repo/infrastructure/utils';

  const props = defineProps({
    item: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['itemClick']);

  const handleClick = () => {
    emit('itemClick', props.item);
  };
</script>

<template>
  <div class="flex gap-2 items-center cursor-pointer library-content-item hover:text-blue-600" @click="handleClick">
    <div class="w-8">
      <file-type-icon v-if="item.lft > 0" class="icon" ext="Folder" />
      <IconFile v-else :size="21" />
    </div>
    <div class="name text-nowrap overflow-hidden whitespace-nowrap text-ellipsis truncate max-w-80">{{
      stripTagsAndCut(item.name || item.question, 20)
    }}</div>
  </div>
</template>

<style scoped lang="scss">
  .library-content-item {
    :deep {
      .svg-icon {
        width: 1.6em;
        height: 1.6em;
      }
    }
  }
</style>
