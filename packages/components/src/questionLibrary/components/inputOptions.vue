<script setup lang="ts">
  import { computed, PropType, ref, watch } from 'vue';
  import { SimpleOrRichEditor } from '@repo/rich-editor';
  import { getOptionIndex } from '../questionInputTypes/helper';
  import type { OptionIndexType } from '../questionInputTypes/helper';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
    indexType: {
      type: String as PropType<OptionIndexType>,
      default: 'alphabet',
    },
    multipleCorrect: {
      type: Boolean,
      default: false,
    },
    optionsMutated: {
      type: Boolean,
      default: false,
    },
    existsCorrect: {
      type: Boolean,
      default: true,
    },
    optionEditable: {
      type: Boolean,
      default: true,
    },
    answerRequired: {
      type: Boolean,
    },
  });

  const emit = defineEmits(['update:modelValue', 'stain']);

  const options = computed({
    get: () => props.modelValue || [],
    set: (val) => emit('update:modelValue', val),
  });
  const handleSwitchCorrect = async (idx: number) => {
    if (!props.existsCorrect) {
      return;
    }
    if (!props.multipleCorrect) {
      options.value.forEach((opt, i) => {
        opt.isCorrect = i === idx;
      });
    } else {
      options.value[idx].isCorrect = !options.value[idx].isCorrect;
    }
  };

  const handleRemoveOption = async (idx: number) => {
    options.value.splice(idx, 1);
    options.value = options.value.map((opt, i) => ({ ...opt, sequence: getOptionIndex(i, props.indexType) }));
  };

  const handleAddOption = async () => {
    const opt = {
      content: '',
      isCorrect: false,
      sequence: getOptionIndex(options.value.length),
    };
    options.value.push(opt);
  };

  const handleMoveUp = async (idx: number) => {
    if (idx === 0) return;
    [options.value[idx], options.value[idx - 1]] = [options.value[idx - 1], options.value[idx]];
  };

  const handleMoveDown = async (idx: number) => {
    if (idx === options.value.length - 1) return;
    [options.value[idx], options.value[idx + 1]] = [options.value[idx + 1], options.value[idx]];
  };

  const dirty = ref(false);

  watch(
    () => options,
    () => {
      if (!dirty.value) {
        dirty.value = true;
      } else {
        emit('stain');
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div class="flex-1 flex flex-col gap-4">
    <div v-for="(opt, idx) in options" :key="idx" class="flex gap-4 items-center hover:bg-gray-50 p-2">
      <div
        class="w-8 h-8 rounded-full text-center leading-8 cursor-pointer"
        :class="{ 'bg-green-500 text-white': opt.isCorrect, 'bg-gray-200': !opt.isCorrect }"
        @click="() => handleSwitchCorrect(idx)"
      >
        {{ getOptionIndex(idx, indexType) }}
      </div>
      <div class="flex-1">
        <div v-if="!optionEditable">
          {{ opt.content }}
        </div>
        <simple-or-rich-editor v-else v-model.trim="opt.content" simple-type="text" />
      </div>
      <a-button-group v-if="!optionsMutated">
        <a-button :disabled="idx === 0" @click="() => handleMoveUp(idx)">
          <template #icon>
            <IconArrowUp />
          </template>
        </a-button>
        <a-button :disabled="idx + 1 >= options.length" @click="() => handleMoveDown(idx)">
          <template #icon>
            <IconArrowDown />
          </template>
        </a-button>
        <a-button @click="() => handleRemoveOption(idx)">
          <template #icon>
            <IconDelete />
          </template>
        </a-button>
      </a-button-group>
    </div>
    <div v-if="!optionsMutated" class="px-2">
      <a-button type="outline" size="mini" @click="handleAddOption">
        <template #icon>
          <IconPlus />
        </template>
        添加选项
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
