<script setup lang="ts">
  import { isArray, isPlainObject } from 'lodash';
  import { computed, PropType } from 'vue';

  const props = defineProps({
    answer: {
      type: [Object, Array, Number, String] as PropType<any>,
      required: true,
    },
  });

  const answerContent = computed(() => {
    if (isArray(props.answer)) {
      return props.answer.join('、');
    }
    if (isPlainObject(props.answer)) {
      return Object.values(props.answer).join('、');
    }
    return props.answer;
  });
</script>

<template>
  <span v-if="answerContent?.length > 3">
    <a-popover :content="answerContent">
      <span class="text-blue-500 cursor-pointer"> 查看 </span>
    </a-popover>
  </span>
  <span v-else>
    {{ answerContent }}
  </span>
</template>

<style scoped lang="scss"></style>
