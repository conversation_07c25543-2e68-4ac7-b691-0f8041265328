import SingleChoiceInput from './questionInputTypes/singleChoiceInput.vue';
import MultipleChoiceInput from './questionInputTypes/multipleChoiceInput.vue';
import ShortAnswerInput from './questionInputTypes/shortAnswerInput.vue';
import JudgementInput from './questionInputTypes/judgementInput.vue';
import FillInput from './questionInputTypes/fillInput.vue';
import SingleChoiceAnswer from './questionUsage/singleChoiceAnswer.vue';
import MultipleChoiceAnswer from './questionUsage/multipleChoiceAnswer.vue';
import JudgementChoiceAnswer from './questionUsage/judgementChoiceAnswer.vue';
import ShortAnswerAnswer from './questionUsage/shortAnswerAnswer.vue';
import FillInputAnswer from './questionUsage/fillInputAnswer.vue';
import AttachmentInput from './questionInputTypes/attachmentInput.vue';
import AttachmentAnswer from './questionUsage/attachmentAnswer.vue';

export type PresetType = 'singleChoice' | 'multipleChoice' | 'judgement' | 'fill' | 'textInput' | 'attachmentInput';
export type PresetTypeDefine = {
  presetType: PresetType;
  name: string;
  component: any;
  answerCmp: any;
};

const presetTypes: PresetTypeDefine[] = [
  {
    presetType: 'singleChoice',
    name: '单选题',
    component: SingleChoiceInput,
    answerCmp: SingleChoiceAnswer,
  },
  {
    presetType: 'multipleChoice',
    name: '多选题',
    component: MultipleChoiceInput,
    answerCmp: MultipleChoiceAnswer,
  },
  {
    presetType: 'judgement',
    name: '判断题',
    component: JudgementInput,
    answerCmp: JudgementChoiceAnswer,
  },
  {
    presetType: 'fill',
    name: '填空题',
    component: FillInput,
    answerCmp: FillInputAnswer,
  },
  {
    presetType: 'textInput',
    name: '文本题',
    component: ShortAnswerInput,
    answerCmp: ShortAnswerAnswer,
  },
  {
    presetType: 'attachmentInput',
    name: '附件题',
    component: AttachmentInput,
    answerCmp: AttachmentAnswer,
  },
];

const presetTypesMap = [];
presetTypes.forEach((item) => {
  presetTypesMap[item.presetType] = item;
});

const getPresetTypeDisplay = (presetType: string) => {
  const preset = presetTypes.find((item) => item.presetType === presetType);
  return preset ? preset.name : '';
};

type QuestionItem = {
  presetType: PresetType;
};

const answerAvailable = (idx: number, question: QuestionItem, answer: any) => {
  switch (question.presetType) {
    case 'singleChoice':
    case 'multipleChoice':
    case 'judgement':
      return answer !== undefined;
    case 'fill':
      return Object.values(answer).every((item) => item?.trim());
    case 'textInput':
      return !!answer?.trim();
    case 'attachmentInput':
      // eslint-disable-next-line no-case-declarations
      const res = JSON.parse(answer || '[]');
      return res.length > 0;
    default:
      return false;
  }
};
// eslint-disable-next-line no-shadow
enum VerifyStatus {
  Pending = 0, // 待审核
  Approved = 1, // 已通过
  Rejected = 2, // 已驳回
}

export { getPresetTypeDisplay, presetTypesMap, answerAvailable, VerifyStatus };

export default presetTypes;
