<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import StudentArchive from '../student/studentArchive.vue';

  const props = defineProps({
    raw: Array,
  });

  const lists = computed(() => {
    return props.raw;
  });
  const currentStudent = ref(null);
  const archiveVisible = ref(false);
  const handleView = async (record: any) => {
    await request(`/resourceRoom/student/${record.studentId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      currentStudent.value = res.data;
      archiveVisible.value = true;
    });
  };
</script>

<template>
  <a-table :data="lists" size="mini" :pagination="false">
    <template #columns>
      <a-table-column title="电子档案" data-index="symbol">
        <template #cell="{ record }">
          <a-button @click="handleView(record)">电子档案</a-button>
        </template>
      </a-table-column>
      <a-table-column title="姓名" data-index="studentName" />
      <a-table-column title="性别" data-index="gender" />
    </template>
  </a-table>
  <a-modal fullscreen :visible="archiveVisible" :render-to-body="false">
    <template #footer></template>
    <student-archive
      v-if="currentStudent && archiveVisible"
      v-model:visible="archiveVisible"
      :base-info="currentStudent"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
