<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const items = computed(() => {
    return props.raw;
  });
</script>

<template>
  <div class="flex flex-col gap-1">
    <a-timeline>
      <a-timeline-item v-for="(r, index) in items" :key="index" :label="r.date">
        [{{ r.operatorName }}] {{ r.operation }}
        <div v-if="!['-', ''].includes(r.remark)" class="rounded bg-gray-100 p-2">{{ r.remark }}</div>
      </a-timeline-item>
    </a-timeline>
  </div>
</template>

<style scoped lang="scss"></style>
