<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const reasons = computed(() => {
    return props.raw
      ?.filter((item) => item.checked)
      ?.map((item) => {
        return item.content;
      });
  });
</script>

<template>
  <div class="flex flex-col gap-1">
    <div v-for="(r, index) in reasons" :key="index"> {{ index + 1 }}、 {{ r }} </div>
    <div v-if="!reasons">无</div>
  </div>
</template>

<style scoped lang="scss"></style>
