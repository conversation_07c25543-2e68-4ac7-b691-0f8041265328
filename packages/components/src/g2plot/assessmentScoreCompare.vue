<script setup lang="ts">
  import { Line } from '@antv/g2plot';
  import { onMounted, ref, watch } from 'vue';

  const props = defineProps({
    data: {
      type: Array,
      required: true,
    },
  });

  const options = {
    xField: 'criterion',
    yField: 'score',
    seriesField: 'timesLabel',
    // X 轴相关配置
    xAxis: {
      nice: true,
      // tickCount: 8,
      // 文本标签
      label: false,
      // 坐标轴线的配置项 null 表示不展示
      line: {
        style: {
          stroke: '#aaa',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#ddd',
            lineDash: [4, 2],
          },
        },
        alternateColor: 'rgba(0,0,0,0.05)',
      },
    },
    // Y 轴相关配置
    yAxis: {
      // max: 3000,
      // 文本标签
      label: {
        autoRotate: false,
        style: {
          fill: '#aaa',
          fontSize: 12,
        },
        // 数值格式化为千分位
        formatter: (v) => `${v} 分`,
      },
      // 坐标轴线的配置项 null 表示不展示
      line: {
        style: {
          stroke: '#aaa',
        },
      },
      tickLine: {
        style: {
          lineWidth: 2,
          stroke: '#aaa',
        },
        length: 5,
      },
      grid: {
        line: {
          style: {
            stroke: '#ddd',
            lineDash: [4, 2],
          },
        },
        alternateColor: 'rgba(0,0,0,0.05)',
      },
    },
    // label
    label: {
      layout: [{ type: 'hide-overlap' }], // 隐藏重叠label
      style: {
        textAlign: 'right',
      },
      formatter: (item) => item.value,
    },
    // point
    point: {
      size: 5,
      style: {
        lineWidth: 1,
        fillOpacity: 1,
      },
      shape: (item) => {
        if (item.category === 'Cement production') {
          return 'circle';
        }
        return 'diamond';
      },
    },
    legend: {
      position: 'top-right',
      itemName: {
        style: {
          fill: '#000',
        },
        formatter: (name) => name,
      },
    },
    // 度量相关配置
    meta: {
      // year 对应 xField || yField
      criterion: {
        range: [0, 1],
      },
    },
  };

  const line = ref<any>(null);

  watch(
    () => props.data,
    (value) => {
      line.value.update({
        data: value,
      });
    },
  );

  onMounted(() => {
    line.value = new Line('assessment-score-compare', {
      data: props.data,
      ...options,
    });
    line.value.render();
  });
</script>

<template>
  <div id="assessment-score-compare" class="h-60" />
</template>

<style scoped lang="scss"></style>
