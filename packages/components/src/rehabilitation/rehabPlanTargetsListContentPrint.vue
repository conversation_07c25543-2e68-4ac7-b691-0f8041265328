<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import IepLongTermTargetsModal from './iepLongTermTargetsModal.vue';
  import IepShortTermTargetsModal from './iepShortTermTargetsModal.vue';
  import IepLongTermTargetEditModal from './iepLongTermTargetEditModal.vue';
  import IepChapterContentsViewModal from './iepChapterContentsViewModal.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const { loading, setLoading } = useLoading();
  const longTermTargetsAddVisible = ref(false);
  const targets = ref<any[]>([]);

  const targetsMap = ref<Record<number, any[]>>({});

  const sameDateAndDomainSpan = (courseName, options) => {
    const { record, column, rowIndex } = options;
    const { dateRangeDisplay, domain } = record;
    const listData = targetsMap.value[courseName];

    // 只处理 date 和 domain 列的合并
    if (column.dataIndex === 'dateRangeDisplay') {
      let startIndex = rowIndex;

      // 寻找相同日期和域的连续行的开始索引
      while (startIndex > 0 && listData[startIndex - 1].dateRangeDisplay === dateRangeDisplay) {
        startIndex -= 1;
      }

      // 计算相同日期和域的连续行数
      let rowSpanCount = 0;
      while (
        startIndex + rowSpanCount < listData.length &&
        listData[startIndex + rowSpanCount].dateRangeDisplay === dateRangeDisplay
      ) {
        rowSpanCount += 1;
      }

      // 如果是该组的起始行，返回合并的行数
      if (rowIndex === startIndex) {
        return { rowspan: rowSpanCount, colspan: 1 };
      }
      return { rowspan: 0, colspan: 0 };
    }
    if (column.dataIndex === 'domain') {
      let startIndex = rowIndex;

      // 寻找相同日期和域的连续行的开始索引
      while (startIndex > 0 && listData[startIndex - 1].domain === domain) {
        startIndex -= 1;
      }

      // 计算相同日期和域的连续行数
      let rowSpanCount = 0;
      while (startIndex + rowSpanCount < listData.length && listData[startIndex + rowSpanCount].domain === domain) {
        rowSpanCount += 1;
      }

      // 如果是该组的起始行，返回合并的行数
      if (rowIndex === startIndex) {
        return { rowspan: rowSpanCount, colspan: 1 };
      }
    }

    return { rowspan: 0, colspan: 0 };
  };

  const loadTargets = async () => {
    targetsMap.value = {};
    setLoading(true);
    try {
      const { data } = await request(`/rehabilitation/rehabilitationPlan/targets/${props.iep.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          pageSize: 9999,
        },
      });
      targets.value = data || [];

      data?.forEach((item) => {
        const courseName = item.courseName || '其他';
        targetsMap.value[courseName] = targetsMap.value[courseName] || [];
        targetsMap.value[courseName].push({
          ...item,
          dateRangeDisplay: item.dateRange?.join(' ~ '),
        });

        // sort by dateRangeDisplay and domain
        targetsMap.value[courseName].sort((a, b) => {
          if (a.dateRangeDisplay === b.dateRangeDisplay) {
            return a.domain.localeCompare(b.domain);
          }
          return a.dateRangeDisplay.localeCompare(b.dateRangeDisplay);
        });
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShowLongTermTargetAdd = () => {
    longTermTargetsAddVisible.value = true;
  };

  watch(
    () => props.iep,
    async (value) => {
      if (value?.id) {
        await loadTargets();
      }
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <a-skeleton v-if="loading" :animatio="true">
    <a-skeleton-line :rows="5" />
  </a-skeleton>
  <div v-else>
    <a-empty v-if="!targets?.length">
      <div> 暂无长期目标，请先添加 </div>
      <div v-if="editable" class="mt-2">
        <a-button type="primary" size="mini" @click="handleShowLongTermTargetAdd">
          <template #icon>
            <IconPlus />
          </template>
          添加长期目标
        </a-button>
      </div>
    </a-empty>
    <a-button v-if="editable" size="mini" type="primary" @click="handleShowLongTermTargetAdd">
      <template #icon>
        <IconPlus />
      </template>
      添加长期目标
    </a-button>
    <div v-for="(t, courseName) in targetsMap" :key="courseName" class="mt-2">
      <a-divider orientation="left">
        {{ courseName }}
        <IconDoubleDown />
      </a-divider>
      <a-table
        :data="t"
        :pagination="false"
        size="mini"
        :bordered="{ cell: true }"
        :span-method="(options) => sameDateAndDomainSpan(courseName, options)"
      >
        <template #columns>
          <a-table-column title="#" :width="50">
            <template #cell="{ rowIndex }">
              {{ rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="时间范围" data-index="dateRangeDisplay" :width="120" />
          <a-table-column title="">
            <template #title>
              <span class="text-sm text-blue-700">长期目标</span>
            </template>
            <a-table-column title="领域" data-index="domain" :width="140" />
            <a-table-column title="长期目标" data-index="content" />
            <a-table-column title="初评分" data-index="preScore" :width="70" />
            <a-table-column title="达成分" data-index="afterScore" :width="70" />
          </a-table-column>

          <a-table-column title="短期目标">
            <template #cell="{ record }">
              <a-table size="mini" :bordered="false" :show-header :data="record.shortTermTargets" :pagination="false">
                <template #columns>
                  <a-table-column title="#" :width="50">
                    <template #cell="{ rowIndex }">
                      {{ rowIndex + 1 }}
                    </template>
                  </a-table-column>
                  <a-table-column title="短期目标" data-index="content" />
                  <a-table-column title="初评分" data-index="preScore" :width="70" />
                  <a-table-column title="达成分" data-index="afterScore" :width="70" />
                </template>
              </a-table>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <div class="mb-2 h-4"> </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
