<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    longTermTarget: {
      type: Array as PropType<any[]>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const assessmentForms = ref<any[]>([]);
  const assessmentResultsMap = ref<any>({});
  const { loading, setLoading } = useLoading();
  const target = ref<any>({});

  const lessons = ref<any[]>([]);

  const loadCriterionResults = async () => {
    setLoading(true);
    try {
      const { data } = await request('/evaluation/customCriterionResult', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          student: props.iep.student.id,
          pageSize: 999,
          period: props.iep.gradePeriod,
        },
      });

      const forms = {};
      const resultsMap = {};
      data.items?.forEach((item) => {
        forms[item.customCriterionId] = {
          value: item.customCriterionId,
          label: item.criterionName,
        };

        resultsMap[item.customCriterionId] = resultsMap[item.customCriterionId] || [];
        resultsMap[item.customCriterionId].push({
          value: item.id,
          label: `第${item.timesLabel} [${item.evaluationDate}]`,
        });
      });

      assessmentForms.value = Object.values(forms);
      assessmentResultsMap.value = resultsMap;
    } finally {
      setLoading(false);
    }
  };

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });
  const handleOk = async () => {
    setLoading(true);
    try {
      if (!target.value.courseId) {
        throw new Error('请选择评估');
      }
      if (!target.value.dateRange?.filter((item) => item).length) {
        throw new Error('请选择时间范围');
      }
      if (target.value.length === 0) {
        throw new Error('请至少添加一个长期目标');
      }
      if (!target.value.domain || !target.value.content || target.value.preScore === undefined) {
        throw new Error('请完善长期目标信息');
      }

      if (props.iep.submitStatus !== 'Draft' && props.iep.submitStatus !== 'Rejected') {
        throw new Error('计划已经提交，不能修改');
      }

      const data = {
        ...props.longTermTarget,
        rehabilitationPlanId: props.iep.id,
        domain: target.value.domain,
        termType: 'LongTerm',
        content: target.value.content,
        dateRange: target.value.dateRange,
        preScore: target.value.preScore,
        afterScore: target.value.afterScore,
        courseId: target.value.courseId,
        // courseName: lessons.value.find((l) => l.id === target.value.courseId)?.name,
      };

      await request(`/rehabilitation/rehabilitationPlanTarget/${props.longTermTarget.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data,
      });

      emit('ok');
      return true;
    } catch (e: any) {
      Message.error(e.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const targets = computed({
    get() {
      return [target];
    },
    set(val) {
      // eslint-disable-next-line prefer-destructuring
      target.value = val[0];
    },
  });

  const handleOpen = () => {
    target.value = props.longTermTarget;
  };

  const handleClose = () => {
    target.value = {};
    modalVisible.value = false;
  };

  onMounted(async () => {
    await loadCriterionResults();
    lessons.value = assessmentForms.value;
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="修改长期目标"
    :width="1000"
    :ok-loading="loading"
    :on-before-ok="handleOk"
    @open="handleOpen"
    @close="handleClose"
  >
    <a-form :model="target" size="mini" auto-label-width>
      <div class="flex gap-2">
        <!--        <a-form-item label="学科" required>-->
        <!--          <a-select v-model="target.courseId" :options="lessons" />-->
        <!--        </a-form-item>-->
        <a-form-item label="时间范围" required>
          <a-range-picker v-model="target.dateRange" />
        </a-form-item>
      </div>
    </a-form>
    <a-table class="mt-2" size="small" :data="[target]" :pagination="false">
      <template #columns>
        <a-table-column title="领域" :width="150">
          <template #cell>
            <a-textarea v-model="target.domain" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标">
          <template #cell>
            <a-textarea v-model="target.content" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标评估" :width="120">
          <template #cell>
            <a-input-number v-model="target.preScore" size="mini">
              <template #suffix>分</template>
            </a-input-number>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
