<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import ReferFromMyLibraryModal from './components/referFromMyLibraryModal.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    longTermTarget: {
      type: Object as PropType<any>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const { loading, setLoading } = useLoading();

  const shortTermTargets = ref<any[]>([]);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });

  const handleAddManual = () => {
    shortTermTargets.value = [...shortTermTargets.value, { preScore: 0 }];
  };

  const handleClose = () => {
    modalVisible.value = false;
    shortTermTargets.value = [];
  };

  const handleDelete = (rowIndex) => {
    shortTermTargets.value.splice(rowIndex, 1);
  };

  const handleOpen = () => {
    shortTermTargets.value = (props.longTermTarget.shortTermTargets || []).map((item) => {
      return {
        ...item,
        expand: item?.chapterContents?.length > 0 ? '1' : undefined,
      };
    });
  };

  const handleOk = async () => {
    if (!props.editable) {
      emit('ok');
      return true;
    }
    setLoading(true);
    try {
      if (shortTermTargets.value.some((t) => !t.content || t.preScore === undefined)) {
        throw new Error('请完善短期目标信息');
      }

      if (props.iep.submitStatus !== 'Draft' && props.iep.submitStatus !== 'Rejected') {
        throw new Error('计划已经提交，不能修改');
      }

      const data: any = [];
      shortTermTargets.value?.forEach((item) => {
        data.push({
          ...item,
          rehabilitationPlanId: props.iep.id,
          longTermTargetId: props.longTermTarget.id,
          termType: 'ShortTerm',
          content: item.content,
          preScore: item.preScore,
          afterScore: item.afterScore,
          courseId: props.longTermTarget.courseId,
          courseName: props.longTermTarget.courseName,
        });
      });

      await request(
        `/rehabilitation/rehabilitationPlanTarget/batchCreate?longTermTargetId=${props.longTermTarget.id}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'POST',
          data,
        },
      );

      emit('ok');
      return true;
    } catch (e: any) {
      Message.error(e.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const referFromMyLibVisible = ref(false);
  const handleReferFromMyLib = () => {
    referFromMyLibVisible.value = true;
  };

  const disabledDate = (current) => {
    const dateRange = props.longTermTarget.dateRange?.map((item) => new Date(item));
    return current && (current < dateRange[0] || current > dateRange[1]);
  };

  const handleImportFromLibrary = (item) => {
    shortTermTargets.value = [
      ...shortTermTargets.value,
      {
        content: item.content,
        teachingScene: item.teachingScene,
        preScore: 0,
      },
    ];
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :width="1000"
    :ok-loading="loading"
    :on-before-ok="handleOk"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #title> 添加短期目标（领域： {{ longTermTarget.domain }}） </template>
    <div> 长期目标： {{ longTermTarget.content }} </div>
    <a-space v-if="editable">
      <a-button size="mini" type="outline" class="mt-2" @click="handleAddManual">
        <template #icon>
          <IconPlus />
        </template>
        添加短期目标
      </a-button>
      <a-button size="mini" type="outline" class="mt-2" @click="handleReferFromMyLib">
        <template #icon>
          <IconPlus />
        </template>
        从短期目标库引用
      </a-button>
    </a-space>

    <a-table class="mt-2" size="small" :data="shortTermTargets" :pagination="false">
      <template #columns>
        <a-table-column title="时间范围">
          <template #cell="{ record }">
            <a-range-picker v-model="record.dateRange" :disabled-date="disabledDate" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标">
          <template #cell="{ record }">
            <a-textarea v-model.trim="record.content" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="教学情境">
          <template #cell="{ record }">
            <a-textarea v-model.trim="record.teachingScene" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="前测" :width="120">
          <template #cell="{ record }">
            <a-input-number v-model="record.preScore" size="mini" :min="0" :max="5">
              <template #suffix>分</template>
            </a-input-number>
          </template>
        </a-table-column>
        <a-table-column v-if="editable" title="操作" :width="80">
          <template #cell="{ rowIndex }">
            <a-button type="text" size="mini" status="danger" @click="() => handleDelete(rowIndex)"> 删除 </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <refer-from-my-library-modal
      v-model:visible="referFromMyLibVisible"
      :short-term-targets="shortTermTargets"
      @import="handleImportFromLibrary"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>

<style scoped lang="scss"></style>
