<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import RehabAssessmentReferModal from './rehabAssessmentReferModal.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    targets: {
      type: Array as PropType<any[]>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const assessmentReferVisible = ref(false);
  const assessmentForms = ref([]);
  const assessmentResultsMap = ref({});
  const { loading, setLoading } = useLoading();

  const longTermTargets = ref<any[]>([]);
  const lessons = ref<any[]>([]);

  const formData = ref<any>({
    courseId: null,
  });

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });

  const loadCriterionResults = async () => {
    setLoading(true);
    try {
      const { data } = await request('/evaluation/customCriterionResult', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          student: props.iep.student.id,
          pageSize: 999,
          period: props.iep.gradePeriod,
        },
      });

      const forms = {};
      const resultsMap = {};
      data.items?.forEach((item) => {
        forms[item.customCriterionId] = {
          value: item.customCriterionId,
          label: item.criterionName,
          type: item.type,
        };

        resultsMap[item.customCriterionId] = resultsMap[item.customCriterionId] || [];
        resultsMap[item.customCriterionId].push({
          value: item.id,
          label: `第${item.timesLabel} [${item.evaluationDate}]`,
        });
      });

      assessmentForms.value = Object.values(forms);
      assessmentResultsMap.value = resultsMap;
    } finally {
      setLoading(false);
    }
  };

  const handleShowRefer = () => {
    assessmentReferVisible.value = true;
  };

  const handleAddManual = () => {
    longTermTargets.value = [
      ...longTermTargets.value,
      {
        preScore: 0,
        courseId: formData.value.courseId,
      },
    ];
  };

  const handleAddReference = (targets) => {
    longTermTargets.value = [...longTermTargets.value, ...targets];
  };

  const handleClose = () => {
    modalVisible.value = false;
    longTermTargets.value = [];
  };

  const handleDelete = (rowIndex) => {
    longTermTargets.value.splice(rowIndex, 1);
  };

  const handleOk = async () => {
    if (!props.editable) {
      emit('ok');
      return true;
    }
    setLoading(true);
    try {
      if (!formData.value.courseId) {
        throw new Error('请选择评估');
      }
      if (!formData.value.dateRange?.filter((item) => item).length) {
        throw new Error('请选择时间范围');
      }
      if (longTermTargets.value.length === 0) {
        throw new Error('请至少添加一个长期目标');
      }
      if (longTermTargets.value.some((t) => !t.domain || !t.content || t.preScore === undefined)) {
        throw new Error('请完善长期目标信息');
      }

      if (props.iep.submitStatus !== 'Draft' && props.iep.submitStatus !== 'Rejected') {
        throw new Error('计划已经提交，不能修改');
      }

      const data: any[] = [];
      longTermTargets.value?.forEach((item) => {
        data.push({
          rehabilitationPlanId: props.iep.id,
          domain: item.domain,
          termType: 'LongTerm',
          content: item.content,
          dateRange: formData.value.dateRange,
          preScore: item.preScore,
          afterScore: item.afterScore,
          courseId: formData.value.courseId,
          courseName: lessons.value.find((l) => l.value === formData.value.courseId)?.label,
        });
      });

      await request('/rehabilitation/rehabilitationPlanTarget/batchCreate', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data,
      });

      emit('ok');
      return true;
    } catch (e: any) {
      Message.error(e.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleOpen = async () => {
    formData.value = {
      ...formData.value,
      dateRange: [],
    };
  };

  const currentLesson = computed(() => {
    return lessons.value.find((l) => l.value === formData.value.courseId);
  });

  onMounted(async () => {
    await loadCriterionResults();
    lessons.value = assessmentForms.value;
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="添加长期目标"
    :width="1000"
    :ok-loading="loading"
    :on-before-ok="handleOk"
    @open="handleOpen"
    @close="handleClose"
  >
    <a-form :model="formData" size="mini" auto-label-width>
      <div class="flex gap-2">
        <a-form-item label="评估" required>
          <a-select v-model="formData.courseId" :options="lessons" />
        </a-form-item>
        <a-form-item label="时间范围" required>
          <a-range-picker v-model="formData.dateRange" />
        </a-form-item>
        <a-form-item :label-attrs="{ style: { display: 'none' } }">
          <a-space>
            <a-button
              :disabled="!formData.courseId || currentLesson?.type !== 'Criterion'"
              size="mini"
              type="primary"
              status="success"
              @click="handleShowRefer"
            >
              <template #icon>
                <IconPlus />
              </template>
              从目标池引用
            </a-button>
            <a-button size="mini" type="outline" @click="handleAddManual">
              <template #icon>
                <IconPlus />
              </template>
              手动添加
            </a-button>
          </a-space>
        </a-form-item>
      </div>
    </a-form>

    <a-table class="mt-2" size="small" :data="longTermTargets" :pagination="false">
      <template #columns>
        <a-table-column title="领域" :width="150">
          <template #cell="{ record }">
            <a-textarea v-model="record.domain" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标">
          <template #cell="{ record }">
            <a-textarea v-model="record.content" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标评估" :width="120">
          <template #cell="{ record }">
            <a-input-number v-model="record.preScore" size="mini">
              <template #suffix>分</template>
            </a-input-number>
          </template>
        </a-table-column>
        <!--        <a-table-column title="后测" :width="120">-->
        <!--          <template #cell="{ record }">-->
        <!--            <a-input-number v-model="record.afterScore" size="mini">-->
        <!--              <template #suffix>分</template>-->
        <!--            </a-input-number>-->
        <!--          </template>-->
        <!--        </a-table-column>-->
        <a-table-column title="操作" :width="80">
          <template #cell="{ rowIndex }">
            <a-button type="text" size="mini" status="danger" @click="() => handleDelete(rowIndex)"> 删除 </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <rehab-assessment-refer-modal
      v-if="formData.courseId && assessmentReferVisible"
      v-model:visible="assessmentReferVisible"
      :targets="targets"
      :iep="iep"
      :assessment-forms="assessmentForms"
      :criterion-id="formData.courseId"
      :assessment-results-map="assessmentResultsMap"
      @ok="handleAddReference"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
