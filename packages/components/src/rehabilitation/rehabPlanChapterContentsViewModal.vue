<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
    },
    longTermTarget: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);
  const shortTermTargets = computed(() =>
    props.longTermTarget.shortTermTargets?.filter((item) => item.chapterContents?.length),
  );

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" title="教学目标查看" :width="800">
    <div v-for="shortTermTarget in shortTermTargets" :key="shortTermTarget.id">
      <a-divider orientation="left" :margin="8">
        <small>{{ shortTermTarget.dateRange?.join(' ~ ') }}</small>
      </a-divider>
      <div class="py-2">
        {{ shortTermTarget.content }}
      </div>
      <a-table size="mini" :pagination="false" :data="shortTermTarget.chapterContents" class="mt-2">
        <template #columns>
          <a-table-column title="章节" data-index="chapter" />
          <a-table-column title="目标" data-index="name" />
          <a-table-column title="前测" data-index="preScore" />
          <a-table-column title="后测" data-index="afterScore" />
        </template>
      </a-table>
    </div>

    <a-empty v-if="!shortTermTargets?.length" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
