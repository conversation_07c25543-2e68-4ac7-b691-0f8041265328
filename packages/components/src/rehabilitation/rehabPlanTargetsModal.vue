<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import RehabPlanTargetsListContent from './rehabPlanTargetsListContent.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
    },
    raw: {
      type: Object as PropType<any>,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:modelValue', 'hide']);
  const targets = ref<any[]>([]);
  const targetsMap = ref<Record<number, any[]>>({});
  const iepContent = ref<any>(null);

  const modalVisible = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });

  const handleOpen = () => {
    modalVisible.value = true;
  };

  const handleClose = () => {
    modalVisible.value = false;
    targetsMap.value = {};
    targets.value = [];

    emit('hide');
  };
  const resList = ref();
  /*  const isFinished = (): boolean => {
    const res = Object.values(iepContent.value?.targetsMap);
    resList.value = res;
    if (res.length === 0) {
      return true;
    }
    res.forEach((item: any) => {
      item.forEach((value: any) => {
        if (value.shortTermTargets.length === 0) {
          return false;
        }
      });
    });
    return true;
  }; */

  const isFinished = (): boolean => {
    /**/
    if (!iepContent.value) return false;

    const res = Object.values(iepContent.value?.targetsMap);
    resList.value = res;
    if (res.length === 0) {
      return true;
    }

    const isIncomplete = res.some((item: any) => {
      return item.some((value: any) => {
        return value.shortTermTargets.length === 0;
      });
    });

    return !isIncomplete;
  };

  const handlePreOk = async () => {
    if (isFinished()) {
      emit('update:modelValue', false);
    } else {
      Message.warning('完善短期目标后才能提交');
      return false;
    }

    return true;
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    fullscreen
    hide-cancel
    ok-text="完成"
    @ok="handlePreOk"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #title>
      {{ raw?.student?.gradeClass?.name || '' }} {{ raw?.student?.name }} {{ raw?.gradePeriod }} 的康复教育计划
    </template>
    <div v-if="modalVisible">
      <rehab-plan-targets-list-content v-bind="$attrs" ref="iepContent" :iep="raw" />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
