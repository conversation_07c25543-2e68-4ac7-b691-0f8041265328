<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { getCourseListByCbIdIn, getChapter, getChapterListCbIdIn } from '@repo/infrastructure/openapi/course';
  import { getChapterAssessmentList } from '@repo/infrastructure/openapi/chapterAssessmentController'; // 获取章节评分
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    record: {
      type: Object,
    },
    evaluation: {
      type: Object,
    },
  });
  const data = ref([]);
  const record = ref({ ...props.record });
  const evaluation = ref({ ...props.evaluation });
  const teachers = ref([]);
  const currentTeacher = ref<any>({});
  const currentCourse = ref<any>({});
  const cbIds = ref([]);
  const currentChapter = ref();
  const chapterOptions = ref<Record<string, any>>([]);
  const curseList = ref([]);
  const chapterAssessList = ref(new Map());
  const teachingAssessCriteria = ref();
  const assessmentResultsByStudent = ref(new Map());
  const assessmentResultsByTarget = ref();

  const byStudent = async (val: number) => {
    await getChapterAssessmentList({
      chapterId: val,
    }).then((res: any) => {
      assessmentResultsByStudent.value = new Map();
      assessmentResultsByTarget.value = res.data;
      assessmentResultsByTarget.value.forEach((item: any) => {
        const result = ref(assessmentResultsByStudent.value.get(item.studentId));
        if (result.value != null) {
          if (item.type === 'pre') Object.assign(result.value, { pre: item.totalScore });
          else Object.assign(result.value, { after: item.totalScore });
        } else if (item.type === 'pre')
          assessmentResultsByStudent.value.set(item.studentId, { name: item.student.name, pre: item.totalScore });
        else assessmentResultsByStudent.value.set(item.studentId, { name: item.student.name, after: item.totalScore });
      });
    });
  };

  const handleChange = async (val: number) => {
    if (!val) return;
    currentChapter.value = data.value.find((chapter: any) => chapter.id === val);
    if (teachers.value.length > 0) {
      currentTeacher.value = teachers.value.find((teacher: any) => teacher.id === currentChapter.value?.createdById);
      currentCourse.value = curseList.value.find((curse: any) => curse.id === currentChapter.value?.courseId);
    }
    if (!chapterAssessList.value.has(val)) {
      await getChapter({
        id: val,
      }).then((res) => {
        chapterAssessList.value.set(val, res.data);
      });
    }
    teachingAssessCriteria.value = [];
    teachingAssessCriteria.value = chapterAssessList.value.get(val).content.teachingAssessCriteria;

    // 需要修改 效率不高
    byStudent(val);
  };

  const resetData = () => {
    currentChapter.value = {};
    chapterOptions.value = [];
  };

  const loadData = async () => {
    await request('/org/companyUser', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        branchOffice: record.value.branchOfficeId,
      },
    }).then((res) => {
      teachers.value = res.data.items;
      cbIds.value = res.data.items.map((item: any) => item.id);
    });
    if (cbIds.value.length > 0) {
      /*      getCourseListByCbIdIn({
        page: 1,
        pageSize: 999,
        cbIds: cbIds.value,
      }).then((res: any) => {
        curseList.value = res.data.list;
      }); */
      try {
        const result = await request('/course/course/courseListByCbIdIn', {
          baseURL: PROJECT_URLS.GO_PROJECT_API,
          method: 'get',
          params: {
            page: 1,
            pageSize: 999,
            cbIds: cbIds.value,
          },
        });
        curseList.value = result.data.list;
      } finally {
        /**/
      }
      await getChapterListCbIdIn({
        page: 1,
        pageSize: 999,
        createdAt: evaluation.value.year,
        cbIds: cbIds.value,
      }).then((res: any) => {
        data.value = res.data.list;
        chapterOptions.value = [];
        data.value.forEach((item: any) => {
          chapterOptions.value.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    }
  };
  onMounted(async () => {
    await loadData();
  });
</script>

<!--教案信息-->
<template>
  <!--  <div>{{ record }}</div>-->
  <!--  <div>{{ evaluation }}</div>-->
  <!--  <div>{{ data }}</div>-->
  <!--  <div>{{ curseList }}</div>-->
  <!--  <div>{{ chapterOptions }}</div>-->
  <!--  <div>{{ currentChapter }}</div>-->
  <!--  <div>{{ currentTeacher }}</div>-->
  <!--  <div>{{ chapterAssessList }}</div>-->
  <!--  <a-divider />-->
  <!--  <div>{{ teachingAssessCriteria }}</div>-->
  <!--  <div>{{ assessmentResultsByStudent }}</div>-->
  <!--  <div>{{ chapterAssessList.get(currentChapter.id) }}</div>-->
  <!--  <a-button @click="loadData">获取数据</a-button>-->
  <a-select
    placeholder="请选择需要查看的教案"
    allow-clear
    allow-search
    :options="chapterOptions"
    class="w-80"
    style="width: auto"
    @change="handleChange"
  ></a-select>
  <div v-if="currentChapter != null" class="mt-8">
    <div class="font-bold">课程信息</div>
    <div class="flex-1 flex justify-between mb-2">
      <span>教师：{{ currentTeacher.name }}</span>
      <span>科目：{{ currentChapter.name }}</span>
      <span>年级：{{ currentCourse.grade }}</span>
      <span>教材版本：{{ currentCourse.description }}</span>
      <span>上课时间：{{ currentCourse.classTime }}</span>
      <AttachmentsPreviewDisplay class="mr-5" :raw="currentChapter.content.lessonPrepareAttachments" />
    </div>
    <div class="mb-4">
      <span>引用说明：-</span>
    </div>
    <div class="bg-gray-50 p-3 rounded-lg">
      <div v-for="(item, index) in currentChapter.content.teachingPrepare" :key="index" class="mb-5">
        <div class="font-bold">{{ item.name }}:</div>
        <div v-html="item.udf1"></div>
      </div>
    </div>

    <!--===========-->
    <a-tabs class="mt-2">
      <a-tab-pane key="viaStudent" title="测试结果">
        <a-table
          row-key="id"
          class="m-2"
          :data="assessmentResultsByStudent.values()"
          size="mini"
          :default-expand-all-rows="true"
          :pagination="false"
        >
          <template #columns>
            <a-table-column title="学生" data-index="name" />
            <a-table-column title="前测结果" data-index="pre" />
            <a-table-column title="后测结果" data-index="after" />
          </template>
        </a-table>
      </a-tab-pane>
      <!--      <a-tab-pane key="viaCriteria" title="按评测目标查看">
        <a-table
          class="m-2"
          size="mini"
          :data="teachingAssessCriteria"
          :default-expand-all-rows="true"
          :pagination="false"
        >
          <template #columns>
            <a-table-column title="目标" data-index="name" />
            <a-table-column title="评测结果" data-index="criteria">
              <template #cell="{}">
                <a-button size="mini">查看评测结果</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-tab-pane>-->
    </a-tabs>
    <!--===========-->
  </div>
</template>

<style scoped lang="scss"></style>
