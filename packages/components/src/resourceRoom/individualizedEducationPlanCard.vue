<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import Iep from '../student/archive/iep.vue';

  const props = defineProps({
    studentSchema: {
      type: Object,
      required: true,
    },
    evaluation: { type: Object },
    record: {
      type: Object,
    },
  });
  const student = ref<any>(null);
  const studentList = ref();
  const studentOptions = ref<any>([]);

  const getStudentList = async () => {
    const res = await request(`/resourceRoom/student`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    studentList.value = null;
    studentList.value = res.data.items;
    studentList.value.forEach((item: any) => {
      studentOptions.value.push({
        label: item.name,
        value: item.id,
      });
    });
    if (studentList.value.length > 0) {
      // eslint-disable-next-line prefer-destructuring
      // student.value = studentList.value[0];
    }
  };
  const handleChange = (val) => {
    student.value = null;
    student.value = studentList.value.find((item: any) => item.id === val);
  };

  onMounted(async () => {
    await getStudentList();
  });
</script>

<template>
  <a-select
    placeholder="请选择学生"
    :options="studentOptions"
    allow-clear
    style="width: 140px"
    class="pr-5"
    @change="handleChange"
  ></a-select>
  <iep v-if="studentSchema && student != null" :student="student" :student-schema="studentSchema" />
</template>

<style scoped lang="scss"></style>
