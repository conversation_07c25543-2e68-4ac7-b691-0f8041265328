<script setup lang="ts">
  import { getChapterListCbIdIn } from '@repo/infrastructure/openapi/course';

  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    evaluation: { type: Object },
    record: {
      type: Object,
    },
  });
  const data = ref();
  const chapterOptions = ref([]);
  const cbIds = ref([]);
  const lessonPrepares = ref([]);
  const currentLessonPrepare = ref([]);
  const currentAttachments = ref([]);

  const loadDataList = async () => {
    await request('/org/companyUser', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        branchOffice: props.record.branchOfficeId,
      },
    }).then((res) => {
      cbIds.value = res.data.items.map((item: any) => item.id);
    });
    if (cbIds.value.length > 0) {
      await getChapterListCbIdIn({
        page: 1,
        pageSize: 999,
        createdAt: props.evaluation.year,
        cbIds: cbIds.value,
      }).then((res: any) => {
        data.value = res.data.list;
        chapterOptions.value = [];
        lessonPrepares.value = [];
        data.value.forEach((item: any) => {
          chapterOptions.value.push({
            label: item.name,
            value: item.id,
          });
          lessonPrepares.value.push({
            lessonPrepare: item.content.lessonPrepare,
            attachments: item.content.lessonPrepareAttachments,
            id: item.id,
          });
        });
      });
    }
  };
  const handleChange = (val: number) => {
    if (val) {
      const res = lessonPrepares.value.find((item) => item.id === val);
      currentLessonPrepare.value = res.lessonPrepare;
      currentAttachments.value = res.attachments;
    } else {
      currentLessonPrepare.value = [];
      currentAttachments.value = [];
    }
  };

  onMounted(async () => {
    await loadDataList();
  });
</script>

<!--备课安排-->
<template>
  <a-select
    placeholder="请选择需要查看的备课"
    allow-clear
    allow-search
    :options="chapterOptions"
    class="w-80 mr-8"
    style="width: auto"
    size="mini"
    @change="handleChange"
  ></a-select>
  <AttachmentsPreviewDisplay v-if="currentAttachments != null" class="mr-5" :raw="currentAttachments" />
  <div v-if="currentLessonPrepare != null && currentLessonPrepare.length > 0" class="bg-gray-50 p-3 rounded-lg mt-4">
    <div v-for="(i, index) in currentLessonPrepare" :key="index" class="mb-2">
      <div>
        <span class="font-bold">{{ index + 1 }}、{{ i.name }}</span>
      </div>
      <div v-html="i.udf1"></div>
    </div>
  </div>
  <a-empty v-else />
</template>

<style scoped lang="scss"></style>
