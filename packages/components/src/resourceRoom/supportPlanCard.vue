<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    evaluation: { type: Object },
    record: {
      type: Object,
    },
  });
  const data = ref();
  const loadData = async () => {
    const res = await request(
      `/resourceRoom/individualizedSupportPlan/findByTerm/${props.evaluation?.year}/${props.record?.branchOfficeId}`,
      {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      },
    );
    data.value = res.data.items;
  };
  const columns = ref([
    { title: '所属学期', dataIndex: 'gradePeriod' },
    { title: '学生', dataIndex: 'student.name' },
    { title: '参与会议人员', dataIndex: 'participants' },
    /* { title: '协作者', dataIndex: '' }, */
    { title: '制定日期', dataIndex: 'meetingDate' },
    { title: '提交状态', slotName: 'submitStatus' },
    { title: '操作', slotName: 'operation' },
  ]);
  const columnsFamilyMembers = ref([
    { title: '关系', dataIndex: 'relationship' },
    { title: '姓名', dataIndex: 'name' },
    { title: '电话', dataIndex: 'phone' },
    { title: '生日', dataIndex: 'birthday' },
    { title: '年龄', dataIndex: 'age' },
    { title: '备注', dataIndex: 'remark' },
  ]);
  const columnsDiagnosis = ref([
    { title: '评估内容', dataIndex: 'content' },
    { title: '评估时间', dataIndex: 'evaluateTime' },
    { title: '评估人', dataIndex: 'evaluator' },
    { title: '评估结果概况', dataIndex: 'results' },
  ]);
  const columnsSupport = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      align: 'center',
      width: 60,
    },
    { title: '项目', dataIndex: 'item' },
    { title: '内容', dataIndex: 'content' },
    { title: '主要负责人', dataIndex: 'personInCharge' },
  ]);
  const columnsResettlementAdvices = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      align: 'center',
      width: 60,
    },
    { title: '安置', dataIndex: 'subject' },
    { title: '内容', dataIndex: 'content' },
    { title: '主要负责人', dataIndex: 'personInCharge' },
    { title: '时间', slotName: 'dateRange' },
  ]);
  onMounted(async () => {
    await loadData();
  });
  const supportPlan = ref();
  const modalVisible = ref(false);
  const currentRecord = ref(null);
  const handelView = (arg: any) => {
    currentRecord.value = arg;
    modalVisible.value = true;
  };
  const handlePreOpen = async () => {
    if (currentRecord.value != null) {
      const res = await request(`/resourceRoom/individualizedSupportPlan/${currentRecord.value?.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      supportPlan.value = res.data;
    }
  };
  const handleClose = () => {
    modalVisible.value = false;
  };
</script>

<template>
  <a-table :columns="columns" :data="data" :bordered="{ cell: true }" :pagination="false">
    <template #submitStatus="{ record }">
      <span v-if="record.submitStatus === 'Draft'">草稿</span>
      <span v-else-if="record.submitStatus === 'Submitted'">已提交</span>
      <span v-else>已驳回</span>
    </template>
    <template #operation="{ record }">
      <a-button type="outline" size="mini" @click="handelView(record)">查看详情</a-button>
    </template>
  </a-table>
  <a-modal :visible="modalVisible" width="80%" :closable="false" @before-open="handlePreOpen">
    <a-divider orientation="left">
      <span class="font-bold">基本信息</span>
    </a-divider>
    <div class="ml-2 rounded bg-gray-50 p-5">
      <div class="flex justify-around">
        <span class="flex-1">
          学生：<span>{{ supportPlan?.student.name }}</span>
        </span>
        <span class="flex-1">
          年龄：<span>{{ supportPlan?.student.age }}</span>
        </span>
        <span class="flex-1">
          障碍类型：<span>{{ supportPlan?.student.disorders }}</span>
        </span>
        <span class="flex-1">
          系统证号：<span>{{ supportPlan?.student.symbol }}</span>
        </span>
      </div>
    </div>
    <a-divider orientation="left">
      <span class="font-bold mt-5">家庭成员</span>
    </a-divider>
    <a-table :columns="columnsFamilyMembers" :data="supportPlan?.familyMembers" :pagination="false" class="mb-4" />

    <a-divider orientation="left">
      <span class="font-bold">课程类教育诊断</span>
    </a-divider>
    <a-table :columns="columnsDiagnosis" :data="supportPlan?.educationDiagnosisOfLesson" />
    <a-divider orientation="left">
      <span class="font-bold">生理类教育诊断</span>
    </a-divider>
    <a-table :columns="columnsDiagnosis" :data="supportPlan?.educationDiagnosisOfPhysical" />

    <a-divider orientation="left">
      <span class="font-bold">心理类教育诊断</span>
    </a-divider>
    <a-table :columns="columnsDiagnosis" :data="supportPlan?.educationDiagnosisOfPsychological" />

    <a-divider orientation="left">
      <span class="font-bold">其他教育诊断</span>
    </a-divider>
    <a-table :columns="columnsDiagnosis" :data="supportPlan?.educationDiagnosisOfOthers" />

    <a-divider orientation="left">
      <span class="font-bold">优势/劣势</span>
    </a-divider>
    <div class="rounded bg-gray-50 p-2">
      <div class="rounded bg-green-50 p-2">
        <div v-for="(v, k) in supportPlan?.advantage" :key="k">{{ k + 1 }}、 {{ v }}</div>
      </div>
      <div class="rounded bg-red-50 p-2">
        <div v-for="(v, k) in supportPlan?.vulnerability" :key="k">{{ k + 1 }}、 {{ v }}</div>
      </div>
    </div>

    <a-divider orientation="left">
      <span class="font-bold">安置建议</span>
    </a-divider>
    <a-table :columns="columnsResettlementAdvices" :data="supportPlan?.resettlementAdvices" :bordered="{ cell: true }">
      <template #dateRange="{ record }">
        <span v-if="record.dateRange">{{ record.dateRange[0] }} - {{ record.dateRange[1] }}</span>
        <span v-else> - </span>
      </template>
    </a-table>

    <a-divider class="font-bold" orientation="left">
      <span class="font-bold">支持与服务</span>
    </a-divider>
    <a-table :columns="columnsSupport" :data="supportPlan?.supports" :bordered="{ cell: true }" :pagination="false" />

    <template #footer="{}">
      <a-button size="mini" @click="handleClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
