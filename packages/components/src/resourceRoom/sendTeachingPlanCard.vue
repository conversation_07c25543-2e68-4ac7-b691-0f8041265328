<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    data: {
      type: Array,
      required: true,
    },
  });

  const studentOptions = ref<any[]>([]);
  const currentStudent = ref();
  const data = ref<any[]>(props.data); // 使用 any 类型
  const detailsVisible = ref(false);
  const currentRecord = ref({});

  const handelView = (record: any) => {
    currentRecord.value = record;
    detailsVisible.value = true;
  };
  const handlePreOk = () => {
    detailsVisible.value = false;
  };
  onMounted(() => {
    data.value.forEach((item: any) => {
      // 使用 any 类型
      studentOptions.value.push({
        label: item.student.name,
        value: item.student.id,
      });
    });
  });
  const columns = ref([
    { title: '姓名', dataIndex: 'student.name' },
    { title: '性别', dataIndex: 'student.gender' },
    { title: '年龄', dataIndex: 'age' },
    { title: '学期', dataIndex: 'period' },
    { title: '制定教师', dataIndex: 'planTeacher' },
    { title: '是否完成', dataIndex: 'finished', slotName: 'finished' },
    { title: '日期范围', slotName: 'dateRange', align: 'center', width: 200 },
    { title: '附件查看', slotName: 'attachments', align: 'center', width: 100 },
    { title: '操作', slotName: 'operation', width: 100 },
  ]);
  const sendRecordList = ref([]);
  const handlePreOpen = async () => {
    const res = await request('/resourceRoom/sendEducationRecord', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: { sendEducationPlan: currentRecord.value.id, pageSize: 999 },
    });
    sendRecordList.value = res.data.items;
  };
</script>

<template>
  <!--  <div>{{ data }}</div>-->
  <a-table v-if="data" :columns="columns" :data="data" :bordered="{ cell: true }" :pagination="false">
    <template #dateRange="{ record }">
      <div v-if="record.dateRange?.length > 0">{{ record.dateRange[0] }} / {{ record.dateRange[1] }}</div>
      <div v-else>-</div>
    </template>
    <template #finished="{ record }">
      <div v-if="record.finished">
        <icon-check-circle style="color: green" />
      </div>
      <div v-else>
        <icon-close-circle style="color: red" />
      </div>
    </template>
    <template #attachments="{ record }">
      <AttachmentsPreviewDisplay :raw="record?.attachments" />
    </template>
    <template #operation="{ record }">
      <a-button type="outline" size="mini" @click="handelView(record)">查看详情</a-button>
    </template>
  </a-table>
  <a-modal :visible="detailsVisible" :closable="false" hide-cancel width="80%" @before-open="handlePreOpen">
    <!--active-key-->
    <a-collapse :default-active-key="[1]" accordion>
      <a-collapse-item key="0" header="详情类容">
        <div v-html="currentRecord?.detailContent"></div>
        <!--        <div>{{ currentRecord }}</div>-->
      </a-collapse-item>
      <a-collapse-item v-for="(item, index) in sendRecordList" :key="index + 1">
        <template #header>
          <span class="flex-1">
            第{{ index + 1 }}次送教记录
            <span class="text-gray-400 text-xs"> &nbsp;-&nbsp;{{ item.date }}</span>
          </span>
        </template>
        <div class="border ml-2 rounded bg-white p-5 ml-[-10px] w-full">
          <div class="font-bold"></div>
          <a-divider orientation="left"><span class="font-bold">基本信息：</span></a-divider>
          <div class="flex justify-between mb-2 rounded bg-gray-50 p-5">
            <span class="flex-1">送教类型：{{ item.type }}</span>
            <span class="flex-1">送教教师：{{ item.teacher }}</span>
            <span class="flex-1">送教日期：{{ item.date }}</span>
            <span class="flex-1">课时：{{ item.classHour }}</span>
            <AttachmentsPreviewDisplay class="flex" :raw="item?.attachments" />
          </div>

          <a-divider orientation="left"><span class="font-bold">送教内容：</span></a-divider>
          <div class="mb-4">
            <!--            <span> 详情 </span>-->
            <div class="rounded bg-gray-50 p-5 inline-block w-full" v-html="item?.teachingPlan"></div>
          </div>
          <div class="rounded bg-gray-50 p-5">
            <div class="mb-4"><span class="font-bold">下阶段送教重点：</span>{{ item.nextSendPoint }}</div>
            <div class="mb-4">
              <span class="font-bold">送教计划：</span>
              <div v-for="(v, k) in item?.effectEvaluation" class="pl-4">
                <!-- 条目之间的分隔线 -->
                <a-divider v-if="k > 0" />

                <!-- 使用 flex 布局将所有内容放在同一行，分布两端 -->
                <div class="flex items-center justify-between">
                  <!-- 排列显示 k + 1 和 target -->
                  <div class="flex items-center">
                    <span class="font-bold">{{ k + 1 }}、</span>
                    <span>{{ v.target }}</span>
                  </div>
                  <!-- 让“前测”和“后测”分别居右 -->
                  <div class="ml-auto flex space-x-4">
                    <span>前测：{{ v.preTest }}</span>
                    <span>后测：{{ v.afterTest }}</span>
                  </div>
                </div>
              </div>
            </div>
            <span class="font-bold">家长培训：</span>
            <div class="mb-4" v-html="item.guardianTraining"></div>
            <div class="flex justify-between mb-2">
              <!--<span class="flex-1">效果评估：{{ item.type }}</span>-->
              <span class="flex-1"><span class="font-bold">家长/学生：</span>{{ item.type }}</span>
            </div>
          </div>
        </div>
        <!--        <div>{{ currentRecord }}</div>-->
      </a-collapse-item>
    </a-collapse>
    <template #footer>
      <a-button type="primary" size="mini" class="bg-gray-600" @click="handlePreOk">关闭</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
