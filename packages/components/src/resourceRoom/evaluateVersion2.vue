<template>
  <a-modal
    fullscreen
    :closable="false"
    :visible="visible"
    :footer="false"
    :render-to-body="true"
    @before-open="handelPreOpen"
  >
    <div class="flex justify-between items-center mr-8">
      <div>
        <span class="ml-4 mr-4">学校：{{ school?.name }}</span>
        <span class="mr-4">指标：{{ evaluationIndicator?.name ?? evaluationIndicator?.criterionName }}</span>
      </div>
      <div>
        <a-popconfirm content="确认暂存？" @ok="handelOk(false)">
          <a-button
            size="mini"
            type="primary"
            status="success"
            :disabled="type === 'teacher' ? isSubmitted : isConfirm"
          >
            暂存评分
          </a-button>
        </a-popconfirm>
        <a-popconfirm content="提交评分后将无法修改，是否确认提交？" type="warning" @ok="handelOk(true)">
          <!--提交的时候也需要分类一下 || -->
          <a-button type="primary" size="mini" class="ml-2" :disabled="type === 'teacher' ? isSubmitted : isConfirm">
            <span v-if="type === 'teacher'">提交评分</span>
            <span v-else>确认评分</span>
          </a-button>
        </a-popconfirm>
        <a-button class="ml-2" size="mini" @click="handleBack">
          <icon-arrow-left />
          返回
        </a-button>
      </div>
    </div>
    <a-divider />

    <span class="mr-5 ml-4">评价名称：{{ evaluationIndicator?.name ?? evaluationIndicator?.criterionName }}</span>
    <span
      >评价年度：<a-input
        v-model="formData.year"
        style="width: 200px"
        placeholder="请输入年度"
        :disabled="type === 'expert'"
    /></span>
    <a-tabs :default-active-key="0" class="mt-4" @tab-click="tabClick">
      <a-tab-pane v-for="(v, k) in navbar" :key="k" :title="v" class="p-4">
        <a-table
          key="id"
          :pagination="false"
          :columns="columns"
          :data="criterionDetails[k]"
          column-resizable
          :bordered="{ cell: true }"
          :span-method="spanMethod"
          @cell-dblclick="handleCellClick"
          ><!--没提交可以编辑-->
          <template #score="{ record }">
            <a-input-number
              v-model="record.score"
              mode="button"
              min="0"
              :default-value="record.score"
              :disabled="type === 'teacher' ? isSubmitted : isConfirm"
              @change="handelChange(record)"
            />
          </template>
          <template #systemData="{ record }">
            <div v-if="!viewVisible" class="text-xs text-gray-400"> 暂存后查看 </div>
            <a-button
              v-else-if="record.reference != null && viewVisible"
              size="mini"
              @click="handleViewSystemData(record, k)"
              >查看</a-button
            >
            <div v-else class="text-center">-</div>
          </template>
          <template #attachments="{ record }">
            <!--分成图片,文件...-->
            <span v-if="type === 'teacher'" @click="setCurrentRecord(record)">
              <uploader-modal
                v-model="record.attachments"
                btn-text-empty="上传附件"
                btn-text="查看附件"
                @update:model-value="handleUpdate"
              />
            </span>
            <AttachmentsPreviewDisplay v-else-if="record.attachments.length" :raw="record.attachments" class="ml-10" />
            <span v-else class="text-gray-300">暂无附件</span>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <a-modal
    :visible="editContextVisible"
    :on-before-ok="handlePreOk"
    :closable="false"
    width="800"
    @cancel="handleCancel"
  >
    <a-textarea v-if="false" v-model="editContext.secondary" style="height: 100px" />
    <span>三级指标</span>
    <a-textarea v-model="editContext.tertiary" style="height: 100px" />
    <span>评价标准</span>
    <a-textarea v-model="editContext.standard" style="height: 100px" />
  </a-modal>

  <system-data-reference-display
    v-model="systemDataVisible"
    :visible="systemDataVisible"
    :record="referenceRecord"
    :data-options="quotes"
    :evaluation="evaluation"
  />
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import SystemDataReferenceDisplay from './systemDataReferenceDisplay.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    fusionSchool: {
      type: Object,
    },
    evaluationIndicator: {
      // 常规巡检标准 name and id
      type: Object,
    },
    type: {
      type: String,
    },
  });

  const emits = defineEmits(['update:modelValue']);
  const attachments = ref();
  const navbar = ref(['行政与管理', '经费与设施设备', '师资保障', '教育教学', '成果与特色']);
  const editContext = ref<any>({
    attachments: [],
    id: null,
    score: 0,
    secondary: '',
    standard: '',
    systemData: '',
    tertiary: '',
  });
  const editKey = ref({
    key: '',
    id: '',
  });
  const editContextVisible = ref(false);
  const evaluation = ref({ ...props.evaluationIndicator });
  const currentEvaluationSelect = ref(null);
  const evaluationList = ref([]);
  const switchEvaluationVisible = ref(false);
  const criterion = ref(null);
  const criterionDetails = ref<Array<any>>([]);
  const school = ref({ ...props.fusionSchool });
  const data = ref([]);
  const isSubmitted = ref(false); // 是否提交，暂存or 提交
  const isConfirm = ref(false); // 是否确认评分
  const currentIndex = ref(0);
  const viewVisible = ref(true);
  const formData = ref({
    year: new Date().getFullYear(),
    id: null,
  });
  const type = ref(props.type);
  const systemDataVisible = ref(false);
  const quotes = ref();
  const referenceRecord = ref();
  const attachmentColumnOptions = ref({
    key: 'attachments',
    inputControl: 'bigAttachmentDirect',
    inputControlProps: {
      draggable: false,
      accepts: 'image/*,application/pdf',
      showTips: false,
    },
  });
  const currentRecord = ref();

  // 更新节点的函数
  const updateNodeById = (nodes, id, newData) => {
    let found = false;
    nodes.forEach((node) => {
      if (node.id === id) {
        Object.assign(node, newData);
        found = true; // 标记为已找到
        return;
      }
      if (node.children && node.children.length > 0) {
        found = updateNodeById(node.children, id, newData);
      }
    });
    return found; // 返回找到的结果
  };

  const updateAllNodes = (nodes) => {
    nodes.forEach((node) => {
      // 检查是否存在 attachments 且是数组
      if (node.attachments && Array.isArray(node.attachments)) {
        node.attachments.forEach((attachment) => {
          if (attachment.url) {
            // 将 url 的值赋给新属性 udf1
            attachment.udf1 = attachment.url;
            // 删除 url 属性
            delete attachment.url;
          }
        });
      }
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        updateAllNodes(node.children);
      }
    });
  };

  // 点击上传附件 记录当前 行数据
  const setCurrentRecord = (record: any) => {
    currentRecord.value = record;
  };
  // 上传回调
  const handleUpdate = (val) => {
    const res = currentRecord.value.attachments;
    // 上传的操作
    // 判断是删除还是上传
    // if (val.length > res.length) {
    //   // 上传
    //   currentRecord.value.attachments.push({
    //     id: null,
    //     name: val[val.length - 1].name,
    //     udf1: val[val.length - 1].url,
    //   });
    // }

    // if (val.length >= 1 && val[val.length - 1].name != null)
    //   currentRecord.value.attachments.push({
    //     id: null,
    //     name: val[val.length - 1].name,
    //     udf1: val[val.length - 1].url,
    //   });
    updateNodeById(data.value, currentRecord.value.id, currentRecord.value);
    updateAllNodes(data.value);
  };

  const handleViewSystemData = (record: any, k: number) => {
    // referenceRecord.value = record;
    data.value[k].children.forEach((item: any) => {
      item.children.forEach((item2: any) => {
        if (item2.id === record.id) {
          referenceRecord.value = item2;
          // alert(formData.value.year);
          if (school.value.branchOfficeId) {
            Object.assign(referenceRecord.value, { branchOfficeId: school.value.branchOfficeId });
          }
          Object.assign(evaluation.value, { year: formData.value.year });
        }
      });
    });
    systemDataVisible.value = true;
  };
  const handelChange = (record: any) => {
    updateNodeById(data.value, record.id, record);
  };
  const handleCellClick = (record: any, column, ev, rowIndex) => {
    if (['评价标准', '三级指标'].includes(column.title) && isSubmitted.value) {
      // 不允许编辑
      // 没提交可以编辑
      editKey.value.id = record.id;
      editContext.value = record;
      editContextVisible.value = true;
    }
  };

  const handleCancel = () => {
    editContextVisible.value = false;
  };
  const handlePreOk = async () => {
    const currentDetail = criterionDetails.value[currentIndex.value];
    const item = currentDetail.find((items: any) => items.id === editKey.value.id);
    if (item) {
      Object.assign(item, editContext.value);
      // 使用 editKey.value.key 来更新对应的属性
      updateNodeById(data.value, editKey.value.id, editContext.value);
    }
    editContextVisible.value = false; // 隐藏编辑框或执行其他操作
  };

  const columns = ref([
    { title: '二级指标', dataIndex: 'secondary' },
    { title: '三级指标', dataIndex: 'tertiary' },
    { title: '评价标准', dataIndex: 'standard' },
    { title: '分值', dataIndex: 'score', slotName: 'score', width: 140 },
    { title: '系统数据', dataIndex: 'systemData', slotName: 'systemData', width: 140, align: 'center' },
    { title: '附件', dataIndex: 'attachments', slotName: 'attachments', width: 140, align: 'center' },
  ]);

  const tabClick = (key: number) => {
    currentIndex.value = key;
  };

  const handelCancel = () => {
    emits('update:modelValue', false);
  };

  const resetData = () => {
    data.value = [];
    navbar.value = [];
    criterionDetails.value = [];
    evaluationList.value = [];
    currentEvaluationSelect.value = null;
    criterion.value = null;
    switchEvaluationVisible.value = false;
    evaluation.value = {};
    if (!(type.value === 'teacher')) school.value = {};
    isSubmitted.value = false; // 是否提交，暂存or 提交
    isConfirm.value = false; // 是否确认评分
    currentIndex.value = 0;
    formData.value = {
      year: new Date().getFullYear(),
      id: null,
    };
  };

  const handelOk = async (trueOrFalse: boolean) => {
    if (type.value === 'expert') {
      // 如果是专家提交... 就设置是否确认提交
      isConfirm.value = trueOrFalse;
    }
    if (type.value === 'teacher') {
      isSubmitted.value = trueOrFalse;
    }
    if (Number(formData.value.year)) {
      /**/
    } else {
      Message.error('年度请输入当前年份');
      return;
    }
    const submitData = {
      year: formData.value.year,
      evaluationCriterionId: type.value === 'teacher' ? evaluation.value.id : evaluation.value.criterionId,
      evaluationCriterionName: evaluation.value.name,
      fusionSchoolId: school.value.id,
      submitted: isSubmitted.value,
      details: data.value,
      confirm: isConfirm.value,
      resourceRoomEvaluationId: formData.value.id, // 资源教室评估 id
    };
    try {
      await request('/resourceRoom/resourceRoomEvaluation/evaluate', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: submitData,
      });
    } catch (e) {
      /**/
    } finally {
      resetData();
      emits('update:modelValue', false);
    }
  };

  const handleBack = async () => {
    resetData();
    emits('update:modelValue', false);
  };

  /*= =======vue2 方法 */

  const spanMethod = (line: any) => {
    const currentRow = line.rowIndex;
    const currentData = criterionDetails.value[currentIndex.value][currentRow];
    if (line.columnIndex === 0) {
      let rowspan = 1;
      // 向下检查后续行，统计相同的二级指标
      for (let i = currentRow + 1; i < criterionDetails.value[currentIndex.value].length; i += 1) {
        if (criterionDetails.value[currentIndex.value][i].secondary === currentData.secondary) {
          rowspan += 1;
        } else {
          break; // 一旦遇到不同的值就停止
        }
      }
      // 只在第一行显示合并
      if (rowspan > 1) {
        return { rowspan, colspan: 1 };
      }
    }
    // 其他列不需要合并
    return {};
  };
  const loading = ref(true);

  const handleRes = (treeRes: any) => {
    criterionDetails.value = [];
    data.value = treeRes.data;
    navbar.value = [];
    treeRes.data.forEach((item, index) => {
      navbar.value.push(item.target);
      // 确保 criterionDetails 为当前索引初始化
      if (!criterionDetails.value[index]) {
        criterionDetails.value[index] = [];
      }
      // 二级指标
      item.children.forEach((child) => {
        child.children.forEach((item2) => {
          criterionDetails.value[index].push({
            id: item2.id,
            secondary: child.target,
            tertiary: item2.target,
            standard: item2.criterion,
            score: item2.score,
            systemData: '',
            attachments: item2.attachments,
            reference: item2.reference,
          });
        });
      });
      // 排序
      criterionDetails.value[index].sort((a, b) => {
        if (a.secondary < b.secondary) return -1;
        if (a.secondary > b.secondary) return 1;
        return 0;
      });
    });
  };

  const loadData = async () => {
    // 指标
    if (!evaluation.value || !evaluation.value.id) {
      return;
    }
    try {
      // 找到对应的指标
      // 查看是否存在
      const r = await request('/resourceRoom/resourceRoomEvaluation', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          fusionSchool: school.value.id, // id
          criterionId: evaluation.value.id,
        },
      });
      if (r.data.items && r.data.items.length) {
        viewVisible.value = true;
        const res = r.data.items[0];
        formData.value.id = res.id;
        formData.value.year = res.year;
        isSubmitted.value = res.submitted;
        isConfirm.value = res.confirmed;
        evaluation.value = { ...evaluation.value, year: res.year };
        // evaluation.value = res.year;

        const item = r.data.items[0]; // 修正为 r.data.items
        formData.value = item;

        const criterionRes = await request(`/resourceCenter/evaluationCriterion/${item.criterionId}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        criterion.value = criterionRes.data;

        const treeRes = await request(`/resourceRoom/resourceRoomEvaluation/getTree/${item.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        // criterionDetails.value = treeRes.data;
        // data.value = treeRes.data;
        handleRes(treeRes);
      } else {
        viewVisible.value = false;
        const criterionRes = await request(`/resourceCenter/evaluationCriterion/${evaluation.value.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        criterion.value = criterionRes.data.items;
        const treeRes = await request(`/resourceCenter/evaluationCriterion/getTree/${evaluation.value.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        });
        handleRes(treeRes);
      }
    } catch (error) {
      /**/
    } finally {
      loading.value = false;
    }
  };

  const loadDataByType = async () => {
    if (type.value === 'expert' && evaluation.value) {
      // 管理端直接更具 评估的id 来加载数据
      const treeRes = await request(`/resourceRoom/resourceRoomEvaluation/getTree/${evaluation.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      formData.value.id = evaluation.value.id;
      formData.value.year = evaluation.value.year;
      isSubmitted.value = evaluation.value.submitted;
      isConfirm.value = evaluation.value.confirmed;
      handleRes(treeRes);
    }
    if (type.value === 'teacher' && evaluation.value) {
      await loadData();
    }
  };

  // 不知道为什么多此一举
  const loadResourceRoom = async () => {
    if (school.value.id)
      await request(`/resourceCenter/fusionSchool/${school.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      }).then(({ data: res }) => {
        school.value = res;
      });
  };

  // 加载常规巡检标准
  const loadEvaluations = async () => {
    if (evaluation.value.id) {
      const res = await request(`/resourceCenter/evaluationCriterion/${evaluation.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      evaluation.value = res.data;
      await loadData();
    }
  };

  const handelPreOpen = async () => {
    // await loadEvaluations();
    // await loadData();
    await loadDataByType();
  };
  const handleSwitchClose = () => {
    switchEvaluationVisible.value = false;
  };
  const getOptions = async () => {
    const { data: result } = await request('/resourceCenter/evaluationCriterion/getSystemDataOptions', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    quotes.value = result;
  };

  onMounted(async () => {
    // await loadEvaluations();
    // await loadResourceRoom();
    await loadDataByType();
    await getOptions();

    // await loadData();
  });
  watch(
    () => props.fusionSchool,
    (val) => {
      school.value = { ...val };
    },
  );
  watch(
    () => props.evaluationIndicator,
    (val) => {
      evaluation.value = { ...val };
    },
  );
</script>

<style lang="scss" scoped>
  table {
    border-collapse: collapse; /* 合并边框 */
    width: 100%; /* 100% 宽度 */
  }

  th,
  td {
    border: 1px solid black; /* 设置单元格边框 */
    padding: 8px; /* 内边距 */
    text-align: left; /* 左对齐 */
  }
</style>
