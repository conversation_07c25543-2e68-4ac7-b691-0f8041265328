<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    data: {
      type: Object,
    },
    type: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const data = ref<any>({ ...props.data });

  // 其实可以写一个全局的resetData ...
  const resetData = () => {};

  const handlePreOk = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handlePreOpen = async () => {};
  watch(
    () => props.data,
    (newVal) => {
      resetData();
      data.value = { ...newVal };
    },
  );
</script>

<template>
  <!--  <div>{{ data }}</div>-->
  <div v-for="(item, index) in data[type]" :key="index">
    <a-divider v-if="index > 0" />
    <div>
      <div>
        <span>年度：</span>{{ item.year }} <span class="ml-10">创建于：</span>{{ item.createdDate.split(' ')[0] }}
      </div>
      <div>
        <span>时限：</span>{{ item.timePeriod }} <span class="ml-10">工作类型：</span>{{ item.type }}
        <AttachmentsPreviewDisplay :raw="item.attachments" class="ml-10" />
      </div>
    </div>
    <div class="font-bold mt-5">计划内容</div>
    <div class="bg-gray-50 p-5 rounded-lg" v-html="item.content" />
  </div>
</template>

<style scoped lang="scss"></style>
