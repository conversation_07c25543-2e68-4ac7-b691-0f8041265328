<script setup lang="ts">
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import { ref, watch } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
  });

  const dataList = ref<any[]>(Object.values(props.data));

  watch(
    () => props.data,
    (newVal) => {
      dataList.value = Object.values(newVal);
    },
    { immediate: true },
  );
</script>

<template>
  <!--  <div>{{ dataList }}</div>-->
  <div v-for="(v, k) in dataList" :key="k">
    <a-divider v-if="k > 0" />
    <div class="space-y-2">
      <div><span>事件日期：</span>{{ v.eventDate }}</div>
      <div> <span>标题：</span> {{ v.tag }}</div>
      <div><span>标签：</span>{{ v.subject }}</div>
      <div><span>活动主题：</span>{{ v.eventSubject }}</div>
      <div><span>事件地点：</span>{{ v.eventLocation }}</div>
      <div><span>目标内容：</span>{{ v.targetContent }}</div>
      <div><span>参与人员：</span>{{ v.participants }} </div>
      <div>
        <span>产出结果：</span>{{ v.result }}
        <AttachmentsPreviewDisplay :raw="v.attachments" class="ml-10" />
      </div>
    </div>
    <div class="mt-5">
      <span class="font-bold">事件记录</span>
      <div class="bg-gray-50 p-5 rounded-lg" v-html="v.record"></div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
