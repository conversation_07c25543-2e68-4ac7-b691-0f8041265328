<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { center } from '@antv/g2plot/lib/plots/sankey/sankey';

  const props = defineProps({
    data: {
      type: Object,
    },
  });
  const data = ref({ ...props.data });
  const columns = ref([
    { title: '姓名', dataIndex: 'name', slotName: 'name' },
    { title: '是否在职', dataIndex: 'inPosition', slotName: 'inPosition', align: 'center' },
    { title: '最高学历', dataIndex: 'highestEducation', slotName: 'highestEducation' },
    { title: '职称等级', dataIndex: 'proTitle', slotName: 'proTitle' },
    { title: '特殊专业经历', dataIndex: 'hasSpecialEducationExp', slotName: 'hasSpecialEducationExp', align: 'center' },
    { title: '从教类别', dataIndex: 'teachType', slotName: 'teachType' },
    { title: '任教学段', dataIndex: 'teachGrade', slotName: 'teachGrade' },
    { title: '教学科目', dataIndex: 'teachingSubjects', slotName: 'teachingSubjects' },
  ]);

  const displayData = ref<Record<string, any>>([]);

  const resetData = () => {
    displayData.value = [];
  };
  const handleData = () => {
    Object.values(data.value).forEach((item: any) => {
      displayData.value.push({
        name: item.name,
        inPosition: item.inPosition,
        highestEducation: item.highestEducation,
        proTitle: item.proTitle,
        hasSpecialEducationExp: item.hasSpecialEducationExp,
        teachType: item.teachType,
        teachGrade: item.teachGrade,
        teachingSubjects: item.teachingSubjects,
      });
    });
  };

  onMounted(() => {
    handleData();
  });

  watch(
    () => props.data,
    (newVal) => {
      resetData();
      data.value = { ...newVal };
      handleData();
    },
  );
</script>

<template>
  <a-table :columns="columns" :data="displayData" column-resizable :bordered="{ cell: true }" :pagination="false">
    <template #inPosition="{ record }">
      <icon-check-circle v-if="record.inPosition" class="text-green-500" />
      <icon-close-circle v-else class="text-red-500" />
    </template>
    <template #hasSpecialEducationExp="{ record }">
      <icon-check-circle v-if="record.hasSpecialEducationExp" class="text-green-500" />
      <icon-close-circle v-else class="text-red-500" />
    </template>
    <template #teachingSubjects="{ record }">
      <span v-for="(item, idx) in record.teachingSubjects" :key="idx" class="text-gray-600 mr-4">{{ item }}</span>
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
