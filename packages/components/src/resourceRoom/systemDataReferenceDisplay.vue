<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import TeachersInfoCard from './teachersInfoCard.vue';
  import SchoolWideIntegratedEducationWorkCard from './schoolWideIntegratedEducationWorkCard.vue';
  import MajorEventsOfResourceRoomCard from './majorEventsOfResourceRoomCard.vue';
  import RegularTourSubmissionCard from './regularTourSubmissionCard.vue';
  import LessonPlanCard from './lessonPlanCard.vue';
  import LessonPreparationArrangementCard from './lessonPreparationArrangementCard.vue';
  import IndividualizedEducationPlanCard from './individualizedEducationPlanCard.vue';
  import SendTeachingPlanCard from './sendTeachingPlanCard.vue';
  import SupportPlanCard from './supportPlanCard.vue';
  import ContinuingEducationCard from './continuingEducationCard.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
    dataOptions: {
      type: Array,
    },
    evaluation: {
      type: Object,
    },
  });
  const dataTypes = {
    ATTACHMENT_MAJOR_EVENTS_TAG: 1,
    ASSET_INFORMATION_REGISTRATION: 2,
    PERSONAL_INFO_COMPLETENESS: 3,
    TEACHER_PLAN_SCHOOL_INTEGRATION: { label: '全校融合教育工作', code: 4 },
    TEACHER_PLAN_RELATED_DEPARTMENTS: { label: '相关部门融合教育', code: 5 },
    TEACHER_PLAN_RESOURCE_ROOM: { label: '资源教室工作计划', code: 6 },
    RESOURCE_TEACHER_RECORD_ANNOUNCEMENT: 7, // 资源教室大事记
    RESOURCE_TEACHER_RECORD_CASE_CONFERENCE: 8,
    RESOURCE_TEACHER_RECORD_REGULAR_INSPECTION: 9,
    IEP_ACCOUNT_ACTIVITY: 10,
    RESOURCE_TEACHER_RECORD_NEWS_PROPAGANDA: 11,
    MEETING_TRAINING_RECORD: 12,
    EVALUATION_EDUCATION_PLAN: 13,
    IEP_COLLABORATION_OBJECT: 14,
    IEP_PLAN_GOALS: 15,
    PARENT_QUESTIONNAIRE: 16,
    RESEARCH_PROJECTS: 17,
    AWARDS: 18,
    LESSON_PLAN: 19,
    LESSON_PLAN_PREPARATION: 20,
    IEP_SUPPORT_PLANS: 21,
    IEP_GOAL_EXTRACTION: 22,
    CLASS_TEACHER_PLAN: 23,
    CLASS_TEACHER_PLAN_RECORD: 24,
    REGULAR_TOUR_SUBMISSION: 25,
    CUSTOM_TABLE: 26,
    INDIVIDUALIZED_EDUCATION_PLAN: 27, // iep
    SUPPORT_PLAN: 28, // 支持计划
    SEND_TEACHING_PLAN: 29, // 送教计划
    INTEGRATED_EDUCATION_CONTINUING_EDUCATION_TRAINING: 30, // "融合教育继续教育培训"
  };

  const emits = defineEmits(['update:modelValue']);
  const data = ref();
  const record = ref<any>({ ...props.record });
  const evaluation = ref<any>({ ...props.evaluation });
  const dataOptions = ref<any>({ ...props.dataOptions });

  const loadData = async () => {
    if (record.value.resourceRoomEvaluationId != null || evaluation.value.id != null) {
      const id = evaluation.value.criterionId != null ? evaluation.value.criterionId : evaluation.value.id;
      if (!record.value.branchOfficeId) return;
      const { data: res } = await request(
        `/resourceCenter/evaluationCriterion/getReferenceData/${id}/${record.value.branchOfficeId}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
          data: record.value.reference,
        },
      );
      data.value = res;
    }
  };

  const getLabel = (v: number): string => {
    return Object.values(dataOptions.value).find((item) => item.value === v).label;
  };
  // 其实可以写一个全局的resetData ...
  const resetData = () => {
    /**/
  };

  const handlePreOk = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };

  const studentSchema = ref();

  const handlePreOpen = async () => {
    await loadData();
    studentSchema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/student');
  };

  onMounted(async () => {
    studentSchema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/student');
    await loadData();
  });

  watch(
    () => props.record,
    async (newVal) => {
      record.value = { ...newVal };
      await loadData();
    },
  );
  watch(
    () => props.evaluation,
    async (newVal) => {
      evaluation.value = { ...newVal };
    },
  );
  watch(
    () => props.dataOptions,
    (newVal) => {
      dataOptions.value = { ...newVal };
    },
  );
</script>

<template>
  <a-modal
    :on-before-ok="handlePreOk"
    :visible="visible"
    fullscreen
    :closable="false"
    @cancel="handleCancel"
    @before-open="handlePreOpen"
  >
    <!--    <div>{{ record }}</div>-->
    <!--    <div>{{ evaluation }}</div>-->
    <div class="bg-slate-50 rounded p-2 mb-4">
      <div class="mb-2">指标：{{ record.target }}</div>
      <div>规则：{{ record.criterion }}</div>
    </div>
    <!--    <a-button @click="loadData">刷新</a-button>-->
    <!--    <div>{{ dataOptions }}</div>-->
    <div v-if="data != null">
      <a-card v-for="v in record.reference" :key="v" class="mb-5" :hoverable="true" :bordered="false">
        <template #title>
          <span class="font-bold text-xl">{{ getLabel(v) }}</span>
        </template>
        <!--个人信息完整度-->
        <teachers-info-card v-if="v === dataTypes.PERSONAL_INFO_COMPLETENESS" :data="data[v]" />
        <!--工作计划-->
        <school-wide-integrated-education-work-card
          v-if="v === dataTypes.TEACHER_PLAN_SCHOOL_INTEGRATION.code"
          :data="data[v]"
          :type="dataTypes.TEACHER_PLAN_SCHOOL_INTEGRATION.label"
        />
        <school-wide-integrated-education-work-card
          v-if="v === dataTypes.TEACHER_PLAN_RELATED_DEPARTMENTS.code"
          :data="data[v]"
          :type="dataTypes.TEACHER_PLAN_RELATED_DEPARTMENTS.label"
        />
        <school-wide-integrated-education-work-card
          v-if="v === dataTypes.TEACHER_PLAN_RESOURCE_ROOM.code"
          :data="data[v]"
          :type="dataTypes.TEACHER_PLAN_RESOURCE_ROOM.label"
        />
        <!--资源教室大事记-->
        <major-events-of-resource-room-card
          v-if="v === dataTypes.RESOURCE_TEACHER_RECORD_ANNOUNCEMENT"
          :data="data[v]"
        />
        <major-events-of-resource-room-card
          v-if="v === dataTypes.RESOURCE_TEACHER_RECORD_CASE_CONFERENCE"
          :data="data[v]"
        />
        <major-events-of-resource-room-card
          v-if="v === dataTypes.RESOURCE_TEACHER_RECORD_REGULAR_INSPECTION"
          :data="data[v]"
        />
        <major-events-of-resource-room-card
          v-if="v === dataTypes.RESOURCE_TEACHER_RECORD_NEWS_PROPAGANDA"
          :data="data[v]"
        />
        <!-- 常规巡回提交、咨询服务、资产信息 -->
        <regular-tour-submission-card v-if="v === dataTypes.REGULAR_TOUR_SUBMISSION" :data="data[v]" />
        <!--教案-->
        <lesson-plan-card v-if="v === dataTypes.LESSON_PLAN" :evaluation="evaluation" :record="record" />
        <!--备课安排-->
        <lesson-preparation-arrangement-card
          v-if="v === dataTypes.LESSON_PLAN_PREPARATION"
          :evaluation="evaluation"
          :record="record"
        />
        <!--个别化教育计划-->
        <individualized-education-plan-card
          v-if="v === dataTypes.INDIVIDUALIZED_EDUCATION_PLAN"
          :evaluation="evaluation"
          :record="record"
          :student-schema="studentSchema"
        />
        <send-teaching-plan-card v-if="v === dataTypes.SEND_TEACHING_PLAN" :data="data[v]" />
        <support-plan-card v-if="v === dataTypes.SUPPORT_PLAN" :evaluation="evaluation" :record="record" />
        <!--融合教育继续教育-->
        <continuing-education-card
          v-if="v === dataTypes.INTEGRATED_EDUCATION_CONTINUING_EDUCATION_TRAINING"
          :data="data[v]"
        />
      </a-card>
    </div>
    <template #footer="{}">
      <div class="mr-10">
        <a-button size="mini" @click="handleCancel">取消</a-button>
        <a-popconfirm content="是否确认？" type="warning" @ok="handlePreOk">
          <a-button type="primary" size="mini" class="ml-2">确认</a-button>
        </a-popconfirm>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
