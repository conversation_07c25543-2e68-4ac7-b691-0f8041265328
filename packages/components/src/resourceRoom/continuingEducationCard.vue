<script setup lang="ts">
  const props = defineProps({
    data: {
      type: Array,
    },
  });
  const columns = [
    { title: '教师', dataIndex: 'teacher.name' },
    { title: '职位', dataIndex: 'title', slotName: 'title' },
    { title: '区级教研培训', dataIndex: 'districtTraining' },
    { title: '校级培训', dataIndex: 'schoolTraining' },
    { title: '课例学习', dataIndex: 'lessonTraining' },
    { title: '总学时', slotName: 'total' },
  ];
  const activeRowClass = (record: any) => {
    return '';
  };
</script>

<template>
  <a-table
    :columns="columns"
    :data="props.data"
    :bordered="{ cell: true }"
    :column-resizable="true"
    :row-class="activeRowClass"
  >
    <template #title="{ record }">
      <span v-for="item in record.title" :key="item" class="bg-gray-100 pr-1 pl-1 mr-1 rounded">{{ item }}</span>
    </template>
    <template #total="{ record }">
      <span>
        {{ record.schoolTraining + record.districtTraining + (record.lessonTraining ? record.lessonTraining : 0) }}
      </span>
    </template>
  </a-table>
</template>

<style scoped lang="scss">
  :deep(.row > .arco-table-td) {
    background-color: #fff8f8 !important;
  }
</style>
