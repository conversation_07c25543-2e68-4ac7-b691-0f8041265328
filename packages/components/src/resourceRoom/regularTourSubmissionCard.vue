<script setup lang="ts">
  import { ref, watch } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
    },
  });
  const data = ref({ ...props.data });
  const columns = ref([
    { title: '借用时间', dataIndex: 'planBorrowDate' },
    { title: '资产名', dataIndex: 'details', slotName: 'name' },
    { title: '数量', dataIndex: 'borrowCount' },
    { title: '备注', dataIndex: 'applyComment' },
  ]);
  watch(
    () => props.data,
    (newVal) => {
      data.value = { ...newVal };
    },
  );
</script>

<!--常规巡回申请资产信息-->
<template>
  <!--  <div>{{ data }}</div>-->
  <div>资产信息</div>
  <a-table
    v-if="data['资产信息']"
    :data="data['资产信息']"
    :columns="columns"
    column-resizable
    :bordered="{ cell: true }"
    :pagination="false"
  >
    <template #name="{ record }">
      <div v-for="(i, index) in record.details" :key="index">{{ i.name }}</div>
    </template>
  </a-table>
  <a-empty v-else />

  <div class="mt-5">咨询服务</div><hr />
  <div v-if="data['咨询服务']"></div>
  <a-empty v-else />

  <div class="mt-5">常规巡回</div><hr />
  <div v-if="data['常规巡回']"></div>
  <a-empty v-else />
</template>

<style scoped lang="scss"></style>
