import api from '@repo/infrastructure/openapi';
import { Message } from '@arco-design/web-vue';

export const getCommonCourses = async (): Promise<API.Course[]> => {
  const { data } = await api.course.getCommonCourseList();
  return data;
};

export const commonCoursesUpdate = async (ids: number[]) => {
  const { data } = await api.course.updateCommonCourse({ ids });
  return data;
};

export const addToCommonCourses = async (id: number) => {
  const raw = await getCommonCourses();
  if (!raw) {
    const data = await commonCoursesUpdate([id]);
    return data || [];
  }
  if (raw.some((item: any) => item.id === id)) return raw;
  const ids = [id, ...raw.map((item: any) => item.id)];
  const data = await commonCoursesUpdate(ids);

  Message.success('添加至常用课程成功');
  return data;
};

export const removeFromCommonCourses = async (id: number) => {
  const raw = await getCommonCourses();
  if (!raw) return raw;
  const ids = raw.map((item: any) => item.id).filter((courseId: any) => courseId !== id);
  const data = await commonCoursesUpdate(ids);

  Message.success('移除常用课程成功');
  return data;
};
