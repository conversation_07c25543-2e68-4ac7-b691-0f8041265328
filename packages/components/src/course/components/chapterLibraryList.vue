<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { Modal } from '@arco-design/web-vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import ChapterReferPreviewModal from './chapterReferPreviewModal.vue';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';
  import ChapterLibraryFilter from './chapterLibraryFilter.vue';

  const detailLoading = ref(false);
  const schema = ref(null);
  const tableRef = ref<any>(null);
  const coursesMap = ref<any>({});
  const courseStore = useSchoolCourseStore();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo();
  const userStore = useUserStore();
  const boStore = useCommonStore({
    api: '/org/branchOffice/simpleList',
    queryParams: {
      pageSize: 999,
      orgNature: menuInfo?.app?.label || userStore.getUserNature(),
    },
  });

  const previewVisible = ref(false);
  const currentPreview = ref<any>({});
  const teacherOptions = ref<any[]>([]);
  const boOptions = ref<any[]>([]);

  let orgNature = menuInfo?.app?.label;
  if (!orgNature) {
    orgNature = userStore.getUserNature();
  }

  const queryParams = ref<Record<string, any>>({
    orgNature,
  });

  const emit = defineEmits(['refer']);

  const visibleColumns = ['name', 'category', 'description', 'grade', 'createdBy', 'period', 'referenceCount'];
  const teachersMap = ref<any>({});
  const teacherStore = useCommonStore({
    api: '/org/companyUser/allTeachers',
    queryParams: {
      pageSize: 999,
      orgNature,
    },
  });

  const handleRefer = (record) => {
    previewVisible.value = false;
    emit('refer', record);
  };

  const handleLoadDetail = async (record) => {
    detailLoading.value = true;
    try {
      const { data } = await request(`/course/chapter-library/${record.id}`);
      currentPreview.value = data;
    } finally {
      detailLoading.value = false;
    }
  };

  const handleRowActions = async (action, record) => {
    if (action.key === 'refer') {
      Modal.confirm({
        title: '请确认',
        content: '确定要引用此篇教案吗？',
        onOk: () => handleRefer(record),
      });
    } else if (action.key === 'preview') {
      await handleLoadDetail(record);
      previewVisible.value = true;
    }
  };

  const handleSearch = async () => {
    tableRef.value.queryParams = {
      ...tableRef.value.queryParams,
      category: undefined,
      ...queryParams.value,
      orgNature,
    };
    /* 原本的属性没被修改 */
    if (!queryParams.value.category) tableRef.value.queryParams.category = '';
    if (!queryParams.value.period) tableRef.value.queryParams.period = '';
    if (!queryParams.value.grade) tableRef.value.queryParams.grade = '';

    tableRef.value?.loadData();
  };

  const handleQuickFilter = async (quickFilter) => {
    const { name, createdById } = queryParams.value;
    queryParams.value = {
      name,
      createdById,
      ...(quickFilter || {}),
    };

    await handleSearch();
  };

  onMounted(async () => {
    [coursesMap.value, schema.value, teachersMap.value, teacherOptions.value, boOptions.value] = await Promise.all([
      courseStore.getSchoolCoursesMap(),
      SchemaHelper.getInstanceByApi('/course/chapter-library'),
      teacherStore.getMap(),
      teacherStore.getOptions('/org/companyUser/allTeachers'),
      boStore.getOptions(),
    ]);
  });
</script>

<template>
  <div v-if="schema">
    <chapter-library-filter @filter="handleQuickFilter" />
    <div class="flex justify-between">
      <div class="flex-1">
        <a-form layout="inline" size="mini" :model="queryParams">
          <a-form-item label="学校">
            <a-select v-model="queryParams.boId" allow-search :options="boOptions" allow-clear />
          </a-form-item>
          <a-form-item label="原创教师">
            <a-select v-model="queryParams.createdById" allow-search :options="teacherOptions" allow-clear />
          </a-form-item>
          <a-form-item label="课文（章节主题）">
            <a-input v-model="queryParams.name" allow-clear />
          </a-form-item>
          <a-button size="mini" @click="handleSearch">
            <template #icon>
              <IconSearch />
            </template>
            搜索
          </a-button>
        </a-form>
      </div>
      <table-action
        v-if="tableRef"
        :visible-components="['refresh', 'delete']"
        :schema="schema"
        :table="tableRef"
        component-size="mini"
      />
    </div>
    <crud-table
      ref="tableRef"
      :schema="schema"
      size="mini"
      class="mt-2"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      :show-row-actions="true"
      @row-action="handleRowActions"
    >
    </crud-table>

    <chapter-refer-preview-modal
      v-model:visible="previewVisible"
      :teachers-map="teachersMap"
      :courses-map="coursesMap"
      :record="currentPreview"
      @refer="handleRefer"
    />
  </div>
</template>

<style scoped lang="scss"></style>
