<script setup lang="ts">
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { inject, onMounted, ref } from 'vue';
  import { CommonApi } from '@repo/infrastructure/crud';
  import openapi from '@repo/infrastructure/openapi';

  const props = defineProps({
    course: {
      type: Object,
      default: () => ({}),
    },
  });

  const router = inject('router');
  const schema = SchemaHelper.getInstanceByApi('/course/chapter');
  const chaptersTree = ref<any[]>([]);

  let api: any;

  const handleGoEdit = () => {
    router.push({
      name: 'courseEdit',
      params: {
        courseId: props.course.id,
      },
    });
  };

  const loadChapterTree = async () => {
    const res = await openapi.course.getChapterTree({
      courseId: props.course.id,
    });
    chaptersTree.value = res.data;
  };

  onMounted(async () => {
    api = CommonApi.getInstance(schema);
    await loadChapterTree();
  });
</script>

<template>
  <a-card
    :title="`${course.name} 课程章节列表`"
    class="mt"
    :field-names="{
      key: 'id',
      title: 'name',
      children: 'children',
    }"
  >
    <template #extra>
      <a-button size="mini" type="dashed" @click="handleGoEdit">
        <template #icon>
          <IconPen />
        </template>
        编辑课程
      </a-button>
    </template>
    <a-tree
      v-if="chaptersTree?.length > 0"
      :data="chaptersTree"
      :show-icon="true"
      :block-node="true"
      show-line
      default-expand-all
    >
      <template #title="data">
        <div class="chapter-item" :class="`level-${data.depth}`">
          <!--          <div v-if="data.depth === 1" class="level-1-index">-->
          <!--            {{ data.depthIndex }}-->
          <!--          </div>-->
          <div class="title"> {{ data.number }} {{ data.name }}</div>
          <div class="actions">
            <a-button type="text" size="mini">教学演示</a-button>
            <a-button type="text" size="mini">教学设计</a-button>
            <a-button class="start-class-btn" shape="round" size="mini" type="primary">上课 </a-button>
          </div>
        </div>
      </template>
    </a-tree>

    <a-empty v-else description="暂无章节，请先添加" />
  </a-card>
</template>

<style scoped lang="less">
  :deep(.arco-tree-node-title) {
    padding: 0;
    margin: 4px 0 4px 10px;

    .arco-tree-node-title-text {
      flex: 1;
    }
  }

  .chapter-item {
    display: flex;
    line-height: 180%;
    padding: 4px 8px;
    border-radius: 4px;

    .title {
      flex: 1;
      margin: 0 10px;
    }

    .actions {
      display: none;

      .start-class-btn {
        margin-left: 10px;
      }
    }

    &:hover {
      background: #f2f2f2;

      .actions {
        display: block;
      }
    }

    &.level-1 {
      font-size: 16px;
      background: #f2f2f2;
      margin-left: 30px;
      position: relative;

      .title {
        margin-left: 3px;
      }

      .level-1-index {
        position: absolute;
        left: -40px;
        top: 0;
        width: 36px;
        height: 36px;
        border-radius: 100%;
        background: rgb(var(--arcoblue-4));
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
