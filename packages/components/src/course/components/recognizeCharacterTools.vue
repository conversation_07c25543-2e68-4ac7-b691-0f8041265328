<script setup lang="ts">
  import { ref, onMounted, computed, onUnmounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import AssessmentTest from '../../teachingTools/AssessmentTest.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    currentTool: {
      type: Object,
    },
    course: {
      type: Object,
    },
    chapter: {
      type: Object,
    },
  });
  const visible = ref(props.modelValue);
  const chapterThesaurus = ref<any>(null);
  const student = ref<any>(null);
  const currentStudent = ref<any>(null);
  const loadWordsByChapter = async () => {
    try {
      const { data: res } = await request('/resourceRoom/chapterThesaurus', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          chapterId: props.chapter?.id,
          courseId: props.course?.id,
        },
      });
      const [firstItem] = res.items;
      chapterThesaurus.value = firstItem;
    } finally {
      /**/
    }
  };
  const handleRemoveChar = (word: string) => {
    if (chapterThesaurus.value && chapterThesaurus.value.words) {
      chapterThesaurus.value = {
        ...chapterThesaurus.value,
        words: chapterThesaurus.value.words.filter((item: string) => item !== word),
      };
    }
  };
  const isTesting = ref(false);
  const addVisible = ref(false);
  const charsInput = ref('');
  const handleAdd = () => {
    addVisible.value = true;
  };
  const processedChars = computed(() => {
    if (!charsInput.value) return [];
    return Array.from(new Set(charsInput.value.match(/[\u4e00-\u9fa5]/g) || []));
  });
  const handleAddChars = async () => {
    if (!chapterThesaurus.value) {
      chapterThesaurus.value = { words: [], chapterId: props.chapter?.id, courseId: props.course?.id };
    }

    chapterThesaurus.value.words = [...processedChars.value, ...(chapterThesaurus.value.words || [])];

    addVisible.value = false;
  };

  const loading = ref(false);
  const handleSave = async () => {
    try {
      loading.value = true;
      let url = '/resourceRoom/chapterThesaurus';
      let method = 'post';
      if (chapterThesaurus.value.id) {
        url = `/resourceRoom/chapterThesaurus/${chapterThesaurus.value.id}`;
        method = 'put';
      }
      const { data: res } = await request(url, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method,
        data: chapterThesaurus.value,
      });
      charsInput.value = '';
      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };
  const loadStudent = async () => {
    const { data } = await request(`/resourceRoom/student`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    student.value = data.items;
  };
  const saveTestResult = async (data: any) => {
    try {
      await request('/resourceRoom/thesaurus', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: {
          ...data,
        },
      });
      await request('/resourceRoom/recognizeCharactersResult', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: {
          ...data,
          chapterId: props.chapter.id,
          courseId: props.course.id,
          category: props.course.category,
        },
      });
      Message.success('测试结果已保存');
    } finally {
      /**/
    }
  };

  const handleFinish = async (results: any) => {
    isTesting.value = false;
    const testResults = {
      familiar: results.filter((r) => r.known).map((r) => r.char),
      unfamiliar: results.filter((r) => !r.known).map((r) => r.char),
      testDate: new Date().toISOString().split('T')[0],
      spendTime: results.reduce((sum, r) => sum + r.time, 0),
      accuracy: (results.filter((r) => r.known).length / results.length) * 100,
      student: { id: currentStudent.value },
    };
    await saveTestResult(testResults);
  };

  onMounted(async () => {
    await loadWordsByChapter();
    await loadStudent();
  });
  onUnmounted(() => {
    chapterThesaurus.value = null;
  });
</script>

<template>
  <a-modal fullscreen :visible="visible" :title="currentTool?.title">
    <div v-if="!isTesting" class="flex h-[85vh]">
      <div class="flex-1 flex flex-col">
        <div class="flex justify-end p-4 space-x-2">
          <a-button type="outline" size="mini" @click="handleAdd"><icon-plus />添加生字</a-button>
          <a-button type="outline" size="mini" :loading="loading" @click="handleSave"><icon-save />保存</a-button>
        </div>
        <div v-if="chapterThesaurus?.words" class="w-full flex flex-wrap gap-x-4 gap-y-2 p-4">
          <div
            v-for="word in chapterThesaurus.words"
            :key="word"
            class="group relative text-2xl p-2 w-12 h-12 rounded border border-gray-300 cursor-pointer"
          >
            <icon-close
              class="absolute top-0 right-0 opacity-0 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 group-hover:pointer-events-auto cursor-pointer text-xs"
              @click="handleRemoveChar(word)"
            />

            <span class="flex justify-center items-center h-full">{{ word }}</span>
          </div>
        </div>
        <a-empty v-else class="w-full" description="暂无生字" />
      </div>

      <div class="w-px mx-4 bg-gray-300" />

      <div class="flex-1">
        <a-select
          v-model="currentStudent"
          placeholder="请选择学生"
          :options="student"
          style="width: 200px"
          size="mini"
          :field-names="{ label: 'name', value: 'id' }"
        />
        <a-button class="ml-2" size="mini" :disabled="!currentStudent" @click="isTesting = true">开始测试</a-button>
      </div>
    </div>
    <a-modal :visible="addVisible" title="添加生字" @ok="handleAddChars" @cancel="addVisible = false">
      <a-textarea
        v-model="charsInput"
        :auto-size="{ minRows: 4, maxRows: 8 }"
        placeholder="请输入要添加的汉字，系统会自动分字并去重"
      />
      <div v-if="processedChars?.length > 0" class="preview">
        <div class="preview-title">处理结果 (共 {{ processedChars?.length }} 个字)：</div>
        <div class="preview-chars">
          <span v-for="char in processedChars" :key="char" class="preview-char">
            {{ char }}
          </span>
        </div>
      </div>
    </a-modal>
    <AssessmentTest v-if="isTesting" :characters="chapterThesaurus?.words || []" @finish="handleFinish" />
  </a-modal>
</template>

<style scoped lang="scss">
  .preview-title {
    margin-bottom: 8px;
    color: #86909c;
  }

  .preview-chars {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
