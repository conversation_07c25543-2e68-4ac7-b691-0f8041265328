<script lang="ts" setup>
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message, Modal } from '@arco-design/web-vue';
  import { onMounted, ref } from 'vue';
  import { cloneDeep } from 'lodash';
  import { CommonApi } from '@repo/infrastructure/crud';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { chapterDuplicate } from '@repo/infrastructure/openapi/course';
  import { useUserStore } from '@repo/infrastructure/store';
  import { getLeafParentPath } from '../../utils/assessment';
  import ChapterReferFromLibraryModal from './chapterReferFromLibraryModal.vue';

  const props = defineProps({
    courseId: {
      type: Number,
      required: true,
    },
    apiAlias: {
      type: String,
      default: '/course/chapter',
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const userStore = useUserStore();

  const chaptersTree = ref<any[]>([]);
  const { setLoading: setSwitchLoading, loading: switchLoading } = useLoading();
  const currentChapter = ref<Record<string, any>>({});

  const createChapterTitle = ref<any>('');
  const editChapterVisible = ref<any>(false);
  const editChapterData = ref<any>({});
  const treeLoading = ref<any>(false);
  const referFromLibraryVisible = ref(false);
  const duplicateModalVisible = ref(false);
  const duplicateOptions = ref<any>({
    includeSub: true,
    courseId: props.courseId,
  });

  const emit = defineEmits(['switchChapter']);

  const handleShowCreateChapter = ({ parent, editData }: any) => {
    let title: string;
    if (editData?.id) {
      title = '编辑章节';
      editChapterData.value = cloneDeep(editData);
    } else if (parent) {
      // title = `在 ${parent.number} ${parent.name} 下创建章节`;
      title = `在 ${parent.name} 下创建章节`;
      editChapterData.value = {
        parentId: parent.id,
      };
    } else {
      title = '创建顶级章节';
    }
    createChapterTitle.value = title;
    editChapterVisible.value = true;
  };

  const chapterSchema = SchemaHelper.getInstanceByApi(props.apiAlias);
  let chapterApi: any;

  const loadChapters = async () => {
    if (!props.courseId) return;
    treeLoading.value = true;
    chaptersTree.value = [];
    try {
      const { data } = await chapterApi.fetchList({
        courseId: props.courseId,
      });

      chaptersTree.value = data || [];
    } finally {
      treeLoading.value = false;
    }
  };

  const handleSaveChapter = async () => {
    const { id } = editChapterData.value;
    treeLoading.value = true;
    try {
      if (id) {
        const { data } = await chapterApi.updateRecord({
          ...editChapterData.value,
          courseId: props.courseId,
          id,
        });
        currentChapter.value = data;
      } else {
        const { data } = await chapterApi.createRecord({
          ...editChapterData.value,
          courseId: props.courseId,
        });
        currentChapter.value = data;
      }

      editChapterVisible.value = false;
      editChapterData.value = {};

      Message.success('保存章节成功');
      await loadChapters();

      emit('switchChapter', currentChapter.value);
    } catch (error) {
      Message.error('保存章节失败');
    } finally {
      treeLoading.value = false;
    }
  };
  const handleHideCreateChapter = () => {
    editChapterVisible.value = false;
    editChapterData.value = {};
  };

  const handleSwitchChapter = async (selectedKeys: (string | number)[], data: any) => {
    currentChapter.value = {
      ...data.node,
    };
    emit('switchChapter', data.node);
  };

  const handleActionSelect = (key: string) => {
    if (key === 'addSub') {
      handleShowCreateChapter({ parent: currentChapter.value });
    } else if (key === 'addSib') {
      handleShowCreateChapter({ parent: currentChapter.value?.parent });
    } else if (key === 'edit') {
      handleShowCreateChapter({ editData: currentChapter.value });
    } else if (key === 'delete') {
      Modal.confirm({
        title: '删除章节',
        // content: `确定删除 【${currentChapter.value.number} ${currentChapter.value.name}】 吗？(此操作不可恢复，且所有下级章节及章节内容将会被一同删除，请再次确认！)`,
        content: `确定删除 【${currentChapter.value.name}】 吗？(此操作不可恢复，且所有下级章节及章节内容将会被一同删除，请再次确认！)`,
        onOk: async () => {
          try {
            await request(`${props.apiAlias}/${currentChapter.value.id}`, {
              method: 'DELETE',
              baseURL: PROJECT_URLS.GO_PROJECT_API,
            });
            currentChapter.value = {};
            Message.success('删除章节成功');
            emit('switchChapter', undefined);
            await loadChapters();
          } catch (error) {
            Message.error('删除章节失败');
          }
        },
      });
    } else if (key === 'duplicate') {
      duplicateModalVisible.value = true;
      duplicateOptions.value = {
        ...duplicateOptions.value,
        name: currentChapter.value.name,
        chapterId: currentChapter.value.id,
      };
    }
  };

  const handleResortChapters = async (info: any) => {
    const { dragNode, dropNode, dropPosition } = info;
    treeLoading.value = true;
    try {
      await request(`${props.apiAlias}/move-node`, {
        method: 'PUT',
        data: {
          fromId: dragNode.id,
          toId: dropNode.id,
          position: dropPosition,
        },
      });
      Message.success('章节排序成功');
      await loadChapters();
    } catch (error) {
      Message.error('章节排序失败');
    } finally {
      treeLoading.value = false;
    }
  };

  const getCurrentChapterPath = () => {
    const paths = getLeafParentPath(chaptersTree.value, currentChapter.value.id);
    return paths.map((item: any) => item.name).join(' / ');
  };

  const handleShowReferFromLibrary = () => {
    referFromLibraryVisible.value = true;
  };

  const handleChapterRefer = (record: any) => {
    editChapterData.value = {
      ...editChapterData.value,
      reference: record,
      referenceSourceId: record.id,
      name: record.name,
      description: record.description,
      content: record.content,
    };
    editChapterVisible.value = true;
    referFromLibraryVisible.value = false;
  };

  const handleDuplicate = async () => {
    treeLoading.value = true;
    try {
      console.log({
        ...duplicateOptions.value,
        courseId: props.courseId,
      });

      // return false;
      await chapterDuplicate({
        ...duplicateOptions.value,
        courseId: props.courseId,
      });

      await loadChapters();
    } finally {
      treeLoading.value = false;
    }
  };

  defineExpose({
    switchLoading,
    setSwitchLoading,
    currentChapter,
    chaptersTree,
    getCurrentChapterPath,
  });

  onMounted(async () => {
    chapterApi = CommonApi.getInstance(chapterSchema);
    await loadChapters();
  });
</script>

<template>
  <div class="chapters-wrapper">
    <a-spin :loading="treeLoading" style="width: 100%">
      <div v-if="editable" class="actions">
        <a-dropdown-button
          v-if="currentChapter?.id"
          size="mini"
          @click="() => handleShowCreateChapter({})"
          @select="handleActionSelect"
        >
          <a-space>
            <IconPlus />
            添加一级目录
          </a-space>
          <template #icon>
            <icon-down />
          </template>
          <template v-if="currentChapter?.id" #content>
            <a-dgroup title="创建章节">
              <a-doption value="addSub">
                <IconPlus />
                <!--                在 {{ currentChapter.number }} {{ currentChapter.name }} 下添加-->
                在 {{ currentChapter.name }} 下添加
              </a-doption>
              <a-doption value="addSib">
                <IconPlus />
                在 {{ currentChapter.name }} 同级添加
                <!--                在 {{ currentChapter.number }} {{ currentChapter.name }} 同级添加-->
              </a-doption>
            </a-dgroup>
            <a-dgroup title="管理当前章节">
              <a-doption value="duplicate">
                <IconCopy />
                复制 {{ currentChapter.name }}
                <!--                修改 {{ currentChapter.number }} {{ currentChapter.name }}-->
              </a-doption>
              <a-doption value="edit">
                <IconEdit />
                修改 {{ currentChapter.name }}
                <!--                修改 {{ currentChapter.number }} {{ currentChapter.name }}-->
              </a-doption>
              <a-doption value="delete">
                <IconDelete />
                删除 {{ currentChapter.name }}
                <!--                删除 {{ currentChapter.number }} {{ currentChapter.name }}-->
              </a-doption>
            </a-dgroup>
          </template>
        </a-dropdown-button>
        <a-button v-else size="mini" class="fixed" @click="handleShowCreateChapter">
          <a-space>
            <IconPlus />
            添加一级目录
          </a-space>
        </a-button>
      </div>
      <a-tree
        v-if="chaptersTree?.length"
        :data="chaptersTree"
        :field-names="{
          key: 'id',
          title: 'name',
          children: 'children',
        }"
        :selected-keys="currentChapter?.id ? [currentChapter.id] : undefined"
        block-node
        default-expand-all
        :draggable="editable"
        show-line
        @drop="handleResortChapters"
        @select="handleSwitchChapter"
      >
        <template #switcher-icon="data">
          <div class="flex space-x-1">
            <icon-check-circle-fill v-if="data?.submitted" style="color: skyblue" />
            <icon-minus v-if="!data?.libraryId && (data?.children || !data?.parent) && !data?.submitted" />
            <icon-drive-file v-if="data?.parent && !data?.libraryId && !data?.children" />
            <icon-share-alt v-if="data?.libraryId" style="color: green" />
          </div>
        </template>
        <template #title="data">
          <!--!currentChapter?.referenceSourceId && currentChapter?.libraryId-->
          <a-tooltip :content="data.name">
            <div class="node-name">
              <!--              {{ data.number }}-->
              {{ data.name }}
            </div>
          </a-tooltip>
          <a-link v-if="editable" class="action" @click.stop="() => handleShowCreateChapter({ parent: data })">
            <IconPlusCircleFill />
          </a-link>
        </template>
      </a-tree>
      <a-empty v-else description="暂无章节，请先添加" />
    </a-spin>
    <a-modal
      v-model:visible="editChapterVisible"
      :on-before-ok="handleSaveChapter"
      :title="createChapterTitle"
      @cancel="handleHideCreateChapter"
      @close="handleHideCreateChapter"
    >
      <a-form :model="editChapterData" layout="vertical">
        <a-form-item required label="章节名称" layout="horizontal">
          <a-input v-model="editChapterData.name" />
        </a-form-item>

        <a-form-item
          label="引用教材说明"
          tooltip="说明：教学使用的教材非本教学科目正常教授内容，需在此处标注引用的教材版本以及年级上下册信息。"
        >
          <a-textarea v-model="editChapterData.description" placeholder="请输入" />
        </a-form-item>

        <a-form-item v-if="editChapterData.referenceSourceId" label="引用章节">
          <a-space>
            <a-tag color="green">
              <template #icon>
                <IconCheck />
              </template>
              是
            </a-tag>
            您可以自行修改章节标题和内容
          </a-space>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="flex items-center justify-between">
          <a-button
            v-if="userStore.isAuthorized('teaching:teachingImpl:course:refer')"
            type="outline"
            size="small"
            @click="handleShowReferFromLibrary"
          >
            <template #icon>
              <IconShareAlt />
            </template>
            从教案库引用
          </a-button>
          <a-space>
            <a-button @click="handleHideCreateChapter">取消</a-button>
            <a-button type="primary" :loading="treeLoading" @click="handleSaveChapter"> 保存</a-button>
          </a-space>
        </div>
      </template>
    </a-modal>
    <chapter-refer-from-library-modal v-model:visible="referFromLibraryVisible" @refer="handleChapterRefer" />

    <a-modal v-model:visible="duplicateModalVisible" title="章节复制" :on-before-ok="handleDuplicate">
      <a-form auto-label-width :model="duplicateOptions">
        <a-form-item label="新章节名称">
          <a-input v-model="duplicateOptions.name" />
        </a-form-item>
        <a-form-item label="包含子章节">
          <a-switch v-model="duplicateOptions.includeSub" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
  .chapters-wrapper {
    min-width: 200px;
    border-right: 1px solid #f0f0f0;
    padding-right: 10px;
    overflow-x: auto;
    max-height: 90vh;

    .actions {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
      position: sticky;
      top: 0;
      background-color: white;
      z-index: 100;
    }
  }

  :deep .arco-tree-node-drag-icon {
    margin-left: 0;
    right: auto;
    top: 5px;
    left: 0;
  }

  .node-name {
    text-overflow: ellipsis;
    overflow: hidden;
    padding-left: 10px;
    white-space: nowrap;
  }

  :deep .arco-tree-node-title {
    position: relative;

    .action {
      position: absolute;
      right: 0;
      top: 6px;
      opacity: 0;
      font-size: 18px;
    }

    &:hover .action {
      opacity: 0.8;
    }
  }
</style>
