<script setup lang="ts">
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useList } from '@repo/infrastructure/crud';
  import { onMounted, ref, watch } from 'vue';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { addToCommonCourses, removeFromCommonCourses } from '../courseUtils';

  const props = defineProps({
    queryParams: {
      type: Object,
      default: () => ({}),
    },
    emptyDescription: {
      type: String,
      default: '暂无课程，请先添加',
    },
    mode: {
      type: String,
      default: 'mine',
    },
    commonCourses: {
      type: Array,
      default: () => [],
    },
    apiAlias: {
      type: String,
      default: '/course/course',
    },
    pageSize: {
      type: Number,
      default: 20,
    },
  });
  let ossProcessor: IAttachmentProcessor;

  const thumbWidth = 133;
  const thumbHeight = 180;

  const ready = ref<any>(false);

  const emits = defineEmits(['editCourse', 'commonCoursesUpdated']);

  const schema = SchemaHelper.getInstanceByApi(props.apiAlias);
  const { listInit, loadData, listData, loading, setLoading } = useList<Record<string, any>>({
    schema,
    pageSize: props.pageSize,
  });

  const handleEditCourse = (course: any) => {
    emits('editCourse', course);
  };

  const handleAddToCommon = async (id: number) => {
    setLoading(true);
    try {
      const data = await addToCommonCourses(id);
      emits('commonCoursesUpdated', data);
    } finally {
      setLoading(false);
    }
  };
  const handleRemoveFromCommon = async (id: number) => {
    setLoading(true);
    try {
      const data = await removeFromCommonCourses(id);
      emits('commonCoursesUpdated', data);
    } finally {
      setLoading(false);
    }
  };

  watch(
    () => props.queryParams,
    async () => {
      if (ready.value) {
        await loadData(props.queryParams);
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  defineExpose({
    listData,
    loadData,
  });

  onMounted(async () => {
    ossProcessor = getOssProcessor({
      saveTarget: 'AliyunOSS',
    });
    await listInit();
    await loadData();
    ready.value = true;
  });
</script>

<template>
  <a-spin :loading="loading">
    <div class="course-list">
      <div
        v-for="(item, index) in listData as any[]"
        :key="index"
        class="course-item relative"
        style="display: inline-block; padding: 0; margin-bottom: 10px"
      >
        <div class="thumb">
          <a-image
            alt="暂无封面"
            :width="thumbWidth"
            :height="thumbHeight"
            fit="contain"
            :src="ossProcessor.thumbUrl(item.coverImage, thumbWidth, thumbHeight, 'fill')"
          />
        </div>
        <div class="title">
          <div class="name">{{ item.category }}</div>
          <div class="description"> {{ item.grade }} {{ item.period }}</div>
          <div class="buttons">
            <slot name="buttons" :record="item">
              <slot :record="item" name="default-actions">
                <a-button size="mini" type="primary">课程详情</a-button>
                <a-button
                  v-if="commonCourses?.findIndex((c: any) => c.id === item.id) === -1"
                  class="mt"
                  size="mini"
                  type="primary"
                  @click="() => handleAddToCommon(item.id)"
                  >添加至常用
                </a-button>
                <a-button
                  v-else
                  class="mt"
                  size="mini"
                  status="danger"
                  type="primary"
                  @click="() => handleRemoveFromCommon(item.id)"
                  >移除常用
                </a-button>
              </slot>
              <a-button v-if="mode === 'mine'" size="mini" class="mt" @click="() => handleEditCourse(item)"
                >修改课程信息</a-button
              >
            </slot>
            <slot name="extra-actions"></slot>
          </div>
        </div>
        <slot name="extra" :record="item"></slot>
      </div>
    </div>
    <a-empty v-if="!listData.length" :description="emptyDescription" class="mt" />
  </a-spin>
</template>

<style scoped lang="less">
  .course-list {
    display: flow;
    flex-wrap: nowrap;
    overflow-x: auto;
  }

  .course-item {
    margin-right: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f2f2f2;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    .thumb {
      overflow: hidden;
      border-radius: 4px;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
      img {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      transition: bottom 0.3s;
      position: absolute;
      bottom: -100%;
      height: 100%;
      width: 100%;
      margin-top: 8px;
      background: linear-gradient(180deg, rgba(#444, 0.6) 0%, rgba(#444, 1) 100%);
      .name {
        margin: 20px 10px 10px;
        font-size: 14px;
        color: #f2f2f2;
        text-shadow: 0px -2px 2px rgba(0, 0, 0, 0.6);
      }
      .description {
        color: #f2f2f2;
        font-size: 11px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        margin: 0 10px;
      }
    }

    .buttons {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin: 30px 10px 10px;

      .arco-button {
        margin-bottom: 10px;
      }
    }

    &:hover {
      border-color: var(--color-primary-light-4);
      .thumb {
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
      }
      .title {
        bottom: 0;
      }
    }
  }
</style>
