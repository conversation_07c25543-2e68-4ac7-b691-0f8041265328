<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { getClientRole } from '@repo/env-config';
  import { AttachmentsPreviewDisplay, AvatarDisplay } from '@repo/ui/components/data-display/components';
  import WebPrinter from '@repo/ui/components/data-display/webPrinter.vue';
  import TeachingArchiveDetail from '../teachingArchive/teachingArchiveDetail.vue';
  import AnnotationSidebar from '../../common/annotatable/annotationSidebar.vue';
  import { AnnotationModuleSource, getAnnotationBlocks } from '../../utils/annotationBlock';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
    coursesMap: {
      type: Object,
      required: true,
    },
    teachersMap: {
      type: Object,
      required: true,
    },
    showRefer: {
      type: Boolean,
      default: true,
    },
  });

  const currentPreview = computed(() => props.record);
  const annotationSidebarRef = ref<any>(null);

  const printAreaId = computed(() => {
    if (!currentPreview.value?.id) {
      return '';
    }

    return `teaching-archive-detail-${currentPreview.value.id}`;
  });

  const client = getClientRole();

  const emit = defineEmits(['update:visible', 'refer']);

  const previewVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleRefer = () => {
    previewVisible.value = false;
    emit('refer', currentPreview.value);
  };

  const annotationItems = ref<any[]>([]);
  const annotationModule = ref<AnnotationModuleSource>({
    module: 'CHAPTER_CONTENT_DOCUMENT',
    sourceId: 0,
  });

  const handleOpen = async () => {
    if (currentPreview.value?.id) {
      annotationModule.value = {
        module: 'CHAPTER_CONTENT_DOCUMENT',
        sourceId: currentPreview.value.originId || currentPreview.value.id,
      };
      annotationItems.value = await getAnnotationBlocks(annotationModule.value);
    }
  };

  const handleClose = () => {
    annotationItems.value = [];
  };
</script>

<template>
  <a-modal v-model:visible="previewVisible" fullscreen title="教案预览" @open="handleOpen" @close="handleClose">
    <div class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner flex gap-2">
      <div class="flex-1">
        <div :id="printAreaId" class="bg-white py-4 px-5 mx-auto page shadow-xl">
          <a-descriptions v-if="currentPreview?.id" title="课程信息" :column="4">
            <a-descriptions-item label="课程名称" colspan="4">{{ currentPreview.name }}</a-descriptions-item>
            <a-descriptions-item label="原创教师">
              <avatar-display mode="capsule" :user-info="teachersMap[currentPreview.createdById]" />
            </a-descriptions-item>
            <a-descriptions-item label="科目">{{ coursesMap[currentPreview.category]?.name }}</a-descriptions-item>
            <a-descriptions-item label="年级"
              >{{ currentPreview.grade }} {{ currentPreview.period }}</a-descriptions-item
            >
            <a-descriptions-item label="教材版本">{{ currentPreview.description }}</a-descriptions-item>
            <a-descriptions-item v-if="currentPreview?.content?.attachment?.length" label="课件">
              <!--<a-descriptions-item v-if="currentPreview?.content?.lessonPrepareAttachments?.length" label="课件">-->
              <!--<attachments-preview-display :raw="currentPreview?.content?.lessonPrepareAttachments" />-->
              <attachments-preview-display :raw="currentPreview?.content?.attachment" />
            </a-descriptions-item>
          </a-descriptions>
          <teaching-archive-detail
            v-if="currentPreview && previewVisible"
            v-model:annotations="annotationItems"
            :annotation-module="annotationModule"
            :chapter="currentPreview"
            current-directory=""
            :show-assessment="false"
          />
        </div>
      </div>
      <annotation-sidebar
        v-model:annotations="annotationItems"
        :related-modules="['student']"
        :annotation-module="annotationModule"
        annotation-category="调整"
      />
    </div>
    <template #footer>
      <div class="flex gap-2 items-center justify-end">
        <web-printer v-if="printAreaId" :selector="printAreaId" />
        <a-popconfirm
          v-if="showRefer && client === 'Company'"
          content="确定要引用此篇教案吗？"
          @ok="() => handleRefer()"
        >
          <a-button type="outline">
            <template #icon>
              <IconShareInternal />
            </template>
            引用
          </a-button>
        </a-popconfirm>
        <a-button type="primary" @click="previewVisible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .paper-wrapper {
    min-height: calc(100vh - 160px);
    overflow-y: scroll;
    .page {
      width: 1000px;
    }
  }
</style>
