<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import QuestionUsageList from '../../questionLibrary/questionUsageList.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    criteria: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'update:criteria']);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const result = ref(props.criteria.testResult || {});

  const handleBeforeOpen = () => {
    result.value = props.criteria.testResult;
  };

  const handleOk = () => {
    emit('update:criteria', {
      ...props.criteria,
      testResult: result.value,
    });
    modalVisible.value = false;
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="criteria?.name"
    :width="700"
    ok-text="完成"
    @before-open="handleBeforeOpen"
    @ok="handleOk"
  >
    <question-usage-list
      v-if="modalVisible && criteria"
      v-model:result="result"
      :course-id="course?.id"
      :criteria="criteria"
      :question-ids="criteria.questionIds"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
