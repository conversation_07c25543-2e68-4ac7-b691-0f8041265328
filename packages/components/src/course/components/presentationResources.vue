<script setup lang="ts">
  import { computed, nextTick, PropType, ref, watch } from 'vue';
  import UploaderButton from '@repo/ui/components/upload/uploader.vue';
  import openapi from '@repo/infrastructure/openapi';
  import { PPTPage } from '../../wps/types';
  import { formatUploadedFileList, ResourceSelectedItem } from '../../resource/constants';
  import PresentationResourcesList from './presentationResourcesList.vue';
  import DigitalResourceSelect from '../../resource/digital/components/digitalResourceSelect.vue';

  const props = defineProps({
    slide: {
      type: Object as PropType<PPTPage | undefined>,
      required: true,
    },
    chapter: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const localUploadRef = ref<any>();
  const emit = defineEmits(['update:chapterContent']);
  const resourcesList = ref<any[]>([]);

  const refreshResources = async (slide: PPTPage | undefined) => {
    resourcesList.value = [];
    props.chapter.content?.presentationItems?.forEach((item: any) => {
      if (item.slideId === slide?.slideId) {
        resourcesList.value.push(item);
      }
    });
  };

  const selectedLibResource = computed({
    get: () => {
      return resourcesList.value
        .filter((item: any) => item.source === 'Lib')
        .map((item: any) => {
          return {
            id: item.resourceId,
            type: item.type,
          } as ResourceSelectedItem;
        });
    },
    set: async (val: ResourceSelectedItem[]) => {
      const raw = props.chapter?.content || {};
      raw.presentationItems = raw.presentationItems || [];
      raw.presentationItems = raw.presentationItems.filter((item: any) => item.source !== 'Lib');

      const resList: any[] = val.map((item: ResourceSelectedItem) => ({
        slideId: props.slide?.slideId,
        resourceId: item.id,
        type: item.type,
      }));
      raw.presentationItems = [...raw.presentationItems, ...resList];

      await openapi.course.updateChapterContent(
        {
          id: props.chapter.id!,
        },
        raw,
      );
      const { data: chapter } = await openapi.course.getChapter({
        id: props.chapter.id!,
      });

      emit('update:chapterContent', chapter, raw);
      await nextTick(() => {
        refreshResources(props.slide);
      });
    },
  });

  const handleDeleteResource = async (index: number) => {
    const raw = props.chapter?.content || {};
    raw.presentationItems = raw.presentationItems || [];
    raw.presentationItems.splice(index, 1);
    await openapi.course.updateChapterContent(
      {
        id: props.chapter.id!,
      },
      raw,
    );
    const { data: chapter } = await openapi.course.getChapter({
      id: props.chapter.id!,
    });
    emit('update:chapterContent', chapter, raw);
    await nextTick(() => {
      refreshResources(props.slide);
    });
  };

  const handleUploaded = async (files: any[]) => {
    const raw = props.chapter?.content || {};
    raw.presentationItems = raw.presentationItems || [];
    const resources = formatUploadedFileList(files, localUploadRef.value.processor);

    const { data } = await openapi.resource.batchCreateDigitalResource({
      source: 'User' as any,
      resources,
    });

    raw.presentationItems = raw.presentationItems.concat(
      data.map((resource: any) => ({
        slideId: props.slide?.slideId,
        resourceId: resource.id,
        type: resource.type,
      })),
    );
    await openapi.course.updateChapterContent(
      {
        id: props.chapter.id!,
      },
      raw,
    );
    const { data: chapter } = await openapi.course.getChapter({
      id: props.chapter.id!,
    });
    emit('update:chapterContent', chapter, raw);
    await nextTick(() => {
      refreshResources(props.slide);
    });
  };

  watch(
    () => props.slide,
    async (newVal: any) => {
      await refreshResources(newVal);
    },
  );
</script>

<template>
  <a-card v-if="slide?.slideId" class="resource-wrapper">
    <template #title>
      <small> 第 {{ slide.slideIndex + 1 }} 页 </small>
    </template>
    <template #extra>
      <a-space>
        <a-button size="mini">
          <template #icon>
            <IconPlus />
          </template>
          添加活动
        </a-button>
        <uploader-button
          ref="localUploadRef"
          button-text="本地上传"
          save-type="digital-resource"
          accept="video/*,audio/*,image/*,text/html"
          :sub-folder="`user-upload/${chapter.id}`"
          @uploaded="handleUploaded"
        >
          <template #upload-button>
            <a-button
              :loading="localUploadRef?.loading"
              size="mini"
              type="secondary"
              style="position: relative; top: -1px"
            >
              <template #icon>
                <IconUpload />
              </template>
              本地上传
            </a-button>
          </template>
        </uploader-button>
        <digital-resource-select v-model="selectedLibResource" />
      </a-space>
    </template>
    <div>
      <presentation-resources-list :resources="resourcesList" @delete-resource="handleDeleteResource" />
    </div>
  </a-card>
</template>

<style scoped lang="less"></style>
