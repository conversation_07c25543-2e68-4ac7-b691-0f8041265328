<script setup lang="ts">
  import { computed } from 'vue';
  import ChapterLibraryList from './chapterLibraryList.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'refer']);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleRefer = (record) => {
    emit('refer', record);
  };
</script>

<template>
  <a-modal v-model:visible="modalVisible" title="从教案库引用" fullscreen hide-cancel>
    <chapter-library-list @refer="(record) => handleRefer(record)" />
  </a-modal>
</template>
