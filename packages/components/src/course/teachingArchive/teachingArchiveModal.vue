<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { getChapter } from '@repo/infrastructure/openapi/course';
  import { getChapterAssessmentList } from '@repo/infrastructure/openapi/chapterAssessmentController';
  import { merge } from 'lodash';
  import WebPrinter from '@repo/ui/components/data-display/webPrinter.vue';
  import { AttachmentsPreviewDisplay, AvatarDisplay } from '@repo/ui/components/data-display/components';
  import ChapterManage from '../components/chapterManage.vue';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';
  import TeachingArchiveDetail from './teachingArchiveDetail.vue';
  import { AnnotationModuleSource, getAnnotationBlocks } from '../../utils/annotationBlock';
  import AnnotationSidebar from '../../common/annotatable/annotationSidebar.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    courseId: {
      type: Number,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const { loading: chapterLoading, setLoading: setChapterLoading } = useLoading();

  const course = ref<any>(null);
  const chapter = ref<any>(null);
  const rawAssessResults = ref<any[]>([]);
  const assessResultsViaStudent = ref<any[]>([]);
  const assessResultsViaCriteriaMap = ref<any>({});
  const courseStore = useSchoolCourseStore();
  const coursesMap = ref({});
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  const annotationItems = ref<any[]>([]);
  const annotationModule = ref<AnnotationModuleSource>(null);

  // load course info and chapters
  const loadBasicInfo = async () => {
    const { data: rawCourse } = await request(`/course/course/${props.courseId}`);
    course.value = rawCourse;
  };

  const printAreaId = computed<string>(() => {
    if (!chapter.value?.id) {
      return null;
    }

    return `teaching-archive-detail-${chapter.value.id}`;
  });

  const handleOpen = async () => {
    await loadBasicInfo();
  };

  const handleClose = () => {
    course.value = null;
    chapter.value = null;
    annotationItems.value = [];
  };

  const handleChapterSwitch = async (selectedChapter) => {
    setChapterLoading(true);
    chapter.value = null;
    try {
      const { data } = await getChapter({
        id: selectedChapter.id,
      });
      chapter.value = data;

      const { data: raw } = await getChapterAssessmentList({
        chapterId: selectedChapter.id,
      });

      annotationModule.value = {
        module: 'CHAPTER_CONTENT_DOCUMENT',
        sourceId: selectedChapter.originId || selectedChapter.id,
      };
      annotationItems.value = await getAnnotationBlocks(annotationModule.value);

      rawAssessResults.value = raw;

      const avs: any = {};
      const avc: any = {};
      raw?.forEach((item) => {
        const gradeClass = item.student.udf1 || '未分配班级';
        avs[gradeClass] = avs[gradeClass] || {};
        avs[gradeClass][item.student.id] = avs[gradeClass][item.student.id] || {
          id: `student-${item.student.id}`,
          name: item.student.name,
        };
        // merge pre and after score
        if (avs[gradeClass][item.student.id].children) {
          avs[gradeClass][item.student.id].children = merge(avs[gradeClass][item.student.id].children, item.scores);
        } else {
          avs[gradeClass][item.student.id].children = item.scores;
        }

        avs[gradeClass][item.student.id][item.type] = item.totalScore;

        item.scores?.forEach((s) => {
          if (s.children?.length) {
            s.children.forEach((c) => {
              avc[c.id] = avc[c.id] || {
                id: c.id,
                name: c.name,
                gradeClass,
                students: [],
              };

              const exists = avc[c.id].students.find((st) => st.id === item.student.id);
              if (!exists) {
                avc[c.id].students.push({
                  id: item.student.id,
                  name: item.student.name,
                  gradeClass,
                  [item.type]: c[item.type],
                });
              } else {
                exists[item.type] = c[item.type];
                avc[c.id].students = avc[c.id].students.map((st) => {
                  if (st.id === item.student.id) {
                    return exists;
                  }
                  return st;
                });
              }
            });
          } else {
            avc[s.id] = avc[s.id] || {
              id: s.id,
              name: s.name,
              gradeClass,
              students: [],
            };

            const exists = avc[s.id].students.find((st) => st.id === item.student.id);
            if (!exists) {
              avc[s.id].students.push({
                id: item.student.id,
                name: item.student.name,
                gradeClass,
                [item.type]: s.score,
              });
            } else {
              exists[item.type] = s.score;
              avc[s.id].students = avc[s.id].students.map((st) => {
                if (st.id === item.student.id) {
                  return exists;
                }
                return st;
              });
            }
          }
        });
      });

      assessResultsViaCriteriaMap.value = avc;

      const avsRes: any[] = [];
      Object.keys(avs).forEach((gradeClass) => {
        const students = Object.values(avs[gradeClass]).map((item: any) => {
          return {
            ...item,
            pre: undefined,
            after: undefined,
          };
        });
        avsRes.push({
          id: `gradeClass-${gradeClass}`,
          name: gradeClass,
          children: students,
        });
      });

      assessResultsViaStudent.value = avsRes;
    } finally {
      setChapterLoading(false);
    }
  };

  const chapterManageRef = ref<any>();

  const currentDirectory = computed(() => {
    if (chapterManageRef.value) {
      const chapterPath = chapterManageRef.value.getCurrentChapterPath();
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      chapter.value.number = chapterManageRef.value.currentChapter.number;
      return chapterPath;
    }
    return '';
  });

  onMounted(async () => {
    coursesMap.value = await courseStore.getSchoolCoursesMap();
  });
</script>

<template>
  <a-modal
    v-model:visible="visible"
    fullscreen
    :hide-cancel="true"
    ok-text="完成"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #title>
      教学档案
      <div v-if="chapter?.id"> - {{ chapterManageRef?.currentChapter?.number }} {{ currentDirectory }} </div>
    </template>
    <div v-if="visible" class="flex gap-2">
      <div class="w-64">
        <chapter-manage
          ref="chapterManageRef"
          :course-id="courseId"
          :editable="false"
          @switch-chapter="handleChapterSwitch"
        />
      </div>
      <div class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner flex gap-2">
        <div class="flex-1">
          <div :id="printAreaId" class="bg-white py-4 px-5 mx-auto page shadow-xl">
            <a-descriptions v-if="course?.id" title="课程信息" :column="4">
              <a-descriptions-item label="教师">
                <avatar-display mode="capsule" :user-info="course.createdBy" />
              </a-descriptions-item>
              <!--            <a-descriptions-item label="课程名称">{{ course.name }}</a-descriptions-item>-->
              <a-descriptions-item label="科目">{{ coursesMap[course.category]?.name }}</a-descriptions-item>
              <a-descriptions-item label="年级">{{ course.grade }} {{ course.period }}</a-descriptions-item>
              <a-descriptions-item label="教材版本">{{ course.description }}</a-descriptions-item>
              <a-descriptions-item label="上课时间">{{ chapter?.classTime || '-' }}</a-descriptions-item>
              <a-descriptions-item v-if="chapter?.content?.lessonPrepareAttachments?.length" label="课件">
                <attachments-preview-display :raw="chapter?.content?.lessonPrepareAttachments" />
              </a-descriptions-item>

              <a-descriptions-item label="引用教材说明">{{ chapter?.description || '-' }}</a-descriptions-item>
            </a-descriptions>
            <a-divider :margin="10" />
            <a-spin class="w-full" :loading="chapterLoading">
              <teaching-archive-detail
                v-if="chapter && chapter.content && !chapterLoading"
                v-model:annotations="annotationItems"
                :chapter="chapter"
                :current-directory="currentDirectory"
                :assess-results-via-student="assessResultsViaStudent"
                :assess-results-via-criteria-map="assessResultsViaCriteriaMap"
                :annotation-module="annotationModule"
              />
              <a-empty v-else-if="!chapter" description="请先在左侧选择章节" />
              <a-empty v-else-if="chapter && !chapter.content" description="所选章节无内容" />
            </a-spin>
          </div>
        </div>

        <annotation-sidebar
          v-model:annotations="annotationItems"
          :related-modules="['student']"
          :annotation-module="annotationModule"
          annotation-category="调整"
        />
      </div>
    </div>
    <template #footer>
      <web-printer v-if="printAreaId" :selector="printAreaId" />
      <a-button type="primary" @click="visible = false">完成</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .paper-wrapper {
    height: calc(100vh - 160px);
    overflow-y: scroll;
    .page {
      width: 800px;
    }
  }
</style>
