<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import ReferToIepModal from '../../components/referToIepModal.vue';
  import { getLeafParentPath } from '../../../utils/assessment';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object as PropType<any>,
      required: true,
    },
    students: {
      type: Array as PropType<any[]>,
      required: true,
    },
    target: {
      type: Object as PropType<any>,
      required: true,
    },
    sequence: {
      type: String,
    },
    resultDataMap: {
      type: Object as PropType<any>,
      required: true,
    },
    type: {
      type: String as PropType<'pre' | 'after'>,
      default: 'pre',
    },
    chaptersTree: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const emit = defineEmits(['update:resultDataMap']);

  const currentStudent = ref<Record<string, any>>({});

  const scoresResult = computed({
    get: () => {
      const result = props.resultDataMap;
      props.students?.forEach((s) => {
        if (!result[s.id]) {
          result[s.id] = {
            scoresMap: {},
          };
        }
        if (!result[s.id].scoresMap) {
          result[s.id].scoresMap = {};
        }
        if (!result[s.id].scoresMap[props.target.id]) {
          result[s.id].scoresMap[props.target.id] = undefined;
        }
      });
      return result;
    },
    set: (value) => {
      emit('update:resultDataMap', value);
    },
  });

  const getPreScore = (studentId: any) => {
    if (!props.resultDataMap[studentId]?.preScoresMap) {
      return undefined;
    }
    return props.resultDataMap[studentId].preScoresMap[props.target.id];
  };

  const radioDisabled = (studentId: any) => {
    return props.type === 'after' && getPreScore(studentId) === undefined;
  };

  const referToIepVisible = ref(false);
  const currentCriterion = ref<any>({});

  const handleShowReferToIep = (student: any, target: any, preScore: any) => {
    console.log(scoresResult.value, student.id, target.id);
    currentStudent.value = student;
    currentCriterion.value = {
      ...target,
      preScore,
      afterScore: scoresResult.value[student.id].scoresMap[target.id],
    };
    referToIepVisible.value = true;
  };
  const handleHideReferToIep = () => {
    referToIepVisible.value = false;
    currentCriterion.value = {};
  };

  defineExpose({
    getPreScore,
    radioDisabled,
    handleShowReferToIep,
  });
</script>

<template>
  <div class="bg-white rounded p-2">
    <div v-if="sequence" class="text-base p-2"> {{ sequence }}、{{ target.name }} </div>
    <div v-for="(s, i) in students" :key="i" class="p-2 flex items-center gap-2">
      <div class="w-24"> {{ s.name }}: </div>
      <div class="flex-1 flex gap-2 items-center">
        <a-radio-group
          v-model="scoresResult[s.id].scoresMap[target.id]"
          type="button"
          size="mini"
          :disabled="radioDisabled(s.id)"
        >
          <a-tooltip v-for="(score, idx) in course.teachingAssessScores" :key="idx" :content="score.name">
            <a-radio :value="score.id"> {{ score.id }} ({{ score.name }}) </a-radio>
          </a-tooltip>
        </a-radio-group>
        <div v-if="type === 'after'" class="flex gap-2 items-center">
          <div v-if="radioDisabled(s.id)">
            <a-tooltip content="请先该学生的完成前测">
              <a-button type="outline" size="mini" shape="circle" status="danger" :disabled="true">
                <template #icon>
                  <IconQuestion />
                </template>
              </a-button>
            </a-tooltip>
          </div>
          <small v-else> (前测：{{ getPreScore(s.id) }}) </small>
          <a-space v-if="scoresResult[s.id].scoresMap[target.id]">
            <a-tooltip content="手动关联至IEP教学实施目标">
              <a-button
                v-if="!target.children?.length"
                size="mini"
                @click="() => handleShowReferToIep(s, target, getPreScore(s.id))"
              >
                <template #icon>
                  <IconShareExternal />
                </template>
              </a-button>
            </a-tooltip>
          </a-space>
        </div>
      </div>
    </div>
  </div>

  <refer-to-iep-modal
    v-model:visible="referToIepVisible"
    :criterion="currentCriterion"
    :student="currentStudent"
    :course="course"
    :chapters-tree="chaptersTree"
    :chapter="chapter"
    @hide="handleHideReferToIep"
  />
</template>

<style scoped lang="scss"></style>
