<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import StudentSelect from '../../../student/studentSelect.vue';
  import StudentSelectDeluxeEdition from '../../../student/studentSelectDeluxeEdition.vue';

  const props = defineProps({
    target: {
      type: Object,
      required: true,
    },
    chapter: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:target']);
  const selectedStudents = computed({
    get: () => props.target.students || [],
    set: (value) => {
      emit('update:target', {
        ...props.target,
        students: value,
      });
    },
  });
  const removedStudentIds = ref<number[]>([]);

  const handleStudentChange = (studentId: number, student: any) => {
    if (selectedStudents.value.some((s) => s.id === studentId)) {
      return;
    }
    removedStudentIds.value = removedStudentIds.value.filter((id) => id !== studentId);
    selectedStudents.value = [
      ...selectedStudents.value,
      {
        id: studentId,
        name: student.name,
      },
    ];
  };
  const handleMultiStudentChange = (studentIds: number[], raw: any) => {
    if (raw) {
      removedStudentIds.value = removedStudentIds.value.filter((id) => !studentIds.includes(id));
      const recordIds = selectedStudents.value.map((item) => item.id);
      const students: any = [];
      raw?.forEach((item: any) => {
        if (!recordIds.includes(item.id))
          students.push({
            id: item.id,
            name: item.name,
          });
      });
      selectedStudents.value = [...selectedStudents.value, ...students];
    }
  };

  const handleRemove = (studentId: number) => {
    selectedStudents.value = selectedStudents.value.filter((student) => student.id !== studentId);
    if (studentId) removedStudentIds.value.push(studentId);
  };
  const handlePreOk = async () => {
    if (removedStudentIds.value.length > 0)
      await request('/course/chapter-assess', {
        baseURL: PROJECT_URLS.GO_PROJECT_API,
        method: 'put',
        params: {
          questionId: props.target?.id,
          chapterId: props.chapter?.id,
          studentIds: removedStudentIds.value,
        },
      });
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="选择学生" hide-cancel ok-text="完成" :on-before-ok="handlePreOk">
    <student-select v-if="false" :selected-ids="selectedStudents.map((s) => s.id)" @change="handleStudentChange" />
    <!--学生选择器<优化>-->
    <studentSelectDeluxeEdition
      :selected-ids="selectedStudents.map((s) => s.id)"
      :multiple="true"
      @change-multi="handleMultiStudentChange"
    />
    <div class="flex flex-wrap gap-2 mt-4">
      <a-tag
        v-for="student in selectedStudents"
        :key="student.id"
        color="arcoblue"
        closable
        @close="() => handleRemove(student.id)"
      >
        {{ student.name }}
      </a-tag>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
