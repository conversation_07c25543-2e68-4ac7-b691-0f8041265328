<script setup lang="ts">
  import { ref } from 'vue';
  import StudentSelect from '../../../student/studentSelect.vue';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter']);
  const selectedStudents = ref(props.chapter.chapterStudents || []);
  const removedStudentIds = ref<number[]>([]);

  const handleStudentChange = (studentId: number, student: any) => {
    if (selectedStudents.value.some((s) => s.id === studentId)) {
      return;
    }
    removedStudentIds.value = removedStudentIds.value.filter((id) => id !== studentId);
    selectedStudents.value = [
      ...selectedStudents.value,
      {
        id: studentId,
        name: student.name,
      },
    ];
  };

  const handleRemove = (studentId: number) => {
    selectedStudents.value = selectedStudents.value.filter((student) => student.id !== studentId);
    if (studentId) removedStudentIds.value.push(studentId);
  };

  const handleOk = async () => {
    emit('update:chapter', {
      ...props.chapter,
      chapterStudents: selectedStudents.value,
    });

    return true;
  };

  const handleOpen = () => {
    selectedStudents.value = props.chapter.chapterStudents || [];
    removedStudentIds.value = [];
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="选择学生" hide-cancel ok-text="完成" :on-before-ok="handleOk" :on-open="handleOpen">
    <student-select :selected-ids="selectedStudents.map((s) => s.id)" @change="handleStudentChange" />
    <div class="flex flex-wrap gap-2 mt-4">
      <a-tag
        v-for="student in selectedStudents"
        :key="student.id"
        color="arcoblue"
        closable
        @close="() => handleRemove(student.id)"
      >
        {{ student.name }}
      </a-tag>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
