<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import { Modal } from '@arco-design/web-vue';
  import { EditorOrDisplay } from '@repo/rich-editor';

  const defaultItems = ['教学反思'];
  const { prompt } = usePrompt();

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save']);

  const contents = computed({
    get: () => props.chapter?.content?.teachingReflection || [],
    set: (value) => {
      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          teachingReflection: value,
        },
      });
    },
  });

  const handleAddItem = async () => {
    const name = await prompt({
      title: '添加教学反思项目',
      placeholder: '请输入教学反思项目名称',
    });

    contents.value.push({
      name,
      udf1: '',
    });
    emit('save');
  };

  const handleUpdateContentName = async (index, content) => {
    const name = await prompt({
      title: '修改教学反思名称',
      placeholder: '请输入教学反思项目名称',
      raw: content.name || '教学反思',
    });

    contents.value[index].name = name;
    emit('save');
  };

  const handleDeleteContentItem = (index, content) => {
    Modal.confirm({
      title: '请确认',
      content: `确定删除该教学反思项目吗(${content.name})？`,
      onOk: () => {
        contents.value.splice(index, 1);
        emit('save');
      },
    });
  };

  const handleSave = () => {
    emit('save');
  };

  onMounted(() => {
    if (props.chapter?.content?.teachingReflection) {
      contents.value = props.chapter.content.teachingReflection || [];
    } else {
      contents.value = defaultItems.map((item) => {
        return {
          name: item,
          udf1: '',
        };
      });
    }
  });
</script>

<template>
  <div class="content-wrapper">
    <a-space>
      <a-button size="mini" type="outline" @click="handleAddItem">
        <template #icon>
          <IconPlus />
        </template>
        添加教学反思
      </a-button>
    </a-space>
    <div v-for="(c, idx) in contents" :key="idx" class="mt-4">
      <a-space>
        <div class="content-title font-medium text-base"> {{ idx + 1 }}、{{ c.name }} </div>
        <a-space>
          <IconEdit class="cursor-pointer" @click="() => handleUpdateContentName(idx, c)" />
          <IconDelete class="cursor-pointer" @click="() => handleDeleteContentItem(idx, c)" />
        </a-space>
      </a-space>
      <editor-or-display
        v-model="c.udf1"
        class="mt-2"
        :editor-style="{ minHeight: '200px', maxHeight: '200px' }"
        @save="handleSave"
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
