<script setup lang="ts">
  import { computed, nextTick, onMounted, PropType, ref, watch } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import { Message, Modal } from '@arco-design/web-vue';
  import { EditorOrDisplay } from '@repo/rich-editor';
  import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';
  import { isObject } from 'lodash';
  import FileTypeIcon from '@repo/ui/components/icon/fileTypeIcon.vue';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';
  import AnnotationSidebar from '../../common/annotatable/annotationSidebar.vue';
  import AnnotatableBlock from '../../common/annotatableBlock.vue';

  /**
   * this is the old 备课安排 changed name to 教学设计
   * new 备课安排 is in lessonResourcePrepare.vue
   */
  const defaultItems = ['内容及步骤'];
  const { prompt } = usePrompt();

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const annotationModuleSource = computed<AnnotationModuleSource>(() => {
    return {
      ...props.annotationSource,
      paragraphId: 'LESSON_PREPARE',
    };
  });

  const rawAnnotations = ref<any[]>();
  const filteredAnnotations = computed({
    get: () => {
      return rawAnnotations.value?.filter((item: any) => item.paragraphId === 'LESSON_PREPARE') || [];
    },
    set: (value) => {
      emit('update:annotations', {
        ...rawAnnotations.value?.filter((item: any) => item.paragraphId !== 'LESSON_PREPARE'),
        ...(value || []),
      });
    },
  });

  const contents = computed({
    get: () => props.chapter?.content?.lessonPrepare || [],
    set: (value) => {
      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          lessonPrepare: value,
        },
      });
    },
  });

  const attachments = computed({
    get: () => props.chapter?.content?.lessonPrepareAttachments || [],
    set: (value) => {
      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          lessonPrepareAttachments: value,
        },
      });
      nextTick(() => {
        emit('save');
      });
    },
  });

  const handleAddItem = async () => {
    const name = await prompt({
      title: '添加教学设计',
      placeholder: '请输入教学设计项目名称',
    });

    contents.value.push({
      name,
      udf1: '',
    });
    emit('save');
  };

  const handleUpdateContentName = async (index, content) => {
    const name = await prompt({
      title: '修改教学设计名称',
      placeholder: '请输入教学设计项目名称',
      raw: content.name,
    });

    contents.value[index].name = name;
    emit('save');
  };

  const handleDeleteContentItem = (index, content) => {
    Modal.confirm({
      title: '请确认',
      content: `确定删除该教学设计项目吗(${content.name})？`,
      onOk: () => {
        contents.value.splice(index, 1);
        emit('save');
      },
    });
  };

  const handleSave = () => {
    emit('save');
  };

  const rowSelection = ref({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  const selectedKeys = ref([]);

  const downloadVisible = ref(false);
  const handleDownload = () => {
    downloadVisible.value = true;
  };

  const handleBatchDownload = () => {
    const selectedUrls = selectedKeys.value;
    if (!selectedUrls || selectedUrls.length === 0) {
      return;
    }
    selectedUrls.forEach((url) => {
      fetch(url)
        .then((response) => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then((blob) => {
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = url.split('/').pop();
          link.click(); // 触发下载
          URL.revokeObjectURL(link.href);
        })
        .catch((error) => {
          Message.error('下载失败');
        });
    });
    downloadVisible.value = false;
  };

  onMounted(() => {
    if (props.chapter?.content?.lessonPrepare) {
      contents.value = props.chapter.content.lessonPrepare || [];
    } else {
      contents.value = defaultItems.map((item) => {
        return {
          name: item,
          udf1: '',
        };
      });
    }
  });

  watch(
    () => props.annotations,
    (newVal) => {
      if (!newVal) {
        rawAnnotations.value = [];
        return;
      }
      rawAnnotations.value = isObject(newVal) ? Object.values(newVal) : newVal;
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <div class="content-wrapper flex gap-2">
    <div class="flex-1">
      <a-space>
        <a-button size="mini" type="outline" @click="handleAddItem">
          <template #icon>
            <IconPlus />
          </template>
          添加教学设计
        </a-button>
        <uploader-modal v-model="attachments" btn-text-empty="上传附件" btn-text="查看附件" />
        <a-button v-if="attachments?.length > 0" size="mini" type="outline" @click="handleDownload">附件下载</a-button>
      </a-space>
      <div v-for="(c, idx) in contents" :key="idx" class="mt-4">
        <a-space>
          <div class="content-title font-medium text-base"> {{ idx + 1 }}、{{ c.name }} </div>
          <a-space>
            <IconEdit class="cursor-pointer" @click="() => handleUpdateContentName(idx, c)" />
            <IconDelete class="cursor-pointer" @click="() => handleDeleteContentItem(idx, c)" />
          </a-space>
        </a-space>
        <editor-or-display
          v-model="c.udf1"
          class="mt-2"
          :editor-style="{ minHeight: '200px', maxHeight: '200px' }"
          @save="handleSave"
        >
          <template #default="{ raw }">
            <annotatable-block
              v-model:annotations="filteredAnnotations"
              :annotation-module-source="annotationModuleSource"
              :related-modules="['student']"
              :initial-text="raw"
              annotation-category="调整"
            />
          </template>
        </editor-or-display>
      </div>
      <a-empty v-if="!contents.length" description="暂无教学设计，请先添加" />
    </div>

    <annotation-sidebar
      v-model:annotations="filteredAnnotations"
      :annotation-module="annotationModuleSource"
      :related-modules="['student']"
      annotation-category="调整"
      :fixed="false"
    />
  </div>
  <a-modal
    v-if="downloadVisible"
    width="600px"
    :visible="downloadVisible"
    title="请勾选附件以下载"
    :closable="false"
    cancel-text="关闭"
    ok-text="下载"
    size="mini"
    @ok="handleBatchDownload"
    @cancel="downloadVisible = false"
  >
    <a-table v-model:selected-keys="selectedKeys" row-key="url" :row-selection="rowSelection" :data="attachments">
      <template #columns>
        <a-table-column title="附件名称" data-index="name"> </a-table-column>
        <a-table-column title="类型" data-index="url" align="center">
          <template #cell="{ record }">
            <file-type-icon :file="record.name" :thumb-size="{ height: 20 }" :url="record.url || record.udf1" />
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
