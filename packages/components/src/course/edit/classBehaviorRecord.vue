<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import AvatarDisplay from '@repo/ui/components/data-display/components/avatarDisplay.vue';
  import { IconStarFill } from '@arco-design/web-vue/es/icon';
  import { Modal } from '@arco-design/web-vue';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);
  const behaviorRecords = computed({
    get: () => props.chapter.content?.classBehaviorRecord || {},
    set: (value) => {
      const data = {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          classBehaviorRecord: value,
        },
      };
      emit('update:chapter', data);
      emit('save');
    },
  });
  const students = computed(() => {
    return props.chapter.chapterStudents || [];
  });

  const handleAddStar = (studentId: string) => {
    behaviorRecords.value = {
      ...behaviorRecords.value,
      [studentId]: behaviorRecords.value[studentId] ? behaviorRecords.value[studentId] + 1 : 1,
    };
  };

  const handleRemoveStart = (studentId: string) => {
    Modal.confirm({
      title: '请确认',
      content: '确定要减少一颗星星吗？',
      onOk: () => {
        const raw = behaviorRecords.value;

        if (raw[studentId] && raw[studentId] > 0) {
          raw[studentId] -= 1;
          behaviorRecords.value = { ...raw };
        }
      },
      mask: false,
    });
  };
</script>

<template>
  <div v-if="behaviorRecords" class="grid grid-cols-2 sm:grid-cols-4 gap-4">
    <div
      v-for="student in students"
      :key="student.id"
      class="text-center rounded-lg bg-slate-100 p-4 hover:bg-slate-100 shadow-lg"
    >
      <div>
        <avatar-display :user-info="student" :size="60" @click="() => handleAddStar(student.id)" />
      </div>
      <div class="flex flex-wrap gap-2 items-center justify-center mt-4 text-xl text-yellow-500 cursor-pointer">
        <IconStarFill
          v-for="s in behaviorRecords[student.id]"
          :key="s"
          :title="s"
          @click="() => handleRemoveStart(student.id)"
        />
        <div v-if="!behaviorRecords[student.id] || behaviorRecords[student.id] <= 0" class="text-slate-300">
          <IconStarFill />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
