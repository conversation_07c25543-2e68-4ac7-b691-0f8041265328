<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import SparkMD5 from 'spark-md5';
  import { Message } from '@arco-design/web-vue';
  import { listTableInputModal } from '@repo/ui/components/form/inputComponents';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { collapsedNameDisplay } from '@repo/infrastructure/ui';
  import CriterionSelectModal from '../components/criterionSelectModal.vue';
  import TeacherQuestionLibraryModal from '../../teacherQuestionLibrary/teacherQuestionLibraryModal.vue';
  import SelectTargetStudentsModal from './targets/selectTargetStudentsModal.vue';
  import SelectFromIepModal from '../components/selectFromIepModal.vue';
  import TargetIepTag from './targets/targetIepTag.vue';

  // 后6位
  const getNameHash = (name: string) => {
    const spark = new SparkMD5();
    spark.append(name);
    // add timestamp
    spark.append(`${new Date().getTime()}`);
    return spark.end().toLowerCase().slice(-6);
  };

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
  });

  const { prompt } = usePrompt();
  const emit = defineEmits(['update:chapter', 'update:course', 'save']);
  const nodes = ref<any[]>([]);
  const { loading, setLoading } = useLoading();

  const selectTargetStudentVisible = ref(false);
  const currentTarget = ref<any>({});

  const handleShowSelectTargetStudent = (node: any) => {
    currentTarget.value = node;
    selectTargetStudentVisible.value = true;
  };

  const currentNode = ref<any>({});
  const questionLibVisible = ref(false);
  const teachingAssessScores = computed({
    get: () => props.course?.teachingAssessScores || [],
    set: (value) => {
      emit('update:course', {
        ...props.course,
        teachingAssessScores: value,
      });
    },
  });

  const scoreSetField = {
    key: 'teachingAssessScores',
    inputWidgetProps: {
      columns: [
        { key: 'id', label: '分值', inputWidget: 'numberInput' },
        { key: 'name', label: '显示', inputWidget: 'textInput' },
      ],
    },
  };

  const onChapterUpdated = () => {
    emit('update:chapter', {
      ...props.chapter,
      content: {
        ...props.chapter.content,
        teachingAssessCriteria: nodes.value,
      },
    });
  };

  const handleTargetUpdated = (target: any) => {
    nodes.value = nodes.value.map((node) => {
      if (node.id === target.id) {
        return target;
      }
      return node;
    });

    onChapterUpdated();
  };

  const handleNodeSelected = (node: any, form: any) => {
    nodes.value = [...nodes.value, node];
    onChapterUpdated();
    if (!props.course?.teachingAssessScores?.length) {
      if (!form.scores?.length) {
        Message.info('该量表未设置评分项目，请先手动设置');
      }
      emit('update:course', {
        ...props.course,
        teachingAssessScores:
          props.course.teachingAssessScores?.length ||
          (form.scores || []).map((item) => {
            return {
              id: item.score,
              name: item.label,
            };
          }),
      });
    }
  };

  const handleSelectFromIep = (selectedItems: any[]) => {
    nodes.value = [
      ...nodes.value,
      ...(selectedItems || []).map((item) => {
        return {
          ...item,
          id: `CTM-${getNameHash(item.name)}`,
        };
      }),
    ];
    onChapterUpdated();
  };

  const handleEditTopNode = async (node?: any) => {
    const name = await prompt({
      title: `${node ? '编辑' : '添加'}评测目标`,
      placeholder: '请输入评测目标名称',
      inputWidget: 'textarea',
      raw: node?.name,
    });

    if (!name) {
      return;
    }
    if (node) {
      node.name = name;
    } else {
      nodes.value = [...nodes.value, { name, id: `CTM-${getNameHash(name)}` }];
    }

    onChapterUpdated();
  };

  const handleDeleteTopNode = (index: number) => {
    nodes.value.splice(index, 1);
    onChapterUpdated();
  };

  const handleEditSubNode = async (node?: any, parentNode?: any) => {
    const name = await prompt({
      title: `${node ? '编辑' : '添加'}评测目标 (${parentNode.name})`,
      placeholder: '请输入二级评测目标名称',
      inputWidget: 'textarea',
      raw: node?.name,
    });

    if (!name) {
      return;
    }
    if (node) {
      node.name = name;
    } else {
      parentNode.children = parentNode.children || [];
      parentNode.children.push({ name, id: `CTM-${getNameHash(name)}` });
    }

    onChapterUpdated();
  };

  const handleDeleteSubNode = (index: number, parentNode: any) => {
    parentNode.children.splice(index, 1);
    onChapterUpdated();
  };

  const handleShowQuestionSelect = (node: any) => {
    questionLibVisible.value = true;
    currentNode.value = node;
  };

  const handleSave = () => {
    setLoading(true);
    emit('save');
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const onQuestionSelected = (questions: any[]) => {
    currentNode.value.questionIds = questions.map((item) => item.id);
    onChapterUpdated();
  };

  watch(
    () => questionLibVisible.value,
    (value) => {
      if (!value) {
        currentNode.value = {};
      }
    },
  );

  onMounted(() => {
    nodes.value = props.chapter?.content?.teachingAssessCriteria || [];
  });
</script>

<template>
  <div>
    <a-space class="justify-between items-center w-full">
      <a-space>
        <criterion-select-modal @selected="handleNodeSelected" />
        <select-from-iep-modal :selected-nodes="nodes" :course="course" @selected="handleSelectFromIep" />
        <a-button size="mini" type="outline" @click="() => handleEditTopNode()">
          <template #icon>
            <IconPlus />
          </template>
          自定义目标
        </a-button>
      </a-space>
      <a-space>
        <list-table-input-modal v-model="teachingAssessScores" title="评分项设置" :schema-field="scoreSetField" />
        <a-button type="primary" size="mini" :loading="loading" @click="handleSave">
          <template #icon>
            <IconSave />
          </template>
          保存
        </a-button>
      </a-space>
    </a-space>
    <div v-for="(node, idx) in nodes" :key="idx" class="mt-2 p-2 border-t border-slate-300 hover:bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="text-base font-medium flex-1 flex gap-2" @dblclick="() => handleEditTopNode(node)">
          {{ idx + 1 }}、{{ node.name }}
          <target-iep-tag v-if="node.iepTargetIds?.length" />
        </div>
        <a-space>
          <a-popover
            :content="node.students?.map((item) => item.name)?.join('、') || '暂无参与此目标评测的学生'"
            class="w-64"
          >
            <a-button type="outline" status="warning" size="mini" @click="() => handleShowSelectTargetStudent(node)">
              <template #icon>
                <IconUser />
              </template>
              <div v-if="!node.students?.length">选择学生</div>
              <div v-else>{{ collapsedNameDisplay(node.students, { unit: '名学生' }) }}</div>
            </a-button>
          </a-popover>
          <a-tooltip content="添加下一级评测目标">
            <a-button type="outline" size="mini" @click="() => handleEditSubNode(null, node)">
              <template #icon>
                <IconPlus />
              </template>
            </a-button>
          </a-tooltip>
          <a-button
            size="mini"
            :type="!node.questionIds?.length ? undefined : 'outline'"
            @click="() => handleShowQuestionSelect(node)"
          >
            <template #icon>
              <IconQuestion />
            </template>
            试题<span v-if="node.questionIds?.length">[{{ node.questionIds?.length }}]</span>
          </a-button>
          <a-button size="mini" @click="() => handleEditTopNode(node)">
            <template #icon>
              <IconEdit />
            </template>
          </a-button>
          <a-popconfirm
            type="warning"
            content="确定要删除这个评测目标吗？其下所有子目标将一并被删除！"
            style="width: 270px"
            @ok="() => handleDeleteTopNode(idx)"
          >
            <a-button size="mini">
              <template #icon>
                <IconDelete />
              </template>
            </a-button>
          </a-popconfirm>
        </a-space>
      </div>
      <div v-if="node.children" class="ml-2">
        <div
          v-for="(child, i) in node.children"
          :key="i"
          class="flex justify-between items-center px-2 my-1 py-1 hover:bg-slate-200"
        >
          <div class="flex-1" @dblclick="() => handleEditSubNode(child, node)">
            {{ `${idx + 1}.${i + 1}、${child.name}` }}
          </div>
          <a-space>
            <a-button
              size="mini"
              :type="!child.questionIds?.length ? undefined : 'outline'"
              @click="() => handleShowQuestionSelect(child)"
            >
              <template #icon>
                <IconQuestion />
              </template>
              试题<span v-if="child.questionIds?.length">[{{ child.questionIds?.length }}]</span>
            </a-button>
            <a-button size="mini" @click="() => handleEditSubNode(child, node)">
              <template #icon>
                <IconEdit />
              </template>
            </a-button>
            <a-popconfirm
              type="warning"
              content="确定要删除这个评测目标吗？"
              style="width: 250px"
              @ok="() => handleDeleteSubNode(i, node)"
            >
              <a-button size="mini">
                <template #icon>
                  <IconDelete />
                </template>
              </a-button>
            </a-popconfirm>
          </a-space>
        </div>
      </div>
    </div>
    <a-empty v-if="!nodes.length" description="暂无评测目标，请先添加" />

    <teacher-question-library-modal
      v-if="currentNode?.id"
      v-model:visible="questionLibVisible"
      v-model:selected="currentNode.questionIds"
      :course="course"
      mode="select"
      @update:selected="onQuestionSelected"
    />

    <select-target-students-modal
      v-if="selectTargetStudentVisible"
      v-model:visible="selectTargetStudentVisible"
      v-model:target="currentTarget"
      :chapter="chapter"
      @update:target="handleTargetUpdated"
    />
  </div>
</template>

<style scoped lang="scss">
  .node-item {
    &:hover {
      background-color: #f9f9f9;
    }
  }
</style>
