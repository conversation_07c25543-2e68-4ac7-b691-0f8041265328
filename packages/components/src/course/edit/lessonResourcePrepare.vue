<script setup lang="ts">
  import { computed, nextTick, onMounted, PropType, ref } from 'vue';
  import DigitalResourceSelect from '@repo/components/resource/digital/components/digitalResourceSelect.vue';
  import UploaderButton from '@repo/ui/components/upload/uploaderButton.vue';
  import openapi from '@repo/infrastructure/openapi';
  import { collapsedNameDisplay } from '@repo/infrastructure/ui';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';
  import { formatUploadedFileList, ResourceSelectedItem } from '../../resource/constants';
  import PresentationResourcesList from '../components/presentationResourcesList.vue';
  import SelectChapterStudentsModal from './targets/selectChapterStudentsModal.vue';

  const localUploadRef = ref<any>(null);

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const selectChapterStudentVisible = ref(false);
  const resourcesList = ref<any[]>([]);
  const chapterStudents = computed(() => {
    return props.chapter.chapterStudents || [];
  });

  const refreshResources = async () => {
    resourcesList.value = [];
    props.chapter.content?.lessonResourcePrepare?.forEach((item: any) => {
      resourcesList.value.push(item);
    });
  };

  const selectedLibResource = computed({
    get: () => {
      return resourcesList.value
        .filter((item: any) => item.source === 'Lib')
        .map((item: any) => {
          return {
            id: item.resourceId,
            type: item.type,
            name: item.name,
          } as ResourceSelectedItem;
        });
    },
    set: async (val: ResourceSelectedItem[]) => {
      const raw = props.chapter?.content || {};
      raw.lessonResourcePrepare = raw.lessonResourcePrepare || [];
      raw.lessonResourcePrepare = raw.lessonResourcePrepare.filter((item: any) => item.source !== 'Lib');

      const resList: any[] = val.map((item: ResourceSelectedItem) => ({
        resourceId: item.id,
        type: item.type,
        name: item.name,
      }));
      raw.lessonResourcePrepare = [...raw.lessonResourcePrepare, ...resList];

      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...raw,
        },
      });
      await nextTick(() => {
        refreshResources();
        emit('save');
      });
    },
  });

  const handleShowSelectTargetStudent = () => {
    selectChapterStudentVisible.value = true;
  };

  const handleDeleteResource = async (index: number) => {
    const raw = props.chapter?.content || {};
    raw.lessonResourcePrepare = raw.lessonResourcePrepare || [];
    raw.lessonResourcePrepare.splice(index, 1);
    emit('update:chapter', {
      ...props.chapter,
      content: {
        ...raw,
      },
    });
    await nextTick(() => {
      refreshResources();
      emit('save');
    });
  };

  const handleChapterStudentUpdated = async (chapter: any) => {
    emit('update:chapter', chapter);
    await nextTick(() => {
      emit('save');
    });
  };

  const handleUploaded = async (files: any[]) => {
    const raw = props.chapter?.content || {};
    raw.lessonResourcePrepare = raw.lessonResourcePrepare || [];
    const resources = formatUploadedFileList(files, localUploadRef.value.processor);

    const { data } = await openapi.resource.batchCreateDigitalResource({
      source: 'User' as any,
      resources,
    });

    raw.lessonResourcePrepare = raw.lessonResourcePrepare.concat(
      data.map((resource: any) => ({
        resourceId: resource.id,
        type: resource.type,
        name: resource.name,
      })),
    );
    emit('update:chapter', {
      ...props.chapter,
      content: {
        ...raw,
      },
    });
    await nextTick(() => {
      refreshResources();
      emit('save');
    });
  };

  onMounted(() => {
    refreshResources();
  });
</script>

<template>
  <div class="w-full flex justify-between">
    <a-space>
      <uploader-button
        ref="localUploadRef"
        button-text="本地上传"
        save-type="digital-resource"
        accept="video/*,audio/*,image/*,text/html"
        :sub-folder="`user-upload/${chapter.id}`"
        @uploaded="handleUploaded"
      >
        <template #upload-button>
          <a-button
            :loading="localUploadRef?.loading"
            size="mini"
            type="secondary"
            style="position: relative; top: -1px"
          >
            <template #icon>
              <IconUpload />
            </template>
            本地上传
          </a-button>
        </template>
      </uploader-button>
      <digital-resource-select v-model="selectedLibResource" />
    </a-space>
    <a-button type="outline" status="warning" size="mini" @click="() => handleShowSelectTargetStudent()">
      <template #icon>
        <IconUser />
      </template>
      <div v-if="!chapterStudents?.length">选择学生</div>
      <div v-else>{{ collapsedNameDisplay(chapterStudents, { unit: '名学生' }) }}</div>
    </a-button>
  </div>
  <a-divider :margin="10" />
  <div>
    <presentation-resources-list :resources="resourcesList" @delete-resource="handleDeleteResource" />
  </div>

  <select-chapter-students-modal
    v-model:visible="selectChapterStudentVisible"
    :chapter="chapter"
    @update:chapter="handleChapterStudentUpdated"
  />
</template>

<style scoped lang="scss"></style>
