<script lang="ts" setup>
  import { onMounted, PropType, ref, watch } from 'vue';
  import { cloneDeep, sum } from 'lodash';
  import {
    getChapterAssessmentList,
    batchEditChapterAssessment,
  } from '@repo/infrastructure/openapi/chapterAssessmentController';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import QuestionTestModal from '../components/questionTestModal.vue';
  import TargetAssessmentCmp from './targets/targetAssessmentCmp.vue';
  import TargetIepTag from './targets/targetIepTag.vue';
  import TargetStudentsAverage from './targets/targetStudentsAverage.vue';
  import { getLeafParentPath } from '../../utils/assessment';

  const props = defineProps({
    chapter: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    chaptersTree: {
      type: Array as PropType<any[]>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const type = 'after';
  const loading = ref(false);

  const currentResult = ref<any[]>([]);
  const rawResultDataMap = ref<any>({});
  const currentTotalScore = ref(0);
  const currentCriteria = ref<any>({});
  const questTestVisible = ref(false);
  const resultDataMap = ref<any>({});

  const chapterAssessmentQuery = ref<any>({
    chapterId: props.chapter.id,
    type,
  });

  const handleShowTest = (record: any) => {
    currentCriteria.value = record;
    questTestVisible.value = true;
  };

  const loadResults = async () => {
    const { data: afterData } = await getChapterAssessmentList({
      ...chapterAssessmentQuery.value,
      type,
    });

    const { data: preData } = await getChapterAssessmentList({
      ...chapterAssessmentQuery.value,
      type: 'pre',
    });

    rawResultDataMap.value = (afterData || []).reduce((prev: any, curr: any) => {
      prev[curr.studentId] = curr;
      return prev;
    }, {});

    const formattedData = {};

    (preData || []).forEach((item) => {
      const preScoresMap = {};
      (item.scores || []).forEach((score: any) => {
        if (score.children?.length) {
          score.children.forEach((child: any) => {
            preScoresMap[child.id] = child.pre;
          });
        } else {
          preScoresMap[score.id] = score.pre;
        }
      });

      formattedData[item.studentId] = {
        ...(formattedData[item.studentId] || {}),
        id: item.id,
        preScoresMap,
      };
    });

    (afterData || []).forEach((item) => {
      const scoresMap = {};
      (item.scores || []).forEach((score) => {
        if (score.children?.length) {
          score.children.forEach((child: any) => {
            scoresMap[child.id] = child[type];
          });
        } else {
          scoresMap[score.id] = score[type];
        }
      });

      formattedData[item.studentId] = {
        ...(formattedData[item.studentId] || {}),
        id: item.id,
        scoresMap,
      };
    });

    resultDataMap.value = formattedData;
  };

  const handleTestResultUpdated = (criteria: any) => {
    currentResult.value = currentResult.value.map((item) => {
      if (item.id === criteria.id) {
        return criteria;
      }
      if (item.children?.length) {
        item.children = item.children.map((child) => {
          if (child.id === criteria.id) {
            return criteria;
          }
          return child;
        });
      }
      return item;
    });
    currentCriteria.value = null;
    questTestVisible.value = false;
  };

  const handleSave = async () => {
    const postData: any[] = [];
    Object.keys(resultDataMap.value).forEach((studentId) => {
      let record: any = {};
      record.studentId = Number(studentId);
      record.chapterId = props.chapter.id;
      record.type = type;
      record.gradeClassId = props.course.gradeClassId;
      if (rawResultDataMap.value[studentId]) {
        record = {
          ...rawResultDataMap.value[studentId],
          ...record,
        };
      } else {
        record.scores = [];
      }

      const scoresMap = resultDataMap.value[studentId];
      record.scores = cloneDeep(props.chapter.content?.teachingAssessCriteria || []).map((criterion) => {
        if (criterion.children?.length) {
          criterion.children.map((child) => {
            child[type] = scoresMap.scoresMap[child.id];
            child.students = [];
            return child;
          });
        } else {
          criterion[type] = scoresMap.scoresMap[criterion.id];
          criterion.students = [];
        }

        return criterion;
      });

      postData.push(record);
    });

    loading.value = true;
    try {
      await batchEditChapterAssessment(postData);
      await loadResults();

      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => currentResult.value,
    () => {
      const allChildren: number[] = [];
      currentResult.value?.forEach((res) => {
        const children: number[] = res?.children?.map((item) => item.score).filter((item) => item !== null) || [];
        allChildren.push(...children);
      });
      currentTotalScore.value = sum(allChildren);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  const handleAutoPushToIep = (target: any) => {
    const getPreScore = (studentId: any, t) => {
      if (!resultDataMap.value[studentId]?.preScoresMap) {
        return undefined;
      }
      return resultDataMap.value[studentId].preScoresMap[t.id];
    };

    const pushFunc = async (student) => {
      const chapterPath = getLeafParentPath(props.chaptersTree, props.chapter.id);
      const chapterName = chapterPath.map((item: any) => `${item.number} ${item.name}`).join(' / ');

      if (!target.children?.length) {
        const targetContent = {
          subjectId: target.id,
          name: target.name,
          chapter: chapterName,
          preScore: getPreScore(student.id, target),
          afterScore: resultDataMap.value[student.id].scoresMap[target.id],
        };
        await request('/student/iepTargetSearch/autoLink', {
          method: 'POST',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            targetContent,
            iepTargetIds: target.iepTargetIds,
            studentId: student.id,
          },
        });
      } else {
        const contents = target.children.map((child) => {
          return {
            subjectId: child.id,
            name: child.name,
            chapter: chapterName,
            preScore: getPreScore(student.id, child),
            afterScore: resultDataMap.value[student.id].scoresMap[child.id],
          };
        });

        await request('/student/iepTargetSearch/autoLinkBatch', {
          method: 'POST',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            contents,
            iepTargetIds: target.iepTargetIds,
            studentId: student.id,
          },
        });
      }
    };

    Modal.confirm({
      title: '自动关联至IEP教学实施目标',
      content: '确认要自动关联至IEP教学实施目标吗？（已关联过的目标得分将被覆盖，如未能自动关联成功，请使用手动关联）',
      onOk: async () => {
        Message.info('正在自动关联至IEP教学实施目标');
        const promises = target.students.map((student) => pushFunc(student));
        try {
          // 记录第几个出错
          const errorIndex = await Promise.allSettled(promises).then((results) => {
            return results.findIndex((result) => result.status === 'rejected');
          });

          if (errorIndex !== -1) {
            throw new Error(`第${errorIndex + 1}个学生自动关联至IEP教学实施目标失败`);
          }

          Message.clear();
          Message.success('操作成功');
        } catch (error) {
          Message.error('自动关联至IEP教学实施目标失败');
        }
      },
    });
  };

  onMounted(async () => {
    await loadResults();
  });
</script>

<template>
  <a-button size="mini" type="primary" :loading="loading" @click="handleSave">
    <template #icon>
      <IconSave />
    </template>
    保存
  </a-button>
  <a-collapse class="mt-2">
    <a-collapse-item
      v-for="(criterion, idx) in chapter.content?.teachingAssessCriteria"
      :key="idx"
      :disabled="!criterion.students?.length"
    >
      <template #header>
        <div class="flex flex-1 w-full gap-2 items-center justify-between">
          <a-space>
            <div>{{ idx + 1 }}、{{ criterion.name }}</div>
            <target-iep-tag v-if="criterion.iepTargetIds?.length" />
            <a-tooltip v-if="criterion.iepTargetIds?.length" content="尝试自动关联至IEP教学实施目标">
              <a-button size="mini" @click.stop="() => handleAutoPushToIep(criterion)">
                <template #icon>
                  <IconRobotAdd />
                </template>
              </a-button>
            </a-tooltip>
          </a-space>
          <a-space>
            <target-students-average type="after" :target="criterion" :result-data-map="resultDataMap" />
            <a-button
              v-if="criterion.questionIds?.length"
              type="text"
              size="mini"
              @click.stop="() => handleShowTest(criterion)"
            >
              试题
            </a-button>
          </a-space>
        </div>
      </template>
      <div v-if="criterion.students?.length">
        <div v-if="criterion.children?.length" class="flex flex-col gap-2">
          <target-assessment-cmp
            v-for="(child, childIdx) in criterion.children"
            :key="childIdx"
            v-model:result-data-map="resultDataMap"
            :sequence="`${idx + 1}.${childIdx + 1}`"
            :target="child"
            :students="criterion.students"
            :course="course"
            :chapter="chapter"
            :type="type"
            :chapters-tree="chaptersTree"
          />
        </div>
        <div v-else>
          <target-assessment-cmp
            v-model:result-data-map="resultDataMap"
            :target="criterion"
            :students="criterion.students"
            :course="course"
            :chapter="chapter"
            :type="type"
            :chapters-tree="chaptersTree"
          />
        </div>
      </div>
      <div v-else>
        <a-empty description="此目标未关联学生" />
      </div>
    </a-collapse-item>
  </a-collapse>

  <question-test-modal
    v-model:visible="questTestVisible"
    :criteria="currentCriteria"
    :course="course"
    @update:criteria="handleTestResultUpdated"
  />
</template>

<style lang="scss" scoped>
  :deep {
    .arco-collapse-item-header-title {
      flex: 1;
    }
  }
</style>
