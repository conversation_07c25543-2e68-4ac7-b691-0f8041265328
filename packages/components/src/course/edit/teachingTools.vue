<script setup lang="ts">
  import { ref } from 'vue';
  import RecognizeCharacterTools from '../components/recognizeCharacterTools.vue';

  const props = defineProps({
    course: {
      type: Object,
    },
    chapter: {
      type: Object,
    },
  });
  const avatarSize = 80;
  const avatarStyle = { backgroundColor: '#145e98' };
  const avatarClass =
    'tool-item cursor-pointer transition-transform duration-300 ease-in-out hover:scale-110 shadow-md ';
  const visible = ref(false);
  const currentTool = ref<any>(null);

  const tools = [{ key: 'recognizeCharacter', title: '识字词' }];

  const activate = (tool: any) => {
    currentTool.value = tool;
    visible.value = true;
  };
</script>

<template>
  <div class="p-2 flex justify-start space-x-8">
    <div v-for="tool in tools" :key="tool.key" :class="avatarClass" @click="activate(tool)">
      <a-avatar shape="square" :size="avatarSize" :style="avatarStyle">
        <template #default>
          {{ tool.title }}
        </template>
      </a-avatar>
    </div>
  </div>

  <RecognizeCharacterTools
    v-if="currentTool?.key === 'recognizeCharacter'"
    v-model:visible="visible"
    :current-tool="currentTool"
    :course="course"
    :chapter="chapter"
  />
</template>

<style scoped lang="scss">
  .tool-item {
    display: inline-block;
    will-change: transform;
    transform-origin: center;
  }
</style>
