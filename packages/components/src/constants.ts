const studentAttachmentTypeOptions = [
  { label: '课程类诊断报告', value: 'LessonDiagnosis' },
  { label: '生理类诊断报告', value: 'PhysiologicalDiagnosis' },
  { label: '心理类诊断报告', value: 'PsychologicalDiagnosis' },
  { label: '其他类诊断报告', value: 'OtherDiagnosis' },
  { label: '其他', value: 'Other' },
];

const studentAttachmentTypeOptionsMap = {};
studentAttachmentTypeOptions.forEach((item) => {
  studentAttachmentTypeOptionsMap[item.value] = item;
});

const studentAttachmentsColumns = [
  {
    label: '类型',
    key: 'type',
    inputWidget: 'selectInput',
    inputWidgetProps: {
      options: studentAttachmentTypeOptions,
      placeholder: '请选择或输入',
      allowCreate: true,
    },
  },
  {
    label: '附件',
    key: 'attachments',
    inputWidget: 'uploadInput',
    inputWidgetProps: {
      draggable: false,
      multiple: true,
    },
    displayProps: {
      component: 'AttachmentsPreviewDisplay',
    },
  },
];

export const questionnaireFormScopeOptions = [
  {
    value: 'conference',
    label: '会议培训',
  },
  { value: 'public', label: '公开发布' },
  {
    value: 'protected',
    label: '全单位发布',
  },
  { value: 'organization', label: '部分单位发布' },
];

export { studentAttachmentTypeOptions, studentAttachmentsColumns, studentAttachmentTypeOptionsMap };
