<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    record: {
      type: Object,
      required: true,
    },
  });

  const criterion = ref<any>({});

  const pageTitle = computed(() => {
    return `${props.record?.student?.name} ${props.record?.period} 第${props.record?.timesLabel}`;
  });

  const handleBeforeOpen = async () => {
    if (props.record?.customCriterionId) {
      const { data } = await request(`/evaluation/customCriterion/${props.record.customCriterionId}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      criterion.value = data;
    }
  };
</script>

<template>
  <a-modal v-bind="$attrs" ok-text="完成" hide-cancel :title="pageTitle" @open="handleBeforeOpen">
    <div class="text-lg">
      <div class="flex gap-2">
        所属评估：
        {{ criterion.name }}
      </div>
      <div class="flex flex-wrap gap-2 mt-2">
        <attachments-preview-display
          v-for="(attachments, type) in record?.diagnosisAttachments"
          :key="type"
          :raw="attachments"
        >
          <a-button>
            <template #icon>
              <IconAttachment />
            </template>
            {{ type }} ({{ attachments.length }}个)
          </a-button>
        </attachments-preview-display>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
