<script setup lang="ts">
  import { computed } from 'vue';
  import { VuePrintNext } from 'vue-print-next';
  import AssessmentAnalysisView from './assessmentAnalysisView.vue';

  // const { resultId } = route.query;
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:visible']);
  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });
  const handlePrint = () => {
    // eslint-disable-next-line no-new
    new VuePrintNext({
      el: `#analysis`,
      popTitle: '评估',
      zIndex: 9999,
      printMode: 'popup',
      hide: '.no-print',
    });
  };
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen title="能力评估" hide-cancel ok-text="关闭">
    <assessment-analysis-view v-bind="$attrs" id="analysis" />
    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button size="mini" @click="handlePrint">打印</a-button>
        <a-button size="mini" type="primary" @click="modalVisible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
  .score-display {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 30px;
  }
  .student-score-item {
    display: flex;
    align-items: center;
    margin: 3px 0;
    .name {
      display: block;
      white-space: nowrap;
      margin-right: 10px;
      min-width: 50px;
    }
  }
</style>
