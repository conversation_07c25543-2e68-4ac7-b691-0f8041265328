<script setup lang="ts">
  import { PropType, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import { allStudentScoresMap, addToStudentScoreMap } from '../utils/assessment';

  const props = defineProps({
    snapshot: {
      type: Object as PropType<any>,
      required: true,
    },
    detailsData: {
      type: Array as PropType<any[]>,
      required: true,
    },
    dataSort: {
      type: Function,
    },
  });

  const data = ref<any[]>([]);

  watch(
    () => props.detailsData,
    (val) => {
      if (!val?.length) {
        return;
      }
      const rawData = cloneDeep(val);
      const puredData = addToStudentScoreMap(props.snapshot, rawData || []);

      if (props.dataSort) {
        data.value = props.dataSort(puredData);
      } else {
        data.value = puredData;
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<template>
  <div v-for="(domain, di) in data" :key="di">
    <a-divider orientation="left">{{
      `${domain.numberSequence ? domain.numberSequence + '.' : ''} ${domain.name}`
    }}</a-divider>
    <a-table
      :data="domain.children"
      border
      size="small"
      :pagination="false"
      row-key="id"
      class="mb-8"
      :default-expand-all-rows="false"
    >
      <template #columns>
        <a-table-column prop="name" title="评估项目">
          <template #cell="{ record }">
            <span> {{ record.numberSequence }} </span>
            <span class="ml-2"> {{ record.name }} </span>
          </template>
        </a-table-column>
        <a-table-column title="评分" :width="100">
          <template #cell="{ record }">
            <div
              v-if="
                allStudentScoresMap[record.id]?.score !== undefined && allStudentScoresMap[record.id]?.score !== null
              "
            >
              {{ allStudentScoresMap[record.id].score + ' 分' }}
            </div>
            <div v-else-if="!record?.children?.length" class="text-red-500">未评测</div>
          </template>
        </a-table-column>
        <a-table-column title="支持程度" :width="90">
          <template #cell="{ record }">
            <div v-if="allStudentScoresMap">
              {{ allStudentScoresMap[record.id]?.supportLevel }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="目标标准" :width="120">
          <template #cell="{ record }">
            <div v-if="allStudentScoresMap">
              {{ allStudentScoresMap[record.id]?.targetStandard }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="目标分级" :width="120">
          <template #cell="{ record }">
            <div v-if="allStudentScoresMap">
              {{ allStudentScoresMap[record.id]?.targetLevel }}
            </div>
          </template>
        </a-table-column>
        <slot name="extra-columns"></slot>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="scss"></style>
