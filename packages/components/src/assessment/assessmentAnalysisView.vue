<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import StudentSelect from '@repo/components/student/studentSelect.vue';
  import RemoteItemSelect from '@repo/components/common/remoteItemSelect.vue';
  import AssessmentScoreCompare from '@repo/components/g2plot/assessmentScoreCompare.vue';
  import { Message } from '@arco-design/web-vue';
  import { cloneDeep } from 'lodash';
  import AssessmentDetailView from '@repo/components/assessment/assessmentDetailView.vue';
  import AiButton from '@repo/components/common/aiButton.vue';
  import { randomColor, randomDarkColor } from '@repo/ui/components/utils/randomColor.ts';

  // const { resultId } = route.query;
  const props = defineProps({
    resultId: {
      type: String,
      default: '',
    },
    currentTimes: {
      type: Number,
    },
    showResultFilter: {
      type: Boolean,
      default: true,
    },
  });

  const aiLoading = ref(false);
  const loading = ref(false);
  const resultItem = ref<any>({});
  const resultItems = ref<any[]>([]);
  const analysisForm = ref<any>({
    dimension: 'domain',
  });
  const criterionDetails = ref<any>({});
  const userStore = useUserStore();
  const { userInfo } = userStore;
  const resultQuery = ref<any>({});
  const criterionScores = ref<any[]>([]);
  const timesOptions = ref<any[]>([]);

  const maxScore = computed(() => {
    return criterionScores.value.reduce((prev, current) => {
      return prev.score > current.score ? prev : current;
    }).score;
  });

  const criterionScoresTargetLevelOptions = computed(() => {
    const options = criterionScores.value
      .map((item) => {
        return {
          label: item.targetLevel,
          value: item.score,
        };
      })
      .filter((item, index, self) => self.findIndex((t) => t.label === item.label) === index);
    options.push({
      label: '未评测',
      value: 'NONE',
    });
    return options;
  });

  const criterionScoresTargetStandardOptions = computed(() => {
    return criterionScores.value.map((item) => {
      return {
        label: item.targetStandard,
        value: item.score,
      };
    });
  });

  const targetLevelScores = computed(() => {
    const targetLevel = criterionScoresTargetLevelOptions.value.find(
      (item) => item.value === analysisForm.value.targetLevel,
    );
    if (targetLevel?.label === 'NONE') {
      return [undefined];
    }
    return criterionScores.value.filter((item) => item.targetLevel === targetLevel.label).map((item) => item.score);
  });

  /**
   * 递归过滤掉最后一层评分不符合目标分级的评分，如果过滤后没有子集(children)也去掉
   * @param details
   * @param targetLevel
   */
  const filterDetailViaTargetLevel = (details, targetLevel) => {
    return details.filter((item) => {
      if (item.children && item.children.length) {
        item.children = filterDetailViaTargetLevel(item.children, targetLevel);
        return item.children.length;
      }
      return targetLevelScores.value.includes(item.score);
    });
  };

  /**
   * find node leaves
   * 找到节点的所有叶子节点
   */
  const findNodeLeaves = (nodes: any[], id) => {
    const result: any[] = [];
    nodes?.forEach((node) => {
      if (node.children?.length) {
        result.push(...findNodeLeaves(node.children, id));
      } else {
        result.push(node);
      }
    });
    return result;
  };

  /**
   * 1. 是否有筛选目标分级、领域、指标
   * 2. 如果有指标，找到所有次数中下属的指标的 children 并合并
   * 3. 如果没有指标，有领域，找到所有次数中领域的children 并合并
   * 4. 如果没有领域，找到所有次数中的 leaf 节点 并合并
   */
  const plotData = computed(() => {
    const rawItemsList = cloneDeep(resultItems.value);
    const result: any[] = [];
    for (let i = 0; i < rawItemsList.length; i += 1) {
      const rawItem = rawItemsList[i];
      const rawDetails = rawItem.resultSnapshot?.details;
      if (analysisForm.value.criterion) {
        const allLeaves = findNodeLeaves(rawDetails, analysisForm.value.criterion);
        result.push(
          ...(allLeaves?.map((item) => {
            return {
              criterion: item.name,
              timesLabel: `第${rawItem.timesLabel}`,
              score: item.score,
            };
          }) || []),
        );
      } else if (analysisForm.value.domain) {
        const domain = rawDetails.find((item) => item.id === analysisForm.value.domain);
        if (domain) {
          const allLeaves = findNodeLeaves(domain.children, analysisForm.value.domain);
          result.push(
            ...(allLeaves?.map((item) => {
              return {
                criterion: item.name,
                timesLabel: `第${rawItem.timesLabel}`,
                score: item.score,
              };
            }) || []),
          );
        }
      } else {
        const allLeaves = findNodeLeaves(rawDetails, analysisForm.value.domain);
        result.push(
          ...(allLeaves?.map((item) => {
            return {
              criterion: item.name,
              timesLabel: `第${rawItem.timesLabel}`,
              score: item.score,
            };
          }) || []),
        );
      }
    }

    return result;
  });

  const detailsData = computed(() => {
    if (!analysisForm.value.targetLevel) {
      return criterionDetails.value?.details || [];
    }

    const raw = cloneDeep(criterionDetails.value?.details);
    return filterDetailViaTargetLevel(raw, analysisForm.value.targetLevel);
  });

  const dimensionOptions = [
    {
      label: '领域',
      value: 'domain',
    },
    {
      label: '指标',
      value: 'criterion',
    },
  ];

  // studentId: {itemId: {score, targetLevel, targetStandard, supportLevel}}

  const handleTimesChange = (times?: any) => {
    if (times) {
      const existsTimes = resultItems.value.find((item) => item.times === times)?.times;
      if (!existsTimes) {
        times = resultItems.value[0].times;
      }
    } else {
      times = resultItems.value[0].times;
    }

    criterionDetails.value = {};
    resultItem.value = {};
    criterionScores.value = [];
    analysisForm.value = {
      times,
      dimension: analysisForm.value.dimension || 'domain',
    };

    let data = resultItems.value.find((item) => item.times === times);
    if (!data) {
      // eslint-disable-next-line prefer-destructuring
      data = resultItems.value[0];
    }

    resultItem.value = data;
    criterionDetails.value = data.resultSnapshot;
    criterionScores.value = data.resultSnapshot?.scores || [];
  };

  const loadData = async () => {
    loading.value = true;

    if (!resultQuery.value.student?.id || !resultQuery.value.customCriterionId) {
      return;
    }

    timesOptions.value = [];

    try {
      const { data } = await request(`/evaluation/customCriterionResult/byStudentWithDetail`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          studentId: resultQuery.value.student.id,
          criterionId: resultQuery.value.customCriterionId,
        },
      });

      criterionDetails.value = [];
      resultItem.value = {};

      if (!data.length) {
        Message.error('未找到评估结果');
        throw new Error('未找到评估结果');
      }

      resultItems.value = data;
      timesOptions.value = data.map((item) => {
        return {
          label: `第${item.timesLabel}`,
          value: item.times,
        };
      });

      handleTimesChange();
      analysisForm.value.times = props.currentTimes;
      handleTimesChange(analysisForm.value.times);
    } finally {
      loading.value = false;
    }
  };

  const loadDataByResult = async (id: any) => {
    loading.value = true;
    const { data } = await request(`/evaluation/customCriterionResult/${id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    resultQuery.value = {
      student: data.student,
      gradeClass: data.student?.gradeClass,
      customCriterionId: data.customCriterionId,
      times: data.times,
    };
  };

  const handleGradeClassChange = (id, gradeClass) => {
    resultQuery.value.gradeClass = gradeClass;
  };

  const handleStudentChange = (id, student) => {
    resultQuery.value.student = student;
  };

  const getCriterionGroupBy = (item) => {
    return item.category?.name;
  };

  const handleDimensionChange = (value) => {
    analysisForm.value.dimension = value;
    analysisForm.value.domain = undefined;
    analysisForm.value.criterion = undefined;
  };

  const handleAiAnalysis = async () => {
    aiLoading.value = true;
    try {
      const { data } = await request(`/evaluation/customCriterionResult/aiAnalysis/${resultItem.value?.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      if (data) {
        Message.success('AI分析成功');
        resultItem.value = {
          ...resultItem.value,
          resultSnapshot: {
            ...resultItem.value.resultSnapshot,
            aiAnalysis: data,
          },
        };
      }
    } finally {
      aiLoading.value = false;
    }
  };

  // 分数统计
  const result = ref<any>({
    total: 0,
    score: 0,
    percent: 0,
    fields: [],
  });

  // 答题总数
  let totalQuestion = 0;
  const computedDetailScore = (item: any): number => {
    if (item?.children) {
      let totalScore = 0;
      item.children.forEach((v) => {
        totalScore += computedDetailScore(v);
      });
      return totalScore;
    }
    totalQuestion += 1;
    return item?.score || 0;
  };

  const resetData = () => {
    totalQuestion = 0;
    result.value = {
      total: 0,
      score: 0,
      percent: 0,
      fields: [],
    };
  };

  const computedScores = () => {
    const detailList = resultItems.value[0]?.resultSnapshot?.details || [];
    const scoreLength = resultItems.value[0]?.resultSnapshot?.scores?.length || 0; // * 题目数得到总分

    detailList.forEach((item: any) => {
      const score = computedDetailScore(item);
      result.value.fields.push({
        field: item?.name,
        score,
      });
      result.value.score += score;
    });
    result.value.total = scoreLength * totalQuestion;
    result.value.percent = `${((result.value.score / result.value.total) * 100).toFixed(2)}%`;
  };

  watch(
    () => resultQuery.value,
    async () => {
      await loadData();
      computedScores();
    },
    { deep: true, immediate: true },
  );

  // 深度监听?
  watch(
    () => props.resultId,
    async (val) => {
      if (val) {
        resetData();
        await loadDataByResult(props.resultId);
      }
    },
  );

  onMounted(async () => {
    userInfo.value = userStore.userInfo;
    if (props.resultId) {
      await loadDataByResult(props.resultId);
    }
  });
</script>

<template>
  <div>
    <a-spin class="w-full" :loading="loading">
      <div class="flex gap-2">
        <a-card v-if="showResultFilter" size="small" title="评估结果筛选">
          <a-form :auto-label-width="true" :model="resultItem" size="mini">
            <a-form-item label="评估量表">
              <remote-item-select
                ref="criterionSelectRef"
                v-model="resultQuery.customCriterionId"
                size="mini"
                :group-by="getCriterionGroupBy"
                :preload="true"
                api="/evaluation/customCriterion"
              />
            </a-form-item>
            <a-form-item label="选择学生">
              <student-select
                size="mini"
                :use-grade-class-filter="true"
                :student="resultQuery.student"
                :grade-class="resultQuery.student?.gradeClass"
                @grade-class-change="handleGradeClassChange"
                @change="handleStudentChange"
              />
            </a-form-item>
          </a-form>
        </a-card>
        <a-card v-if="resultItem?.id" size="small" class="flex-1" title="评估结果分析">
          <template #extra>
            <!--escape manager touch it-->
            <ai-button :disabled="!resultItem?.id || !showResultFilter" :loading="aiLoading" @ok="handleAiAnalysis"
              >AI分析评估结果</ai-button
            >
          </template>
          <a-form size="mini" auto-label-width :model="analysisForm">
            <div class="grid grid-cols-4 gap-x-2">
              <a-form-item label="学生">
                {{ resultQuery.student?.name }} ({{ resultQuery.student?.gender }})
              </a-form-item>
              <a-form-item label="系统证号">
                <small>
                  {{ resultQuery.student?.symbol }}
                </small>
              </a-form-item>
              <a-form-item label="年龄">
                {{ resultQuery.student?.age }}岁
                <small class="ml-1"> ({{ resultQuery.student?.birthday }}) </small>
              </a-form-item>
              <a-form-item label="障碍类型">
                {{ resultQuery.student?.disorders }}
              </a-form-item>
              <a-form-item label="评估次数">
                <a-select
                  v-model="analysisForm.times"
                  size="mini"
                  :options="timesOptions"
                  @change="handleTimesChange"
                />
              </a-form-item>
              <a-form-item label="目标分级">
                <a-select
                  v-model="analysisForm.targetLevel"
                  allow-clear
                  size="mini"
                  :options="criterionScoresTargetLevelOptions"
                  @change="handleTargetLevelChange"
                />
              </a-form-item>
              <a-form-item label="统计维度">
                <a-select
                  v-model="analysisForm.dimension"
                  size="mini"
                  :options="dimensionOptions"
                  @change="handleDimensionChange"
                />
              </a-form-item>
              <a-form-item label="领域">
                <a-select
                  v-model="analysisForm.domain"
                  allow-clear
                  size="mini"
                  :options="
                    criterionDetails?.details.map((item) => {
                      return { label: item.name, value: item.id };
                    })
                  "
                />
              </a-form-item>
              <a-form-item
                v-if="analysisForm.domain && analysisForm.dimension === 'criterion'"
                label="指标"
                class="col-span-2"
              >
                <a-tree-select
                  v-model="analysisForm.criterion"
                  allow-clear
                  size="mini"
                  :field-names="{
                    title: 'name',
                    key: 'id',
                  }"
                  :data="
                    criterionDetails?.details
                      ?.find((item) => item.id === analysisForm.domain)
                      ?.children?.filter((item) => item?.children?.length)
                  "
                />
              </a-form-item>
            </div>
          </a-form>
        </a-card>
      </div>
      <a-card v-if="resultItem?.resultSnapshot?.aiAnalysis" title="AI分析" class="mt-2">
        <template #extra>
          {{ resultItem?.resultSnapshot?.aiAnalysis?.shortAnalysis }}
        </template>
        {{ resultItem?.resultSnapshot?.aiAnalysis?.longAnalysis }}
      </a-card>
      <a-card v-if="resultItem?.id && plotData?.length" size="small" class="flex-1 mt-2">
        <div class="px-4 bg-gray-50 rounded space-x-4 flex h-10 justify-start items-center">
          <span class="px-1 border rounded-lg">总分：{{ result.total }}</span>
          <span class="px-1 border rounded-lg">得分：{{ result.score }}</span>
          <span class="px-1 border rounded-lg">百分：{{ result.percent }}</span>
          <div class="slash" />
          <div class="flex justify-start space-x-2">
            <a-tag
              v-for="(field, fieldIndex) in result.fields"
              :key="fieldIndex"
              size="mini"
              :color="randomDarkColor()"
            >
              {{ field.field }}{{ ` ${field.score} 分` }}
            </a-tag>
          </div>
        </div>
        <assessment-score-compare :data="plotData" />
      </a-card>
    </a-spin>
    <a-collapse class="mt-2">
      <a-collapse-item header="评估详情">
        <a-empty v-if="!detailsData?.length" description="暂无数据" />
        <assessment-detail-view v-else :snapshot="criterionDetails" :details-data="detailsData" />
      </a-collapse-item>
    </a-collapse>
  </div>
</template>

<style lang="scss" scoped>
  .score-display {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 30px;
  }
  .student-score-item {
    display: flex;
    align-items: center;
    margin: 3px 0;
    .name {
      display: block;
      white-space: nowrap;
      margin-right: 10px;
      min-width: 50px;
    }
  }
  .slash {
    width: 2px;
    height: 20px;
    border: 1px solid #4d0070;
    transform: rotate(20deg);
  }
</style>
