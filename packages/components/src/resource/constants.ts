import { IconFileAudio, IconFileImage, IconFileVideo, IconThunderbolt } from '@arco-design/web-vue/es/icon';
import { IAttachmentProcessor, UploadedAttachment } from '@repo/infrastructure/upload';
import { ButtonProps } from '@arco-design/web-vue';

export type ResourceSelectedItem = {
  id: number;
  type: string;
  [key: string]: any;
};

export type ResourceAction = {
  key: string;
  label: string;
  props: ButtonProps;
  visible: (item: ResourceSelectedItem) => boolean;
  handler: (item: ResourceSelectedItem, e: Event) => void;
};

export type ResourceType = {
  id: string;
  name: string;
  icon: any;
};
export const resourceType: ResourceType[] = [
  {
    id: 'video',
    name: '视频资源',
    icon: IconFileVideo,
  },
  {
    id: 'audio',
    name: '音频资源',
    icon: IconFileAudio,
  },
  {
    id: 'image',
    name: '图片资源',
    icon: IconFileImage,
  },
  {
    id: 'game',
    name: '交互游戏',
    icon: IconThunderbolt,
  },
];

export const filenameToDigitalResourceType = (name: string): API.CourseResourceType => {
  const ext = name.split('.').pop()?.toLowerCase() || '';
  if (['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'webm'].includes(ext)) {
    return 'video' as any;
  }
  if (['mp3', 'wav', 'wma', 'ogg', 'flac', 'aac'].includes(ext)) {
    return 'audio' as any;
  }
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
    return 'image' as any;
  }
  return 'game' as any;
};

export const formatUploadedFileList = (files: UploadedAttachment[], processor: IAttachmentProcessor) => {
  return files.map((item) => {
    const type = filenameToDigitalResourceType(item.name);
    let thumb: string;
    switch (type) {
      case 'video':
        thumb = processor.videoCoverUrl(item.url, 300, 200);
        break;
      case 'image':
        thumb = processor.thumbUrl(item.url, 300, 200);
        break;
      default:
        thumb = '';
    }
    return {
      name: item.name,
      url: item.url,
      type,
      thumb,
    };
  });
};
