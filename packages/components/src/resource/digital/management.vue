<script lang="ts" setup>
  import { computed, inject, nextTick, onMounted, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CommonApi } from '@repo/infrastructure/crud';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import openapi from '@repo/infrastructure/openapi';
  import { formatUploadedFileList, resourceType } from '../constants';
  import DigitalResourceList from './components/digitalResourceList.vue';
  import ChapterManage from '../../course/components/chapterManage.vue';

  const router = inject('router');
  const route = router.currentRoute;
  const courseId = Number(route.query.courseId);
  const course = ref<Record<string, any>>({});
  const { setLoading, loading } = useLoading();
  const schema = SchemaHelper.getInstanceByApi('/resource/course');
  let courseApi: any;
  const activeTab = ref<any>('all');
  const chapterManageRef = ref<any>();
  const uploaderVisible = ref<any>(false);
  const uploaderRef = ref<any>();

  const digitalResourceListRef = ref<any>();

  const handleFinish = () => {
    router.push('/resource/digital');
  };

  const handleChapterSwitch = async (chapter: any) => {
    chapterManageRef.value.currentChapter = chapter;
    if (chapter) {
      await nextTick(async () => {
        await digitalResourceListRef.value.refresh();
      });
    } else {
      digitalResourceListRef.value.listData.value = [];
    }
  };

  const handleResourceTypeChange = async (type: string | number) => {
    activeTab.value = type;
    await nextTick(async () => {
      await digitalResourceListRef.value.refresh();
    });
  };

  const handleUploaded = async (items: any[]) => {
    await openapi.resource.batchCreateDigitalResource({
      source: 'Lib' as any,
      courseId,
      chapterId: chapterManageRef.value?.currentChapter?.id,
      resources: formatUploadedFileList(items, uploaderRef.value.processor),
    });
    await digitalResourceListRef.value.refresh();
  };

  const handleBeforeUploadClose = () => {
    uploaderRef.value.clear();
  };

  const digitalResourceQueryParams = computed(() => {
    return {
      courseId,
      chapterId: chapterManageRef.value?.currentChapter?.id,
      type: activeTab.value === 'all' ? undefined : activeTab.value,
    };
  });

  onMounted(async () => {
    setLoading(true);
    courseApi = CommonApi.getInstance(schema);
    const { data } = await courseApi.fetchOne(courseId);
    course.value = data;
    setLoading(false);
  });
</script>

<template>
  <a-split class="wrapper" default-size="280px">
    <template #first>
      <a-card :loading="loading" :title="`章节列表`">
        <chapter-manage
          ref="chapterManageRef"
          :course-id="courseId"
          api-alias="/resource/chapter"
          @switch-chapter="async (data: any) => handleChapterSwitch(data)"
        />
      </a-card>
    </template>
    <template #second>
      <a-card>
        <template #title>
          <a-space>
            <a-input size="mini" placeholder="搜索" />
            <a-button size="mini" type="outline" @click="() => digitalResourceListRef.refresh()">
              <template #icon>
                <IconRefresh />
              </template>
            </a-button>
          </a-space>
        </template>
        <template #extra>
          <a-space>
            <a-button
              v-if="chapterManageRef?.currentChapter?.id"
              size="mini"
              type="primary"
              @click="() => (uploaderVisible = true)"
            >
              <template #icon>
                <IconUpload />
              </template>
              上传资源
            </a-button>
            <a-button size="mini" status="success" type="primary" @click="handleFinish">
              <template #icon>
                <IconCheck />
              </template>
              完成
            </a-button>
          </a-space>
        </template>
        <a-spin :loading="chapterManageRef?.switchLoading || digitalResourceListRef?.loading" class="content-wrapper">
          <div v-show="chapterManageRef?.currentChapter?.id" class="resources-wrapper">
            <a-tabs v-model:active-key="activeTab" position="left" @change="handleResourceTypeChange">
              <a-tab-pane key="all" title="全部">
                <template #title>
                  <IconFile />
                  全部
                </template>
              </a-tab-pane>
              <a-tab-pane v-for="type in resourceType" :key="type.id" :title="type.name">
                <template #title>
                  <component :is="type.icon" />
                  {{ type.name }}
                </template>
              </a-tab-pane>
            </a-tabs>
            <digital-resource-list ref="digitalResourceListRef" :query-params="digitalResourceQueryParams" />
          </div>

          <a-empty v-if="!chapterManageRef?.currentChapter?.id" description="请在左侧列表中选择章节" />
        </a-spin>
      </a-card>

      <a-modal
        v-model:visible="uploaderVisible"
        :esc-to-close="false"
        :hide-cancel="true"
        :mask-closable="false"
        :on-before-close="handleBeforeUploadClose"
        :unmount-on-close="true"
        ok-text="完成"
      >
        <template #title>
          <IconUpload />
          上传资源
        </template>
        <uploader
          v-if="uploaderVisible"
          ref="uploaderRef"
          save-type="digital-resource"
          :sub-folder="`sys/${courseId}`"
          accept="video/*,audio/*,image/*,text/html"
          tips="仅支持视频、音频、图片及游戏文件"
          @uploaded="handleUploaded"
        />
      </a-modal>
    </template>
  </a-split>
</template>

<style lang="less" scoped>
  .wrapper {
    display: flex;

    .content-wrapper {
      flex: 1;
      width: 100%;
    }

    .resources-wrapper {
      display: flex;
    }
  }
</style>
