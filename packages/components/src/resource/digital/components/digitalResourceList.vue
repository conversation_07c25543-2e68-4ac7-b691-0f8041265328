<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { coverUploadInput } from '@repo/ui/components/form/inputComponents';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { CommonApi, useList } from '@repo/infrastructure/crud';
  import DigitalResourcePreview from './digitalResourcePreview.vue';
  import DigitalResourceIcon from './digitalResourceIcon.vue';

  const props = defineProps({
    queryParams: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  });

  const resourceSchema = SchemaHelper.getInstanceByApi('/resource/digital');
  let commonApi: CommonApi<any, any>;
  const { listInit, loadData, listData, pagination, loading } = useList({
    schema: resourceSchema,
    pageSize: 999,
  });

  const refresh = async () => {
    await loadData({
      ...props.queryParams,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });
  };

  const previewVisible = ref<boolean>(false);
  const currentIndex = ref<number>(NaN);

  const handlePreview = (index: number) => {
    currentIndex.value = index;
    previewVisible.value = true;
  };

  const handleCurrentIndexChange = (index: number) => {
    currentIndex.value = index;
    if (index < 0) {
      previewVisible.value = false;
    }
  };

  const { setLoading: setEditLoading, loading: editLoading } = useLoading();
  const editInfoVisible = ref<boolean>(false);
  const currentEditResource = ref<any>({});
  const handleEditResourceInfo = (resource: any) => {
    currentEditResource.value = {
      ...resource,
    };
    editInfoVisible.value = true;
  };
  const handleEditInfoCancel = () => {
    editInfoVisible.value = false;
    currentEditResource.value = {};
  };

  const handleEditInfoSave = async () => {
    setEditLoading(true);
    try {
      await commonApi.updateRecord({
        ...currentEditResource.value,
      });
    } finally {
      setEditLoading(false);
    }
    editInfoVisible.value = false;
    currentEditResource.value = {};
    await refresh();
  };

  const handleDeleteResource = async () => {
    setEditLoading(true);
    try {
      await commonApi.deleteRecord(currentEditResource.value.id);
      handleEditInfoCancel();
      await refresh();
    } finally {
      setEditLoading(false);
    }
  };

  defineExpose({
    loadData,
    listData,
    refresh,
    loading,
  });

  onMounted(async () => {
    await listInit();
    commonApi = CommonApi.getInstance(resourceSchema);
  });
</script>

<template>
  <div class="wrapper">
    <a-empty v-if="!listData.length" description="暂无相关资源，请先上传" />
    <div v-else class="resource-list-wrapper">
      <digital-resource-icon
        v-for="(item, index) in listData"
        :key="item.id"
        :index="index"
        :resource="item"
        @preview="() => handlePreview(index)"
      >
        <template #extra-actions>
          <a-button type="secondary" size="mini" status="success" @click="handleEditResourceInfo(item)">
            修改信息
          </a-button>
        </template>
      </digital-resource-icon>
    </div>
    <a-pagination
      v-if="listData.length > 0"
      :current="pagination.current"
      :page-size="pagination.pageSize"
      :total="pagination.total!"
    />
    <digital-resource-preview
      v-if="!isNaN(currentIndex) && currentIndex >= 0 && previewVisible"
      :data-list="listData"
      :current-index="currentIndex"
      @update:current-index="handleCurrentIndexChange"
    />

    <a-modal
      v-if="editInfoVisible"
      v-model:visible="editInfoVisible"
      title="修改资源信息"
      :ok-loading="editLoading"
      @before-close="handleEditInfoCancel"
    >
      <a-form :model="currentEditResource">
        <a-form-item label="资源名称" required>
          <a-input v-model="currentEditResource.name" />
        </a-form-item>
        <a-form-item label="资源描述">
          <a-input v-model="currentEditResource.description" />
        </a-form-item>
        <a-form-item label="缩略图">
          <cover-upload-input
            v-model="currentEditResource.thumb"
            class="cover-input"
            :box-height="83"
            :box-width="130"
            tips="上传缩略图"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="flex justify-between">
          <a-popconfirm content="确定要删除这个资源吗？" @ok="handleDeleteResource">
            <a-button status="danger">删除资源</a-button>
          </a-popconfirm>
          <a-button type="primary" @click="handleEditInfoSave">保存</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
  .wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .resource-list-wrapper {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    > div {
      margin: 0 16px 16px 0;
    }
  }

  .arco-pagination {
    margin-top: 20px;
  }

  .cover-input {
    :deep(.preview) {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 260px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
