<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { useList } from '@repo/infrastructure/crud';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { COURSE_CATEGORIES, COURSE_GRADES, COURSE_PERIODS } from '@repo/infrastructure/constants';
  import DigitalResourceIcon from './digitalResourceIcon.vue';
  import DigitalResourcePreview from './digitalResourcePreview.vue';
  import { ResourceSelectedItem, resourceType } from '../../constants';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ResourceSelectedItem[]>,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = ref<any>(false);
  const selectedItems = ref<ResourceSelectedItem[]>([]);
  const queryParams = ref<any>({
    chapterId: undefined,
    type: 'all',
  });
  const classCategory = ref<any>(undefined);
  const grade = ref<any>(undefined);
  const period = ref<any>(undefined);
  const chaptersList = ref<any[]>([]);
  const coursesList = ref<any[]>([]);
  const selectedChapter = ref<any>();

  const schema = SchemaHelper.getInstanceByApi('/resource/digital');
  const { loading, listData, listInit, listReset, loadData, handlePageChange, handlePageSizeChange, pagination } =
    useList({
      schema,
      pageSize: 30,
    });

  const handleShowModal = () => {
    visible.value = true;
    selectedItems.value = [...props.modelValue];
  };

  const previewCurrentIndex = ref<number>(NaN);
  const previewVisible = ref<any>(false);
  const handlePreview = (index: number) => {
    previewCurrentIndex.value = index;
    previewVisible.value = true;
  };
  const handleCurrentIndexChange = (index: number) => {
    previewCurrentIndex.value = index;
    if (index < 0) {
      previewVisible.value = false;
    }
  };

  watch(
    () => selectedChapter.value,
    (newVal) => {
      queryParams.value.chapterId = newVal?.value;
    },
  );

  watch(
    () => [grade.value, period.value, classCategory.value],
    async () => {
      listReset();
      coursesList.value = [];
      chaptersList.value = [];
      queryParams.value.chapterId = undefined;
      if (!grade.value || !period.value || !classCategory.value) {
        return;
      }
      const { data } = await request('/resource/course', {
        params: {
          grade: grade.value,
          period: period.value,
          category: classCategory.value,
          pageSize: 1,
        },
      });

      coursesList.value = data?.list || [];
      const courseId = coursesList.value[0]?.id;
      if (courseId) {
        const { data: chapters } = await request('/resource/chapter', {
          params: {
            courseId,
            pageSize: 999,
          },
        });

        chaptersList.value = chapters || [];
      }
    },
    { immediate: true },
  );

  watch(
    () => queryParams.value,
    async () => {
      if (!queryParams.value.chapterId) {
        return;
      }
      await loadData({
        ...queryParams.value,
        type: queryParams.value.type === 'all' ? undefined : queryParams.value.type,
      });
    },
    { deep: true },
  );

  const handleCancel = () => {
    visible.value = false;
    selectedItems.value = [];
  };

  const handleComplete = () => {
    emit('update:modelValue', selectedItems.value);
    visible.value = false;
    selectedItems.value = [];
  };

  const handleAddToSelect = (item: any) => {
    selectedItems.value.push(item);
  };

  onMounted(async () => {
    await listInit();
  });
</script>

<template>
  <div>
    <a-button size="mini" type="primary" @click="handleShowModal">
      <template #icon>
        <IconPlus />
      </template>
      从资源库添加
    </a-button>
    <a-modal v-model:visible="visible" title="选择资源库资源" ok-text="完成" width="860px" :mask-closable="false">
      <div>
        <a-space class="query-wrapper">
          <a-select v-model="classCategory" size="mini" placeholder="课程分类" style="width: 120px">
            <a-option v-for="g in COURSE_CATEGORIES" :key="g" :value="g" :label="g" />
          </a-select>
          <a-select v-model="grade" size="mini" placeholder="选择年级" style="width: 100px">
            <a-option v-for="g in COURSE_GRADES" :key="g" :value="g" :label="g" />
          </a-select>
          <a-select v-model="period" size="mini" placeholder="上下册" style="width: 90px">
            <a-option v-for="g in COURSE_PERIODS" :key="g" :value="g" :label="g" />
          </a-select>
          <a-tree-select
            v-model="selectedChapter"
            size="mini"
            style="width: 210px"
            :data="chaptersList"
            label-in-value
            :field-names="{ title: 'name', key: 'id' }"
            :placeholder="coursesList.length ? '选择章节' : '请先选择课程'"
          >
            <template #label>{{ selectedChapter?.label }}</template>
            <template #tree-slot-title="node"> {{ node.number }} {{ node.name }} </template>
          </a-tree-select>
          <a-select v-model="queryParams.type" size="mini" placeholder="资源类型" style="width: 100px">
            <a-option value="all">全部资源</a-option>
            <a-option v-for="t in resourceType" :key="t.id" :value="t.id">
              {{ t.name }}
            </a-option>
          </a-select>
        </a-space>
      </div>
      <a-divider />
      <a-spin class="mt w-100-percent w-full" :loading="loading">
        <div class="resource-list">
          <digital-resource-icon
            v-for="(resource, index) in listData"
            :key="resource.id"
            :resource="resource"
            :class="{
              selected: selectedItems.some((item) => item.id === resource.id),
            }"
            @preview="() => handlePreview(index)"
          >
            <template #extra-actions>
              <a-button
                v-if="!selectedItems.some((item) => item.id === resource.id)"
                size="mini"
                status="success"
                @click="() => handleAddToSelect(resource)"
                >添加选中</a-button
              >
              <a-button
                v-else
                size="mini"
                status="danger"
                @click="
                  () =>
                    selectedItems.splice(
                      selectedItems.findIndex((item) => item.id === resource.id),
                      1,
                    )
                "
                >取消选择</a-button
              >
            </template>
            <template #append>
              <div class="selected-tag">已选择</div>
            </template>
          </digital-resource-icon>
        </div>
        <a-empty
          v-if="listData.length === 0"
          :description="!queryParams.chapterId ? '请先选择课程章节' : '暂无相关资源'"
        />
        <div class="pager">
          <a-pagination
            :current="pagination.current"
            :total="pagination.total || 0"
            :page-size="pagination.pageSize"
            hide-on-single-page
            @change="handlePageChange"
            @show-size-change="handlePageSizeChange"
          />
        </div>
      </a-spin>
      <template #footer>
        <div class="flex justify-between">
          <div>已选择 {{ selectedItems.length }} 个资源</div>
          <a-space>
            <a-button size="mini" @click="handleCancel">取消</a-button>
            <a-button type="primary" status="success" size="mini" @click="handleComplete">完成</a-button>
          </a-space>
        </div>
      </template>

      <digital-resource-preview
        v-if="!isNaN(previewCurrentIndex) && previewCurrentIndex >= 0 && previewVisible"
        :data-list="listData"
        :current-index="previewCurrentIndex"
        @update:current-index="handleCurrentIndexChange"
      />
    </a-modal>
  </div>
</template>

<style scoped lang="less">
  .query-wrapper {
    margin-bottom: 10px;
  }

  .resource-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .pager {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .selected-tag {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(var(--green-8), 0.8);
    color: #fff;
    padding: 2px 5px;
    font-size: 10px;
  }
  .selected .selected-tag {
    display: block;
  }
</style>
