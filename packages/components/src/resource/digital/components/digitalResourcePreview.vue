<script setup lang="ts">
  import { computed, onMounted, PropType } from 'vue';
  import { getToken } from '@repo/infrastructure/auth';
  import { useUserStore } from '@repo/infrastructure/store';
  import { GameFrame, VideoPlayer } from '@repo/ui/components/data-display';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import DigitalResourceIcon from './digitalResourceIcon.vue';

  const props = defineProps({
    dataList: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
    currentIndex: {
      type: Number,
      default: 0,
    },
  });

  const userInfo = useUserStore();
  const emit = defineEmits(['update:currentIndex']);
  const index = computed({
    get: () => props.currentIndex,
    set: (val) => {
      emit('update:currentIndex', val);
    },
  });

  const item = computed(() => props.dataList[index.value]);
  const toMediaUrl = (id: number) => {
    return `${PROJECT_URLS.GO_PROJECT_API}/resource/digital/media/${id}?token=${getToken()}&userId=${userInfo.id}&loginSource=PC`;
  };

  onMounted(() => {
    // listen esc
    window.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        index.value = -1;
      }
    });
  });
</script>

<template>
  <div v-if="dataList?.length" class="preview-container flex justify-between">
    <a-button
      size="large"
      type="secondary"
      shape="round"
      status="warning"
      class="close-button"
      @click="() => (index = -1)"
    >
      <template #icon>
        <IconClose />
      </template>
    </a-button>
    <div v-if="index > 0" class="prev nav" @click="index -= 1">
      <IconArrowLeft />
      <div class="nav-content">
        <digital-resource-icon :resource="dataList[index - 1]">
          <template #actions>
            <div></div>
          </template>
        </digital-resource-icon>
      </div>
    </div>
    <div v-if="index < dataList.length - 1" class="next nav" @click="index += 1">
      <div class="nav-content">
        <digital-resource-icon :resource="dataList[index + 1]">
          <template #actions>
            <div></div>
          </template>
        </digital-resource-icon>
      </div>
      <IconArrowRight />
    </div>

    <div class="item-content">
      <div class="inner">
        <a-image v-if="item.type === 'image'" :src="item.thumb || toMediaUrl(item.id || item.resourceId)" />
        <div v-else-if="item.type === 'video' || item.type === 'audio'" class="player">
          <video-player :src="toMediaUrl(item.id || item.resourceId)" :poster="item.thumb" />
        </div>
        <game-frame v-else-if="item.type === 'game'" :resource-id="item.id || item.resourceId" />

        <div class="title">
          {{ item.name?.split('.')?.slice(0, -1).join('.') }}
        </div>
        <div class="description">
          {{ item.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .preview-container {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 997;
    left: 0;
    top: 0;

    .nav {
      display: flex;
      align-items: center;

      .content {
        display: flex;
        align-items: center;
        margin-left: 8px;

        .name {
          margin-left: 8px;
        }
      }
    }

    .close-button {
      position: fixed;
      right: 16px;
      top: 16px;
      z-index: 999;
    }

    &:after {
      content: '';
      display: block;
      clear: both;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(#111, 0.9);
    }

    .nav {
      position: fixed;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1001;
      cursor: pointer;

      .nav-content {
        display: flex;
        align-items: center;
        margin-left: 8px;

        .title {
          margin-left: 8px;
        }
      }

      .arco-icon {
        font-size: 32px;
        margin: -20px 0 0 16px;
      }

      &.next {
        right: 0;

        .arco-icon {
          font-size: 32px;
          margin: -20px 16px 0 0;
        }
      }
    }
  }

  .item-content {
    width: 100%;
    height: 100%;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 998;

    .inner {
      width: 70%;
      height: 80%;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .title {
        font-size: 24px;
        margin-top: 16px;
        color: #f2f2f2;
      }

      .description {
        margin-top: 8px;
        font-size: 12px;
        color: #ddd;
      }
    }
  }
</style>
