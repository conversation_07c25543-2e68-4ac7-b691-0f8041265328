<script setup lang="ts">
  import { PropType } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import { useUserStore } from '@repo/infrastructure/store';
  import { IconThunderbolt, IconFileAudio, IconPlayCircle } from '@arco-design/web-vue/es/icon';

  defineProps({
    resource: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    size: {
      type: Object,
      default: () => ({ width: 120, height: 90 }),
    },
  });

  const userStore = useUserStore();
  const { userInfo } = userStore;

  const toMediaUrl = (id: number, thumb?: string) => {
    return `${PROJECT_URLS.GO_PROJECT_API}/resource/digital/media/${id}?token=${getToken()}&userId=${userInfo.id}&loginSource=PC&thumb=${thumb || ''}`;
  };
  const emit = defineEmits(['preview']);

  const handlePreview = () => {
    emit('preview');
  };
</script>

<template>
  <div class="digital-resource-item" :style="{ width: `${size.width + 8}px`, height: `${size.height + 46}px` }">
    <a-image
      v-if="resource.type === 'image'"
      :width="size.width"
      :height="size.height"
      :src="resource.thumb || toMediaUrl(resource.id || resource.resourceId, '1')"
      :preview="false"
      fit="contain"
    />
    <div v-else-if="resource.type === 'video'" class="video-icon">
      <a-image :width="size.width" :height="size.height" :src="resource.thumb" :preview="false" fit="contain" />
      <IconPlayCircle />
    </div>
    <a-image
      v-else-if="resource.thumb"
      :width="size.width"
      :height="size.height"
      :src="resource.thumb"
      :preview="false"
      fit="contain"
    />
    <div v-else class="font-icon" :style="{ width: `${size.width}px`, height: `${size.height}px` }">
      <IconFileAudio v-if="resource.type === 'audio'" />
      <IconThunderbolt v-else-if="resource.type === 'game'" />
    </div>
    <div class="name" :title="resource.name">
      {{ resource.name?.split('.')?.slice(0, -1).join('.') }}
    </div>

    <slot name="actions">
      <div class="actions">
        <a-button type="primary" size="mini" @click="() => handlePreview()">资源预览 </a-button>
        <slot name="extra-actions"></slot>
      </div>
    </slot>

    <slot name="append"></slot>
  </div>
</template>

<style scoped lang="less">
  .digital-resource-item {
    text-align: center;
    cursor: pointer;
    position: relative;
    padding: 4px;
    border-radius: 4px;

    .arco-image,
    :deep(.arco-image-img) {
      border-radius: 4px !important;
    }

    &:hover {
      background: #f2f2f2;

      .actions {
        opacity: 1;
      }
    }

    .name {
      margin: 4px 6px;
      font-size: 12px;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      word-wrap: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .font-icon {
      font-size: 70px;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;

      .arco-icon-thunderbolt {
        stroke: gold;
      }

      .arco-icon-file-audio :deep(path) {
        stroke: deeppink;
      }
    }

    .video-icon {
      position: relative;

      .arco-image {
        border-radius: 4px;
      }

      .arco-icon-play-circle {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 40px;
        color: #fff;
      }
    }

    .actions {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background: rgba(#000, 0.4);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      opacity: 0;

      .arco-btn {
        margin: 4px 0;
      }
    }
  }
</style>
