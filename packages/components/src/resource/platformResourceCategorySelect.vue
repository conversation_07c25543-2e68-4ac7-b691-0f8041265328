<template>
  <div class="wrapper flex flex-col gap-2">
    <slot></slot>
    <div v-for="(items, level) in levels" :key="level" class="level-item">
      <div class="category-title">
        <span v-if="level <= 0">
          <slot name="category-name">课程分类：</slot>
        </span>
        <span v-else> {{ indexes[level - 1] }}级分类： </span>
      </div>
      <div class="category-list">
        <div
          class="category-item"
          :class="{ active: !levelSelected[level] }"
          @click="
            () => {
              if (level === 0) {
                getByParent(0);
                return;
              }
              const pid = levelSelected[level - 1];
              const p = levels[level - 1].find((item) => item.id === pid);
              getByParent(Number(level), p);
            }
          "
        >
          全部
        </div>
        <div
          v-for="(item, index) in items"
          :key="index"
          :class="{ active: levelSelected[level] === item.id }"
          class="category-item"
          @click="() => getByParent(Number(level) + 1, item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, ref, computed } from 'vue';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    categoryApi: {
      type: Object,
      required: true,
    },
    modelValue: {
      type: Number,
      required: true,
    },
  });

  const emits = defineEmits(['update:modelValue', 'loading', 'change']);

  const treeData = ref([]);
  const levels = ref({});
  const levelSelected = ref({});
  const indexes = ['一', '二', '三', '四', '五', '六', '七', '八', '九'];

  const categoryId = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emits('update:modelValue', value);
    },
  });

  const resetSelected = () => {
    levels.value = {
      0: levels.value[0],
    };
    levelSelected.value = {};
  };

  const getByParent = async (level, parent) => {
    emits('loading', true);
    const parentId = parent ? parent.id : null;
    const { data: res } = await request(`${props.categoryApi}/getByParent`, {
      params: {
        parentId,
        deleted: false,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Object.keys(levels.value).forEach((key) => {
      if (key > level - 1 || (!parentId && level === 0)) {
        delete levels.value;
        delete levelSelected.value;
      }
    });

    if (res?.length > 0) {
      levels.value[level] = res;
    }
    if (parentId) {
      levelSelected.value[level - 1] = parentId;
    }
    emits('loading', false);
    emits('change', parent, levelSelected.value);
  };

  onMounted(async () => {
    await getByParent(0);
  });
</script>

<style lang="scss" scoped>
  .level-item {
    margin-bottom: 8px;
    display: flex;
    .category-title {
      margin-right: 10px;
      white-space: nowrap;
    }
    .category-list {
      display: flex;
      flex-wrap: wrap;
      .category-item {
        margin-right: 5px;
        cursor: pointer;
        padding: 0 6px;
        border-radius: 4px;
        &.active {
          color: #165dff;
          font-weight: bold;
        }
        &:hover {
          background: #f2f2f2;
        }
      }
    }
  }
</style>
