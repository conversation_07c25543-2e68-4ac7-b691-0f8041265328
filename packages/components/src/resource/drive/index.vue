<script lang="ts" setup>
  import { computed, onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { PROJECT_URLS } from '@repo/env-config';
  import { usePrompt } from '@repo/ui/components';
  import UploaderButton from '@repo/ui/components/upload/uploaderButton.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import GridView from './components/gridView.vue';
  import ListView from './components/listView.vue';
  import { ResourceAction } from '../constants';

  const props = defineProps({
    extraActions: {
      type: Array as PropType<ResourceAction[]>,
      default: () => [],
    },
    visibleActions: {
      type: Array as PropType<string[]>,
    },
  });

  const viewMode = ref<any>('list');
  const folderTree = ref<any[]>([]);
  const currentFolder = ref<any>({});
  const treeProps = ref<any>({
    key: 'id',
    title: 'name',
    children: 'children',
  });
  const rawFolders = ref<any[]>([]);
  const rawFoldsMap = ref<any>({});
  const dragOvering = ref<any>(false);
  const loadingContent = ref<any>(false);
  const userStore = useUserStore();

  const currentSubFolders = ref<any[]>([]);
  const currentSubFiles = ref<any[]>([]);
  const uploadToFolder = `resource/drive/${dayjs().format('YYYYMMDD')}`;

  const resourceSorting = ref<any>({
    fields: [
      { id: 'resource.name', name: '名称' },
      { id: 'resource.mime', name: '类型' },
      { id: 'resource.size', name: '大小' },
      { id: 'createdDate', name: '创建时间' },
    ],
    field: 'resource.name',
    direction: '+',
  });

  const storageUsage = ref<any>({
    limit: 0,
    used: 0,
    percentage: 0,
  });
  const { prompt } = usePrompt();

  const storageUsageStatus = computed(() => {
    if (storageUsage.value.percentage < 60) {
      return 'success';
    }
    if (storageUsage.value.percentage < 80) {
      return 'warning';
    }
    return 'danger';
  });

  const currentFilesList = computed(() => {
    return [
      ...(currentSubFolders.value || []),
      ...currentSubFiles.value.map((item: any) => {
        return {
          isFile: true,
          ...item,
        };
      }),
    ];
  });

  const breadCrumbs = computed(() => {
    const bc: any[] = [];
    if (currentFolder.value.id === 0) {
      return bc;
    }
    const parentIds = currentFolder.value.parentIds ? [...currentFolder.value.parentIds] : [];
    if (parentIds?.length) {
      parentIds.reverse().forEach((id: number) => {
        if (id === 0 || !rawFoldsMap.value[id]) {
          return;
        }
        bc.push({
          ...rawFoldsMap.value[id],
        });
      });
    }

    bc.push({
      ...currentFolder.value,
      last: true,
    });
    return bc;
  });

  const loadStorageUsage = async () => {
    const { data } = await request('/rehabilitationLibrary/schoolResource/calculateResourceSize', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    storageUsage.value = {
      limit: data.limit,
      used: data.used.toFixed(2),
      percentage: Number(((data.used / data.limit) * 100).toFixed(2)),
    };
  };

  const loadTree = async () => {
    const { data: raw } = await request('/rehabilitationLibrary/schoolResourceCategory', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        sort: 'name',
        pageSize: 1000,
      },
    });
    const tree: any[] = [];

    // 根据 parentId 递归将 raw 转为 tree
    const convert = (parentId: number) => {
      const children = raw.items.filter((item) => item.parentId === parentId);
      children.forEach((child) => {
        child.children = convert(child.id);
      });
      return children;
    };

    raw.items.forEach((item) => {
      if (!item.parentId) {
        tree.push({
          ...item,
          children: convert(item.id),
        });
      }
    });

    rawFolders.value = raw.items;
    rawFoldsMap.value = raw.items.reduce((map, item) => {
      map[item.id] = item;
      return map;
    }, {});
    folderTree.value = [
      {
        id: 0,
        name: '根目录',
        children: tree,
      },
    ];
    if (!currentFolder.value.id) {
      currentFolder.value = {
        id: 0,
        name: '根目录',
      };
    }
  };

  const handleSwitchViewMode = (mode: string) => {
    viewMode.value = mode;
  };

  const handleRefresh = async (reloadTree?: boolean) => {
    loadingContent.value = true;
    try {
      if (reloadTree) {
        await loadTree();
      }
      currentSubFolders.value = rawFolders.value
        .filter((item) => {
          if (!currentFolder.value?.id) {
            return !item.parentId;
          }
          return item.parentId === currentFolder.value.id;
        })
        .sort((a, b) => {
          return a.name.localeCompare(b.name);
        });
      const { data } = await request('/rehabilitationLibrary/schoolResource', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          folderId: currentFolder.value.id || 0,
          pageSize: 1000,
          sort: resourceSorting.value.direction + resourceSorting.value.field,
        },
      });
      currentSubFiles.value = data.items;
    } finally {
      loadingContent.value = false;
    }
  };

  const handleGoFolder = async (data: any) => {
    currentSubFiles.value = [];
    currentSubFolders.value = [];
    currentFolder.value = data || {
      id: 0,
      name: '根目录',
    };

    await handleRefresh();
  };

  const handleCreateNewFolder = async () => {
    try {
      const folderName = await prompt({
        title: '新建文件夹',
        placeholder: '请输入文件夹名称',
        inputPattern: /\S/,
        inputErrorMessage: '文件夹名称不能为空',
      });

      const { data } = await request('/rehabilitationLibrary/schoolResourceCategory', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: {
          name: folderName,
          parentId: currentFolder.value.id,
        },
      });

      // find parent by res.parentIds and push res in tree
      const parentIds = data.parentIds.reverse();
      let parent = folderTree.value[0];
      parentIds.forEach((id) => {
        parent = parent.children.find((item) => item.id === id);
      });
      parent.children.push(data);
      currentSubFolders.value.push(data);
      currentSubFolders.value.sort((a, b) => {
        return a.name.localeCompare(b.name);
      });
      await loadTree();
    } catch (e) {
      Message.error('新建文件夹失败');
    }
  };

  const handleUploaded = async (f: any[], uploadedResources: any[]) => {
    if (!uploadedResources?.length) {
      return;
    }
    const data: any[] = [];
    uploadedResources.forEach((item) => {
      data.push({
        folderId: currentFolder.value.id,
        resource: {
          id: item.id,
          name: item.name,
        },
      });
    });

    await request('/rehabilitationLibrary/schoolResource/batchCreate', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'POST',
      data,
    });
    await handleRefresh();
    await loadStorageUsage();
  };

  const handleChangeSortField = async (command: string) => {
    resourceSorting.value.field = command;
    await handleRefresh();
  };

  const handleChangeSortDirection = async () => {
    resourceSorting.value.direction = resourceSorting.value.direction === '+' ? '-' : '+';
    await handleRefresh();
  };
  const handleFilterResource = async (val: any) => {
    if (val) {
      const { data } = await request(`/rehabilitationLibrary/schoolResource/searchResource/${val}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        params: {
          pageSize: 1000,
          sort: resourceSorting.value.direction + resourceSorting.value.field,
        },
      });
      currentSubFiles.value = data.items;
      currentSubFolders.value = [];
    }
  };
  onMounted(async () => {
    await loadTree();
    await handleGoFolder(currentFolder.value);
    await loadStorageUsage();
  });
  /*
  need add some filter to helper filter some data not created by manager
  * */
</script>

<template>
  <div class="wrapper flex">
    <div class="folder-tree-wrapper">
      <a-card class="folder-list">
        <template #title> 资源目录 </template>
        <a-empty v-if="!folderTree.length" description="暂无数据"></a-empty>
        <a-tree
          v-else
          :data="folderTree"
          :default-expanded-keys="[0]"
          :field-names="treeProps"
          auto-expand-parent
          @select="(keys: any, e: any) => handleGoFolder(e.node)"
        >
          <template #icon>
            <IconFolder />
          </template>
        </a-tree>
      </a-card>
      <!--可用空间显示-->
      <a-card class="storage-usage mt">
        <div class="percentage">
          <a-progress
            :percentage="storageUsage.percentage"
            :show-text="false"
            :status="storageUsageStatus"
            :stroke-width="10"
          />
        </div>
        <div class="title flex justify-between">
          <div>空间用量</div>
          <div class="value"> {{ storageUsage.used }} / {{ storageUsage.limit }} GB </div>
        </div>
      </a-card>
    </div>

    <div class="files">
      <!--路径导航-->
      <a-card class="breadcrumb-wrapper">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <span @click="() => handleGoFolder(folderTree[0])">
              <IconHome />
              根目录
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="(item, index) in breadCrumbs" :key="index">
            <span @click="() => handleGoFolder(item)">{{ item.name }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </a-card>
      <!--内容区域-->
      <a-spin :loading="loadingContent" class="flex" style="flex: 1">
        <a-card class="mt files-wrapper">
          <template #title>
            <div class="flex justify-between">
              <a-space>
                <a-button
                  icon="el-icon-top"
                  plain
                  size="mini"
                  type="primary"
                  @click="
                    () => handleGoFolder(currentFolder.parentId ? rawFoldsMap[currentFolder.parentId] : folderTree[0])
                  "
                  >返回上级
                </a-button>
                <a-space v-if="userStore.isAnyAuthorized(['center:resource:driver:manage', 'resource:manage:drive'])">
                  <uploader-button :sub-folder="uploadToFolder" @uploaded="handleUploaded" />
                  <div>
                    <a-button size="mini" @click="handleCreateNewFolder">
                      <template #icon>
                        <IconFolderAdd />
                      </template>
                      新建文件夹
                    </a-button>
                  </div>
                </a-space>
                <a-input-search
                  v-model="resourceName"
                  :style="{ width: '220px' }"
                  size="mini"
                  placeholder="请输入资源名称"
                  search-button
                  allow-clear
                  @change="handleFilterResource"
                >
                  <template #button-default> 搜索 </template>
                </a-input-search>
              </a-space>
              <a-space>
                <a-dropdown-button
                  size="mini"
                  @click="handleChangeSortDirection"
                  @select="handleChangeSortField as any"
                >
                  {{ resourceSorting.fields.find((item) => item.id === resourceSorting.field)?.name }}
                  <IconArrowDown v-if="resourceSorting.direction === '-'" />
                  <IconArrowUp v-else />
                  <template #content>
                    <a-doption v-for="item in resourceSorting.fields" :key="item.id" :value="item.id"
                      >{{ item.name }}
                    </a-doption>
                  </template>
                </a-dropdown-button>
                <a-button-group>
                  <a-button size="mini" @click="() => handleRefresh()">
                    <template #icon>
                      <IconRefresh />
                    </template>
                  </a-button>
                  <a-button
                    v-if="viewMode === 'list'"
                    class="mode-switch-btn"
                    size="mini"
                    @click="() => handleSwitchViewMode('grid')"
                  >
                    切换网格视图
                  </a-button>
                  <a-button v-else class="mode-switch-btn" size="mini" @click="() => handleSwitchViewMode('list')">
                    切换列表视图
                  </a-button>
                </a-button-group>
              </a-space>
            </div>
          </template>

          <div class="folder-contents">
            <ListView
              v-if="viewMode === 'list'"
              :files="currentSubFiles"
              :folders="currentSubFolders"
              :actions="extraActions"
              :visible-actions="visibleActions"
              :unable-action="['submit']"
              @refresh="handleRefresh"
              @go-folder="handleGoFolder"
            />
            <GridView
              v-else
              :files="currentSubFiles"
              :folders="currentSubFolders"
              @refresh="handleRefresh"
              @go-folder="handleGoFolder"
            />
          </div>

          <a-empty v-if="!currentFilesList.length && !loadingContent" description="该目录下暂无内容"></a-empty>

          <div v-if="dragOvering" class="is-dragover"> 将文件拖到此处上传</div>
        </a-card>
      </a-spin>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .wrapper {
    margin-bottom: 20px;

    .folder-tree-wrapper {
      margin-right: 10px;
      width: 240px;
      display: flex;
      flex-direction: column;

      .storage-usage {
        height: 80px;
        font-size: 14px;
        color: #666;

        .title {
          margin-top: 8px;
        }

        .value {
          font-size: 12px;
        }
      }

      .folder-list {
        flex: 1;

        .el-tree {
          overflow: auto;
          height: calc(100% - 100px);
        }
      }
    }

    .files {
      flex: 1;
      display: flex;
      flex-direction: column;

      .files-wrapper {
        flex: 1;
      }
    }
  }

  .custom-tree-node {
    &.active {
      color: #0e42d2;
      font-weight: 500;
    }

    .name {
      font-size: 14px;
    }
  }

  ::v-deep .el-link,
  .el-link {
    cursor: pointer !important;
  }

  .el-breadcrumb__item {
    height: 30px;
    line-height: 30px;

    span {
      cursor: pointer;
      padding: 0 2px;
    }
  }

  .breadcrumb-wrapper {
    font-size: 12px;

    :deep(.arco-breadcrumb-item) {
      cursor: pointer;
    }
  }
</style>
