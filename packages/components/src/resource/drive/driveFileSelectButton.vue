<script setup lang="ts">
  import { ref } from 'vue';
  import DriveIndex from './index.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    selected: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['selected']);

  const selectedItems = ref([]);
  const modalVisible = ref(props.visible);

  const actions = [
    {
      label: '选择',
      key: 'select',
      visible(record) {
        return record.isFile && selectedItems.value.map((item) => item.url).indexOf(record.url) === -1;
      },
      handler(record) {
        const item = {
          url: record.resource.dvu,
          name: record.resource.name,
        };
        if (props.multiple) {
          selectedItems.value.push(item);
        } else {
          emit('selected', item);
          modalVisible.value = false;
        }
      },
    },
  ];

  const handleOpen = () => {
    selectedItems.value = props.selected;
  };

  // const modalVisible = computed({
  //   get: () => props.visible,
  //   set: (value) => {
  //     emit('update:visible', value);
  //   },
  // });

  const handleShowModal = () => {
    modalVisible.value = true;
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :unmount-on-close="true"
    fullscreen
    title="云盘文件选择"
    render-to-body
    @open="handleOpen"
  >
    <drive-index :visible-actions="['select']" :extra-actions="actions" />
  </a-modal>
  <slot :handle-show="handleShowModal">
    <a-button size="mini" type="outline" @click="handleShowModal">
      <template #icon>
        <IconPlus />
      </template>
      从云盘共享中选择
    </a-button>
  </slot>
</template>

<style scoped lang="scss"></style>
