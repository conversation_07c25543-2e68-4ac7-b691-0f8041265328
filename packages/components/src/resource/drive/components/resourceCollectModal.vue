<script lang="ts" setup>
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@repo/infrastructure/store';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    resource: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:visible']);

  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });
  const currentFolder = ref();
  const collectFolders = ref<any>([]);
  const keyWorld = ref('');

  const filterFolders = computed(() => {
    return collectFolders.value.filter((folder) =>
      folder.name.toLowerCase().includes(keyWorld.value.trim().toLowerCase()),
    );
  });

  const folderResourceCatch = ref<any>({});
  const currentFolderResource = ref([]);

  const userStore = useUserStore();

  const loadFolderResource = async (refresh: boolean = false) => {
    Message.clear('top');
    currentFolderResource.value = [];
    if (!refresh && folderResourceCatch.value?.[currentFolder.value.id]) {
      currentFolderResource.value = folderResourceCatch.value?.[currentFolder.value.id];
      return;
    }
    Message.loading('...');
    const { data: res } = await request('/rehabilitationLibrary/schoolResource', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        folderId: currentFolder.value?.id || 0,
        cbId: userStore.userInfo.id,
        pageSize: 1000,
      },
    });
    folderResourceCatch.value[currentFolder.value.id] = res.items || [];
    currentFolderResource.value = folderResourceCatch.value?.[currentFolder.value.id];
    Message.clear('top');
  };

  const handlePreOk = async () => {
    if (!currentFolder.value?.id) {
      Message.warning('请选择提交目录');
      return false;
    }
    const exits = currentFolderResource.value.filter((item) => item.resource.id === props.resource.resource.id);
    if (exits.length > 0) {
      Message.info('无需重复提交');
      return false;
    }
    try {
      const data: any = {
        ...props.resource,
      };
      data.id = null;
      data.folderId = currentFolder.value.id;
      const { data: res } = await request('/rehabilitationLibrary/schoolResource', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data,
      });
      Message.success('提交成功');
      await loadFolderResource(true);
      return true;
    } finally {
      /**/
    }
  };

  const handleSelectFolder = async (folder: any) => {
    currentFolder.value = folder;
    await loadFolderResource(false);
  };

  const loadCollectFolder = async () => {
    const { data: res } = await request('/rehabilitationLibrary/schoolResourceCategory/getCollectFolder', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    collectFolders.value = res.items;
  };
  const handleWithdraw = async (resource: any) => {
    try {
      const { data: res } = await request(`/rehabilitationLibrary/schoolResource/directDelete/${resource?.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'delete',
      });
      await loadFolderResource(true);
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    await loadCollectFolder();
  });
</script>

<!--资源收集目录-->
<template>
  <a-modal v-model:visible="visible" width="60%" title="已开放搜集的文件夹">
    <div class="flex w-full h-[60vh]">
      <!--目录导航-->
      <div class="w-[25%] mr-1 px-1">
        <div class="text-center font-bold bg-gray-100 rounded overflow-hidden shadow mb-2">
          <div class="flex justify-between items-center py-1 px-2 shadow bg-gray-50">
            <div>目录</div>
            <a-button size="mini" type="outline" @click="handlePreOk">提交到此处</a-button>
          </div>
          <hr class="border-gray-400" />
          <a-input-search v-model="keyWorld" size="mini" class="mt-2" placeholder="请输入目录名称" />
        </div>
        <div class="w-full h-[80%] rounded overflow-auto shadow">
          <div
            v-for="folder in filterFolders"
            :key="folder.id"
            class="flex justify-between items-center py-1 px-2 hover:shadow hover:bg-gray-200 cursor-pointer"
            :class="currentFolder?.id === folder?.id ? 'bg-gray-100 shadow' : ''"
            @click="handleSelectFolder(folder)"
          >
            <div>
              <icon-folder class="mr-2" :style="{ color: [1, 4, 9].includes(i) ? '#0dbc31' : '' }" />
              <span :class="[1, 4, 9].includes(i) ? 'text-green-500' : ''">{{ folder.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-400 w-[1px]" />
      <div class="w-[85%] bg-gray-50 px-2 py-1 ml-1 overflow-auto">
        <div v-if="currentFolderResource.length > 0">
          <div
            v-for="resourceItem in currentFolderResource"
            :key="resourceItem?.id"
            :style="{
              background: 'linear-gradient(90deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
              borderLeft: '3px solid #bababa',
            }"
            class="w-full rounded bg-white px-3 py-1 mb-2 shadow flex justify-between items-center"
          >
            <div>{{ resourceItem?.resource.name }} </div>
            <div>
              <a-popconfirm type="warning" content="确认撤回？" @ok="handleWithdraw(resourceItem)">
                <a-button size="mini" type="text">
                  <span class="text-red-500">撤回</span>
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </div>
        <a-empty v-else description="暂无提交文件到该目录" />
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end items-center space-x-2">
        <a-button size="mini" @click="visible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  ::-webkit-scrollbar {
    height: 1px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #cccccc;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  * {
    scrollbar-width: thin;
    scrollbar-color: #cccccc transparent;
  }
</style>
