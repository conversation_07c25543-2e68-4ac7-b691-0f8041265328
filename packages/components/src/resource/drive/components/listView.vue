<script lang="ts" setup>
  import { computed, PropType, ref, onMounted } from 'vue';
  import FileTypeIcon from '@repo/ui/components/icon/fileTypeIcon.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { UNIT_NATURES_OPTIONS } from '@repo/infrastructure/constants';
  import useDriveList from '../driveList';
  import { ResourceAction } from '../../constants';
  import resourceCollectModal from './resourceCollectModal.vue';

  const props = defineProps({
    folders: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    files: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    actions: {
      type: Array as PropType<ResourceAction[]>,
      default: () => [],
    },
    unableAction: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    visibleActions: {
      type: Array as PropType<string[]>,
      default: () => ['download', 'rename', 'delete'],
    },
  });

  const userStore = useUserStore();

  const emits = defineEmits(['goFolder', 'refresh']);
  const {
    deleteFileConfirmMsg,
    deleteFolderConfirmMsg,
    handleGoFolder,
    handleDownload,
    handleDeleteFile,
    handleDeleteFolder,
    handleFileRename,
    handleFolderRename,
    fileSizeDisplay,
  } = useDriveList({
    emits,
  });

  const lists = computed(() => {
    let fileViewIndex = -1;
    return [
      ...(props.folders || []).map((item: any) => {
        return {
          ...item,
          isFile: false,
          children: undefined,
        };
      }),
      ...props.files.map((item: any) => {
        fileViewIndex += 1;
        return {
          ...item,
          isFile: true,
          children: undefined,
          fileViewIndex,
        };
      }),
    ];
  });

  const attachments = computed(() => {
    return props.files.map((item: any) => {
      return {
        id: item.resource.id,
        name: item.resource.name,
        url: item.resource?.dvu,
      };
    });
  });
  const modalModel = ref([
    {
      key: 'share',
      title: '资源共享',
      visible: false,
      rangeDescription: '共享范围',
    },
    {
      key: 'collect',
      title: '收集目录设置',
      visible: false,
      rangeDescription: '收集范围',
    },
  ]);

  const currentRecord = ref<any>({
    share: {
      unitNature: [],
      shearBoIds: [],
      downloadEnable: false,
    },
    collect: {
      unitNature: [],
      shearBoIds: [],
    },
  });

  const initCurrentRecord = (record: any) => {
    if (!record?.share) {
      record.share = {
        unitNature: [],
        shearBoIds: [],
        downloadEnable: false,
      };
    }
    if (!record?.collect) {
      record.collect = {
        unitNature: [],
        shearBoIds: [],
      };
    }
    currentRecord.value = {
      ...record,
    };
  };
  const rangeButton = ref('part');

  const isSelectAll = (record: any, key: string) => {
    const result = UNIT_NATURES_OPTIONS.filter((item) => !record?.[key]?.unitNature.includes(item.value));
    if (result?.length) {
      rangeButton.value = 'part';
    } else {
      rangeButton.value = 'all';
    }
  };
  // 可指定单位，单位类型
  const handleShear = async (record) => {
    modalModel.value[0].visible = true;
    // shearModalVisible.value = true;
    initCurrentRecord(record);
    isSelectAll(record, 'share');
  };
  const handlePreOk = async () => {
    await request(`/rehabilitationLibrary/schoolResourceCategory/${currentRecord.value?.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: currentRecord.value,
    });
  };
  const getUnableAction = (key: string): boolean => {
    return props.unableAction.includes(key);
  };

  const submitVisible = ref(false);
  const submitRecord = ref();

  const handleSubmit = (record) => {
    submitVisible.value = true;
    submitRecord.value = record;
  };

  const branchOptions = ref<any>([]);
  const loadSchool = async () => {
    const { data: res } = await request('/org/branchOffice/list', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        pageSize: 999,
      },
    });
    branchOptions.value = res.items;
  };
  const rangeSelectVisible = computed(() => {
    return rangeButton.value === 'part';
  });
  const handleSetRange = (key: string, m: any) => {
    rangeButton.value = key;
    if (key === 'all') {
      currentRecord.value[m.key].unitNature = UNIT_NATURES_OPTIONS.map((o) => o.value);
    } else {
      currentRecord.value[m.key].unitNature = [];
    }
  };

  const handleCollect = (record: any) => {
    modalModel.value[1].visible = true;
    initCurrentRecord(record);
    isSelectAll(record, 'collect');
  };
  onMounted(async () => {
    await loadSchool();
  });
</script>

<template>
  <div v-if="lists.length > 0" class="list-view">
    <a-table :data="lists" size="small" stripe>
      <template #columns>
        <a-table-column property="name" title="名称">
          <template #cell="{ record }">
            <div class="list-item-wrapper flex cursor-pointer justify-between">
              <div v-if="record.isFile" class="list-item file" :title="record.name">
                <attachments-preview-display :raw="attachments" :open-index="record.fileViewIndex">
                  <div class="list-item">
                    <a-tooltip :content="record.resource.name">
                      <file-type-icon
                        :file="record.resource?.name"
                        :thumb-size="{ height: 20 }"
                        :url="record.resource?.dvu"
                        class="icon"
                      />
                    </a-tooltip>
                    <span class="name">
                      <slot :row="record" name="file-name">
                        {{ record.name || record.resource?.name }}
                      </slot>
                    </span>
                  </div>
                </attachments-preview-display>
              </div>
              <div v-else class="list-item folder" @click="() => handleGoFolder(record)">
                <a-tooltip :content="record.name">
                  <file-type-icon class="icon" ext="Folder" />
                </a-tooltip>
                <span class="name flex-1">
                  <slot :row="record" name="file-name">
                    {{ record.name }}
                  </slot>
                </span>
              </div>

              <div class="actions">
                <a-space v-if="record.isFile">
                  <a-button
                    v-for="(action, idx) in actions"
                    v-show="action.visible(record)"
                    :key="idx"
                    size="mini"
                    type="text"
                    v-bind="action.props"
                    @click="(e) => action.handler(record, e)"
                  >
                    {{ action.label }}
                  </a-button>
                  <a-button
                    v-if="visibleActions.includes('download') && !unableAction.includes('download')"
                    size="mini"
                    type="text"
                    @click="() => handleDownload(record.resource.id)"
                    >下载
                  </a-button>
                  <a-button
                    v-if="
                      userStore.isAnyAuthorized(['center:resource:drive:manage', 'resource:drive:manage']) &&
                      visibleActions.includes('rename')
                    "
                    size="mini"
                    type="text"
                    @click="() => handleFileRename(record.resource)"
                    >重命名
                  </a-button>
                  <a-button v-if="!getUnableAction('submit')" size="mini" type="text" @click="handleSubmit(record)"
                    >提交</a-button
                  >
                  <div
                    v-if="
                      userStore.isAnyAuthorized([
                        'center:resource:drive:manage',
                        'resource:drive:manage',
                        'resource:drive:use',
                      ]) && visibleActions.includes('delete')
                    "
                  >
                    <a-popconfirm
                      :content="deleteFileConfirmMsg"
                      style="margin-top: 1.5px"
                      @ok="() => handleDeleteFile(record.id)"
                    >
                      <a-button size="mini" type="text">删除</a-button>
                    </a-popconfirm>
                  </div>
                </a-space>
                <a-space v-else>
                  <!--                <el-button type="text" size="mini">进入</el-button>-->
                  <a-button
                    v-if="
                      userStore.isAnyAuthorized(['center:resource:drive:manage', 'resource:drive:manage']) &&
                      visibleActions.includes('rename')
                    "
                    size="mini"
                    type="text"
                    @click="() => handleFolderRename(record)"
                    >重命名
                  </a-button>
                  <a-tooltip
                    v-if="!getUnableAction('share')"
                    content="共享后，共享方可查看或下载指定文件、目录以及子目录。"
                  >
                    <a-button size="mini" type="text" status="success" @click="handleShear(record)">
                      共享
                      <icon-check
                        v-if="record.share?.unitNature?.length || record.share?.shearBoIds?.length"
                        class="ml-1"
                      />
                    </a-button>
                  </a-tooltip>
                  <a-button v-if="!getUnableAction('collect')" size="mini" type="text" @click="handleCollect(record)">
                    搜集
                    <icon-check
                      v-if="record.collect?.unitNature?.length || record.collect?.shearBoIds?.length"
                      class="ml-1"
                    />
                  </a-button>
                  <div
                    v-if="
                      userStore.isAnyAuthorized([
                        'center:resource:drive:manage',
                        'resource:drive:manage',
                        'resource:drive:use',
                      ]) && visibleActions.includes('delete')
                    "
                  >
                    <a-popconfirm
                      :content="deleteFolderConfirmMsg"
                      style="margin-top: 1.5px"
                      @ok="() => handleDeleteFolder(record)"
                    >
                      <a-button size="mini" type="text">删除</a-button>
                    </a-popconfirm>
                  </div>
                </a-space>
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column :width="100" property="size" title="文件尺寸">
          <template #cell="{ record }">
            <span v-if="record.size">{{ fileSizeDisplay(record.resource.size) }}</span>
          </template>
        </a-table-column>

        <a-table-column :width="120" property="createdDate" title="创建时间">
          <template #cell="{ record }">
            <a-tooltip :content="record.createdDate" placement="top">
              <span>{{ record.createdDate?.substring(0, 10) }}</span>
            </a-tooltip>
          </template>
        </a-table-column>

        <a-table-column :width="150" property="createdBy" title="创建人">
          <template #cell="{ record }">
            {{ record.createdBy?.name }}
          </template>
        </a-table-column>
      </template>
    </a-table>

    <a-modal
      v-for="m in modalModel"
      :key="m.key"
      v-model:visible="m.visible"
      :title="m.title"
      width="400px"
      @ok="handlePreOk(m.key)"
    >
      <div class="space-y-4 text-sm text-gray-700 mb-4">
        <div class="flex items-center">
          <label class="w-20 text-right mr-2">{{ m.rangeDescription || '' }}：</label>
          <a-button-group v-model="rangeButton" size="mini">
            <a-button
              key="all"
              :type="rangeButton === 'all' ? 'primary' : ''"
              class="border"
              @click="handleSetRange('all', m)"
            >
              全单位
            </a-button>
            <a-button
              key="part"
              class="border"
              :type="rangeButton === 'part' ? 'primary' : ''"
              @click="handleSetRange('part', m)"
            >
              部分单位
            </a-button>
          </a-button-group>
        </div>
      </div>

      <div class="space-y-4 text-sm text-gray-700">
        <!-- 单位类型 -->
        <div v-if="rangeSelectVisible" class="flex items-center">
          <label class="w-20 text-right mr-2">机构类型：</label>
          <a-select
            v-model="currentRecord[m.key].unitNature"
            placeholder="请选择类型"
            size="mini"
            :options="UNIT_NATURES_OPTIONS"
            allow-search
            multiple
            class="flex-1"
          />
        </div>

        <!-- 单位 -->
        <div v-if="rangeSelectVisible" class="flex items-center">
          <label class="w-20 text-right mr-2">{{ m.key === 'share' ? '分享' : '收集' }}单位：</label>
          <a-select
            v-model="currentRecord[m.key].shearBoIds"
            placeholder="请选择单位"
            size="mini"
            :field-names="{ label: 'name', value: 'id' }"
            :options="branchOptions || []"
            allow-search
            multiple
            class="flex-1"
          />
        </div>

        <div v-if="m.key === 'share'" class="flex items-center">
          <label class="w-20 text-right mr-2">允许下载：</label>
          <a-switch v-model="currentRecord.share.downloadEnable" type="round" />
        </div>

        <a-divider orientation="left" class="!mt-6 !mb-2">
          <span class="text-gray-400 text-xs">说明</span>
        </a-divider>

        <div v-if="m.key === 'share'" class="text-gray-500 text-xs leading-relaxed px-1">
          1. 共享后，共享方拥有对该资源的指定权限，默认为查看权限；<br />
          2. 共享目录不可下载。
        </div>
        <div v-if="m.key === 'collect'" class="text-gray-500 text-xs leading-relaxed px-1">
          1. 指定当前目录为资源搜集目录，教师可将自己云盘空间的文件提交到该目录<br />
        </div>
      </div>
    </a-modal>
    <resourceCollectModal v-if="submitVisible" v-model:visible="submitVisible" :resource="submitRecord" />
  </div>
</template>

<style lang="less" scoped>
  .list-item-wrapper {
    .actions {
      //visibility: hidden;
      width: 150px;
      display: flex;
      justify-content: flex-end;
    }

    //&:hover {
    //  .actions {
    //    visibility: visible;
    //  }
    //}
  }

  .list-item {
    flex: 1;
    display: flex;
    align-items: center;
    max-width: 300px;

    .name {
      height: 30px;
      line-height: 30px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.file .icon {
      margin-right: 8px;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.folder .icon {
      width: 30px;
      height: 30px;
      font-size: 12px;
      display: flex;
      margin-right: 8px;
      align-items: center;
      justify-content: center;
    }

    ::v-deep .file-icon {
      display: flex;
      align-items: center;
    }
  }
</style>
