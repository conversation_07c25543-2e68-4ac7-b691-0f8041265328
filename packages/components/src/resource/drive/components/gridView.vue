<script lang="ts" setup>
  import { ref, PropType, computed } from 'vue';
  import FileTypeIcon from '@repo/ui/components/icon/fileTypeIcon.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import useDriveList from '../driveList';
  import { ResourceAction } from '../../constants';

  const props = defineProps({
    folders: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    files: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    actions: {
      type: Array as PropType<ResourceAction[]>,
      default: () => [],
    },
    visibleActions: {
      type: Array as PropType<string[]>,
      default: () => ['download', 'rename', 'delete'],
    },
  });

  const userStore = useUserStore();

  const emits = defineEmits(['goFolder', 'refresh']);
  const {
    deleteFileConfirmMsg,
    deleteFolderConfirmMsg,
    handleGoFolder,
    handleDownload,
    handleDeleteFile,
    handleDeleteFolder,
    handleFileRename,
    handleFolderRename,
  } = useDriveList({ emits });

  const menuVisible = ref<any>(false);
  const currentItem = ref<any>(null);

  const handleRemoveListener = () => {
    menuVisible.value = false;
    document.removeEventListener('click', handleRemoveListener);
  };

  const styleMenu = (menu: any, event: any) => {
    if (event.clientX > 1800) {
      menu.style.left = `${event.clientX - 100}px`;
    } else {
      menu.style.left = `${event.clientX + 1}px`;
    }
    document.addEventListener('click', handleRemoveListener); // 给整个document新增监听鼠标事件，点击任何位置执行foo方法
    if (event.clientY > 700) {
      menu.style.top = `${event.clientY}px`;
    } else {
      menu.style.top = `${event.clientY}px`;
    }
  };

  const handleContextMenu = (item: any, e: any) => {
    menuVisible.value = false;
    menuVisible.value = true;
    e.preventDefault(); // 关闭浏览器右键默认事件
    currentItem.value = {
      ...item,
      isFile: !!item.resource,
    };
    const menu = document.querySelector('#resourceGridContext');
    styleMenu(menu, e);
  };

  const filesList = computed(() => {
    let fileViewIndex = -1;
    return [
      ...props.files.map((item: any) => {
        fileViewIndex += 1;
        return {
          ...item,
          isFile: true,
          children: undefined,
          fileViewIndex,
        };
      }),
    ];
  });

  const attachments = computed(() => {
    return props.files.map((item: any) => {
      return {
        id: item.resource.id,
        name: item.resource.name,
        url: item.resource?.dvu,
      };
    });
  });
</script>

<template>
  <div class="grid-list">
    <div
      v-for="(item, index) in folders"
      :key="index"
      class="grid-item folder"
      @click="() => handleGoFolder(item)"
      @contextmenu.stop="(e) => handleContextMenu(item, e)"
    >
      <file-type-icon ext="Folder" />
      <span class="name break-all">{{ item.name }}</span>
    </div>
    <div
      v-for="item in filesList"
      :key="item.id"
      class="grid-item file"
      @contextmenu.stop="(e) => handleContextMenu(item, e)"
    >
      <attachments-preview-display :raw="attachments" :open-index="item.fileViewIndex" class="mx-auto text-center">
        <a-tooltip :content="item.resource?.name">
          <div class="text-center mx-auto">
            <file-type-icon :file="item.resource?.name" :thumb-size="{ height: 50 }" :url="item.resource?.dvu" />
            <div class="name break-all">{{ item.resource?.name }}</div>
          </div>
        </a-tooltip>
      </attachments-preview-display>
    </div>

    <div v-show="menuVisible" id="resourceGridContext" class="menu">
      <div v-if="currentItem">
        <div v-if="currentItem.isFile" class="contextmenu__item" @click="() => handleDownload(currentItem.resource.id)">
          <i class="el-icon-download"></i> 下载
        </div>
        <div
          v-if="userStore.isAnyAuthorized(['center:resource:drive:manage', 'resource:drive:manage'])"
          class="contextmenu__item"
          @click="() => (currentItem.isFile ? handleFolderRename(currentItem) : handleFileRename(currentItem.resource))"
          >重命名
        </div>
        <a-popconfirm
          v-if="
            currentItem.isFile && userStore.isAnyAuthorized(['center:resource:drive:manage', 'resource:drive:manage'])
          "
          :title="deleteFileConfirmMsg"
          @confirm="() => handleDeleteFile(currentItem.id)"
        >
          <template #content>
            <div class="contextmenu__item">
              <i class="el-icon-delete"></i>
              删除
            </div>
          </template>
        </a-popconfirm>
        <a-popconfirm
          v-else-if="userStore.isAnyAuthorized(['center:resource:drive:manage', 'resource:drive:manage'])"
          :title="deleteFolderConfirmMsg"
          @confirm="() => handleDeleteFolder(currentItem.id)"
        >
          <template #content>
            <div class="contextmenu__item">
              <i class="el-icon-delete"></i>
              删除
            </div>
          </template>
        </a-popconfirm>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  @gridSize: 120px;

  .grid-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, @gridSize);
    grid-gap: 10px;

    .grid-item {
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(@gridSize);
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .icon {
        font-size: 40px;
        margin-bottom: 10px;
      }

      .name {
        line-height: 120%;
        font-size: 12px;
        color: #333;
        padding: 4px 0;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      :deep .file-icon {
        font-size: 30px;
      }
    }
  }

  .contextmenu__item {
    display: block;
    padding: 0 16px;
  }

  .contextmenu__item:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .menu {
    position: absolute;
    background-color: #fff;
    /*height: 106px;*/
    font-size: 12px;
    color: #444040;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    white-space: nowrap;
    z-index: 1000;
  }

  .contextmenu__item:hover {
    cursor: pointer;
    background: rgb(var(--primary-10));
    border-color: rgb(var(--primary-10));
    color: #fff;
  }
</style>
