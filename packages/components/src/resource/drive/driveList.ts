import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { Message } from '@arco-design/web-vue';
import { usePrompt } from '@repo/ui/components';

const useDriveList = ({ emits }: { emits: any }) => {
  const deleteFolderConfirmMsg = '确定要删除该文件夹吗？（文件夹中所有文件将会被删除）';
  const deleteFileConfirmMsg = '确定要删除该文件吗？';

  // const emits = defineEmits(['goFolder', 'refresh']);
  const { prompt } = usePrompt();

  const fileSizeDisplay = (size: number) => {
    if (size < 1024) {
      return `${size} B`;
    }
    if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    }
    if (size < 1024 * 1024 * 1024) {
      return `${(size / 1024 / 1024).toFixed(2)} MB`;
    }
    return `${(size / 1024 / 1024 / 1024).toFixed(2)} GB`;
  };

  const handleGoFolder = (folder: any) => {
    emits('goFolder', folder);
  };

  const handleDownload = async (resourceId: number) => {
    Message.info('正在下载...');
    const res: string = await request(`/common/uploadedResource/download/${resourceId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    window.open(res);
  };

  const handleDeleteFile = async (resourceId: number) => {
    const res = await request(`/rehabilitationLibrary/schoolResourceCategory/${resourceId}`, {
      method: 'DELETE',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    if (res.data) {
      Message.success('删除成功');
      emits('refresh', true);
    }
    emits('refresh', true);
  };

  const handleDeleteFolder = async (folder: any) => {
    await request(`/rehabilitationLibrary/schoolResourceCategory/${folder.id}`, {
      method: 'DELETE',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    Message.success('删除成功');
    emits('refresh', true);
  };

  const handleFolderRename = async (folder: any) => {
    const name = await prompt({
      title: '重命名文件夹',
      placeholder: '请输入文件夹名',
      raw: folder.name,
    });
    await request(`/rehabilitationLibrary/schoolResourceCategory/${folder.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: {
        ...folder,
        name,
      },
    });
    Message.success('重命名成功');
    emits('refresh', true);
  };

  const handleFileRename = async (file: any) => {
    const ext = file.name.split('.').pop();
    const name = await prompt({
      title: '重命名文件',
      placeholder: '请输入文件名',
      raw: file.name,
    });
    await request(`/common/uploadedResource/${file.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: {
        ...file,
        name: `${name}.${ext}`,
      },
    });

    Message.success('重命名成功');
    emits('refresh', true);
  };

  return {
    deleteFileConfirmMsg,
    deleteFolderConfirmMsg,
    fileSizeDisplay,
    handleGoFolder,
    handleDownload,
    handleDeleteFile,
    handleDeleteFolder,
    handleFolderRename,
    handleFileRename,
  };
};

export default useDriveList;
