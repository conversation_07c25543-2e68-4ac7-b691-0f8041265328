<script setup lang="ts">
  import { computed, onMounted, PropType, watch } from 'vue';

  const props = defineProps({
    pageConfig: {
      type: Object,
      default: null,
    },
    mode: {
      type: String as PropType<'published' | 'design'>,
      default: 'design',
    },
  });

  const pageStyle = computed(() => {
    const styles = {
      width: `${props.pageConfig.pageWidth}px`,
      flex: `0 0 ${props.pageConfig.pageWidth}px`,
    };

    if (props.mode === 'design') {
      if (props.pageConfig.background) {
        styles.background = props.pageConfig.background;
      } else {
        styles.backgroundColor = props.pageConfig.backgroundColor || '#fff';
      }
    }

    return styles;
  });

  const innerStyle = computed(() => {
    return {
      padding: `${props.pageConfig.pageMargin}px`,
    };
  });
</script>

<template>
  <div v-bind="$attrs" :style="pageStyle">
    <div :style="innerStyle">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
