<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { VideoPlayer } from '@repo/ui/components';
  // eslint-disable-next-line import/no-cycle
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import { hexToRgba } from './utils';

  const props = defineProps({
    pageConfig: {
      type: Object as PropType<any>,
      required: true,
    },
    widget: {
      type: Object as PropType<any>,
      required: true,
    },
    mode: {
      type: String as PropType<'design' | 'published'>,
      default: 'design',
    },
  });

  const widgetStyle = computed(() => {
    return {
      marginTop: `${props.widget.style.marginY}px`,
      marginRight: `${props.widget.style.marginX}px`,
      marginBottom: `${props.widget.style.marginY}px`,
      marginLeft: `${props.widget.style.marginX}px`,
      width: `${props.widget.style.width}px`,
      height: `${props.widget.style.height}px`,
      maxWidth: `${props.pageConfig.width}px`,
      backgroundColor: hexToRgba(props.widget.style.backgroundColor, props.widget.style.opacity),
      borderRadius: `${props.widget.style.rounded}px`,
      padding: `${props.widget.style.padding}px`,
    };
  });

  const wrapperStyle = computed(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: props.widget.style.textAlign,
      alignItems: props.widget.style.textAlign,
    };
  });

  const textStyle = computed(() => {
    return {
      fontSize: `${props.widget.style.fontSize}px`,
      color: props.widget.style.color,
      textAlign: props.widget.style.textAlign,
    };
  });

  const ossProcessor = getOssProcessor();

  const ossCoverUrl = computed(() => {
    if (!props.widget.content) {
      return null;
    }

    return ossProcessor.videoCoverUrl(props.widget.content, 600, 400);
  });

  const getSubject = () => {
    return props.widget.style.subject?.replace(/\n/g, '<br />');
  };
</script>

<template>
  <div>
    <div v-if="widget.content">
      <div v-if="mode === 'published'" :style="wrapperStyle">
        <video-player :autoplay="false" :style="widgetStyle" :src="widget.content" class="widget-content" />
        <div v-if="widget.style?.subject" class="subject" :style="textStyle" v-html="getSubject()" />
      </div>
      <div v-else :style="wrapperStyle">
        <img :src="ossCoverUrl" class="widget-content" :style="widgetStyle" />
        <div v-if="widget.style?.subject" class="subject" :style="textStyle" v-html="getSubject()" />
      </div>
    </div>
    <div v-else>请上传视频</div>
  </div>
</template>

<style scoped lang="scss">
  .subject {
    text-align: center;
    padding: 10px 0;
  }
</style>
