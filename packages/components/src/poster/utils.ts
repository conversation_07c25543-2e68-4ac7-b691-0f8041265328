import { defineAsyncComponent } from 'vue';

const getWidgetDisplayCmp = (widget) => {
  switch (widget.type) {
    case 'Text':
      return defineAsyncComponent(() => import('@repo/components/poster/textView.vue'));
    case 'Image':
      return defineAsyncComponent(() => import('@repo/components/poster/imageView.vue'));
    case 'Rich':
      return defineAsyncComponent(() => import('@repo/components/poster/richView.vue'));
    case 'Video':
      return defineAsyncComponent(() => import('@repo/components/poster/videoView.vue'));
    case 'Attachments':
      return defineAsyncComponent(() => import('@repo/components/poster/attachmentView.vue'));
    default:
      return null;
  }
};

const hexToRgba = (hex, opacity) => {
  if (!hex) return '';
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity / 100})`;
};

export { getWidgetDisplayCmp, hexToRgba };
