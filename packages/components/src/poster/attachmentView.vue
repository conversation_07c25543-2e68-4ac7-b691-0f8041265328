<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import FileTypeIcon from '@repo/ui/components/icon/fileTypeIcon.vue';
  // eslint-disable-next-line import/no-cycle
  import { hexToRgba } from './utils';

  const props = defineProps({
    pageConfig: {
      type: Object as PropType<any>,
      required: true,
    },
    widget: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const attachments = computed(() => {
    return props.widget.content ? JSON.parse(props.widget.content) : [];
  });

  const widgetStyle = computed(() => {
    return {
      paddingLeft: `${props.widget.style.paddingX}px`,
      paddingRight: `${props.widget.style.paddingX}px`,
      paddingTop: `${props.widget.style.paddingY}px`,
      paddingBottom: `${props.widget.style.paddingY}px`,
      marginTop: `${props.widget.style.marginY}px`,
      marginRight: `${props.widget.style.marginX}px`,
      marginBottom: `${props.widget.style.marginY}px`,
      marginLeft: `${props.widget.style.marginX}px`,
      fontSize: `${props.widget.style.fontSize}px`,
      textAlign: props.widget.style.textAlign,
      color: props.pageConfig.color || props.widget.style.color,
      backgroundColor: hexToRgba(props.widget.style.backgroundColor, props.widget.style.opacity),
      borderRadius: `${props.widget.style.rounded}px`,
    };
  });

  const handleDownload = (url) => {
    window.open(url);
  };
</script>

<template>
  <div v-if="!widget.content" :style="widgetStyle"> 请上传附件 </div>
  <div v-else>
    <a-space direction="vertical">
      <div v-for="(a, idx) in attachments" :key="idx">
        <a-space :style="widgetStyle" class="cursor-pointer" @click="() => handleDownload(a.url)">
          <file-type-icon :file="a.name" :url="a.url" />
          <a>{{ a.name }}</a>
        </a-space>
      </div>
    </a-space>
  </div>
</template>

<style scoped lang="scss"></style>
