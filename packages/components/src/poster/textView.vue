<script setup lang="ts">
  import { computed, PropType } from 'vue';
  // eslint-disable-next-line import/no-cycle
  import { hexToRgba } from './utils';

  const props = defineProps({
    pageConfig: {
      type: Object as PropType<any>,
      required: true,
    },
    widget: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const content = computed(() => {
    return props.widget.content?.replace(/\n/g, '<br>') || '';
  });

  const widgetStyle = computed(() => {
    return {
      paddingLeft: `${props.widget.style.paddingX}px`,
      paddingRight: `${props.widget.style.paddingX}px`,
      paddingTop: `${props.widget.style.paddingY}px`,
      paddingBottom: `${props.widget.style.paddingY}px`,
      marginTop: `${props.widget.style.marginY}px`,
      marginRight: `${props.widget.style.marginX}px`,
      marginBottom: `${props.widget.style.marginY}px`,
      marginLeft: `${props.widget.style.marginX}px`,
      fontSize: `${props.widget.style.fontSize}px`,
      textAlign: props.widget.style.textAlign,
      color: props.pageConfig.color || props.widget.style.color,
      backgroundColor: hexToRgba(props.widget.style.backgroundColor, props.widget.style.opacity),
      borderRadius: `${props.widget.style.rounded}px`,
    };
  });
</script>

<template>
  <div :style="widgetStyle" class="widget-content" v-html="content"></div>
</template>

<style scoped lang="scss"></style>
