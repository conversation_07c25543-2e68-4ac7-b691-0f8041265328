<script setup lang="ts">
  import { computed, PropType } from 'vue';
  // eslint-disable-next-line import/no-cycle
  import { hexToRgba } from './utils';

  const props = defineProps({
    pageConfig: {
      type: Object as PropType<any>,
      required: true,
    },
    widget: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const widgetStyle = computed(() => {
    return {
      marginTop: `${props.widget.style.marginY}px`,
      marginRight: `${props.widget.style.marginX}px`,
      marginBottom: `${props.widget.style.marginY}px`,
      marginLeft: `${props.widget.style.marginX}px`,
      width: `${props.widget.style.width}px`,
      height: `${props.widget.style.height}px`,
      maxWidth: `${props.pageConfig.width}px`,
      backgroundColor: hexToRgba(props.widget.style.backgroundColor, props.widget.style.opacity),
      borderRadius: `${props.widget.style.rounded}px`,
      paddingLeft: `${props.widget.style.paddingX}px`,
      paddingRight: `${props.widget.style.paddingX}px`,
      paddingTop: `${props.widget.style.paddingY}px`,
      paddingBottom: `${props.widget.style.paddingY}px`,
    };
  });

  const wrapperStyle = computed(() => {
    return {
      display: 'flex',
      justifyContent: props.widget.style.textAlign,
    };
  });
</script>

<template>
  <div :style="wrapperStyle">
    <img v-if="widget.content" :src="widget.content" :style="widgetStyle" class="widget-content" />
    <div v-else>请上传图片</div>
  </div>
</template>

<style scoped lang="scss"></style>
