<template>
  <div class="test-container">
    <!-- 数字进度显示 -->
    <div class="progress-text"> {{ currentIndex + 1 }} / {{ characters.length }} </div>

    <!-- 字符显示 -->
    <div v-if="currentChar" class="character-display">
      {{ currentChar }}
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-button type="primary" status="success" size="large" :loading="isLoading" @click="handleResponse(true)">
      </a-button>
      <a-button type="primary" status="danger" size="large" :loading="isLoading" @click="handleResponse(false)">
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';

  interface Props {
    characters: string[];
  }

  interface Result {
    char: string;
    known: boolean;
    time: number;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    (e: 'finish', results: Result[]): void;
  }>();

  const currentIndex = ref(0);
  const startTime = ref(0);
  const results = ref<Result[]>([]);

  const currentChar = computed(() => props.characters[currentIndex.value]);

  onMounted(() => {
    startTime.value = Date.now();
  });

  const isLoading = ref(false);
  const handleResponse = async (known: boolean) => {
    isLoading.value = true;
    const responseTime = Date.now() - startTime.value;

    const existingIndex = results.value.findIndex((result) => result.char === currentChar.value);

    if (existingIndex === -1)
      results.value.push({
        char: currentChar.value,
        known,
        time: responseTime,
      });

    if (currentIndex.value < props.characters.length - 1) {
      currentIndex.value += 1;
      startTime.value = Date.now();
    } else {
      emit('finish', results.value);
    }
    isLoading.value = false;
  };
</script>

<style scoped>
  .test-container {
    padding: 40px 0;
    text-align: center;
    font-family: 'KaiTi', serif;
  }

  .progress-text {
    font-size: 16px;
    color: #86909c;
    margin-bottom: 20px;
  }

  .character-display {
    font-size: 120px;
    line-height: 1.5;
    margin: 40px 0;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
  }

  .action-buttons .arco-btn {
    padding: 0 40px;
    height: 48px;
    font-family: 'Arial', sans-serif;
  }
</style>
