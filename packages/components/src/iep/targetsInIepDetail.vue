<script setup lang="ts">
  import { PropType } from 'vue';
  import IepTargetsListContent from './iepTargetsListContent.vue';
  import IepTargetsListContentPrint from './iepTargetsListContentPrint.vue';

  const props = defineProps({
    record: {
      type: Object as PropType<Record<string, any>>,
      default: () => {
        return {} as Record<string, any>;
      },
    },
  });
</script>

<template>
  <div>
    <div class="normal-display">
      <iep-targets-list-content :iep="record" :editable="false" />
    </div>
    <div class="print-display">
      <iep-targets-list-content-print :iep="record" :editable="false" />
    </div>
  </div>
</template>

<style scoped lang="scss">
  @media screen {
    .normal-display {
      display: block;
    }
    .print-display {
      display: none;
    }
  }

  @media print {
    .normal-display {
      display: none;
    }
    .print-display {
      display: block;
    }
  }
</style>
