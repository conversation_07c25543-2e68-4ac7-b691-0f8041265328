<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import IepLongTermTargetsModal from './iepLongTermTargetsModal.vue';
  import IepShortTermTargetsModal from './iepShortTermTargetsModal.vue';
  import IepLongTermTargetEditModal from './iepLongTermTargetEditModal.vue';
  import IepChapterContentsViewModal from './iepChapterContentsViewModal.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
    isHeadTeacher: {
      type: Boolean,
      default: false,
    },
  });

  const userInfo = useUserStore();

  const emit = defineEmits(['update:modelValue', 'hide']);
  const { loading, setLoading } = useLoading();
  const longTermTargetsAddVisible = ref(false);
  const shortTermTargetsVisible = ref(false);
  const longTermEditVisible = ref(false);
  const chapterContentsVisible = ref(false);

  const currentLongTermTarget = ref<any>({});

  const targets = ref<any[]>([]);

  const targetsMap = ref<Record<number, any[]>>({});

  const sameDateAndDomainSpan = (courseName, options) => {
    const { record, column, rowIndex } = options;
    const { dateRangeDisplay, domain } = record;
    const listData = targetsMap.value[courseName];

    // 只处理 date 和 domain 列的合并
    if (column.dataIndex === 'dateRangeDisplay') {
      let startIndex = rowIndex;

      // 寻找相同日期和域的连续行的开始索引
      while (startIndex > 0 && listData[startIndex - 1].dateRangeDisplay === dateRangeDisplay) {
        startIndex -= 1;
      }

      // 计算相同日期和域的连续行数
      let rowSpanCount = 0;
      while (
        startIndex + rowSpanCount < listData.length &&
        listData[startIndex + rowSpanCount].dateRangeDisplay === dateRangeDisplay
      ) {
        rowSpanCount += 1;
      }

      // 如果是该组的起始行，返回合并的行数
      if (rowIndex === startIndex) {
        return { rowspan: rowSpanCount, colspan: 1 };
      }
      return { rowspan: 0, colspan: 0 };
    }
    if (column.dataIndex === 'domain') {
      let startIndex = rowIndex;

      // 寻找相同日期和域的连续行的开始索引
      while (startIndex > 0 && listData[startIndex - 1].domain === domain) {
        startIndex -= 1;
      }

      // 计算相同日期和域的连续行数
      let rowSpanCount = 0;
      while (startIndex + rowSpanCount < listData.length && listData[startIndex + rowSpanCount].domain === domain) {
        rowSpanCount += 1;
      }

      // 如果是该组的起始行，返回合并的行数
      if (rowIndex === startIndex) {
        return { rowspan: rowSpanCount, colspan: 1 };
      }
    }

    return { rowspan: 0, colspan: 0 };
  };

  const loadTargets = async () => {
    targetsMap.value = {};
    setLoading(true);
    try {
      let { data } = await request(`/resourceRoom/individualizedEducation/targets/${props.iep.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          pageSize: 9999,
        },
      });
      targets.value = data || [];

      // isHeadTeacher 如果是分组负责人或则是班主任跳过过滤，学科教师只看自己创建的...
      if (!props.isHeadTeacher) {
        const { id } = userInfo;
        data = data.filter((item) => item.createdBy.id === id);
      }
      data?.forEach((item) => {
        const courseName = item.courseName || '其他';
        targetsMap.value[courseName] = targetsMap.value[courseName] || [];
        targetsMap.value[courseName].push({
          ...item,
          dateRangeDisplay: item.dateRange?.join(' ~ '),
        });

        // sort by dateRangeDisplay and domain
        targetsMap.value[courseName].sort((a, b) => {
          if (a.dateRangeDisplay === b.dateRangeDisplay) {
            return a.domain.localeCompare(b.domain);
          }
          return a.dateRangeDisplay.localeCompare(b.dateRangeDisplay);
        });
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShowLongTermTargetAdd = () => {
    longTermTargetsAddVisible.value = true;
  };

  const handleShowShortTermTargets = (longTermTarget) => {
    currentLongTermTarget.value = longTermTarget;
    shortTermTargetsVisible.value = true;
  };

  const handleShowViewChapterContents = (longTermTarget) => {
    currentLongTermTarget.value = longTermTarget;
    chapterContentsVisible.value = true;
  };

  const handleShowEdit = (target) => {
    currentLongTermTarget.value = target;
    longTermEditVisible.value = true;
  };

  const handleDeleteLongTermTarget = async (target) => {
    setLoading(true);
    try {
      await request(`/resourceRoom/individualizedEducationTarget/${target.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'DELETE',
      });
      await loadTargets();
    } finally {
      setLoading(false);
    }
  };

  // watch(
  //   () => props.iep,
  //   async (value) => {
  //     if (value?.id) {
  //       await loadTargets();
  //     }
  //   },
  //   { immediate: true, deep: true },
  // );

  onMounted(async () => {
    if (props.iep?.id) {
      await loadTargets();
    }
  });
  defineExpose({
    targetsMap,
  });
</script>

<template>
  <a-skeleton v-if="loading" :animatio="true">
    <a-skeleton-line :rows="5" />
  </a-skeleton>
  <div v-else>
    <a-empty v-if="!targets?.length">
      <div> 暂无长期目标，请先添加 </div>
      <div v-if="editable" class="mt-2">
        <a-button type="primary" size="mini" @click="handleShowLongTermTargetAdd">
          <template #icon>
            <IconPlus />
          </template>
          添加长期目标
        </a-button>
      </div>
    </a-empty>
    <a-button
      v-else-if="editable"
      size="mini"
      type="primary"
      :disabled="!editable"
      @click="handleShowLongTermTargetAdd"
    >
      <template #icon>
        <IconPlus />
      </template>
      添加长期目标
    </a-button>
    <div v-for="(t, courseName) in targetsMap" :key="courseName" class="mt-2">
      <a-divider orientation="left">
        {{ courseName }}
        <IconDoubleDown />
      </a-divider>
      <a-table
        :data="t"
        :pagination="false"
        size="mini"
        :bordered="{ cell: true }"
        :span-method="(options) => sameDateAndDomainSpan(courseName, options)"
      >
        <template #columns>
          <a-table-column title="#" :width="50">
            <template #cell="{ rowIndex }">
              {{ rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="时间范围" data-index="dateRangeDisplay" :width="120" />
          <a-table-column title="">
            <template #title>
              <span class="text-sm text-blue-700">长期目标</span>
            </template>
            <a-table-column title="领域" data-index="domain" :width="140" />
            <a-table-column title="长期目标" data-index="content" />
            <a-table-column title="初评分" data-index="preScore" :width="70" />
            <a-table-column title="达成分" data-index="afterScore" :width="70" />
          </a-table-column>

          <a-table-column title="短期目标">
            <template #title>
              <span class="text-sm text-blue-600">短期目标</span>
            </template>
            <a-table-column title="短期目标" :width="100">
              <template #cell="{ record }">
                <a-link
                  size="mini"
                  type="text"
                  :status="record.shortTermTargets?.length ? 'success' : 'warning'"
                  @click="() => handleShowShortTermTargets(record)"
                >
                  <span class="text-xs font-medium">查看 ({{ record.shortTermTargets?.length || 0 }})</span>
                </a-link>
              </template>
            </a-table-column>
            <a-table-column title="前测" data-index="shortTermPreScoreAvg" :width="60" />
            <a-table-column title="后测" data-index="afterScore" :width="60" />
          </a-table-column>
          <a-table-column title="教学实施">
            <template #title>
              <span class="text-sm text-blue-500">教学实施</span>
            </template>
            <a-table-column title="教学目标" :width="100">
              <template #cell="{ record }">
                <a-link
                  size="mini"
                  type="text"
                  :status="record.chapterContents?.length ? 'success' : 'warning'"
                  @click="() => handleShowViewChapterContents(record)"
                >
                  <span class="text-xs font-medium">查看 ({{ record.chapterContents?.length || 0 }})</span>
                </a-link>
              </template>
            </a-table-column>
            <a-table-column title="达成分" data-index="afterScore" :width="70" />
          </a-table-column>
          <a-table-column v-if="editable" title="操作" :width="120">
            <template #cell="{ record }">
              <a-space>
                <a-button size="mini" :disabled="!editable" @click="() => handleShowEdit(record)">
                  <template #icon>
                    <IconEdit />
                  </template>
                </a-button>
                <a-popconfirm content="确定要删除这个长期目标吗?" @ok="() => handleDeleteLongTermTarget(record)">
                  <a-button size="mini" status="danger" :disabled="!editable">
                    <template #icon>
                      <IconDelete />
                    </template>
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <div class="mb-2 h-4"> </div>
    </div>
  </div>

  <iep-long-term-targets-modal
    v-if="longTermTargetsAddVisible"
    v-model:visible="longTermTargetsAddVisible"
    :targets="targets"
    :iep="iep"
    :editable="editable"
    @ok="loadTargets"
  />
  <iep-short-term-targets-modal
    v-if="currentLongTermTarget"
    v-model:visible="shortTermTargetsVisible"
    :long-term-target="currentLongTermTarget"
    :iep="iep"
    :editable="editable"
    @ok="loadTargets"
  />
  <iep-long-term-target-edit-modal
    v-model:visible="longTermEditVisible"
    :long-term-target="currentLongTermTarget"
    :iep="iep"
    :editable="editable"
    @ok="loadTargets"
  />
  <iep-chapter-contents-view-modal
    v-model:visible="chapterContentsVisible"
    :editable="editable"
    :long-term-target="currentLongTermTarget"
  />
</template>

<style scoped lang="scss"></style>
