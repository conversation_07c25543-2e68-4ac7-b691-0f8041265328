<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { debouncedWatch } from '@vueuse/core';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Modal } from '@arco-design/web-vue';

  const props = defineProps({
    shortTermTargets: {
      type: Array as PropType<any[]>,
      required: true,
    },
    courseId: {
      type: Number,
      default: null,
    },
    longTermTargetDateRange: {
      type: Object as PropType<any>,
    },
  });

  const emit = defineEmits(['import']);
  const keyword = ref('');
  const loading = ref(false);
  const items = ref<any[]>([]);

  const handleSearch = async () => {
    loading.value = true;
    try {
      const { data } = await request('/resourceRoom/personalIepTargetLibrary', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          'content|teachingScene': `%${keyword.value}%`,
          'pageSize': 20,
        },
      });

      items.value = data.items;
      items.value = items.value.filter((item) => item?.customCriterion?.id === props.courseId);
    } finally {
      loading.value = false;
    }
  };
  const modalStyle = {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  };

  const existsContents = computed(() => props.shortTermTargets.map((item) => item.content));

  const handleImport = (record) => {
    if (existsContents.value.includes(record.content)) {
      Modal.confirm({
        title: '提示',
        content: '该目标内容已存在，是否继续引用？',
        onOk: () => {
          emit('import', record);
        },
      });
    } else {
      emit('import', record);
    }
  };

  const handleOpen = async () => {
    await handleSearch();
  };

  debouncedWatch(keyword, handleSearch, { debounce: 300 });

  const disabledDate = (current) => {
    const dateRange = props.longTermTargetDateRange?.map((item) => new Date(item));
    return current && (current < dateRange[0] || current > dateRange[1]);
  };
</script>

<template>
  <a-modal
    v-bind="$attrs"
    :mask="false"
    draggable
    :modal-style="modalStyle"
    :render-to-body="false"
    simple
    :width="950"
    hide-cancel
    ok-text="完成"
    @open="handleOpen"
  >
    <template #title>
      <a-space>
        从短期目标库引用
        <a-tooltip content="拖动标题栏移动位置">
          <IconQuestionCircle />
        </a-tooltip>
      </a-space>
    </template>
    <a-input v-model.trim="keyword" allow-clear placeholder="请输入关键字进行搜索" />
    <a-table class="mt-2" :loading="loading" :data="items" size="mini" :pagination="false">
      <template #columns>
        <a-table-column title="时间范围" data-index="dateRange">
          <template #cell="{ record }">
            <a-range-picker v-model="record.dateRange" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标内容" data-index="content" />
        <a-table-column title="教学场景" data-index="teachingScene" />
        <a-table-column title="操作">
          <template #cell="{ record }">
            <a-button size="mini" @click="() => handleImport(record)"> 引用 </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>
