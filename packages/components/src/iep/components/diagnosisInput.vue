<script setup lang="ts">
  import ListTableInput from '@repo/ui/components/form/inputComponents/inputControl/listTableInput.vue';
  import UploadInput from '@repo/ui/components/form/inputComponents/inputControl/uploadInput.vue';
  import { computed, PropType, ref, watch } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { studentAttachmentsColumns, studentAttachmentTypeOptionsMap } from '../../constants';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:record']);

  const loading = ref(false);
  const modalVisible = ref(false);
  const attachments = ref([]);
  const attachmentType = computed(() => props.schemaField?.inputWidgetProps?.diagnosisType || 'attachments');
  const attachmentTitle = computed(() => studentAttachmentTypeOptionsMap[attachmentType.value]?.label || '附件');

  const attachmentField = studentAttachmentsColumns.find((column) => column.key === 'attachments');

  const handleShowModal = () => {
    modalVisible.value = true;
    if (props?.record?.student) {
      attachments.value =
        props.record.student.attachments?.find((attachment) => attachment.type === attachmentType.value)?.attachments ||
        [];
    }
  };

  const handleClose = () => {
    modalVisible.value = false;
  };

  const handleSave = async () => {
    loading.value = true;

    const student = {
      ...props.record.student,
      attachments: [
        ...props.record.student.attachments?.filter((attachment) => attachment.type !== attachmentType.value),
        {
          type: attachmentType.value,
          attachments: attachments.value,
        },
      ],
    };

    try {
      await request(`/resourceRoom/student/${props.record.student.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: student,
      });

      Message.success(`保存 ${attachmentTitle.value} 附件成功`);

      emit('update:record', {
        ...props.record,
        student,
      });
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => props.record?.student,
    (student) => {
      if (student) {
        attachments.value =
          student.attachments?.find((attachment) => attachment.type === attachmentType.value)?.attachments || [];
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<template>
  <list-table-input v-bind="$attrs" :schema-field="schemaField">
    <template #extra-actions>
      <a-button size="mini" :disabled="!record?.student?.attachments" @click="handleShowModal">
        <template #icon>
          <IconAttachment />
        </template>
        查看附件({{ attachments.length || 0 }})
      </a-button>
    </template>
  </list-table-input>

  <a-modal
    v-if="modalVisible"
    v-model:visible="modalVisible"
    :ok-loading="loading"
    :on-before-ok="handleSave"
    :title="attachmentTitle"
    :render-to-body="false"
    @close="handleClose"
  >
    <upload-input v-model="attachments" :schema-field="attachmentField" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
