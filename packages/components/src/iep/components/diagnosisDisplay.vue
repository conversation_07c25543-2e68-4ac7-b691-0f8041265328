<script lang="ts" setup>
  import ListTableDisplay from '@repo/ui/components/data-display/components/listTableDisplay.vue';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';
  import { computed, ref } from 'vue';

  const props = defineProps({
    record: {
      type: Object,
      required: true,
    },
    schemaField: {
      type: Object,
      required: true,
    },
  });

  const visible = ref(false);
  const attachments = computed(() => {
    return (
      props.record.student?.attachments?.find(
        (attachment) => attachment.type === props.schemaField.inputWidgetProps.diagnosisType,
      )?.attachments || []
    );
  });

  const handleShowAttachments = () => {
    visible.value = true;
  };
</script>

<template>
  <list-table-display v-bind="$attrs" :schema-field="schemaField">
    <template #prepend>
      <a-button size="mini" class="mb-2" :disabled="!attachments?.length" @click="handleShowAttachments">
        <template #icon>
          <IconAttachment />
        </template>
        查看附件({{ attachments.length || 0 }})
      </a-button>
    </template>
  </list-table-display>

  <attachment-preview-modal v-model="visible" :current-file-index="0" :files-list="attachments" />
</template>
