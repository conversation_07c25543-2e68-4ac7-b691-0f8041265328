<script setup lang="ts">
  import { onMounted, PropType, watch, ref, defineEmits, computed } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { groupBy } from 'lodash';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Modal } from '@arco-design/web-vue';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    record: {
      type: Object,
      default: () => ({}),
    },
    modelValue: {
      type: Object,
      default: () => ({}),
    },
  });

  const emits = defineEmits(['update:record', 'update:modelValue']);

  const modelValue: any = computed({
    get: () => {
      if (props.modelValue?.id) return props.modelValue;
      return {
        id: null,
      };
    },
    set: (val: any) => {
      emits('update:modelValue', val);
    },
  });
  const addNumberSequence = (raw, parentNs?: any) => {
    let i = 1;
    raw.forEach((item) => {
      item.numberSequence = parentNs ? `${parentNs}.${i}` : `${i}`;
      item.fullName = `${item.numberSequence} ${item.name}`;
      if (item.children && item.children.length) {
        addNumberSequence(item.children, item.numberSequence);
      }
      i += 1;
    });
    return raw;
  };

  const criterionLoading = ref(false);
  const criterionScoresDefine = ref();
  const currentCriterion = ref();
  const criterionDetail = ref();
  const queryParams = ref<any>({});

  const handleCriterionChange = async (criterionId, criterion) => {
    criterionScoresDefine.value = groupBy(criterion?.scores || [], (item) => item?.targetLevel || '-');
    currentCriterion.value = criterion;

    if (criterionId) {
      criterionLoading.value = true;
      try {
        const { data } = await request(`/evaluation/customCriterionDetail/getTree/${criterionId}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });

        if (!data?.length) {
          Modal.error({ content: '该量表尚未导入评测项目' });
        }

        criterionDetail.value = addNumberSequence(data);
      } finally {
        criterionLoading.value = false;
      }
    }
  };

  const handleChange = (val: any) => {
    emits('update:record', {
      ...props.record,
      customCriterionDetail: {
        id: val,
      },
    });
  };

  onMounted(async () => {
    await handleCriterionChange(props.record?.customCriterion?.id, props.record?.customCriterion);
  });
  watch(
    () => props.record?.customCriterion,
    async (newVal) => {
      await handleCriterionChange(newVal?.id, newVal);
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>

<template>
  <a-tree-select
    v-model="modelValue.id"
    :loading="criterionLoading"
    :data="criterionDetail"
    allow-search
    class="w-auto"
    placeholder="请选择目标"
    :field-names="{
      key: 'id',
      title: 'fullName',
    }"
    @change="handleChange"
  />
</template>

<style scoped lang="scss"></style>
