<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import IepTargetsListContent from './iepTargetsListContent.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
    },
    isHeadTeacher: {
      type: Boolean,
      default: false,
    },
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:modelValue', 'hide']);
  const targets = ref<any[]>([]);
  const targetsMap = ref<Record<number, any[]>>({});
  const iepContent = ref<any>(null);

  const modalVisible = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });

  // first: 纳入统计就不能修改了，second: 可继续修改
  const canEdit = computed(() => {
    return !(props.iep?.statisticsStatus === 'Enable');
  });

  const handleOpen = () => {
    modalVisible.value = true;
  };

  const handleClose = () => {
    modalVisible.value = false;
    targetsMap.value = {};
    targets.value = [];

    emit('hide');
  };
  const resList = ref();
  /*  const isFinished = (): boolean => {
  const res = Object.values(iepContent.value?.targetsMap);
  resList.value = res;
  if (res.length === 0) {
    return true;
  }
  res.forEach((item: any) => {
    item.forEach((value: any) => {
      if (value.shortTermTargets.length === 0) {
        return false;
      }
    });
  });
  return true;
}; */

  const isFinished = (): boolean => {
    const res = Object.values(iepContent.value?.targetsMap);
    resList.value = res;
    if (res.length === 0) {
      return true;
    }

    const isIncomplete = res.some((item: any) => {
      return item.some((value: any) => {
        return value.shortTermTargets.length === 0;
      });
    });

    return !isIncomplete;
  };

  const handlePreOk = async () => {
    if (isFinished()) {
      emit('update:modelValue', false);
    } else {
      Message.warning('完善短期目标后才能提交');
    }
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    fullscreen
    hide-cancel
    ok-text="完成"
    @ok="handlePreOk"
    @open="handleOpen"
    @close="handleClose"
  >
    <template #title>
      {{ iep?.student?.gradeClass?.name || '' }} {{ iep?.student?.name }} {{ iep?.gradePeriod }} 的个别化教育计划
    </template>
    <div v-if="modalVisible">
      <iep-targets-list-content
        v-bind="$attrs"
        ref="iepContent"
        :is-head-teacher="isHeadTeacher"
        :iep="iep"
        :editable="canEdit"
      />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
