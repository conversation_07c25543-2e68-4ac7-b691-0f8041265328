<script setup lang="ts">
  import { computed, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { getTopLevelNodeByLeafId } from '../utils/assessment';
  import AssessmentDetailView from '../assessment/assessmentDetailView.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    targets: {
      type: Array as PropType<any[]>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    assessmentForms: {
      type: Array as PropType<any[]>,
      required: true,
    },
    assessmentResultsMap: {
      type: Object as PropType<any>,
      required: true,
    },
    criterionId: {
      type: Number,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const { loading, setLoading } = useLoading();
  const { loading: detailLoading, setLoading: setDetailLoading } = useLoading();

  const formData = ref({});
  const snapshot = ref({});
  const selectedTargets = ref(props.targets || []);
  const newSelectedTargets = ref([]);
  const selectedLongTermTargets = computed(() => {
    return selectedTargets.value.map((item) => item.content);
  });

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });

  const handleSelect = (record) => {
    const domainNode = getTopLevelNodeByLeafId(snapshot.value.details, record.id);
    const target = {
      domain: domainNode.name, // 比如 "武将"
      content: record.name, // 比如 "关羽"
      preScore: record.score, // 比如关羽的初始评分
      termType: 'LongTerm', // 长期目标
      // longTermTargetId: record.id,
    };
    selectedTargets.value.push(target);
    newSelectedTargets.value.push(target);
  };
  const handleRemove = (record) => {
    let index = -1;
    let newIndex = -1;
    selectedTargets.value.forEach((idx, item) => {
      if (item.content === record.name) index = idx;
    });
    newSelectedTargets.value.forEach((idx, item) => {
      if (item.content === record.name) newIndex = idx;
    });
    selectedTargets.value.splice(index, 1);
    newSelectedTargets.value.splice(newIndex, 1);
  };
  function sorted(beSorted: any) {
    if (beSorted.length > 0) {
      beSorted.forEach((res) => {
        if (res.children.length > 0) {
          res.children.forEach((item) => {
            item.children.sort((a, b) => {
              if (a.score && b.score) return a.score - b.score;
              if (a.score && !b.score) return -1;
              if (!a.score && b.score) return 1;
              return 0;
            });
          });
        }
      });
    }
  }
  const loadResultDetail = async () => {
    setDetailLoading(true);
    try {
      const { data } = await request(`/evaluation/customAssessmentSnapshot/byResult/${formData.value.assessResultId}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      sorted(data.details);
      snapshot.value = data;
    } finally {
      setDetailLoading(false);
    }
  };

  const handleClose = () => {
    modalVisible.value = false;
    newSelectedTargets.value = [];
    selectedTargets.value = [];
    snapshot.value = {};
    formData.value = {};
  };

  const handleOpen = async () => {
    newSelectedTargets.value = [];
    selectedTargets.value = props.targets || [];
  };

  const handleOk = async () => {
    emit('ok', newSelectedTargets.value);
    handleClose();
  };
  const currentAssessResultId = ref(0);
  const handelChange = (val) => {
    currentAssessResultId.value = val;
    loadResultDetail();
  };

  const listSort = (rawList: any[]) => {
    // 递归遍历 rawList, 其children全部按 children[].score 正序排序
    const raw = rawList.map((item) => {
      if (item.children && item.children.length) {
        item.children = listSort(item.children);
      }
      return item;
    });

    // 对 rawList 进行排序
    raw.sort((a, b) => {
      if (a.score && b.score) return a.score - b.score;
      if (a.score && !b.score) return -1;
      if (!a.score && b.score) return 1;
      return 0;
    });

    return raw;
  };
  watch(
    () => props.criterionId,
    async (val) => {
      formData.value = {
        ...formData.value,
        criterionId: val,
      };
      if (val) {
        formData.value.assessResultId = props.assessmentResultsMap[val][currentAssessResultId.value].value;
        await loadResultDetail();
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="引用目标池"
    :width="1000"
    @open="handleOpen"
    @ok="handleOk"
    @close="handleClose"
  >
    <a-skeleton v-if="loading" animation>
      <a-skeleton-line :rows="5" />
    </a-skeleton>
    <div v-else>
      <a-form :model="formData" size="mini" auto-label-width>
        <div class="flex gap-2">
          <a-form-item label="评估结果">
            <a-select v-model="formData.criterionId" disabled :options="assessmentForms" />
          </a-form-item>
          <a-form-item label="评估次数">
            <a-select
              v-if="formData.criterionId"
              v-model="formData.assessResultId"
              :options="assessmentResultsMap[formData.criterionId]"
              @change="handelChange"
            /><!--添加一个事件，然后让它改变当前的  props.assessmentResultsMap[val][0]  这个零用选中的表示-->
            <small v-else>请先选择评估结果</small>
          </a-form-item>
        </div>
      </a-form>

      <a-skeleton v-if="detailLoading" animation>
        <a-skeleton-line :rows="5" />
      </a-skeleton>
      <assessment-detail-view v-else :snapshot="snapshot" :details-data="snapshot.details" :data-sort="listSort">
        <template #extra-columns>
          <a-table-column title="选择">
            <template #cell="{ record }">
              <div v-if="!record.children?.length">
                <a-button
                  v-if="selectedLongTermTargets.includes(record.name)"
                  size="mini"
                  status="success"
                  type="outline"
                  @click="() => handleRemove(record)"
                >
                  <template #icon>
                    <IconCheck />
                  </template>
                  已选择
                </a-button>
                <a-button v-else size="mini" @click="() => handleSelect(record)">选择</a-button>
              </div>
            </template>
          </a-table-column>
        </template>
      </assessment-detail-view>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
