<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
    },
    columns: {
      type: Array,
    },
  });
  const emits = defineEmits(['update:modelValue', 'viewAttachment']);
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    emits('update:modelValue', false);
  };
  const filteredColumns = computed(() => {
    return props.columns.filter((column) => {
      // 使用 Object.prototype.hasOwnProperty.call 来检查属性
      return (
        (props.data?.items?.length > 0 &&
          Object.prototype.hasOwnProperty.call(props.data?.items[0], column.dataIndex) &&
          column.slotName !== 'attachmentsList') ||
        column.slotName === 'operation'
      );
    });
  });
  const archives = ref(null);
  const drawerVisible = ref(false);
  const loadViews = async (val) => {
    // statistics/d2dEducationShare/record/49/1768
    await request(`statistics/d2dEducationShare/record/${val.recordId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      archives.value = res.data;
      console.log(res.data);
      drawerVisible.value = true;
    });
  };
  const currentItems = ref(null);
  const viewAttachment = (record) => {
    loadViews(record);
    currentItems.value = record;
    emits('viewAttachment', record);
  };
  const handleDrawerOk = () => {
    drawerVisible.value = false;
  };
  const handleDrawerCancel = () => {
    drawerVisible.value = false;
  };

  const isImage = (type: string) => {
    return [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'svgz',
      'bmp',
      'tiff',
      'tif',
      'ico',
      'cur',
      'avif',
      'apng',
      'heic',
      'heif',
    ].includes(type.toLowerCase());
  };

  onMounted(async () => {});
</script>

<template>
  <a-modal :visible="visible" :closable="false" :on-before-ok="handlePreOk" width="80%" @cancel="handleCancel">
    <a-table :columns="filteredColumns" :data="data.items">
      <template #date="{ record }">
        <span>{{ record?.date.split(' ')[0] }}</span>
      </template>
      <template #sendTeachers="{ record }">
        <a-tag v-for="(item, index) in record.sendTeacherList" :key="index" class="mr-2">{{ item.name }}</a-tag>
      </template>
      <template #operation="{ record }">
        <span class="cursor-pointer text-blue-500" @click="viewAttachment(record)">查看记录</span>
      </template>
    </a-table>
  </a-modal>
  <a-drawer
    width="100%"
    :visible="drawerVisible"
    unmount-on-close
    :closable="false"
    @ok="handleDrawerOk"
    @cancel="handleDrawerCancel"
  >
    <template #title>
      {{ currentItems?.studentName }}
      <a-tag class="ml-5">
        {{ currentItems?.gender }}/{{ currentItems?.age || '~' + '岁' }}/{{ currentItems?.disorderType }}
      </a-tag>
      <!--      <a-tag class="ml-5">{{ archives?.archive?.period }}</a-tag>-->
      <!--      <a-tag class="ml-5">{{ archives?.archive?.personInCharge }}</a-tag>-->
    </template>
    <!--    <div v-for="(document, index) in archives.documents" :key="index" @click="handleViewDoc(document)">-->
    <!--      <a-link class="w-full flex-1 justify-start">-->
    <!--        {{ index + 1 + '、' + document.docName }}-->
    <!--      </a-link>-->
    <!--    </div>-->
    <a-tabs :default-active-key="0">
      <a-tab-pane v-for="(item, index) in archives.documents" :key="index" :title="item.docName">
        <div v-if="item?.docUrl">
          <div v-if="item?.docUrl.split('.').pop().toLowerCase() === 'pdf'">
            <iframe
              :src="`${item?.docUrl}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none; min-height: 800px"
            ></iframe>
          </div>
          <div v-else-if="isImage(item?.docUrl.split('.').pop().toLowerCase())">
            <div class="image-container text-center mx-auto">
              <img :src="item?.docUrl" alt="" class="mx-auto" />
            </div>
          </div>
          <div v-else>
            <iframe
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${item?.docUrl}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none; min-height: 800px"
            ></iframe>
          </div>
        </div>
        <div v-else-if="item.attachments.length > 0" class="flex-1 justify-around space-x-4">
          <a-image
            v-for="photo in item.attachments"
            :key="photo.id"
            width="200px"
            height="300px"
            :src="photo.udf1"
          ></a-image>
        </div>
        <a-empty v-else />
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>

<style scoped lang="scss"></style>
