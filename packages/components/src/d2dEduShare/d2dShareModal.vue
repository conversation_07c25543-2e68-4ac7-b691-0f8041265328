<template>
  <a-modal :visible="visible" :closable="false" :on-before-ok="handlePreOk" @cancel="handleClose">
    <template #title>
      <div class="title">
        送教信息分享
        <span v-if="shareAll">(全部)</span>
        <span v-else>(仅筛选)</span>
      </div>
    </template>
    <a-form :loading="loading" label-width="110px" label-position="right" size="small" label-suffix=":">
      <a-form-item label="页面标题" required>
        <a-input v-model.trim="shareData.title" placeholder="在此输入打开分享页面显示的标题" />
      </a-form-item>
      <a-form-item label="查看密码" required>
        <div class="flex">
          <div>
            <a-input v-model="shareData.password" placeholder="输入查看密码" type="number" />
          </div>
          <div class="grey ml-2">仅支持数字</div>
        </div>
      </a-form-item>
      <a-form-item label="分享有效期" required>
        <div class="flex">
          <div>
            <a-radio-group v-model="shareData.expireDays">
              <a-radio v-for="(day, index) in daysList" :key="index" :value="day">{{ day }} 天</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="链接地址">
        <div class="flex">
          <div v-if="shareData.id" class="link">
            <a-input readonly :model-value="assessmentLink()" />
          </div>
          <div class="copy-wrapper ml-2">
            <a-button v-if="shareData.id" size="small" type="primary" @click="handleCopy">复制链接及查看密码 </a-button>
            <a-button v-else size="small" type="primary" @click="handleSubmitAndCopy"> 创建分享链接并复制 </a-button>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { onMounted, ref, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import SparkMD5 from 'spark-md5';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import dayjs from 'dayjs';
  import { useUserStore } from '@repo/infrastructure/store';
  // import { getUserInfo } from '@/common/ts/permission';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    shareAll: {
      type: Boolean,
      default: false,
    },
    filters: {
      type: Object,
    },
    versionType: {
      type: String,
      default: 'simple',
    },
    // searchKeyword: {
    //   type: String,
    //   default: '',
    // },
  });
  const emits = defineEmits(['update:modelValue']);

  const loading = ref(false);
  const daysList = [1, 3, 7, 30];
  const shareData = ref<any>({
    type: 'D2dEdu',
    expireDays: 3,
    linkCode: '',
    configs: {
      page: undefined,
      pageSize: undefined,
    },
  });

  const restData = () => {
    shareData.value = {
      type: 'D2dEdu',
      expireDays: 3,
      linkCode: '',
      configs: {
        page: undefined,
        pageSize: undefined,
      },
    };
  };

  // const shareLink = computed(() => {
  //   const { origin, pathname } = window.location;
  //   const linkCodes = shareData.value.linkCode;
  //   return `${origin}${pathname}?sc=${linkCodes}`;
  // });
  // const shareLink = (): string => {
  //   const { origin, pathname } = window.location;
  //   const linkCodes = shareData.value.linkCode;
  //   return `${origin}${'/module/d2dEduShare.html'}?sc=${linkCodes}`;
  // };

  const userStore = useUserStore();
  const assessmentLink = () => {
    const linkCodes = shareData.value.linkCode;
    return `${userStore.getCompanyBindingUrl()}/module/d2dEduShare.html?sc=${linkCodes}`;
  };

  const handleClose = () => {
    restData();
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    restData();
    emits('update:modelValue', false);
  };

  const getLinkCode = (): string => {
    const random = Math.floor(Math.random() * 1000000);
    shareData.value.password = random;
    const spark = new SparkMD5();
    const token = getToken();
    const str = `${random}${Date.now()}${token}`;
    spark.appendBinary(str);
    return spark.end().toLocaleLowerCase();
  };

  const handleCopy = () => {
    const copyText = `请查看：${shareData.value.title}\n查看密码： ${shareData.value.password}\n链接： ${shareData.value.visitUrl}`;
    navigator.clipboard.writeText(copyText).then(() => {
      Message.clear('top');
      Message.success('分享链接已复制到剪切板');
    });
  };

  const handleSubmitAndCopy = async () => {
    if (!shareData.value.title) {
      Message.error('请输入分享页面标题');
      return;
    }
    if (!shareData.value.password) {
      Message.error('请输入查看密码');
      return;
    }
    if (!shareData.value.expireDays) {
      Message.error('请选择分享有效期');
      return;
    }

    const data = {
      versionType: props.versionType,
      ...shareData.value,
      shareAll: props.shareAll,
      visitUrl: assessmentLink(),
    };

    const rawPass = shareData.value.password;
    loading.value = true;
    try {
      if (!shareData.value.id) {
        await request('/statistics/statisticsShareLink', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'post',
          data,
        }).then((res) => {
          shareData.value = res.data;
        });
      } else {
        shareData.value = await request(`/statistics/statisticsShareLink/${data.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
          data,
        });
      }

      shareData.value.password = rawPass;

      const copyText = `请查看：${shareData.value.title}\n查看密码： ${shareData.value.password}\n链接： ${shareData.value.visitUrl}`;
      // const me = this;
      navigator.clipboard.writeText(copyText).then(() => {
        Message.success('分享链接已复制到剪切板');
      });
    } catch (e) {
      Message.error('创建分享链接失败');
      console.error(e);
    } finally {
      loading.value = false;
    }
  };

  onMounted(async () => {
    // const userInfo = getUserInfo();
    // const titleRandom = Math.floor(Math.random() * 10000);
    // const today = moment().format('YYYYMMDD');
    // this.shareData.title = `来自${userInfo.name}的送教信息分享-${today}-${titleRandom}`;

    /*    const random = Math.floor(Math.random() * 1000000);
    shareData.value.password = random;
    const spark = new SparkMD5();
    const token = getToken();
    const str = `${random}${Date.now()}${token}`;
    spark.appendBinary(str); */

    shareData.value.linkCode = getLinkCode();
    if (!props.shareAll) {
      shareData.value.configs = {
        ...shareData.value.configs,
        ...props.filters,
      };
    }
  });
</script>

<style lang="scss" scoped></style>
