<script setup lang="ts">
  import { nextTick, onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';

  const props = defineProps({
    record: {
      type: Object as PropType<any>,
    },
  });
  const schema = ref();
  const currentRecord = ref<any>();
  const ready = ref(false);
  const records = ref();

  const loadSendRecords = async () => {
    if (!props.record.integerList.length) Message.warning('当前选项暂无数据');
    const { data: res } = await request('/resourceRoom/sendEducationRecord/findByIds', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: props.record.integerList,
    });
    currentRecord.value = res.items[0];
    records.value = res.items;
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationRecord');

    schema.value.schemaFields = schema.value.schemaFields.map((field) => {
      if (field.key === 'date') {
        field.inputWidgetProps = {
          ...field.inputWidgetProps,
          disabledDate: (current) => {
            return current && (current < dateRange.value?.[0] || current > dateRange.value?.[1]);
          },
        };
      }
      return field;
    });
    await loadSendRecords();
    ready.value = !!records.value.length;
  });
  const selectRecord = (item: any) => {
    ready.value = false;
    currentRecord.value = item;
    nextTick(() => {
      ready.value = true;
    }, 200);
  };
</script>

<template>
  <a-split v-model:size="splitSize" class="h-full w-full border-none mt-2" min="180px" max="300px">
    <template #first>
      <a-typography-paragraph>
        <div
          v-for="item in records"
          class="hover:text-blue-500 hover:cursor-pointer mb-2 justify-start space-x-2 rounded mr-4"
          :style="{
            backgroundColor: currentRecord.id === item.id ? 'rgba(41,146,255,0.76)' : '',
            color: currentRecord.id === item.id ? 'rgb(255,255,255)' : '',
          }"
          @click="selectRecord(item)"
        >
          <div class="ml-2 mt-4">
            <span class="mr-2">{{ item.sendEducationPlan.student.name }}</span>
            <span class="text-xs">{{ item.date }}</span>
          </div>
          <hr />
        </div>
      </a-typography-paragraph>
    </template>
    <template #second>
      <a-typography-paragraph class="p-4">
        <customize-component
          v-if="ready"
          :current-record="currentRecord"
          module="SendEducationRecord"
          page="View"
          model-value=""
        >
          <template #default>
            <record-detail v-if="ready" ref="recordDetailRef" :raw="currentRecord" :schema="schema" />
          </template>
        </customize-component>
        <a-empty v-else />
      </a-typography-paragraph>
    </template>
  </a-split>
</template>

<style scoped lang="scss"></style>
