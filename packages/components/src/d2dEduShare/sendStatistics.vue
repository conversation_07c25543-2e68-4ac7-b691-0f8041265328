<script lang="ts" setup>
  import { useUserStore } from '@repo/infrastructure/store';
  import { ref, onMounted, computed, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  // import PhotoList from '@/views/manage/components/center/dataCenter/photoList.vue';
  // import ViewModal from '@/views/manage/components/center/dataCenter/viewModal.vue';
  // import D2dShareModal from '@/views/manage/components/center/dataCenter/d2dShareModal.vue';
  import studentDetailButton from '@repo/components/student/studentDetailButton.vue';
  import avatarDisplay from '@repo/ui/components/data-display/components/avatarDisplay.vue';

  import PhotoList from './photoList.vue';
  import ViewModal from './viewModal.vue';
  import D2dShareModal from './d2dShareModal.vue';

  const props = defineProps({
    shareLinkInfo: {
      type: Object,
    },
  });
  const userStore = useUserStore();
  // const frameSrc = userStore.getMainProjectUrl('', '/d2dEduShare.html');
  const schoolList = ref<any>([]);
  const gradClassList = ref<any>([]);
  const searchData = ref<any>(null);
  const filterSearchData = ref<any>(null);
  const currentIndexCard = ref({ index: 0, key: 'teachersCount' });
  const size = 'mini';
  const period = ref<any>([]);

  const filterMenu = ref<any>([
    { label: '所属学校', options: [], span: 5 },
    { label: '班级分组', options: gradClassList.value, span: 5 },
    { label: '学期', options: period.value, span: 4 },
    {
      label: '状态',
      options: [
        { label: '已完成', value: 1 },
        { label: '进行中', value: 0 },
      ],
      span: 3,
    },
  ]);
  const briefInfo = ref([
    { label: '送教教师', key: 'teachersCount', unit: '人' },
    { label: '送教学生', key: 'studentsCount', unit: '人' },
    { label: '送教学校', key: 'schoolsCount', unit: '所' },
    { label: '送教次数', key: 'sendTimesCount', unit: '次' },
    { label: '送教照片', key: 'sendPhotosCount', unit: '张' },
  ]);
  const baseColumns = ref([
    { title: '姓名/名称', dataIndex: 'studentName', slotName: 'studentName' },
    { title: '老师名字', dataIndex: 'teacherName', slotName: 'teacherName' },
    { title: '所属学校', dataIndex: 'branchOfficeName', slotName: 'branchOfficeName' },
    { title: '送教次数', dataIndex: 'count', slotName: 'count' },
    { title: '送教教师', dataIndex: 'teachersCount', slotName: 'teachersCount' }, // 数量

    { title: '性别', dataIndex: 'gender', slotName: 'gender' },
    { title: '年龄', dataIndex: 'age', slotName: 'age' },
    { title: '障碍类型', dataIndex: 'disorderType', slotName: 'disorderType' },

    { title: '送教学生', dataIndex: 'studentsCount' },

    { title: '送教日期', dataIndex: 'date', slotName: 'date' },
    { title: '送教教师', dataIndex: 'sendTeachers', slotName: 'sendTeachers' }, // teacher
    { title: '现场照片', dataIndex: 'attachmentsList', slotName: 'attachmentsList' }, // 应该只有插槽
    { title: '课时', dataIndex: 'classHours', slotName: 'classHours' },
    { title: '类型', dataIndex: 'sendType', slotName: 'sendType' },

    { title: '学期', dataIndex: 'period', slotName: 'period' },

    { title: '档案', slotName: 'archive' }, // 应该只有插槽
    { title: '送教记录', slotName: 'sendRecord' }, // 应该只有插槽
    { title: '操作', slotName: 'operation' },
  ]);

  const pageSize = ref(20);
  const current = ref(1);
  const pagination = computed(() => {
    return {
      current: current.value,
      pageSize: pageSize.value,
      total: filterSearchData.value?.lists.total || 0,
      showJumper: true,
      showPageSize: true,
      showTotal: true,
      pageSizeOptions: [2, 20, 30, 50, 100, 200],
      size: 'small',
    };
  });

  const searchParams = ref({
    boId: userStore.branchOffice.id,
    // finished: 1,
    page: current.value,
    pageSize: pageSize.value,
    sharedUserId: null,
    topBoId: userStore.company.id,
    // period: '',
    type: 'teachersCount',
    ...(props.shareLinkInfo?.configs || {}),
  });
  const shareLinkInfo = ref({
    ...props.shareLinkInfo,
  });

  const columns = ref([...baseColumns.value]);

  const handleFilter = (val): any => {
    let field = 'studentName';
    if (currentIndexCard.value.key === 'teachersCount') {
      field = 'teacherName';
    } else if (currentIndexCard.value.key === 'schoolsCount') {
      field = 'branchOfficeName';
    } else field = 'studentName';

    filterSearchData.value.lists.items = searchData.value.lists.items
      .filter((item) => val === item?.[field])
      .sort((a, b) => a?.[field].localeCompare(b?.[field]));
  };

  const reset = () => {
    filterSearchData.value = JSON.parse(JSON.stringify(searchData.value));
  };

  const search = async () => {
    await request('/statistics/d2dEducationShare/search', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: searchParams.value,
    }).then((res: any) => {
      searchData.value = res.data;
      filterSearchData.value = JSON.parse(JSON.stringify(res.data));
    });
  };
  const loadData = async () => {
    const loadBoTree = await request('/statistics/d2dEducationShare/filtersItems', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: searchParams.value,
    }).then((res) => {
      schoolList.value = res.data.boTree;
    });
    // await search();
    await Promise.all([loadBoTree, search()]);
  };
  const loadGradClass = async () => {
    await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        boId: searchParams.value.boId,
      },
    }).then((res) => {
      gradClassList.value = [];
      res.data.items.forEach((item: any) => {
        gradClassList.value.push({
          label: item.name,
          value: item.id,
        });
      });
      filterMenu.value.find((item) => item.label === '班级分组').options = gradClassList.value;
    });
  };
  const handelTreeChange = async (val: any) => {
    searchParams.value.boId = val.value;
    await loadGradClass();
  };
  const handleClickCard = (item: any, index) => {
    currentIndexCard.value.index = index;
    currentIndexCard.value.key = item.key;
    searchParams.value.type = item.key;
    search();
  };

  const handlePageChange = (val) => {
    current.value = val;
    search();
  };
  const handleCurrentChange = (val) => {
    pageSize.value = val;
    search();
  };

  const filteredColumns = computed(() => {
    const columnList = columns.value.filter((column: any) => {
      // 使用 Object.prototype.hasOwnProperty.call 来检查属性
      if (currentIndexCard.value.key === 'studentsCount' && column.dataIndex === 'period') return true;

      return (
        (searchData.value?.lists.items.length > 0 &&
          Object.prototype.hasOwnProperty.call(searchData.value.lists.items[0], column.dataIndex)) ||
        (column?.slotName === 'operation' &&
          !['studentsCount', 'sendPhotosCount'].includes(currentIndexCard.value.key)) ||
        (currentIndexCard.value.key === 'studentsCount' && ['archive', 'sendRecord'].includes(column?.slotName))
      );
    });
    return [{ title: '序号', slotName: 'num' }, ...columnList];
  });

  const currentSelectType = ref<null | string>(null);
  const setCurrentSelectType = (type: string) => {
    currentSelectType.value = type;
  };

  const handleChange = (val: any) => {
    switch (currentSelectType.value) {
      case filterMenu.value[1].label:
        if (val === -1) delete searchParams.value.gradeClassId;
        else searchParams.value.gradeClassId = val;
        break;

      case filterMenu.value[2].label:
        if (val === -1) delete searchParams.value.period;
        else searchParams.value.period = val;
        break;

      case filterMenu.value[3].label:
        if (val === -1) delete searchParams.value.finished;
        else searchParams.value.finished = val;
        break;
      case 'month':
        if (val === -1) delete searchParams.value.month;
        else searchParams.value.month = val;
        break;

      default:
        currentSelectType.value = null;
        break;
    }
  };
  const viewModalVisible = ref(false);
  const viewList = ref<any>([]);
  const loadViewData = async (params: any, url, method) => {
    await request(url, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method,
      data: { ...searchParams.value, ...params },
    }).then((res: any) => {
      viewList.value = res.data;
    });
  };
  const drawerVisible = ref(false);

  const showArchives = async (val) => {
    // await request(`/statistics/d2dEducationShare/archive/${val?.sendTeacherList[0].id}/${val.deId}`, {
    await request(`/statistics/d2dEducationShare/archive/${val.deId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      viewList.value = res.data;
      drawerVisible.value = true;
    });
  };

  const handleView = async (record: any, type: string) => {
    const viewParams = ref<any>({});
    let url = '/statistics/d2dEducationShare/detailList';
    let method = 'post';
    switch (type) {
      case 'operation': {
        viewModalVisible.value = true;
        if (currentIndexCard.value.key === 'teachersCount') {
          viewParams.value.teacherId = record.participantId;
        } else if (currentIndexCard.value.key === 'schoolsCount') {
          viewParams.value.boId = record.branchOfficeId;
          viewParams.value.searchRecordBoId = record.branchOfficeId;
        } else {
          viewModalVisible.value = false;
          drawerVisible.value = true;
          url = `/statistics/d2dEducationShare/record/${record.recordId}`;
          method = 'get';
        }
        await loadViewData(viewParams.value, url, method);
        break;
      }
      case 'archive':
        await showArchives(record);
        break;
      case 'sendRecord':
        viewParams.value.studentId = record.studentId;
        viewParams.value.deId = record.deId;
        await loadViewData(viewParams.value, url, method).then(() => {
          viewModalVisible.value = true;
        });
        break;
      default:
        break;
    }
  };

  const loadCount = async () => {
    await request('/statistics/d2dEducationShare/getPeriod', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res: any) => {
      res.data.forEach((item: any) => {
        period.value.push({
          label: item,
          value: item,
        });
      });
    });
  };
  const shareAll = ref(false);
  const shareModalVisible = ref(false);
  const handleShare = (val) => {
    shareAll.value = val;
    shareModalVisible.value = true;
  };

  const isImage = (type: string) => {
    return [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'svgz',
      'bmp',
      'tiff',
      'tif',
      'ico',
      'cur',
      'avif',
      'apng',
      'heic',
      'heif',
    ].includes(type.toLowerCase());
  };

  const isShare = ref(false);
  onMounted(async () => {
    // await loadData();
    // await loadCount();
    await Promise.any([loadData(), loadCount()]);
    if (props.shareLinkInfo?.id) isShare.value = true;
  });
  watch(
    () => props.shareLinkInfo?.id,
    (newVal) => {
      if (newVal) isShare.value = true;
    },
  );
</script>

<template>
  <div class="p-5 mx-auto w-[1300px]">
    <a-card :bordered="false" class="rounded-md">
      <template #title>
        <span>{{ (isShare ? '' : userStore.company.name) + '送教信息统计' }}</span>
      </template>
      <div>
        <a-form auto-label-width>
          <a-row class="flex justify-around">
            <a-col v-for="(item, index) in filterMenu" :key="index" :span="item.span">
              <a-form-item :label="item.label">
                <a-tree-select
                  v-if="item.label === '所属学校'"
                  :disabled="isShare"
                  :size="size"
                  :data="schoolList"
                  :label-in-value="true"
                  :field-names="{
                    key: 'id',
                    title: 'name',
                    children: 'children',
                  }"
                  :placeholder="item.label"
                  allow-search
                  @change="handelTreeChange"
                ></a-tree-select>
                <a-select
                  v-else
                  :size="size"
                  :options="item.options"
                  :placeholder="item.label"
                  @click="setCurrentSelectType(item.label)"
                  @change="handleChange"
                >
                  <a-option :value="-1">全部</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="月份">
                <a-select
                  v-if="!isShare"
                  :size="size"
                  placeholder="请选择月份"
                  @click="setCurrentSelectType('month')"
                  @change="handleChange"
                >
                  <a-option :value="-1">全部</a-option>
                  <a-option v-for="month in 12" :key="month" :value="month">{{ month }}月份</a-option>
                </a-select>
                <a-button :size="size" class="ml-2" type="primary" @click="search">
                  <icon-filter />
                  筛选
                </a-button>

                <a-dropdown v-if="!isShare" trigger="hover">
                  <a-button :size="size" class="ml-2" status="success" type="outline">
                    <icon-share-alt />
                    分享
                  </a-button>
                  <template #content>
                    <a-doption @click="handleShare(false)">仅筛选</a-doption>
                    <a-doption @click="handleShare(true)">全部</a-doption>
                  </template>
                </a-dropdown>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" class="rounded-md mt-3">
      <div class="flex justify-around">
        <div
          v-for="(item, index) in briefInfo"
          :key="index"
          :class="currentIndexCard.index === index ? 'text-blue-600' : ''"
          class="inline-block w-60 h-28 bg-gray-50 rounded-xl pt-4 pl-5 hover:cursor-pointer"
          @click="handleClickCard(item, index)"
        >
          <div>{{ item.label }}</div>
          <div class="mt-5">
            <span class="text-3xl text-slate-600">{{ searchData?.[item.key] ?? 0 }}{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </a-card>
    <a-card :bordered="false" class="rounded-md mt-3">
      <div class="mb-4 flex justify-end">
        <a-input-search
          :style="{ width: '320px' }"
          placeholder="输入名称/姓名"
          button-text="搜索"
          search-button
          :size="size"
          class="mr-2"
          @search="handleFilter"
        />
        <a-button type="outline" :size="size" @click="reset">
          <icon-refresh />
          重置
        </a-button>
      </div>
      <div v-if="currentIndexCard.key !== 'sendPhotosCount'" class="relative flex flex-col min-h-full">
        <a-table
          :columns="filteredColumns"
          :data="filterSearchData?.lists.items"
          :pagination="pagination"
          :bordered="{ cell: true }"
          @page-change="handlePageChange"
          @page-size-change="handleCurrentChange"
        >
          <template #studentName="{ record }">
            <studentDetailButton
              :raw="{ id: record?.studentId, name: record.studentName }"
              :eager="true"
              :more-info="true"
            />
          </template>
          <template #teacherName="{ record }">
            <avatarDisplay :user-info="{ id: record?.participantId, name: record?.teacherName }" mode="capsule" />
          </template>

          <template #num="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
          <template #operation="{ record }">
            <span class="cursor-pointer text-blue-500" @click="handleView(record, 'operation')">查看</span>
          </template>
          <template #archive="{ record }">
            <span class="cursor-pointer text-blue-500" @click="handleView(record, 'archive')">档案</span>
          </template>
          <template #sendRecord="{ record }">
            <span class="cursor-pointer text-blue-500" @click="handleView(record, 'sendRecord')">查看记录</span>
          </template>

          <template #sendTeachers="{ record }">
            <div class="flex justify-start space-x-1">
              <avatarDisplay v-for="item in record?.sendTeacherList" :key="item?.id" :user-info="item" mode="capsule" />
            </div>
            <!--<span v-for="(teacher, index) in record?.sendTeacherList" :key="index" class="ml-2">-->
            <!--  {{ teacher.name }}-->
            <!--</span>-->
          </template>
          <template #attachmentsList="{ record }">
            <span v-if="record.attachmentsList.length" class="ml-2">{{ record.attachmentsList.length }}张</span>
            <span v-else class="ml-2">0张</span>
          </template>
        </a-table>
      </div>
      <div v-else class="max-w-screen-xl min-w-min m-auto">
        <photo-list :search-data="searchData" :is-simple="false" />
        <div class="flex justify-end">
          <a-pagination
            v-model:page-size="pageSize"
            v-model:page="current"
            :total="pagination.total"
            show-page-size
            show-total
            @change="handlePageChange"
            @page-size-change="handleCurrentChange"
          />
        </div>
      </div>
    </a-card>
  </div>
  <view-modal v-model="viewModalVisible" :visible="viewModalVisible" :data="viewList" :columns="columns" />
  <a-drawer
    v-if="drawerVisible"
    :visible="drawerVisible"
    width="100%"
    title="送教详情"
    @cancel="
      () => {
        drawerVisible = false;
      }
    "
    @ok="
      () => {
        drawerVisible = false;
      }
    "
  >
    <a-tabs :default-active-key="0">
      <a-tab-pane v-for="(item, index) in viewList.documents" :key="index" :title="item.docName">
        <div v-if="item?.docUrl">
          <div v-if="item?.docUrl.split('.').pop().toLowerCase() === 'pdf'">
            <iframe
              :src="`${item?.docUrl}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none; min-height: 800px"
            ></iframe>
          </div>
          <div v-else-if="isImage(item?.docUrl.split('.').pop().toLowerCase())">
            <div class="image-container text-center mx-auto">
              <img :src="item?.docUrl" alt="" class="mx-auto" />
            </div>
          </div>
          <div v-else>
            <iframe
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${item?.docUrl}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none; min-height: 800px"
            ></iframe>
          </div>
        </div>
        <div v-else-if="item?.attachments.length > 0" class="flex-1 justify-around space-x-4">
          <a-image
            v-for="photo in item.attachments"
            :key="photo.id"
            width="200px"
            height="300px"
            :src="photo.udf1"
          ></a-image>
        </div>
        <a-empty v-else />
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
  <d2d-share-modal
    v-if="shareModalVisible"
    v-model="shareModalVisible"
    :share-all="shareAll"
    :visible="shareModalVisible"
    :filters="searchParams"
    version-type="archive"
  />
</template>

<style lang="less" scoped>
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
