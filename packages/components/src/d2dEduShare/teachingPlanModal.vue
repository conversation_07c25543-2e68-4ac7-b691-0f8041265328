<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
    },
    planId: {
      type: Number,
    },
  });
  const emits = defineEmits(['update:visible']);
  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  const raw = ref(null);
  const isReady = ref(false);
  const loadPlan = async () => {
    if (!props.planId) return;
    try {
      if (props.type === 'send') {
        const { data } = await request(`/resourceRoom/sendEducationPlan/${props.planId}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        raw.value = data;
      } else if (props.type === 'iep') {
        const { data } = await request(`/resourceRoom/individualizedEducation/${props.planId}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        raw.value = data;
      }
      if (raw.value?.id) isReady.value = true;
    } finally {
      /**/
    }
  };

  const handlePreOpen = async () => {
    await loadPlan();
  };
  const handleClose = async () => {
    raw.value = null;
  };

  const sendSchema = ref(null);
  const iepSchema = ref(null);

  onMounted(async () => {
    const [schemaSend, schemaIep] = await Promise.all([
      SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationPlan'),
      SchemaHelper.getInstanceByDs('/resourceRoom/individualizedEducation'),
    ]);
    sendSchema.value = schemaSend;
    iepSchema.value = schemaIep;
  });
</script>

<template>
  <a-modal v-if="visible" v-model:visible="visible" fullscreen @open="handlePreOpen" @close="handleClose">
    <template #title>
      <div class="flex justify-center space-x-2 items-center text-center">
        <div>{{ record?.name }}</div>
        <div class="text-xs text-gray-400">{{ params?.period }}</div>
        <a-tag size="mini" color="green">{{ type === 'send' ? '送教计划' : 'IEP' }}</a-tag>
      </div>
    </template>

    <a-spin v-if="isReady" class="w-full">
      <record-detail ref="recordDetailRef" :raw="raw" :schema="type === 'send' ? sendSchema : iepSchema" />
    </a-spin>
  </a-modal>
</template>

<style scoped lang="scss"></style>
