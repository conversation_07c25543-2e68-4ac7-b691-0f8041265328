<script setup lang="ts">
  import { onMounted, computed, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    searchData: {
      type: Object,
    },
    type: {
      type: String,
      default: 'doc',
    },
    isSimple: {
      type: Boolean,
      default: true,
    },
  });

  const items = computed(() => {
    return props.searchData?.lists?.items || props.searchData?.lists;
  });

  const currentItems = ref<any>();
  const currentSendRecord = ref<any>();
  const handleChange = (value) => {};
  const drawerVisible = ref(false);
  const archives = ref<any>(null);
  const emits = defineEmits(['viewSendRecord']);

  // load send record
  const loadRecord = async (recordId: number) => {
    if (!recordId) {
      Message.warning('尝试刷新后重试');
      return;
    }
    const { data } = await request(`/resourceRoom/d2dEducationRecord/${recordId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    currentSendRecord.value = data;
  };

  // 查看档案 || 查看送教记录
  const showArchives = async (val) => {
    if (props.isSimple) {
      emits('viewSendRecord', val);
    } else {
      await loadRecord(val?.recordId);
      currentItems.value = { ...val, id: val?.deId };
      await request(`/statistics/d2dEducationShare/archive/${val.deId}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      }).then((res) => {
        archives.value = res.data;
        drawerVisible.value = true;
      });
    }
  };

  const handleDrawerOk = () => {
    drawerVisible.value = false;
  };
  const handleDrawerCancel = () => {
    drawerVisible.value = false;
  };
  const viewList = ref<any[]>([]);
  const docVisible = ref(false);
  const currentActivePane = ref();

  const handleViewSendRecord = (val: any) => {
    currentActivePane.value = val?.id || -1;
    srcList.value = currentSendRecord.value?.attachments.map((item) => item.udf1) || [];
    viewList.value = currentSendRecord.value?.docTemplates.map((doc) => ({
      name: doc.name,
      url: doc?.attachment?.url || doc?.attachment?.udf1,
      id: doc?.id,
    }));
    viewList.value.push({
      name: '现场照片',
      id: -1,
    });

    docVisible.value = true;
  };

  const handleSelectPane = (val: number) => {
    currentActivePane.value = val;
  };

  const groupVisible = ref(false);
  const current = ref();
  const srcList = ref([]);

  const handleShowGroup = (list: any) => {
    srcList.value = list?.map((item) => item?.udf1);
    groupVisible.value = true;
  };

  const currentIndex = ref(0);

  const bodyStyle = {
    padding: '0',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '90vh',
    overflow: 'hidden',
  };
  const prevImage = () => {
    if (currentIndex.value > 0) {
      currentIndex.value -= 1;
    }
  };

  const nextImage = () => {
    if (currentIndex.value < srcList.value.length - 1) {
      currentIndex.value += 1;
    }
  };
  const isImage = (type: string) => {
    return [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'svgz',
      'bmp',
      'tiff',
      'tif',
      'ico',
      'cur',
      'avif',
      'apng',
      'heic',
      'heif',
    ].includes(type);
  };

  onMounted(async () => {});
  watch(
    () => groupVisible.value,
    (newVal) => {
      if (!newVal) {
        currentIndex.value = 0;
      }
    },
  );
</script>

<template>
  <div
    v-if="searchData?.lists.total > 0 || searchData?.lists.length > 0"
    class="flex flex-wrap justify-start px-4 ml-3"
  >
    <a-card
      v-for="(i, index) in items"
      :key="index"
      hoverable
      :style="{ width: 'calc(20% - 20px)', height: '340px' }"
      class="bg-gray-100 mt-4 mb-4 mx-2 rounded-md shadow-xl overflow-hidden"
    >
      <template #cover>
        <a-carousel
          v-if="i.attachmentsList?.length > 0"
          :style="{
            height: '200px',
            position: 'relative',
            overflow: 'hidden',
          }"
          :default-current="1"
          @change="handleChange"
        >
          <a-carousel-item v-for="image in i.attachmentsList" :key="image" @click="handleShowGroup(i.attachmentsList)">
            <img
              :src="image.udf1"
              :style="{ width: '100%', height: '100%', objectFit: 'cover', objectPosition: 'center' }"
            />
          </a-carousel-item>
        </a-carousel>
        <div
          v-else
          :style="{
            height: '200px',
            position: 'relative',
            overflow: 'hidden',
          }"
          class="flex justify-center items-center text-gray-300 font-bold"
          >暂无照片
        </div>
      </template>

      <a-modal v-model:visible="groupVisible" width="70%" :body-style="bodyStyle" :footer="false" :closable="false">
        <div class="relative flex justify-center items-center py-2 h-[80vh]">
          <div
            v-if="currentIndex !== 0"
            class="absolute left-0 z-10 w-[45px] h-[40px] bg-black/60 flex justify-center items-center"
            @click="prevImage"
          >
            <icon-double-left style="color: white; font-size: 20px" />
          </div>
          <img
            :src="srcList[currentIndex]"
            :style="{ objectFit: 'contain', objectPosition: 'center', maxWidth: '100%', maxHeight: '100%' }"
          />
          <div
            v-if="currentIndex !== srcList?.length - 1"
            class="absolute right-0 z-10 w-[45px] h-[40px] bg-black/60 flex justify-center items-center"
            @click="nextImage"
          >
            <icon-double-right style="color: white; font-size: 20px" />
          </div>
        </div>
      </a-modal>

      <a-card-meta class="-mt-2 cursor-pointer" @click="showArchives(i)">
        <template #title>
          <span class="text-xs">
            <span class="font-bold">{{ i.studentSchool || i.schoolName }}&nbsp;&nbsp;-&nbsp;&nbsp;</span>
            <span class="font-bold text-blue-700 text-sm cursor-pointer" @click="showArchives(i)">
              {{ i.studentName || i.name }}
            </span>
          </span>
        </template>
        <template #description>
          <div class="text-xs" :class="!i?.stringList?.length ? 'mt-2' : ''">
            <div>
              简介：<span class="text-xs">
                {{ i?.gender }}
                <span>/{{ i?.age || '~' }}岁</span>
                /{{ i?.disorderType || i?.disorders }}
              </span>
            </div>
            <div class="mt-1">
              <span>教师：</span>
              <span
                v-for="teacher in i.sendTeacherList"
                :key="teacher.id"
                class="bg-gray-500 rounded-md pl-2 pr-2 mr-1 text-white text-xs"
              >
                {{ teacher.name }}
              </span>
              <span>{{ i.teacherIds }}</span>
              <span v-if="i?.teacherOrStudentNum" class="text-sm">{{ '共' + i.teacherOrStudentNum + '位' }}</span>
            </div>
            <div v-if="i?.date && i?.classHours" class="text-xs mt-2 flex justify-between mb-2">
              <span>{{ i.date?.split(' ')[0] }}</span>
              <span>{{ i?.sendType }}{{ i?.classHours }}课时</span>
            </div>
            <div v-if="i?.stringList" class="text-xs mt-2">
              时间范围：
              <div>
                <span>{{ i?.stringList[1] }} &nbsp;&nbsp;至&nbsp;&nbsp; </span>
                <span>{{ i?.stringList[0] }}</span>
              </div>
            </div>
            <hr />
            <div v-if="type === 'doc'" class="text-xs mt-2">本学期第{{ i?.recordSequence?.sequenceInPeriod }}次</div>
          </div>
        </template>
      </a-card-meta>
    </a-card>
  </div>
  <a-empty v-else />

  <a-drawer
    v-if="currentItems"
    width="23%"
    :visible="drawerVisible"
    unmount-on-close
    :closable="false"
    @ok="handleDrawerOk"
    @cancel="handleDrawerCancel"
  >
    <template #title>
      <h1>送教详情</h1>
    </template>
    <div class="w-full h-full rounded">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="学生" :span="1">
          {{ currentItems?.studentName }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="送教负责人">
          {{ archives.archive.personInCharge }}
        </a-descriptions-item>
        <a-descriptions-item label="性别" :span="1">
          {{ currentItems?.gender }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="年龄">
          {{ currentItems?.age || '~' + '岁' }}
        </a-descriptions-item>
        <a-descriptions-item :span="2" label="所属学校">
          {{ currentItems?.studentSchool }}
        </a-descriptions-item>

        <a-descriptions-item :span="2" label="送教学期">
          {{ archives.archive.period }}
        </a-descriptions-item>

        <a-descriptions-item :span="2" label="送教日期">
          {{ currentItems?.date.split(' ')[0] }}
        </a-descriptions-item>

        <a-descriptions-item :span="2" label="送救教师">
          {{ currentItems?.sendTeacherList.map((item) => item.name).join(', ') }}
        </a-descriptions-item>

        <a-descriptions-item :span="2" label="送教记录">
          <div class="flex flex-col items-start">
            <a-link
              v-for="(doc, docIndex) in currentSendRecord?.docTemplates"
              :key="doc.id"
              @click="handleViewSendRecord(doc)"
            >
              {{ `${docIndex + 1}、${doc.name}` }}
            </a-link>
            <a-link @click="handleViewSendRecord">
              {{ `${currentSendRecord?.docTemplates.length + 1}、现场照片` }}
            </a-link>
          </div>
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </a-drawer>

  <a-modal v-model:visible="docVisible" :closable="false" :footer="false" width="80%">
    <a-tabs :default-active-key="viewList[0]?.id" :active-key="currentActivePane" @change="handleSelectPane">
      <a-tab-pane v-for="pane in viewList" :key="pane.id" :title="pane?.name">
        <div class="w-full h-[80vh] overflow-auto">
          <div v-if="currentActivePane === -1" id="image-demo-preview-popup-container" class="w-full h-full">
            <div class="relative flex justify-center items-center py-2">
              <div
                v-if="currentIndex !== 0"
                class="absolute left-0 z-10 flex w-[45px] h-[40px] bg-black/60 flex justify-center items-center"
                @click="prevImage"
              >
                <icon-double-left style="color: white; font-size: 20px" />
              </div>
              <img
                :src="srcList[currentIndex]"
                :style="{ objectFit: 'contain', objectPosition: 'center', maxWidth: '100%', maxHeight: '100%' }"
              />
              <div
                v-if="currentIndex !== srcList?.length - 1"
                class="absolute right-0 z-10 flex w-[45px] h-[40px] bg-black/60 flex justify-center items-center"
                @click="nextImage"
              >
                <icon-double-right style="color: white; font-size: 20px" />
              </div>
            </div>
          </div>

          <div v-else-if="pane?.url?.split('.')?.pop()?.toLowerCase() === 'pdf'">
            <iframe
              :src="`${pane?.url}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none"
              v-bind="$attrs"
            ></iframe>
          </div>

          <div v-else-if="isImage(pane?.url?.split('.')?.pop()?.toLowerCase())">
            <div class="image-container text-center mx-auto">
              <img :src="pane?.url" :alt="pane?.name" class="mx-auto" />
            </div>
          </div>

          <div v-else class="h-full">
            <iframe
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURI(pane?.url)}`"
              width="100%"
              height="100%"
              style="width: 100%; height: 100%; border: none"
              v-bind="$attrs"
            ></iframe>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<style scoped lang="scss">
  .relative {
    user-select: none;
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
