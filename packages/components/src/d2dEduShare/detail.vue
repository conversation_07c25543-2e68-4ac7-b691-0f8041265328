<template>
  <a-modal
    v-model:visible="visible"
    :closable="false"
    fullscreen
    :on-before-ok="handlePreOk"
    :footer="false"
    @cancel="handleCancel"
  >
    <template v-if="currentTab" #title>
      <div class="flex justify-between w-full">
        <div class="flex gap-2 items-center">
          <span>
            {{ d2dEducation?.student.gradeClass?.name }}
            {{ d2dEducation?.student.name }} {{ d2dEducation?.period }}的送教档案
            <a-tag size="mini" color="orange" type="warning">制定计划教师： {{ d2dEducation?.createdBy.name }}</a-tag>
            <a-tag v-if="d2dEducation.personInCharge" size="mini" color="orange" type="warning">
              送教负责人： {{ d2dEducation?.personInCharge }}
            </a-tag>
          </span>
        </div>
        <div>
          <a-button
            v-if="d2dEducation.finished && editEnable"
            size="mini"
            class="ml-2 mr-2"
            type="primary"
            @click="() => handleUnfinish(d2dEducation)"
            ><icon-lock />已归档，点击解除
          </a-button>

          <a-button
            v-if="currentTab.name !== 'records' && editEnable && false"
            size="mini"
            class="mr-2"
            type="outline"
            @click="handleReplace"
          >
            <template #icon><icon-swap /></template>
            <span v-if="currentTab.attachment?.udf1 && currentTab !== 'records'"> 替换当前</span>
            <span v-else> 上传</span>附件
          </a-button>
          <a v-if="false" :href="downloadUrl" download target="_blank">
            <a-button
              v-if="currentTab.attachment?.udf1 || currentTab.name === 'records'"
              :size="size"
              type="outline"
              @click="
                () => {
                  if (!getDocUrl()) Message.warning('当前没有可下载的模版');
                }
              "
            >
              <template #icon>
                <icon-to-bottom />
              </template>
              文件下载
            </a-button>
          </a>
          <a-button
            v-if="!d2dEducation.finished && editEnable"
            size="mini"
            class="ml-2 mr-2"
            type="primary"
            @click="() => handleShowAddRecord()"
          >
            <template #icon>
              <icon-plus />
            </template>
            新增送教记录
          </a-button>

          <a-dropdown trigger="hover">
            <a-button size="mini" type="dashed" class="ml-2 mr-2">
              <template #icon>
                <icon-export />
              </template>
              导出
            </a-button>
            <template #content>
              <a-doption @click="handleExport('source')">导出</a-doption>
              <a-doption @click="handleExport('merge')">合并导出</a-doption>
            </template>
          </a-dropdown>

          <a-button size="mini" class="ml-2" type="outline" status="danger" @click="handleCancel">
            <template #icon>
              <icon-close />
            </template>
            关闭
          </a-button>
        </div>
      </div>
    </template>
    <div v-if="currentTab" class="page-wrapper -mt-2">
      <div class="flex justify-between content-wrapper">
        <a-card class="left">
          <template #title>
            <icon-file />
            档案导航
          </template>
          <div
            v-for="tab in tabs"
            :key="tab.name"
            class="left-item"
            :class="{
              active: currentTab.name === tab.name,
              divider: tab === '-',
            }"
          >
            <a-divider v-if="tab === '-'" />
            <span v-else :title="tab.label" @click="() => handleChangeTab(tab)">
              <a-popover v-if="tab.hasReplaced" :content="`已替换`">
                <icon-check-circle style="color: #00e631" />
              </a-popover>
              <a-popover v-else-if="tab.name != 'records'" content="暂无替换">
                <icon-close-circle style="color: red" />
              </a-popover>

              <component :is="tab.icon" v-if="tab.icon" />
              <span v-else class="index">{{ tab.index }}. </span>
              {{ tab.label }}
              <!--tab.hasReplaced-->
              <icon-double-right />
            </span>
          </div>
        </a-card>
        <div class="main-content">
          <d2d-education-record-edit
            v-if="d2dEducation && editRecordVisible"
            ref="editForm"
            v-model="editRecordVisible"
            :visible="editRecordVisible"
            :d2d-education="d2dEducation"
            :data="currentRecord"
            :record-flush="recordFlush"
            :edit-enable="editEnable"
            @edit-info="handleEditInfo"
            @update-template-id="setCurrentTab"
          />
          <document-template-editing
            v-else
            :current-tab="currentTab"
            :style="{ height: 'calc(100vh - 150px)' }"
            :data-type="currentTab.attachment?.udf1?.split('.')?.pop()?.toLowerCase()"
            @replace="handleReplace"
          />
        </div>
      </div>
    </div>
  </a-modal>
  <a-modal
    :title="currentTab?.label"
    :visible="replaceAttachmentVisible"
    :closable="false"
    :on-before-ok="handleReplacePreOk"
    @cancel="handleReplaceCancel"
  >
    <uploader v-if="false" v-model="fileList" sub-folder="" @update:model-value="handleUpload" />
    <a-upload v-if="true" :file-list="uploadedList" :multiple="false" draggable :custom-request="handleUploads as any">
      <template #upload-item="{ fileItem, index }">
        <div class="h-10 bg-gray-50 rounded mt-1 flex items-center justify-between">
          <div class="flex items-center ml-2">
            <icon-file />
            <span class="ml-2">{{ fileItem.name }} </span>
          </div>

          <div class="flex items-center mr-4 space-x-4">
            <div v-if="fileItem.percent === 0" class="text-blue-500">正在准备上传...</div>
            <div v-else-if="fileItem.percent < 1">已上传 {{ (fileItem.percent * 100).toFixed(2) }}%</div>
            <div v-else class="text-lime-600">
              <icon-check />
              完成
            </div>
            <icon-delete
              class="cursor-pointer"
              @click="
                () => {
                  uploadedList.splice(index, 1);
                }
              "
            />
          </div>
        </div>
      </template>
    </a-upload>
  </a-modal>

  <a-modal
    v-if="addRecordVisible"
    :visible="addRecordVisible"
    :on-before-ok="handleAddPreOk"
    :closable="false"
    @cancel="handleAddCancel"
  >
    <a-form auto-label-width>
      <a-form-item label="送教老师" required>
        <a-select
          v-model="selectTeachers"
          placeholder="请选择送教老师"
          allow-clear
          allow-search
          multiple
          :options="teacherOptions"
          @change="handleChange"
        />
      </a-form-item>
      <a-form-item label="送教日期" required>
        <a-date-picker v-model="formRecord.date" />
      </a-form-item>
      <a-form-item label="总课时" required>
        <a-input-number v-model="formRecord.classHour" :min="0" />
      </a-form-item>
      <a-form-item label="课时设置">
        <div class="flex flex-col gap-y-4 w-full">
          <div
            v-for="t in formRecord.sendTeachers || []"
            :key="t.id"
            class="flex items-center justify-between bg-white shadow-sm"
          >
            <div class="w-auto">{{ t?.name }}</div>
            <a-input-number
              v-model="t.numUdf1"
              :placeholder="t.name + '课时设置'"
              mode="button"
              :min="0"
              :default-value="1"
              class="flex-shrink-0 max-w-[70%]"
            />
          </div>
          <div
            v-if="!formRecord.sendTeachers || formRecord.sendTeachers.length === 0"
            class="text-gray-500 text-center py-4"
          >
            暂无教师数据
          </div>
        </div>
      </a-form-item>
      <a-form-item label="送教类型">
        <a-select v-model="formRecord.type">
          <a-option label="送康" value="送康" />
          <a-option label="送教" value="送教" />
        </a-select>
      </a-form-item>
      <a-form-item label="本次送教简述">
        <a-textarea v-model="formRecord.description" placeholder="请输入简述" />
      </a-form-item>
      <a-form-item v-if="false" label="家长签字">
        <div>
          <signatureInput
            v-model:model-value="formRecord.parentSignature"
            :schema-field="{
              key: 'parentSignature',
              label: '家长签字',
              inputWidget: 'signatureInput',
              inputWidgetProps: {
                width: '100%',
                height: '100%',
                actionDirection: 'bottom',
              },
            }"
          />
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useRoute } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import { Message, Modal } from '@arco-design/web-vue';
  import {
    IconEdit,
    IconDoubleRight,
    IconDelete,
    IconLock,
    IconFile,
    IconSwap,
    IconToBottom,
    IconCheck,
    IconPlus,
  } from '@arco-design/web-vue/es/icon';

  import DocumentTemplateEditing from '@repo/components/teacher/documentTemplateEditing.vue';
  import D2dEducationRecordEdit from '@repo/components/teacher/d2dEducationRecordEdit.vue';

  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import signatureInput from '@repo/ui/components/form/inputComponents/inputControl/signatureInput.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue', 'update:visible']);

  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  const userStore = useUserStore();

  const editEnable = computed(() => {
    const userId = userStore.userInfo?.id;
    const myActions = props.record?.collaborators
      ?.filter((collaborator: any) => collaborator?.id === userId || collaborator?.userId === userId)
      ?.map((c: any) => c.action);
    return myActions?.some((action: string) => action === 'Edit' || action === 'Owner') || false;
  });

  // Props and state
  const d2dEducationId = ref<number | null>(null);
  const d2dEducation = ref<any>(null);
  const currentTab = ref<any>(null);
  const tabs = ref<any[]>([]);
  const loading = ref(false);
  const docTemplates = ref<any[]>([]);
  const editRecordVisible = ref(false);
  const addRecordVisible = ref(false);
  const replaceAttachmentVisible = ref(false);
  const currentRecord = ref<any>({});
  const editForm = ref<any>(null);

  const route = useRoute();
  const size = 'mini';
  const teacherList = ref<any>([]);
  const teacherOptions = ref<any>([]);
  const selectTeachers = ref<any>([]);
  const recordFlush = ref(false); // 子组件监听，只要修改了记录就刷新
  const saveType = ref('add');

  const formRecord = ref<any>({
    sendTeachers: [],
    classHour: 1,
  });

  const uploadedList = ref<any>([]);

  const handleUploads = async (options: any) => {
    if (!options?.fileItem?.file) {
      return;
    }
    const formDataSet = new FormData();
    formDataSet.set('type', 'Wps');
    formDataSet.set('file', options.fileItem.file);
    formDataSet.set('fileName', options.fileItem.name);
    try {
      const { data } = await request('/common/uploadedResource/uploadAndGetDirectUrl', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: formDataSet,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      uploadedList.value.push({
        name: data.name,
        url: data.directVisitUrl,
        id: data.id,
      });
    } finally {
      /**/
    }
  };
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    emits('update:modelValue', false);
  };
  // Load the education plan
  const loadPlan = async () => {
    const { data } = await request(`/resourceRoom/d2dEducation/${d2dEducationId.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    d2dEducation.value = data;
  };

  const handleChange = (val) => {
    // formRecord.value.sendTeachers = [];
    const exist = formRecord.value.sendTeachers.filter((t) => val?.includes(t.id)) || [];
    formRecord.value.sendTeachers = exist;
    const ids = exist.map((e) => e.id);
    if (val.length > 0) {
      val.forEach((item) => {
        if (!ids.includes(item)) {
          const teacher = teacherList.value.find((te) => te.id === item);
          formRecord.value.sendTeachers.push({
            id: item,
            name: teacher?.name,
          });
        }
      });
    } else formRecord.value.sendTeachers = [];
  };

  // Initialize tabs
  const initTabs = async () => {
    docTemplates.value = d2dEducation.value?.docTemplates || [];
    tabs.value = [
      ...docTemplates.value.map((item, index) => ({
        index: index + 1,
        label: item.name,
        name: `d2d-education-document-${index}`,
        type: 'document',
        id: index,
        attachment: item.attachment,
        val: item.id,
        hasReplaced: item?.attachment?.numUdf1,
      })),
      '-',
      {
        icon: IconEdit,
        label: '送教记录',
        name: 'records',
      },
    ];

    // eslint-disable-next-line prefer-destructuring
    currentTab.value = tabs.value[0];
  };
  const loadTeacher = async () => {
    const { data } = await request('/org/companyUser', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });

    teacherList.value = data.items;
    data.items.forEach((item, index) => {
      teacherOptions.value.push({
        label: item.name,
        value: item.id,
      });
    });
  };

  const handleChangeTab = (tab: any) => {
    currentTab.value = tab;
    editRecordVisible.value = tab.name === 'records';
  };

  const handleReplace = () => {
    replaceAttachmentVisible.value = true;
  };
  const handleShowAddRecord = (record?: any) => {
    currentRecord.value = record || {
      d2dEducation: d2dEducation.value,
      student: d2dEducation.value.student,
    };
    addRecordVisible.value = true;
  };

  const handleExport = async (type: string = 'source') => {
    const handleExportSelected = async (exportType: string) => {
      Message.info('正在导出，请稍后在右上角导出结果中查看');
      const ids: any = [];
      ids.push(d2dEducation.value.id);
      try {
        await request(`/resourceRoom/d2dEducation/batchExport?type=${exportType}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'POST',
          data: ids,
        });
      } catch (e) {
        /**/
      }
    };
    await handleExportSelected(type);
  };

  const docTemplateCatch = ref({});
  const loadTemplateById = async (id: number) => {
    try {
      const { data: res } = await request(`/document/docTemplate/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      return res;
    } catch (e) {
      Message.clear('top');
      return {
        attachment: { udf1: 'null' },
      };
    }
  };
  const downloadUrl = computed(() => {
    return docTemplateCatch.value?.[currentTab.value?.val] || null;
  });
  const getDocUrl = async (): Promise<string | null> => {
    let url: string | null = null;
    if (currentTab.value?.attachment?.udf1) {
      url = currentTab.value.attachment?.udf1;
    }
    if (currentTab.value.name === 'records') {
      url = editForm.value?.currentTab?.attachment?.udf1;
    }
    const docId = currentTab.value?.val;
    const catchUrl = docTemplateCatch.value?.[docId];
    if (url !== null) {
      const type: string = url?.split('.')?.pop()?.toLowerCase() || '';

      if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'ico', 'apng'].includes(type?.toLowerCase())) {
        // 图片就直接获取模版链接
        if (docId && !catchUrl) {
          const template = await loadTemplateById(docId);
          url = template?.attachment?.udf1 || template?.attachment?.url || null;

          docTemplateCatch.value[docId] = url;
        }
        return docTemplateCatch.value[docId];
      }
      docTemplateCatch.value[docId] = url;
    }
    return catchUrl !== 'null' ? catchUrl : null;
  };

  const setCurrentTab = (val: number, url) => {
    if (val && url) {
      currentTab.value = {
        val: null,
        attachment: {
          udf1: null,
        },
        name: 'records',
      };
      currentTab.value.val = val;
      currentTab.value.attachment.udf1 = url;
    }
  };

  const fileList = ref([]);
  const saveData = async () => {
    try {
      await request(`/resourceRoom/d2dEducation/${d2dEducation.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: d2dEducation.value,
      });
      Message.success('操作成功');
      fileList.value = [];
      uploadedList.value = [];
      replaceAttachmentVisible.value = false;
    } catch (error) {
      Message.error('操作失败');
    }
  };
  const handleUpload = async (val: any) => {
    // fileList.value = val;
    if (currentTab.value.type !== 'records')
      d2dEducation.value.docTemplates.forEach((item: any) => {
        if (item.id === currentTab.value.val) {
          item.attachment = {
            name: val[0].name,
            udf1: val[0].url,
            id: val[0]?.id, // => 替换附件后没拿到id导致导出失败
            numUdf1: 1 + (item?.numUdf1 || 0),
          };
        }
      });
    else {
      /**/
    }
  };
  const handleReplaceCancel = () => {
    replaceAttachmentVisible.value = false;
  };
  const handleReplacePreOk = async () => {
    if (uploadedList.value.length > 1) {
      Message.warning('请确保只有一个替换文件被上传');
      return;
    }
    await handleUpload(uploadedList.value);
    await initTabs();
    await saveData();
  };

  const handleAddCancel = () => {
    addRecordVisible.value = false;
  };
  const resetData = () => {
    formRecord.value = {
      sendTeachers: [],
      classHour: 1,
    };
    selectTeachers.value = [];
  };
  const handleAddPreOk = async () => {
    if (!formRecord.value.date || formRecord.value.sendTeachers.length === 0) {
      Message.warning('请完善信息后提交');
      return;
    }
    formRecord.value.d2dEducation = {
      id: d2dEducation.value.id,
      period: d2dEducation.value.period,
    };
    let url;
    let method = 'put';
    if (saveType.value === 'add') {
      url = '/resourceRoom/d2dEducationRecord';
      method = 'post';
    } else url = `/resourceRoom/d2dEducationRecord/${formRecord.value.id}`;
    await request(url, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method,
      data: formRecord.value,
    }).then(() => {
      recordFlush.value = !recordFlush.value;
      Message.success('添加成功');
      resetData();
    });
    addRecordVisible.value = false;
  };

  const handleEditInfo = async (item) => {
    // Object.assign(formRecord.value, item);
    formRecord.value = item;
    formRecord.value.d2dEducation = {
      id: d2dEducation.value.id,
    };
    formRecord.value.classHour = Number(item?.classHour);
    selectTeachers.value = [];
    // formRecord.value.sendTeachers = [];
    item.sendTeachers.forEach((res) => {
      selectTeachers.value.push(res.id);
    });
    // handleChange(selectTeachers.value);
    saveType.value = 'update'; // 修改
    addRecordVisible.value = true;
  };

  const handleUnfinish = (row: any) => {
    // if (!userService.isAuthorized('管理操作 /resourceRoom/d2dEducation')) {
    //   alert('您没有权限解除归档');
    //   return;
    // }
    Modal.confirm({
      title: '提示',
      content: `确定解除归档【${row.student.name} - ${row.period}】的送教档案？`,
      onOk: async () => {
        request(`/resourceRoom/d2dEducation/unfinish/${row.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
        })
          .then(() => {
            Message.success('送教档案解除归档成功');
            window.location.reload();
          })
          .catch(() => {
            Message.error('解除归档失败');
          });
        emits('update:modelValue', false);
      },
    });
  };
  const init = async () => {
    d2dEducationId.value = props.record?.id;
    await loadPlan();
    await initTabs();
    await loadTeacher();
    if (route.params.docId) {
      const docId = Number(route.params.docId);
      const tab = tabs.value.find((item) => item.id === docId);
      if (tab) {
        currentTab.value = tab;
      }
    }

    if (route.path.includes('/record')) {
      currentTab.value = tabs.value.find((item) => item.name === 'records');
    } else {
      // eslint-disable-next-line prefer-destructuring
      currentTab.value = tabs.value[0];
    }
  };

  onMounted(() => {
    init();
  });

  watch(
    () => currentTab.value,
    async (newVal) => {
      await getDocUrl();
    },
  );
</script>

<style lang="scss" scoped>
  .page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .content-wrapper {
    margin: 8px;
    //height: calc(100% - 170px);
    flex: 1;
    align-items: flex-start;
  }

  .container {
    margin: 16px;
  }

  .left {
    max-width: 280px;
    height: auto;
    z-index: 2;

    .left-item {
      cursor: pointer;
      padding: 4px 16px 4px 0;
      text-align: left;
      border-radius: 4px 0 0 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      border: 1px solid #fff;
      border-right: none;
      white-space: nowrap;
      position: relative;

      .el-divider {
        margin: 8px 0;
      }

      &.active {
        font-weight: bold;
        color: #0080ff;
      }

      &.divider {
        cursor: default;
        padding: 0;
      }

      &:hover {
      }

      i.right {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 16px;
        height: 16px;
        margin: auto;
      }
    }
  }

  .main-content {
    flex: 1;
    margin-left: 8px;
    position: relative;
    margin-top: -8px;
  }

  .header-append {
    font-size: 12px;
    margin-left: 16px;
    font-weight: normal;
    padding-top: 10px;
  }
</style>
