<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import TeacherQuestionLibrary from './teacherQuestionLibrary.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    course: {
      type: Object as PropType<any>,
      required: true,
    },
    mode: {
      type: String as PropType<'normal' | 'select'>,
      default: 'normal',
    },
    selected: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const libRef = ref<any>(null);
  const ready = ref(false);
  const defaultSelected = ref<any[]>([]);
  const emit = defineEmits(['update:visible', 'update:selected']);
  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(value) {
      emit('update:visible', value);
    },
  });

  const handleSelect = () => {
    emit('update:selected', libRef.value?.selectedQuestions);
    modalVisible.value = false;
  };

  const handleOpen = () => {
    defaultSelected.value = (props.selected || []).map((item) => {
      return {
        id: item,
      };
    });
    ready.value = true;
    return true;
  };

  const handleClose = () => {
    modalVisible.value = false;
    ready.value = false;
  };
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen hide-cancel ok-text="完成" size="small" @open="handleOpen">
    <template #title> {{ course?.name }} 题库管理 </template>
    <teacher-question-library
      v-if="ready && course?.id && modalVisible"
      ref="libRef"
      :default-selected="defaultSelected"
      :mode="mode"
      :course="course"
      v-bind="$attrs"
    />
    <template #footer>
      <div v-if="mode === 'select'" class="flex items-end gap-2">
        <div class="flex-1"> </div>
        <a-button v-if="libRef?.selectedQuestions?.length" type="primary" status="success" @click="handleSelect">
          使用已选中的 {{ libRef?.selectedQuestions?.length }} 个试题
        </a-button>
        <a-button @click="handleClose"> 取消 </a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
