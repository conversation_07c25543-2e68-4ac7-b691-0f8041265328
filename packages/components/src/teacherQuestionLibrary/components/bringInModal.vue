<script setup lang="ts">
  import { computed, ref } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:modelValue', 'refresh']);

  const visible = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });
</script>

<template>
  <a-modal v-model:visible="visible" fullscreen title="题目引入">
    <a-card></a-card>
  </a-modal>
</template>

<style scoped lang="scss"></style>
