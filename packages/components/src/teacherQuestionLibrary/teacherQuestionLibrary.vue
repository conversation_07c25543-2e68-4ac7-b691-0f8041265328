<script setup lang="ts">
  import { IconFolderAdd, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon';
  import { useList } from '@repo/infrastructure/crud';
  import { onMounted, computed, ref, watch, PropType } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import {
    deleteTeacherCatalog as deleteCatalog,
    deleteTeacherQuestion as deleteQuestion,
    getTeacherQuestionList as getQuestionList,
    updateTeacherCatalogName as updateCatalogName,
    createNewTeacherCatalog,
    saveTeacherQuestion,
    getTeacherQuestion,
  } from '@repo/infrastructure/openapi/teacherQuestionLibraryController';
  import { Message, Modal } from '@arco-design/web-vue';
  import { usePrompt } from '@repo/ui/components';
  import { cloneDeep } from 'lodash';
  import { request } from '@repo/infrastructure/request';
  import { useUserStore } from '@repo/infrastructure/store';
  import NewFolderModal from './components/newFolderModal.vue';
  import Breadcrumb from '../questionLibrary/components/breadcrumb.vue';
  import LibraryContentItem from '../questionLibrary/components/libraryContentItem.vue';
  import useQuestionLibraryStore from '../questionLibrary/store';
  import QuestionEdit from '../questionLibrary/questionEdit.vue';
  import { getOptionTypeDisplay } from '../questionLibrary/questionInputTypes/helper';
  import QuestionLibrary from '../questionLibrary/questionLibrary.vue';
  import { VerifyStatus } from '../questionLibrary/presets';

  const props = defineProps({
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    defaultEditData: {
      type: Object,
      default: () => ({}),
    },
    defaultSelected: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    course: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String as PropType<'normal' | 'select'>,
      default: 'normal',
    },
  });
  const userInfo = useUserStore();
  const publicQueryParams = {
    ...props.defaultQueryParams,
    courseId: props.course.id,
  };
  const questionLibraryQueryParams = {
    ...props.defaultQueryParams,
    orgNature: userInfo.branchOffice.school.nature,
  };

  const defaultQuestionEditData = ref<any>({
    courseId: props.course.id,
  });
  const questionEditRef = ref<any>(null);
  const currentQuestion = ref<any>({});
  const questionEditVisible = ref(false);
  const { loading: questionSubmitLoading, setLoading: setQuestionSubmitLoading } = useLoading();

  const selectedQuestions = ref<any[]>(props.defaultSelected || []);
  const isQuestionSelected = (id: number) => {
    return !!selectedQuestions.value.find((item) => item.id === id);
  };

  const { loading: folderLoading, setLoading: setFolderLoading } = useLoading();

  const { loadData, listInit, listData, loading } = useList({
    api: '/teacherQuestionLibrary/catalog/tree',
    defaultQueryParams: {
      ...publicQueryParams,
    },
  });
  const folderTree = computed(() => listData.value);
  const currentFolder = ref<any>({});
  const currentSubFolders = ref<any[]>([]);
  const currentSubFiles = ref<any[]>([]);
  const libraryStore = useQuestionLibraryStore();
  const { prompt } = usePrompt();
  const currentFolderContents = computed(() => {
    return [
      ...currentSubFolders.value.map((item: any) => {
        return {
          ...item,
          type: 'folder',
          children: undefined,
        };
      }),
      ...currentSubFiles.value.map((item: any) => {
        return {
          ...item,
          type: 'file',
        };
      }),
    ];
  });

  const rawFoldsMap = ref({});
  const treeProps = ref({
    key: 'id',
    title: 'name',
    children: 'children',
  });
  const createNewFolderVisible = ref(false);
  const questionLibraryVisible = ref(false);

  // 递归 flat folderTree => rawFoldsMap
  const flatFolderTree = (data: any) => {
    data.forEach((item: any) => {
      rawFoldsMap.value[item.id] = item;
      if (item.children) {
        flatFolderTree(item.children);
      }
    });
  };

  const handleShowQuestionEdit = (item?: any) => {
    currentQuestion.value = cloneDeep(item || {});
    questionEditVisible.value = true;
  };
  const loadFolderContent = async () => {
    currentSubFolders.value = [];
    currentSubFiles.value = [];
    setFolderLoading(true);
    try {
      const { data } = await getQuestionList({
        catalogId: currentFolder.value.id,
        ...publicQueryParams,
      });
      currentSubFolders.value = rawFoldsMap.value[currentFolder.value.id]?.children || [];
      currentSubFiles.value = data || [];
    } finally {
      setFolderLoading(false);
    }
  };

  const handleGoFolder = async (data: any) => {
    if (data.rgt > 0) {
      currentSubFiles.value = [];
      currentSubFolders.value = [];
      currentFolder.value = data || {
        id: 0,
        name: '根目录',
      };

      await loadFolderContent();
    } else {
      handleShowQuestionEdit(data);
    }
  };

  const breadCrumbs = computed(() => {
    const bc: any[] = [];
    if (currentFolder.value.id === 0) {
      return bc;
    }

    // 根据左右值找到所有父级目录
    const parentIds: number[] = [];
    Object.values(rawFoldsMap.value)
      .sort((a: any, b: any) => {
        return a.lft - b.lft;
      })
      .forEach((item: any) => {
        if (item.lft < currentFolder.value.lft && item.rgt > currentFolder.value.rgt) {
          parentIds.push(item.id);
        }
      });

    if (parentIds?.length) {
      parentIds.forEach((id: number) => {
        if (id === 0 || !rawFoldsMap.value[id]) {
          return;
        }
        bc.push({
          ...rawFoldsMap.value[id],
        });
      });
    }

    bc.push({
      ...currentFolder.value,
      last: true,
    });
    return bc;
  });

  watch(
    () => breadCrumbs.value,
    (value) => {
      libraryStore.setBreadcrumbs(value);
      libraryStore.setCatalogId(currentFolder.value.id);
    },
  );

  const loadFolderTree = async () => {
    rawFoldsMap.value = {};
    await loadData();
    flatFolderTree(folderTree.value);
  };

  const handleRefresh = async (refreshAll?: boolean) => {
    if (refreshAll) {
      await loadFolderTree();
    }

    await loadFolderContent();
  };

  const handleEditFolderName = async (item: any) => {
    const name = await prompt({
      title: '修改文件夹名称',
      placeholder: '请输入新的文件夹名称',
      raw: item.name,
    });

    if (!name) {
      Message.error('文件夹名称不能为空');
    }

    try {
      setFolderLoading(true);
      await updateCatalogName(
        {
          id: item.id,
        },
        {
          name,
        },
      );
      await handleRefresh(true);

      Message.success('修改成功');
    } finally {
      setFolderLoading(false);
    }
  };

  const handleDelete = async (item: any) => {
    let msg = '';
    if (item.type === 'folder') {
      msg = `确定删除文件夹 ${item.name} 吗？其子文件夹及试题将会被一同删除`;
    } else {
      msg = `确定删除这个试题吗？`;
    }

    Modal.confirm({
      title: '提示',
      content: msg,
      okButtonProps: {
        status: 'danger',
      },
      onOk: async () => {
        try {
          setFolderLoading(true);
          if (item.type === 'folder') {
            await deleteCatalog({
              id: item.id,
            });
          } else {
            await deleteQuestion({
              id: item.id,
            });
          }
          await handleRefresh(true);
        } finally {
          setFolderLoading(false);
        }
      },
    });
  };

  const handleSaveQuestion = async () => {
    if (!questionEditRef.value) {
      return;
    }

    setQuestionSubmitLoading(true);

    try {
      await questionEditRef.value.handleSubmit();
      questionEditVisible.value = false;
      currentQuestion.value = {};
      await handleRefresh();
    } finally {
      setQuestionSubmitLoading(false);
    }
  };

  const handleAddQuestionSelect = (item: any) => {
    selectedQuestions.value.push(item);
  };

  const handleRemoveQuestionSelect = (item: any) => {
    selectedQuestions.value = selectedQuestions.value.filter((i) => i.id !== item.id);
  };

  const currentQuestions = ref<any>(null);

  const share = async () => {
    if (currentQuestions.value?.id) {
      try {
        await request('/questionLibrary/save/questions', {
          method: 'post',
          data: [
            {
              ...currentQuestions.value,
              id: currentQuestions.value?.questionRepositoryId || null,
              teacherQuestionsId: currentQuestions.value.id,
              orgNature: userInfo.branchOffice.school.nature,
              isVerify: VerifyStatus.Pending,
            },
          ],
        });
        await listInit();
        Message.success('分享成功');
      } finally {
        /**/
      }
    }
  };
  const shareToQuestionRepository = async (item: any) => {
    // 如果question_Id 不为空，isVerify 为空 那么说明是引入的不能分享
    if (item.questionRepositoryId !== null && item.isVerify === null) {
      Message.error('引用数据不能分享');
      return;
    }
    currentQuestions.value = item;
    await share();
  };
  const reShare = async (item: any) => {
    Modal.confirm({
      title: '确认再次分享',
      content: `题目：${item.question}`,
      closable: true,
      onOk: () => {
        currentQuestions.value = item;
        share();
      },
    });
  };
  const injectList = ref<any[]>([]);
  const handlePush = (record: any) => {
    if (record.type === 'file' && !injectList.value.some((item) => item.id === record.id)) {
      injectList.value.push(record);
    }
  };
  const handleRemoveInject = (question: any) => {
    injectList.value = injectList.value.filter((item) => item.id !== question.id);
  };

  const folderTreeVisible = ref(false);
  const handlePreOk = async () => {
    folderTreeVisible.value = true;
  };

  const setInjectFolder = (data: any) => {
    currentFolder.value = data;
  };
  const handleSaveInjectQuestions = async () => {
    try {
      injectList.value.forEach((item) => {
        const { id } = item;
        item.questionRepositoryId = id;
        item.catalogId = currentFolder.value?.id || 0;
        item.courseId = props.course.id;
        item.teacherId = props.course.createdById;
        item.isVerify = null;
        item.id = null;
        item.boId = userInfo.branchOffice.id;
      });
      await request('/teacherQuestionLibrary/question/batch', {
        method: 'post',
        data: injectList.value,
      });
      injectList.value = [];
      currentFolder.value = {
        id: 0,
        name: '根目录',
      };
      Message.success('操作成功');
    } finally {
      /**/
    }
  };
  onMounted(async () => {
    await listInit();
    await loadFolderTree();
    await handleGoFolder(folderTree.value[0] || {});
  });

  defineExpose({
    selectedQuestions,
  });
</script>

<template>
  <div class="question-library-wrapper mx-auto flex gap-2"
    ><a-spin class="w-80" :loading="loading">
      <a-card title="题库目录" class="h-full" size="small">
        <a-empty v-if="!folderTree[0]?.children?.length" description="暂无数据"></a-empty>
        <a-tree
          v-else
          size="small"
          :data="folderTree[0].children"
          :checkable="false"
          :default-expanded-keys="[currentFolder?.id, folderTree[0].id]"
          :field-names="treeProps"
          auto-expand-parent
          selectable
          :default-expand-selected="true"
          :selected-keys="[currentFolder.id, folderTree[0].id]"
          @select="(keys: any, e: any) => handleGoFolder(e.node)"
        >
          <template #icon>
            <IconFolder />
          </template>
        </a-tree>
      </a-card>
    </a-spin>
    <div class="flex flex-col gap-2 flex-1">
      <a-card :body-style="{ display: 'none' }">
        <template #title>
          <breadcrumb :bread-crumbs="breadCrumbs" @node-click="handleGoFolder" />
        </template>
      </a-card>
      <a-spin :loading="folderLoading" class="w-full">
        <a-card class="files-wrapper" :body-style="{ display: 'none' }">
          <template #title>
            <div class="flex justify-between">
              <a-space>
                <a-button
                  v-if="currentFolder.parentId"
                  icon="el-icon-top"
                  plain
                  size="mini"
                  @click="
                    () => handleGoFolder(currentFolder.parentId ? rawFoldsMap[currentFolder.parentId] : folderTree[0])
                  "
                >
                  <template #icon>
                    <IconArrowUp />
                  </template>
                  返回上级
                </a-button>
                <a-button size="mini" type="primary" @click="() => handleShowQuestionEdit()">
                  <template #icon>
                    <IconPlus />
                  </template>
                  创建试题
                </a-button>
                <a-button size="mini" type="outline" @click="() => (createNewFolderVisible = true)">
                  <template #icon>
                    <IconFolderAdd />
                  </template>
                  新建文件夹
                </a-button>
                <a-button size="mini" type="outline" @click="() => (questionLibraryVisible = true)">
                  <template #icon>
                    <IconFolderAdd />
                  </template>
                  从题库引入
                </a-button>
                <!--<a-button v-if="selectedQuestions?.length" type="outline" status="success" size="mini">-->
                <!--  <template #icon>-->
                <!--    <IconCheckSquare />-->
                <!--  </template>-->
                <!--  已选择 {{ selectedQuestions.length }} 道试题-->
                <!--</a-button>-->
              </a-space>
              <a-space>
                <a-button-group>
                  <a-button size="mini" @click="() => loadFolderContent()">
                    <template #icon>
                      <IconRefresh />
                    </template>
                    刷新
                  </a-button>
                </a-button-group>
              </a-space>
            </div>
          </template>
        </a-card>

        <div class="folder-contents mt-2">
          <a-table
            v-if="currentFolderContents?.length"
            :data="currentFolderContents"
            stripe
            size="small"
            :pagination="false"
          >
            <template #columns>
              <a-table-column key="name" title="名称" data-index="name">
                <template #cell="{ record }">
                  <library-content-item :item="record" @item-click="() => handleGoFolder(record)" />
                </template>
              </a-table-column>
              <a-table-column key="type" title="类型" data-index="preset" :width="120">
                <template #cell="{ record }">
                  <span v-if="record.lft > 0"></span>
                  <span v-else>{{ getOptionTypeDisplay(record) }}</span>
                </template>
              </a-table-column>
              <a-table-column key="score" title="分数" data-index="score" :width="80">
                <template #cell="{ record }">
                  <span v-if="record.type === 'folder'"></span>
                  <span v-else>{{ record.score }}</span>
                </template>
              </a-table-column>
              <a-table-column title="操作">
                <template #cell="{ record }">
                  <div v-if="record.type === 'file'">
                    <div class="" :style="mode === 'select' ? { display: 'block' } : {}">
                      <a-button
                        v-if="record.type === 'folder'"
                        type="text"
                        size="mini"
                        @click="() => handleEditFolderName(record)"
                        >修改
                      </a-button>
                      <a-button v-else type="text" size="mini" @click="() => handleShowQuestionEdit(record)"
                        >修改
                      </a-button>
                      <a-button type="text" size="mini" @click="() => handleDelete(record)">删除</a-button>
                      <!--创建-->
                      <a-button
                        v-if="record.questionRepositoryId === null && record.isVerify === null"
                        type="text"
                        size="mini"
                        @click="() => shareToQuestionRepository(record)"
                      >
                        分享至题库
                      </a-button>
                      <!--审核不通过-->
                      <a-button
                        v-else-if="record.isVerify === VerifyStatus.Rejected"
                        type="text"
                        size="mini"
                        @click="reShare(record)"
                      >
                        <span class="text-red-500">已驳回</span>
                      </a-button>
                      <!--引用-->
                      <a-button
                        v-else-if="record.questionRepositoryId !== null && record.isVerify === null"
                        type="text"
                      >
                        <icon-branch style="color: #06b6d4" />
                        <span class="text-cyan-500">引用题目</span>
                      </a-button>
                      <!--已分享-->
                      <a-button v-else-if="record.isVerify === VerifyStatus.Pending" type="text" size="mini">
                        <span>待审核</span>
                      </a-button>
                      <!--通过审核-->
                      <a-button v-else-if="record.isVerify === VerifyStatus.Approved" type="text" size="mini">
                        <icon-share-alt style="color: green" />
                        <span class="text-green-700">已分享</span>
                      </a-button>
                      <a-button
                        v-if="mode === 'select' && isQuestionSelected(record.id) && record.type !== 'folder'"
                        type="text"
                        size="mini"
                        status="success"
                        @click.stop="() => handleRemoveQuestionSelect(record)"
                      >
                        已选择
                      </a-button>
                      <a-button
                        v-else-if="mode === 'select' && record.type !== 'folder'"
                        type="text"
                        size="mini"
                        @click.stop="() => handleAddQuestionSelect(record)"
                        >选择
                      </a-button>
                    </div>
                  </div>
                </template>
              </a-table-column>
            </template>
          </a-table>
          <a-empty v-else description="该目录下暂无内容"></a-empty>
        </div>
      </a-spin>
    </div>
  </div>

  <a-modal
    v-model:visible="questionEditVisible"
    :width="800"
    title="试题编辑"
    :esc-to-close="false"
    :mask-closable="false"
    :closable="false"
    :ok-loading="questionSubmitLoading"
    :on-before-ok="handleSaveQuestion"
  >
    <question-edit
      v-if="questionEditVisible"
      ref="questionEditRef"
      :handle-save-question="saveTeacherQuestion"
      :handle-get-question="getTeacherQuestion"
      :edit-id="currentQuestion?.id"
      :default-edit-data="defaultQuestionEditData"
    >
      <template #extra>
        <div></div>
      </template>
    </question-edit>
  </a-modal>

  <new-folder-modal
    v-model="createNewFolderVisible"
    :course="course"
    :default-edit-data="{ ...defaultEditData, courseId: course.id }"
    :parent-folder="currentFolder"
    :breadcrumb="breadCrumbs"
    :create-folder-func="createNewTeacherCatalog"
    @refresh="() => handleRefresh(true)"
  />

  <a-modal
    :visible="questionLibraryVisible"
    fullscreen
    :on-before-ok="handlePreOk"
    ok-text="选择目录"
    cancel-text="返回"
    @cancel="questionLibraryVisible = false"
  >
    <div v-if="questionLibraryVisible" class="flex justify-between">
      <questionLibrary catalog-type="" :default-query-params="questionLibraryQueryParams" :is-inject="true">
        <template #prepend-actions="{ record }">
          <a-button type="text" @click="handlePush(record)">引入</a-button>
        </template>
      </questionLibrary>
      <div class="w-80 h-[85vh] mr-2 border shadow overflow-y-auto rounded bg-white p-2">
        <div
          v-for="(question, index) in injectList"
          :key="question?.id"
          class="flex items-center bg-gray-50 rounded p-2"
        >
          <span>{{ `${index + 1}、` }}</span>
          <span>{{ question?.question }}</span>
          <div class="flex-1 border-b bg-blue-300 border-dashed mx-2"></div>
          <icon-close class="hover:text-red-500" @click="handleRemoveInject(question)" />
        </div>
      </div>
    </div>
  </a-modal>
  <a-modal
    v-model:visible="folderTreeVisible"
    title="请选择目录"
    :on-before-ok="handleSaveInjectQuestions"
    ok-text="保存"
  >
    <a-tree
      v-if="folderTree[0]?.children?.length"
      size="small"
      :data="folderTree[0].children"
      :checkable="false"
      :default-expanded-keys="[currentFolder?.id, folderTree[0].id]"
      :field-names="treeProps"
      auto-expand-parent
      selectable
      :default-expand-selected="true"
      :selected-keys="[currentFolder.id, folderTree[0].id]"
      @select="(keys: any, e: any) => setInjectFolder(e.node)"
    >
      <template #icon>
        <IconFolder />
      </template>
    </a-tree>
    <a-empty v-else description="请先创建目录" />
  </a-modal>
</template>

<style scoped lang="scss">
  .question-library-wrapper {
    width: 1200px;

    :deep .arco-table-cell-inline-icon {
      margin-right: 5px;
    }
  }

  .actions {
    display: none;
  }

  .actions-wrapper {
    width: 100%;
    height: 24px;
  }

  .arco-table-tr {
    &:hover {
      .actions {
        display: flex;
      }
    }
  }
</style>
