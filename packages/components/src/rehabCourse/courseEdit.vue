<script setup lang="ts">
  import { computed, inject, onMounted, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CommonApi } from '@repo/infrastructure/crud';
  import openapi from '@repo/infrastructure/openapi';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { useTeacherStore, useUserStore } from '@repo/infrastructure/store';
  import ChapterManage from './components/chapterManage.vue';
  import TeachingPrepare from './edit/teachingPrepare.vue';
  import LessonPrepare from './edit/lessonPrepare.vue';
  import TeachingReflection from './edit/teachingReflection.vue';
  import ChapterTarget from './edit/chapterTarget.vue';
  import PreTest from './edit/preTest.vue';
  import AfterTest from './edit/afterTest.vue';
  import useSchoolCourseStore from '../../store/schoolCourseStore';
  import { AnnotationModuleSource, getAnnotationBlocks } from '../utils/annotationBlock';

  const router: any = inject('router');
  const route = router.currentRoute.value || router.currentRoute;
  const courseId = Number(route.params.courseId || route.query.courseId);

  const course = ref<Record<string, any>>({});

  const teacherStore = useTeacherStore();
  const userStore = useUserStore();
  const teachersMap = ref<any>({});

  const schoolCourseStore = useSchoolCourseStore();
  const { setLoading, loading } = useLoading();
  const { loading: saving, setLoading: setSaving } = useLoading();

  // 来源
  const original = computed(() => route?.query?.original);
  const paperCompetitionId = computed(() => route?.query?.competitionId);

  const schema = SchemaHelper.getInstanceByApi('/course/rehab-course');
  let courseApi: any;
  const activeTab = ref<any>('prepare');
  const chapterManageRef = ref<any>();
  const currentChapter = ref<any>(null);
  const preTestRef = ref<any>(null);
  const afterTestRef = ref<any>(null);
  const classTimeEditing = ref<boolean>(false);
  const schoolCoursesMap = ref<any>({});
  // 使用 reactive 来初始化可响应的对象
  const chapterVisible = ref<any>({});

  const annotationSource = computed<AnnotationModuleSource>(() => {
    return {
      sourceId: currentChapter.value?.id,
      module: 'REHAB_CHAPTER_CONTENT',
    };
  });
  const annotations = ref<any>([]);

  const handleSave = async () => {
    if (!currentChapter.value?.id) {
      return;
    }
    setSaving(true);
    try {
      await request(`/course/rehab-chapter/${currentChapter.value.id}`, {
        method: 'PUT',
        data: currentChapter.value,
      });
      await request(`/course/rehab-course/${course.value.id}`, {
        method: 'PUT',
        data: course.value,
      });

      // /course/rehab-chapter/content/{id} [patch]
      await request(`/course/rehab-chapter/content/${currentChapter.value?.id}`, {
        method: 'PATCH',
        data: {
          ...currentChapter.value?.content,
        },
      });

      classTimeEditing.value = false;
      Message.clear();
      Message.success('保存成功');
    } finally {
      setSaving(false);
    }
  };

  const handleFinish = async () => {
    await handleSave();
    router.back();
  };

  // 判断chapterVisible 是否被更新过 更新一次就行
  const flag = ref(0);

  function extractIdsFromChaptersTree(chaptersTree) {
    function traverse(chapters) {
      chapters.forEach((chapter) => {
        chapterVisible.value[chapter.id] = !chapter.children;
        if (Array.isArray(chapter.children)) {
          traverse(chapter.children);
        }
      });
    }
    if (Array.isArray(chaptersTree)) {
      traverse(chaptersTree);
    }
    flag.value = 1;
  }

  const updateChapterVisible = () => {
    if (flag.value === 0) extractIdsFromChaptersTree(chapterManageRef.value?.chaptersTree);
  };
  const handleChapterSwitch = async (chapter: any) => {
    chapterManageRef.value.setSwitchLoading(true);
    if (!chapter) {
      chapterManageRef.value.currentChapter = null;
      currentChapter.value = null;
      // stop();
      return;
    }
    try {
      // await handleSave();
      // stop();
      const res = await openapi.rehabCourse.getRehabChapter({
        id: chapter.id,
      });
      currentChapter.value = {
        ...res.data,
        number: chapter.number,
      };
      chapterManageRef.value.currentChapter = {
        ...res.data,
        number: chapter.number,
      };

      annotations.value = await getAnnotationBlocks(annotationSource.value);

      // start();
    } catch (error) {
      console.error(error);
      Message.error('获取章节信息失败');
    } finally {
      chapterManageRef.value.setSwitchLoading(false);
      updateChapterVisible();
    }
  };

  const getChapterManageRef = () => chapterManageRef.value;

  const hiddenOrShow = () => {
    // eslint-disable-next-line no-self-assign
    chapterVisible.value[currentChapter.value?.id] = chapterVisible.value[currentChapter.value?.id];
  };

  const currentDirectory = computed(() => {
    return chapterManageRef.value.getCurrentChapterPath() || '';
  });

  const handleSubmit = async () => {
    if (!currentChapter.value?.id) {
      return;
    }

    if (!currentChapter.value.classTime) {
      Message.error('请先设置上课时间');
      return;
    }

    setSaving(true);
    try {
      await request(`/course/rehab-chapter/${currentChapter.value.id}/submit`, {
        method: 'PUT',
      });
      Message.success('提交成功');
      currentChapter.value.submitted = true;
    } finally {
      setSaving(false);
    }
  };

  const handlePushToLibrary = async () => {
    if (!currentChapter.value?.id) {
      return;
    }
    setSaving(true);

    const postData = {
      id: currentChapter.value.id,
      name: chapterManageRef.value.getCurrentChapterPath(),
    };

    try {
      const { data } = await request(`/course/rehab-chapter-library`, {
        method: 'POST',
        data: {
          chapter: {
            ...postData,
          },
          attachment: currentChapter.value.content?.lessonPrepareAttachments,
        },
      });
      Message.success('分享至教案库成功');
      currentChapter.value.libraryId = data.id || 1;
    } finally {
      setSaving(false);
    }
  };

  const handlePushToCompetition = () => {
    router.push({
      path: '/teacher/dailyWork/paperCompetition',
      query: {
        id: paperCompetitionId.value,
        chapterId: currentChapter.value.id,
      },
    });
  };

  onMounted(async () => {
    setLoading(true);
    courseApi = CommonApi.getInstance(schema);

    schoolCoursesMap.value = await schoolCourseStore.getSchoolCoursesMap();

    teachersMap.value = await teacherStore.getTeachersMap(route, userStore.getUserNature());

    const { data } = await courseApi.fetchOne(courseId);
    course.value = data;
    setLoading(false);
  });

  const numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];

  defineExpose({
    handleSave,
  });
</script>

<template>
  <a-card :loading="loading" class="flex flex-col content-card">
    <template #title>
      <a-space>
        康复任务 {{ course.name }}
        <div class="text-sm">
          <small>
            {{ course.grade }}
            {{ schoolCoursesMap[course.category]?.name }}
            {{ course.period }}
            <a-popover v-if="course.description" trigger="hover" :content="course.description">
              <IconQuestionCircleFill />
            </a-popover>
          </small>
        </div>
      </a-space>
    </template>
    <template #extra>
      <a-space>
        <a-button size="mini" :loading="saving" @click="handleFinish">
          <template #icon>
            <IconCheck />
          </template>
          完成
        </a-button>
      </a-space>
    </template>
    <div>
      <a-split class="wrapper" default-size="220px">
        <template #first>
          <chapter-manage ref="chapterManageRef" :course-id="courseId" @switch-chapter="handleChapterSwitch" />
        </template>
        <template #second>
          <div v-if="currentChapter?.id">
            <div class="flex justify-between items-center">
              <a-space class="px-3 mt-2 text-lg flex-1 items-center">
                <strong>{{ currentChapter?.number }}</strong>
                {{ currentDirectory }}
                <a-popover v-if="currentChapter?.referenceSourceId" trigger="hover" style="max-width: 300px">
                  <template #content>
                    <div v-if="currentChapter.referenceSource?.id">
                      <div v-if="teachersMap[currentChapter.referenceSource?.createdById]">
                        引用自： {{ teachersMap[currentChapter.referenceSource?.createdById].name }}
                      </div>
                      <div class="text-xs">
                        {{ schoolCoursesMap[currentChapter.referenceSource.category]?.name }}
                        {{ currentChapter.referenceSource?.grade }}{{ currentChapter.referenceSource?.period }}
                      </div>
                      <div class="text-xs"> 原标题：{{ currentChapter.referenceSource?.name }}</div>
                    </div>
                    <div v-else>引用来源教案可能已被删除</div>
                  </template>
                  <a-tag color="blue" size="small">引</a-tag>
                </a-popover>

                <a-switch v-model="chapterVisible[currentChapter.id]" type="round" @change="hiddenOrShow">
                  <template #checked> 启用</template>
                  <template #unchecked> 关闭</template>s
                </a-switch>
              </a-space>
              <a-space>
                <a-tooltip v-if="!classTimeEditing" content="点击修改" @click="() => (classTimeEditing = true)">
                  <div class="cursor-pointer">
                    上课时间：
                    {{ currentChapter.classTime || '点击设置' }}
                  </div>
                </a-tooltip>
                <a-space v-else>
                  <a-date-picker
                    v-model="currentChapter.classTime"
                    placeholder="请选择上课时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    show-time
                    size="mini"
                  />
                  <a-button size="mini" type="primary" @click="handleSave">
                    <template #icon>
                      <IconCheck />
                    </template>
                  </a-button>
                </a-space>
                <a-space v-if="original && paperCompetitionId">
                  <a-popconfirm content="确定要推送本节至论文赛课吗？" @ok="handlePushToCompetition">
                    <a-button size="mini" type="primary" status="success">
                      <template #icon>
                        <IconShareInternal />
                      </template>
                      推送至论文赛课
                    </a-button>
                  </a-popconfirm>
                </a-space>
                <a-space v-else>
                  <a-popconfirm content="确定要提交至教学督导考核吗？" @ok="handleSubmit">
                    <a-button v-if="!currentChapter.submitted" size="mini" type="primary" :loading="saving">
                      提交考核
                    </a-button>
                  </a-popconfirm>
                  <a-button v-if="currentChapter.submitted" size="mini" type="primary" disabled>
                    <template #icon>
                      <IconCheck />
                    </template>
                    已提交
                  </a-button>
                  <a-popconfirm
                    v-if="!currentChapter?.referenceSourceId && !currentChapter?.libraryId"
                    content="确定要分享本章节到教案库吗？"
                    @ok="handlePushToLibrary"
                  >
                    <a-button size="mini" type="outline" status="success" :loading="saving">
                      <template #icon>
                        <IconShareAlt />
                      </template>
                      分享至教案库
                    </a-button>
                  </a-popconfirm>
                  <a-button
                    v-if="!currentChapter?.referenceSourceId && currentChapter?.libraryId"
                    type="primary"
                    status="success"
                    size="mini"
                    disabled
                  >
                    <template #icon>
                      <IconCheck />
                    </template>
                    已分享
                  </a-button>
                </a-space>
              </a-space>
            </div>
            <a-divider :margin="10" />
            <a-spin :loading="chapterManageRef?.switchLoading" class="w-full">
              <a-tabs v-if="chapterVisible[currentChapter.id]" v-model:active-key="activeTab">
                <a-tab-pane key="prepare" title="训练准备">
                  <teaching-prepare
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'prepare'"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    v-model:annotations="annotations"
                    :annotation-source="annotationSource"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <a-tab-pane key="target" title="目标设定">
                  <chapter-target
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'target'"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <a-tab-pane key="preTest" title="训练前测">
                  <pre-test
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'preTest'"
                    ref="preTestRef"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    :chapters-tree="getChapterManageRef()?.chaptersTree"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <a-tab-pane key="lessonPrepare" title="训练安排">
                  <lesson-prepare
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'lessonPrepare'"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    v-model:annotations="annotations"
                    :annotation-source="annotationSource"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <a-tab-pane key="afterTest" title="训练后测">
                  <after-test
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'afterTest'"
                    ref="afterTestRef"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    :chapters-tree="getChapterManageRef()?.chaptersTree"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <a-tab-pane key="reflection" title="训练反思">
                  <teaching-reflection
                    v-if="!chapterManageRef?.switchLoading && activeTab === 'reflection'"
                    v-model:course="course"
                    v-model:chapter="currentChapter"
                    @save="handleSave"
                  />
                </a-tab-pane>
                <!--            <a-tab-pane key="preview" title="教学演示">-->
                <!--              <teaching-demo-->
                <!--                v-if="!chapterManageRef?.switchLoading && activeTab === 'preview'"-->
                <!--                :chapter="chapterManageRef?.currentChapter"-->
                <!--                :course="course"-->
                <!--              />-->
                <!--            </a-tab-pane>-->
                <!--            <a-tab-pane key="design" title="教学设计">-->
                <!--              <teaching-design-->
                <!--                v-if="!chapterManageRef?.switchLoading && activeTab === 'design'"-->
                <!--                :chapter="chapterManageRef?.currentChapter"-->
                <!--                :course="course"-->
                <!--              />-->
                <!--            </a-tab-pane>-->
                <!--            <a-tab-pane key="ppt" title="教学课件">-->
                <!--              <teaching-presentation-->
                <!--                v-if="!chapterManageRef?.switchLoading && activeTab === 'ppt'"-->
                <!--                :chapter="chapterManageRef?.currentChapter"-->
                <!--                :course="course"-->
                <!--              />-->
                <!--            </a-tab-pane>-->
              </a-tabs>
            </a-spin>
          </div>
          <a-empty v-else description="请在左侧列表中选择章节" />
        </template>
      </a-split>
    </div>
  </a-card>
</template>

<style scoped lang="less">
  .content-card :deep {
    .arco-card-body {
      flex: 1;
      flex-shrink: 0;

      > div {
        height: 100%;
      }
    }
  }

  .arco-split {
    height: 100%;

    :deep .arco-split-pane-first {
      overflow-x: hidden;
    }
  }

  :deep .arco-tabs-content-item {
    padding: 0 10px;
  }
</style>
