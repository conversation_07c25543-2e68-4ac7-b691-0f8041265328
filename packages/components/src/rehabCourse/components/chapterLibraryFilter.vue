<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { getGradesList } from '@repo/infrastructure/data/schoolTypes';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import useSchoolCourseStore from '../../../store/schoolCourseStore';

  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo();
  const nature = menuInfo?.app?.label;

  const filters = ref({
    category: true,
    grade: true,
    period: true,
  });

  const periods = ['上册', '下册'];
  const courses = ref<any[]>([]);

  const emit = defineEmits(['filter']);

  const handleFilter = () => {
    const data: any = {};
    if (filters.value.category !== true) {
      data.category = filters.value.category;
    }
    if (filters.value.grade !== true) {
      data.grade = filters.value.grade;
    }
    if (filters.value.period !== true) {
      data.period = filters.value.period;
    }

    emit('filter', data);
  };

  watch(filters, handleFilter, { deep: true });

  onMounted(async () => {
    const courseStore = useSchoolCourseStore();
    courses.value = await courseStore.getSchoolCourses();
  });
</script>

<template>
  <div class="flex flex-col gap-2">
    <div class="flex gap-2">
      <div class="w-14 text-right">科目：</div>
      <div class="flex-1">
        <a-radio-group v-model="filters.category">
          <a-radio :value="true" label="全部">
            <template #radio="{ checked }">
              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> 全部 </a-tag>
            </template>
          </a-radio>
          <a-radio v-for="course in courses" :key="course.id" :value="course.id" :label="course.name">
            <template #radio="{ checked }">
              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> {{ course.name }} </a-tag>
            </template>
          </a-radio>
        </a-radio-group>
      </div>
    </div>
    <!--    <div class="flex gap-2">-->
    <!--      <div class="w-14 text-right">年级：</div>-->
    <!--      <div class="flex-1">-->
    <!--        <a-radio-group v-model="filters.grade">-->
    <!--          <a-radio :value="true" label="全部">-->
    <!--            <template #radio="{ checked }">-->
    <!--              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> 全部 </a-tag>-->
    <!--            </template>-->
    <!--          </a-radio>-->
    <!--          <a-radio v-for="grade in getGradesList(nature)" :key="grade" :value="grade" :label="grade">-->
    <!--            <template #radio="{ checked }">-->
    <!--              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> {{ grade }} </a-tag>-->
    <!--            </template>-->
    <!--          </a-radio>-->
    <!--        </a-radio-group>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="flex gap-2">-->
    <!--      <div class="w-14 text-right">册：</div>-->
    <!--      <div class="flex-1">-->
    <!--        <a-radio-group v-model="filters.period">-->
    <!--          <a-radio :value="true" label="全部">-->
    <!--            <template #radio="{ checked }">-->
    <!--              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> 全部 </a-tag>-->
    <!--            </template>-->
    <!--          </a-radio>-->
    <!--          <a-radio v-for="period in periods" :key="period" :value="period" :label="period">-->
    <!--            <template #radio="{ checked }">-->
    <!--              <a-tag size="medium" bordered :color="checked ? 'blue' : ''"> {{ period }} </a-tag>-->
    <!--            </template>-->
    <!--          </a-radio>-->
    <!--        </a-radio-group>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
  <a-divider :margin="10" />
</template>

<style scoped lang="scss">
  :deep {
    .arco-radio-group .arco-radio {
      margin-right: 3px;
    }
  }
</style>
