<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { Modal } from '@arco-design/web-vue';
  import { resourceType } from '../../resource/constants';
  import DigitalResourceIcon from '../../resource/digital/components/digitalResourceIcon.vue';
  import DigitalResourcePreview from '../../resource/digital/components/digitalResourcePreview.vue';

  const props = defineProps({
    resources: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const currentIndex = ref<number>(NaN);
  const previewVisible = ref<boolean>(false);
  const currentType = ref<any>('all');

  const emit = defineEmits(['deleteResource']);

  const handleCurrentIndexChange = (index: number) => {
    currentIndex.value = index;
    previewVisible.value = index >= 0;
  };

  const handleRemove = (index: number) => {
    Modal.confirm({
      title: '删除资源',
      content: '确定从本页幻灯片中删除该资源的引用吗？',
      onOk: () => {
        emit('deleteResource', index);
      },
    });
  };

  const resourcesList = computed(() => {
    if (!currentType.value || currentType.value === 'all') {
      return props.resources;
    }
    return props.resources.filter((item) => item.type === currentType.value);
  });
</script>

<template>
  <div class="wrapper">
    <a-tabs v-model:active-key="currentType" position="left" @change="(key: any) => (currentType = key)">
      <a-tab-pane key="all" title="全部">
        <template #title>
          <IconFile />
          全部
        </template>
      </a-tab-pane>
      <a-tab-pane v-for="type in resourceType" :key="type.id" :title="type.name">
        <template #title>
          <component :is="type.icon" />
          {{ type.name }}
        </template>
      </a-tab-pane>
    </a-tabs>
    <a-empty v-if="!resourcesList?.length" description="暂无资源，请先从资源库添加或上传"></a-empty>
    <div class="resource-list">
      <digital-resource-icon
        v-for="(resource, index) in resourcesList"
        :key="resource.id"
        :resource="resource"
        @preview="() => handleCurrentIndexChange(resourcesList.indexOf(resource))"
      >
        <template #extra-actions>
          <a-button size="mini" status="danger" @click="() => handleRemove(index)"> 删除资源 </a-button>
        </template>
        <template #append>
          <div v-if="resource.source === 'Lib'" class="resource-tag lib">资源库</div>
          <div v-else-if="resource.source === 'User'" class="resource-tag user">本地上传</div>
        </template>
      </digital-resource-icon>
      <digital-resource-preview
        v-if="!isNaN(currentIndex) && currentIndex >= 0 && previewVisible"
        :data-list="resourcesList"
        :current-index="currentIndex"
        @update:current-index="handleCurrentIndexChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
  .wrapper {
    display: flex;

    .arco-tabs {
      flex: 0 0 120px;
    }
  }

  .resource-list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }

  .resource-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;

    .resource-tag {
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 4px;
      border-radius: 2px;
      color: white;
      font-size: 10px;
      background-color: rgba(var(--primary-8), 0.6);
      &.lib {
        background-color: rgba(var(--green-8), 0.6);
      }
    }
  }
</style>
