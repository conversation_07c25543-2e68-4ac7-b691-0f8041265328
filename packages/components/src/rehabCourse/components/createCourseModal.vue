<script setup lang="ts">
  import { computed, onMounted, ref, toRaw } from 'vue';
  import { CrudForm } from '@repo/ui/components/form';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message } from '@arco-design/web-vue';
  import { CommonApi } from '@repo/infrastructure/crud';
  import { cloneDeep } from 'lodash';
  import { coverUploadInput } from '@repo/ui/components/form/inputComponents';

  const props = defineProps({
    modelValue: Boolean,
    course: Object,
    apiAlias: {
      type: String,
      default: '/course/rehab-course',
    },
  });

  const formRef = ref<any>(null);
  const emits = defineEmits(['update:modelValue', 'onOk']);
  const editData = ref<any>(cloneDeep(props.course || {}));

  const schema = SchemaHelper.getInstanceByApi(props.apiAlias);
  let api: any;

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emits('update:modelValue', value),
  });

  const handleCancel = () => {
    visible.value = false;
  };

  const handleOk = async () => {
    try {
      if (
        !(editData.value.category && editData.value.grade && editData.value.period && editData.value.fromOrgId >= 0)
      ) {
        Message.warning('请完整填写数据');
        return;
      }
      if (editData.value.id) {
        await api.updateRecord(editData.value);
      } else {
        await api.createRecord(editData.value);
      }
      Message.success('保存成功');
      emits('onOk', {});
    } catch (e) {
      // 如果发生任何错误（包括验证失败），捕获并处理
      Message.error('保存失败');
    }
  };

  const handleDelete = async () => {
    try {
      await api.deleteRecord(editData.value.id);
      Message.success('删除课程成功');
      visible.value = false;
      emits('onOk', {});
    } catch (e) {
      Message.error('删除失败');
    }
  };

  onMounted(async () => {
    api = CommonApi.getInstance(schema);
  });
  // 测试
  defineExpose({
    schema,
  });
</script>

<template>
  <a-modal
    v-model:visible="visible"
    :title="editData.id ? '修改课程' : '新建课程'"
    width="600px"
    :destroy-on-close="true"
    :mask-closable="false"
    :on-before-ok="handleOk"
    @cancel="handleCancel"
  >
    <!--    <div>{{ editData }}</div>-->
    <!--    <div>{{ schema }}</div>-->
    <div class="form-wrapper">
      <div class="main-form">
        <!--        <crud-form ref="formRef" v-model="editData" :schema="schema" :show-actions="false" />-->
        <a-form ref="formRef" auto-label-width>
          <a-form-item required label="课程分类">
            <a-select
              v-model="editData.category"
              :options="schema.schemaFieldsMap?.category.inputWidgetProps.options"
            />
          </a-form-item>

          <a-form-item label="年级" required>
            <a-select v-model="editData.grade" :options="schema.schemaFieldsMap?.grade.inputWidgetProps.options" />
          </a-form-item>

          <a-form-item label="上下册" required>
            <a-select v-model="editData.period" :options="schema.schemaFieldsMap?.period.inputWidgetProps.options" />
          </a-form-item>

          <a-form-item label="已授权使用机构">
            <a-input v-model="editData.authorizedOrgId" />
          </a-form-item>

          <a-form-item label="资源机构 ID" required>
            <a-input-number v-model="editData.fromOrgId" min="0" />
          </a-form-item>
        </a-form>
      </div>
      <div class="cover-wrapper">
        <a-card :bordered="false" class="mt">
          <cover-upload-input
            v-model="editData.coverImage"
            :box-height="180"
            tips="点击上传课程封面"
            :schema-field="schema.schemaFieldsMap.coverImage"
          />
        </a-card>
      </div>
    </div>
    <template #footer>
      <div class="flex-container justify-between">
        <a-popconfirm
          v-if="editData.id"
          content="确定要删除这个课程吗？"
          :ok-button-props="{ status: 'danger' }"
          @ok="handleDelete"
        >
          <a-button status="danger">删除这个课程</a-button>
        </a-popconfirm>
        <div v-else> </div>
        <div>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" class="ml" @click="handleOk">保存</a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="less">
  .form-wrapper {
    display: flex;
    .main-form {
      flex: 1;
    }
    .cover-wrapper {
      border-left: 1px solid #ddd;
      width: 180px;
      height: 220px;
      margin-left: 16px;
      padding-left: 16px;

      :deep(.preview) {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 260px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
