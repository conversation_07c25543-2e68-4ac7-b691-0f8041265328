<script setup lang="ts">
import { onMounted, ref} from "vue";
  import { getOssProcessor } from '@repo/infrastructure/upload';

  const props = defineProps({
    modalVisible: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['handelOk', 'handelCancel']);

  const attachmentUrls = ref([]);
  const fileList = ref([]);
  const oss = getOssProcessor();
  const handelOk = () => {
    emits('handelOk');
  };
  const handelCancel = () => {
    emits('handelCancel');
  };

  const handleUpload = async (options: any) => {
    const file = options.fileItem;
    attachmentUrls.value = oss.uploadSimply(file, '');
  };
  onMounted(() => {});
</script>

<template>
  <a-modal :closable="false" :visible="modalVisible" title="上传课件" @ok="handelOk" @cancel="handelCancel">
    <a-upload draggable :custom-request="handleUpload as any" :default-file-list="fileList" :limit="1" />
  </a-modal>
</template>

<style scoped lang="scss"></style>