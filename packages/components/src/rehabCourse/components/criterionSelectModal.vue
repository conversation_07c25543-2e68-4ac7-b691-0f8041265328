<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { groupBy } from 'lodash';
  import { Message } from '@arco-design/web-vue';

  const assessmentFormList = ref<Record<string, any[]>>({});
  const allAssessmentFormList = ref<any[]>([]);
  const criterionTree = ref<any[]>([]);
  const selectedForm = ref<any>();
  const selectedCriterion = ref<any>();
  const modalVisible = ref(false);
  const addTypeVisible = ref(false);
  const addType = ref(1);

  const emit = defineEmits(['selected']);

  const loadAssessmentForm = async (keyword?: string) => {
    const params: any = {
      pageSize: 999,
    };
    if (keyword) {
      params.name = `${keyword}`;
    }
    const { data } = await request('/evaluation/customCriterion/getList', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params,
    });
    allAssessmentFormList.value = data.items;
    assessmentFormList.value = groupBy(data.items, (item) => item.category?.name || '未分类');
  };

  const loadCriterionTree = async (formId: number) => {
    const { data } = await request(`/evaluation/customCriterionDetail/getTree/${formId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    criterionTree.value = data;
  };

  const handleFormChange = async (formId: number) => {
    selectedForm.value = formId;
    selectedCriterion.value = undefined;
    criterionTree.value = [];
    if (!formId) {
      return;
    }
    await loadCriterionTree(formId);
  };

  const handleNodeSelect = (node: any) => {
    addTypeVisible.value = true;
    selectedCriterion.value = node;
  };

  const handleTypeSelectCancel = () => {
    addTypeVisible.value = false;
    selectedCriterion.value = undefined;
    addType.value = 1;
  };

  // 递归获取末级子目标
  const getLeafChildren = (node: any) => {
    if (!node.children || !node.children.length) {
      return [node];
    }
    return node.children.reduce((acc: any[], child: any) => {
      return [...acc, ...getLeafChildren(child)];
    }, []);
  };

  const handleAddTypeSelect = () => {
    let data: any = {
      id: `criteria-${selectedCriterion.value?.id}`,
      name: selectedCriterion.value?.name,
    };
    switch (addType.value) {
      case 2:
        data = {
          ...data,
          children: selectedCriterion.value.children || [],
        };
        break;
      case 3:
        data = {
          ...data,
          children:
            selectedCriterion.value.children?.map((child: any) => ({
              id: `criteria-${child.id}`,
              name: child.name,
            })) || [],
        };
        break;
      case 4:
        data = {
          ...data,
          children: getLeafChildren(selectedCriterion.value).map((child: any) => ({
            id: `criteria-${child.id}`,
            name: child.name,
          })),
        };
        break;
      default: // 1
        break;
    }

    const form = allAssessmentFormList.value.find((item) => item.id === selectedForm.value);

    emit('selected', data, form);
    addTypeVisible.value = false;
    addType.value = 4;
    selectedCriterion.value = undefined;

    Message.success('添加成功');
  };

  onMounted(async () => {
    await loadAssessmentForm();
  });
</script>

<template>
  <div>
    <a-button size="mini" type="outline" @click="() => (modalVisible = true)">
      <template #icon>
        <IconSelectAll />
      </template>
      从量表选择目标
    </a-button>
    <a-modal v-model:visible="modalVisible" title="选择评测目标" :hide-cancel="true" ok-text="完成">
      <div>
        <a-select v-model="selectedForm" size="mini" class="w-48" placeholder="请选择量表" @change="handleFormChange">
          <a-optgroup v-for="(forms, category) in assessmentFormList" :key="category" :label="category">
            <a-option v-for="form in forms" :key="form.id" :value="form.id">
              {{ form.name }}
            </a-option>
          </a-optgroup>
        </a-select>
      </div>
      <div class="mt-2">
        <a-tree :data="criterionTree" placeholder="选择量表评测目标" :disabled="!selectedForm" size="mini">
          <template #title="node">
            <div class="flex items-center gap-2">
              <a-button size="mini" @click="() => handleNodeSelect(node)"> 选择 </a-button>
              {{ node.name }}
            </div>
          </template>
        </a-tree>
      </div>
    </a-modal>

    <a-modal
      v-model:visible="addTypeVisible"
      title="请选择添加方式"
      @ok="handleAddTypeSelect"
      @close="handleTypeSelectCancel"
    >
      <div v-if="selectedCriterion"> 已选择： {{ selectedCriterion.name }} </div>
      <a-space class="mt-2" direction="vertical">
        <a-radio-group v-model="addType" class="flex flex-col gap-2" style="display: flex">
          <a-radio :value="1"> 仅添加此目标 </a-radio>
          <!--          <a-radio v-if="selectedCriterion?.children?.length" :value="2"> 添加此目标及其所有子目标</a-radio>-->
          <a-radio v-if="selectedCriterion?.children?.length" :value="3"> 添加此目标及其所有直接子目标</a-radio>
          <a-radio v-if="selectedCriterion?.children?.length" :value="4"> 添加此目标及其所有末级子目标</a-radio>
        </a-radio-group>
      </a-space>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
