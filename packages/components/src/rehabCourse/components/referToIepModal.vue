<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { cloneDeep } from 'lodash';
  import { getLeafParentPath } from '../../utils/assessment';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    criterion: {
      type: Object as PropType<any>,
      required: true,
    },
    student: {
      type: Object as PropType<any>,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    chaptersTree: {
      type: Array as PropType<any>,
      required: true,
    },
    chapter: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);
  const loading = ref(false);
  const saving = ref(false);

  const iepList = ref<any[]>([]);
  const longTermTargets = ref<any[]>([]);
  const formData = ref<any>({});
  const shortTermTarget = ref<any>({});
  const shortTermTargets = ref<any[]>([]);

  const newTarget = computed(() => {
    const chapterPath = getLeafParentPath(props.chaptersTree, props.chapter.id);
    const chapterName = chapterPath.map((item: any) => `${item.number} ${item.name}`).join(' / ');
    return {
      subjectId: props.criterion.id,
      name: props.criterion.name,
      chapter: chapterName,
      newItem: true,
      preScore: props.criterion.preScore,
      afterScore: props.criterion.afterScore,
    };
  });

  const alreadyReffered = computed(() => {
    if (!formData.value.shortTermTargetId) {
      return false;
    }

    return shortTermTarget.value?.chapterContents?.some(
      (item) => item.chapter === newTarget.value.chapter && item.name === props.criterion.name,
    );
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const loadIepList = async () => {
    loading.value = true;
    try {
      const { data } = await request('/rehabilitation/rehabilitationPlan', {
        params: {
          student: props.student.id,
          sort: '-id',
          pageSize: 99,
          gradePeriod: props.course.gradePeriod,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      iepList.value = data.items || [];
      if (iepList.value?.length === 1) {
        formData.value.iepId = iepList.value[0].id;
      }
    } finally {
      loading.value = false;
    }
  };

  const handleGetShortTermTargets = () => {
    const val = formData.value.longTermTargetId;
    if (!val) {
      shortTermTargets.value = [];
    }
    const data = longTermTargets.value.find((item) => item.id === val).shortTermTargets || [];
    if (!data?.length) {
      Message.error('该长期目标下没有短期目标');
    }
    shortTermTargets.value = data;
  };

  const loadTargets = async () => {
    const { data } = await request(`/rehabilitation/rehabilitationPlan/targets/${formData.value.iepId}`, {
      params: {
        pageSize: 999,
        courseId: props.course.id,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    longTermTargets.value = data || [];
    const availableLongTermTargets = longTermTargets.value.filter((item) => item.shortTermTargets?.length);
    if (availableLongTermTargets?.length === 1) {
      formData.value.longTermTargetId = availableLongTermTargets[0].id;
      handleGetShortTermTargets();
    }
  };

  const handleShortTermChange = (value) => {
    shortTermTarget.value = cloneDeep(shortTermTargets.value.find((item) => item.id === value) || {});

    formData.value.shortTermTargetId = value;

    if (alreadyReffered.value) {
      Message.error('该评估结果已关联至该短期目标');
    }
  };

  const handleRemoveFromShortTerm = (index) => {
    shortTermTarget.value = {
      ...shortTermTarget.value,
      chapterContents: shortTermTarget.value.chapterContents.filter((item, i) => i !== index),
    };
  };

  const handleOpen = async () => {
    await loadIepList();
  };

  const handleClose = () => {
    longTermTargets.value = [];
    iepList.value = [];
    formData.value = {};
    modalVisible.value = false;
  };

  const handleBeforeOk = async () => {
    if (!shortTermTarget.value) return false;

    saving.value = true;

    const chapterContents = [...(shortTermTarget.value.chapterContents || [])];
    if (!alreadyReffered.value) {
      chapterContents.push(newTarget.value);
    }
    try {
      await request(`/rehabilitation/rehabilitationPlanTarget/${shortTermTarget.value.id}`, {
        method: 'PUT',
        data: {
          ...shortTermTarget.value,
          chapterContents,
          rehabilitationPlanId: formData.value.iepId,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      handleClose();
      Message.success('关联成功');
      return true;
    } finally {
      saving.value = false;
    }
  };

  watch(
    () => formData.value.iepId,
    async (value) => {
      if (!value) return;
      await loadTargets();
    },
  );
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="关联至康复计划"
    :on-before-ok="handleBeforeOk"
    :ok-loading="saving"
    ok-text="保存目标关联"
    :ok-button-props="{ disabled: !formData.shortTermTargetId }"
    :width="800"
    @open="handleOpen"
    @close="handleClose"
    @cancel="handleClose"
  >
    <a-skeleton v-if="loading" :animation="true">
      <a-skeleton-line :rows="5" />
    </a-skeleton>
    <a-form :model="formData" size="mini" auto-label-width>
      <a-form-item label="康复计划">
        <a-select v-model="formData.iepId">
          <a-option v-for="item in iepList" :key="item.id" :value="item.id"
            >{{ item.student.name }} {{ item.gradePeriod }} 的康复计划
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="formData.iepId && longTermTargets?.length" label="长期目标">
        <a-select v-model="formData.longTermTargetId" @change="handleGetShortTermTargets">
          <a-option
            v-for="item in longTermTargets"
            :key="item.id"
            :value="item.id"
            :disabled="!item.shortTermTargets?.length"
            class="py-2"
          >
            <div class="mt-2">{{ item.content }}</div>
            <div class="text-xs text-gray-400 mb-2">
              [{{ item.domain }}]
              {{ item.dateRange?.join(' ~ ') }}
            </div>
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="formData.longTermTargetId && shortTermTargets?.length" label="短期目标">
        <a-select v-model="formData.shortTermTargetId" @change="handleShortTermChange">
          <a-option v-for="item in shortTermTargets" :key="item.id" :value="item.id">
            <div class="mt-2">{{ item.content }}</div>
            <div class="text-xs text-gray-400 mb-2">
              {{ item.dateRange?.join(' ~ ') }}
            </div>
          </a-option>
        </a-select>
      </a-form-item>
      <a-table
        v-if="formData.shortTermTargetId"
        size="mini"
        :data="shortTermTarget?.chapterContents"
        :pagination="false"
      >
        <template #empty>
          <a-empty>暂无关联</a-empty>
        </template>
        <template #columns>
          <a-table-column data-index="chapter" title="已关联章节" />
          <a-table-column data-index="name" title="已关联目标" />
          <a-table-column data-index="preScore" title="前测" :width="70" />
          <a-table-column data-index="afterScore" title="后测" :width="70" />
          <a-table-column title="操作" :width="70">
            <template #cell="{ rowIndex }">
              <a-button size="mini" status="danger" type="text" @click="() => handleRemoveFromShortTerm(rowIndex)">
                <template #icon>
                  <IconDelete />
                </template>
              </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <a-table size="mini" class="mt-2" :data="[newTarget]" :pagination="false">
        <template #columns>
          <a-table-column data-index="chapter" title="待关联章节" />
          <a-table-column data-index="name" title="待关联目标" />
          <a-table-column data-index="preScore" title="前测" :width="70" />
          <a-table-column data-index="afterScore" title="后测" :width="70" />
        </template>
      </a-table>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
