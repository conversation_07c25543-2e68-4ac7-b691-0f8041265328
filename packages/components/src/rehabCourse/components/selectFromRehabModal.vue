<script setup lang="ts">
  import { onMounted, ref, watch, PropType, computed } from 'vue';
  import { useList } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    selectedNodes: {
      type: Array as PropType<any[]>,
    },
  });

  const modalVisible = ref(false);
  const allAssessmentFormList = ref<any[]>([]);
  const targets = ref<any[]>([]);
  const longTermTargets = ref<any[]>([]);
  const availablePeriodList = ref<any[]>([]);

  const emit = defineEmits(['selected']);

  const { listData, pagination, queryParams, loadData, loading } = useList({
    api: '/student/rehabTargetSearch',
    axiosConfig: {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
    pageSize: 999,
  });

  const handleSearch = async () => {
    await loadData();
    if (!queryParams.value.longTermTarget) {
      longTermTargets.value = (listData.value || []).map((item) => item.target);
    } else {
      targets.value = listData.value || [];
    }
  };

  const selectedTargetIds = ref<any[]>([]);

  const oldSelectedIds = computed((): number[] => {
    const results = [];
    props.selectedNodes?.forEach((item) => {
      if (item?.iepTargetIds?.length) {
        item?.iepTargetIds.forEach((id) => {
          results.push(id);
        });
      }
    });
    return results;
  });

  const isSelected = (record: any): boolean => {
    const { targetIds } = record;
    return targetIds.every((id: any) => selectedTargetIds.value.includes(id));
  };

  const isNew = (record: any): boolean => {
    const { targetIds } = record;
    return !targetIds.every((id: any) => oldSelectedIds.value.includes(id));
  };

  const handleSelect = (record: string) => {
    const { targetIds } = record;
    if (isSelected(record)) {
      selectedTargetIds.value = selectedTargetIds.value.filter((id) => !targetIds.includes(id));
    } else {
      targetIds.forEach((id) => {
        selectedTargetIds.value.push(id);
      });
    }
  };

  const handleOk = () => {
    // 过滤出新增的
    const selectedItems = targets.value
      .filter((item) => isSelected(item))
      .filter((item) => isNew(item))
      .map((item) => ({
        name: item.target,
        students: item.students,
        iepTargetIds: item.targetIds,
      }));

    if (selectedItems.length) {
      emit('selected', selectedItems);
    }
    modalVisible.value = false;
    return true;
  };

  onMounted(async () => {
    const { data } = await request('/rehabilitation/rehabilitationPlan/availablePeriods', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    availablePeriodList.value = data || [];

    if (data?.length) {
      queryParams.value.period = data[data.length - 1];
    }
  });

  watch(
    () => queryParams.value.period,
    async () => {
      queryParams.value.page = 1;
      listData.value = [];
      queryParams.value.criterionId = undefined;
      queryParams.value.longTermTarget = undefined;
      longTermTargets.value = [];
      if (queryParams.value.period) {
        const { data } = await request('/rehabilitation/rehabilitationPlan/availableCriterion', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            period: queryParams.value.period,
          },
        });

        allAssessmentFormList.value = (data || []).map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
  );

  const handleOpen = () => {
    props.selectedNodes?.forEach((item) => {
      if (item?.iepTargetIds?.length) {
        item?.iepTargetIds.forEach((id) => {
          selectedTargetIds.value.push(id);
        });
      }
    });
  };

  watch(
    () => queryParams.value.criterionId,
    () => {
      queryParams.value.page = 1;
      listData.value = [];
      queryParams.value.longTermTarget = undefined;
      longTermTargets.value = [];
      if (queryParams.value.criterionId && queryParams.value.period) {
        handleSearch();
      }
    },
  );

  watch(
    () => queryParams.value.longTermTarget,
    () => {
      queryParams.value.page = 1;
      listData.value = [];
      if (queryParams.value.longTermTarget) {
        handleSearch();
      }
    },
  );
</script>

<template>
  <a-button size="mini" type="outline" @click="modalVisible = true">
    <template #icon>
      <IconSelectAll />
    </template>
    从康复计划选择目标
  </a-button>
  <a-modal
    v-model:visible="modalVisible"
    :width="1000"
    title="从康复计划选择目标"
    :on-before-ok="handleOk"
    @open="handleOpen"
  >
    <a-form :model="queryParams" auto-label-width layout="inline" class="flex-1" size="mini">
      <a-form-item label="学期" required class="!w-48">
        <a-select v-model="queryParams.period" :options="availablePeriodList" placeholder="请选择" />
      </a-form-item>
      <a-form-item label="科目" required class="!w-52">
        <a-select
          v-model="queryParams.criterionId"
          size="mini"
          class="w-48"
          placeholder="请选择科目"
          :options="allAssessmentFormList"
        />
      </a-form-item>
      <a-form-item label="长期目标" class="flex-1" required>
        <a-select
          v-model="queryParams.longTermTarget"
          placeholder="请选择"
          :loading="loading"
          :options="longTermTargets"
        />
      </a-form-item>
    </a-form>

    <a-table :data="targets" size="mini" class="mt-2" :pagination="pagination" :loading="loading">
      <template #columns>
        <a-table-column title="目标" data-index="target" />
        <a-table-column title="学生" data-index="students">
          <template #cell="{ record }">
            {{ record.students.map((student) => student.name).join(', ') }}
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="80">
          <template #cell="{ record }">
            <a-button
              v-if="isSelected(record)"
              size="mini"
              type="outline"
              status="success"
              @click="() => handleSelect(record)"
            >
              <template #icon>
                <IconCheck />
              </template>
              已选择
            </a-button>
            <a-button v-else size="mini" @click="() => handleSelect(record)">选择</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
