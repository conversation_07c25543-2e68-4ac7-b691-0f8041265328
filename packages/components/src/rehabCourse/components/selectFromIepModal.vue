<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { useList } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    selectedNodes: {
      type: Array as PropType<any[]>,
    },
  });

  const modalVisible = ref(false);
  const allAssessmentFormList = ref<any[]>([]);
  const targets = ref<any[]>([]);
  const longTermTargets = ref<any[]>([]);
  const availablePeriodList = ref<any[]>([]);

  const emit = defineEmits(['selected']);

  const { listData, pagination, queryParams, loadData, loading } = useList({
    api: '/student/iepTargetSearch',
    axiosConfig: {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
    pageSize: 999,
  });

  const handleSearch = async () => {
    await loadData();
    if (!queryParams.value.longTermTarget) {
      longTermTargets.value = (listData.value || []).map((item) => item.target);
    } else {
      targets.value = listData.value || [];
    }
  };

  const selectedTargets = ref<any[]>([]);

  const handleSelect = (target: string) => {
    if (selectedTargets.value.includes(target)) {
      selectedTargets.value = selectedTargets.value.filter((i) => i !== target);
    } else {
      selectedTargets.value = [...selectedTargets.value, target];
    }
  };

  const handleOk = async () => {
    const selectedItems = targets.value
      .filter((item) => selectedTargets.value.includes(item.target))
      .filter((item) => props.selectedNodes?.every((node) => node.name !== item.target))
      .map((item) => ({
        name: item.target,
        students: item.students,
        rehabTargetIds: item.targetIds,
      }));

    if (selectedItems.length) {
      emit('selected', selectedItems);
    }
    modalVisible.value = false;
  };
  onMounted(async () => {
    const { data } = await request('/resourceRoom/individualizedEducation/availablePeriods', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    availablePeriodList.value = data || [];

    if (data?.length) {
      queryParams.value.period = data[data.length - 1];
    }
  });

  const handleOpen = () => {
    selectedTargets.value = props.selectedNodes?.map((item) => item.name) || [];
  };

  watch(
    () => queryParams.value.period,
    async () => {
      queryParams.value.page = 1;
      listData.value = [];
      queryParams.value.criterionId = undefined;
      queryParams.value.longTermTarget = undefined;
      longTermTargets.value = [];
      if (queryParams.value.period) {
        const { data } = await request('/resourceRoom/individualizedEducation/availableCriterion', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            period: queryParams.value.period,
          },
        });

        allAssessmentFormList.value = (data || []).map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
  );

  watch(
    () => queryParams.value.criterionId,
    () => {
      queryParams.value.page = 1;
      listData.value = [];
      queryParams.value.longTermTarget = undefined;
      longTermTargets.value = [];
      if (queryParams.value.criterionId && queryParams.value.period) {
        handleSearch();
      }
    },
  );

  watch(
    () => queryParams.value.longTermTarget,
    () => {
      queryParams.value.page = 1;
      listData.value = [];
      if (queryParams.value.longTermTarget) {
        handleSearch();
      }
    },
  );
</script>

<template>
  <a-button size="mini" type="outline" @click="modalVisible = true">
    <template #icon>
      <IconSelectAll />
    </template>
    从IEP选择目标
  </a-button>
  <a-modal
    v-model:visible="modalVisible"
    :width="1000"
    title="从IEP选择目标"
    :on-before-ok="handleOk"
    @open="handleOpen"
  >
    <a-form :model="queryParams" auto-label-width layout="inline" class="flex-1" size="mini">
      <a-form-item label="学期" required class="!w-48">
        <a-select v-model="queryParams.period" :options="availablePeriodList" placeholder="请选择" />
      </a-form-item>
      <a-form-item label="科目" required class="!w-52">
        <a-select
          v-model="queryParams.criterionId"
          size="mini"
          class="w-48"
          placeholder="请选择科目"
          :options="allAssessmentFormList"
        />
      </a-form-item>
      <a-form-item label="长期目标" class="flex-1" required>
        <a-select
          v-model="queryParams.longTermTarget"
          placeholder="请选择"
          :loading="loading"
          :options="longTermTargets"
        />
      </a-form-item>
    </a-form>

    <a-table :data="targets" size="mini" class="mt-2" :pagination="pagination" :loading="loading">
      <template #columns>
        <a-table-column title="目标" data-index="target" />
        <a-table-column title="学生" data-index="students">
          <template #cell="{ record }">
            {{ record.students.map((student) => student.name).join(', ') }}
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="80">
          <template #cell="{ record }">
            <a-button
              v-if="selectedTargets.includes(record.target)"
              size="mini"
              type="outline"
              status="success"
              @click="() => handleSelect(record.target)"
            >
              <template #icon>
                <IconCheck />
              </template>
              已选择
            </a-button>
            <a-button v-else size="mini" @click="() => handleSelect(record.target)">选择</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
