<script setup lang="ts">
  import { PropType } from 'vue';
  import useCourseAssess from '../courseAssess';
  import GradeClassSelect from '../../student/gradeClassSelect.vue';

  const props = defineProps({
    chapter: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    type: {
      type: String as PropType<'pre' | 'after'>,
      default: 'pre',
    },
  });

  const { studentsList, studentLoading, currentStudent, currentResult, handleGradeChange, gradeClass } =
    useCourseAssess({
      chapter: props.chapter,
      course: props.course,
      type: props.type,
    });
</script>

<template>
  <div class="flex gap-2">
    <div class="w-48 border-r border-r-slate-200 pr-2">
      <grade-class-select class="w-48" @change="handleGradeChange" />
      <a-divider :margin="10" />
      <div class="flex gap-2 text-xs">
        <div class="text-green-500">
          <IconCheck />
          已评测
        </div>
        <div class="text-red-500">
          <IconQuestionCircle />
          未评测
        </div>
      </div>
      <a-divider :margin="10" />
      <a-spin class="mx-1 mt-2 w-full" :loading="studentLoading">
        <div v-for="(student, idx) in studentsList" :key="idx" @click="() => (currentStudent = student)">
          <div
            class="flex gap-2 items-center cursor-pointer"
            :class="{ 'text-blue-500': currentStudent?.id === student.id }"
          >
            <div :class="student[`chapterAssessment${type}`] ? 'text-green-500' : 'text-red-500'">
              <IconCheck v-if="student[`chapterAssessment${type}`]" />
              <IconQuestionCircle v-else />
            </div>
            {{ student.name }}
            <small class="text-xs"> {{ student.age }}岁 {{ student.disorders }} </small>
          </div>
        </div>
        <div v-if="!studentsList?.length">
          <a-empty v-if="!gradeClass?.id" description="请先选择一个班级" />
          <a-empty v-else description="所选班级没有学生" />
        </div>
      </a-spin>
    </div>
    <div class="flex-1">
      <a-empty v-if="!currentStudent?.id" description="从左侧选择一个学生开始评测" />
      <div v-else>
        <div class="flex justify-between items-center mx-1">
          <div class="flex gap-2 items-center">
            <span class="text-base">
              {{ currentStudent.name }}
            </span>
            <span class="text-xs"> {{ currentStudent.age }}岁 {{ currentStudent.disorders }} </span>
          </div>
          <div class="actions">
            <a-button v-if="currentStudent[`chapterAssessment${type}`]" size="mini" type="outline" status="danger">
              {{ `重置 ${currentStudent.name} 的${type === 'pre' ? '预测' : '后测'}结果` }}
            </a-button>
          </div>
        </div>
        <a-divider :margin="10"></a-divider>
        <div>
          {{ course.teachingAssessScores }}
          {{ currentResult }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
