<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import AnnotatableBlock from '../../common/annotatableBlock.vue';
  import { AnnotationItem, AnnotationModuleSource } from '../../utils/annotationBlock';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    currentDirectory: {
      type: String,
      required: true,
    },
    showAssessment: {
      type: Boolean,
      default: true,
    },
    assessResultsViaStudent: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    assessResultsViaCriteriaMap: {
      type: Object,
      default: () => ({}),
    },
    annotations: {
      type: Array as PropType<AnnotationItem[]>,
      default: () => [],
    },
    annotationModule: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'refer', 'update:annotations']);
  const annotationsList = computed({
    get: () => props.annotations,
    set: (value) => {
      emit('update:annotations', value);
    },
  });

  const currentCriteria = ref<any>(null);
  const viewByCriteriaVisible = ref<any>(false);

  const handleViewAssessmentViaCriterion = async (criteria: any) => {
    currentCriteria.value = criteria;
    viewByCriteriaVisible.value = true;
  };
</script>

<template>
  <div>
    <div class="text-lg font-medium">{{ chapter.number }} {{ currentDirectory }}</div>
    <a-divider :margin="10" />
    <div class="mb-4">
      <div class="text-base font-medium"> 一、训练准备</div>
      <div v-for="(item, idx) in chapter.content.teachingPrepare" :key="idx" class="mx-2">
        <div class="mt-2 text-base"> {{ idx + 1 }}、{{ item.name }}</div>
        <!--        <rich-text-display class="mt-1 text-gray-600" :raw="item.udf1" />-->
        <annotatable-block
          v-model:annotations="annotationsList"
          class="mt-1 text-gray-600"
          :initial-text="item.udf1"
          :related-modules="['student']"
          :annotation-module-source="{
            ...annotationModule,
            paragraphId: item.paragraphId || `teaching-prepare-${idx}`,
          }"
          annotation-category="调整"
        />
      </div>
      <a-empty v-if="!chapter.content.teachingPrepare?.length" content="暂无训练准备" />
    </div>
    <div class="mb-4">
      <div class="text-base font-medium"> 二、训练安排</div>
      <div v-for="(item, idx) in chapter.content.lessonPrepare" :key="idx" class="mx-2">
        <div class="mt-2 text-base"> {{ idx + 1 }}、{{ item.name }}</div>
        <!--        <rich-text-display class="mt-1 text-gray-600" :raw="item.udf1" />-->
        <annotatable-block
          v-model:annotations="annotationsList"
          class="mt-1 text-gray-600"
          :initial-text="item.udf1"
          :related-modules="['student']"
          :annotation-module-source="{
            ...annotationModule,
            paragraphId: item.paragraphId || `teaching-plan-${idx}`,
          }"
          annotation-category="调整"
        />
      </div>
      <a-empty v-if="!chapter.content.lessonPrepare?.length" content="暂无训练安排" />
    </div>
    <div v-if="showAssessment" class="mb-4">
      <div class="text-base font-medium"> 三、学生评测</div>
      <a-tabs class="mt-2">
        <a-tab-pane key="viaStudent" title="按学生查看">
          <a-table
            row-key="id"
            class="m-2"
            :data="assessResultsViaStudent"
            size="mini"
            :default-expand-all-rows="true"
            :pagination="false"
          >
            <template #columns>
              <a-table-column title="目标" data-index="name" />
              <!--<a-table-column title="前测结果" data-index="pre" />
              <a-table-column title="后测结果" data-index="after" />-->
              <a-table-column title="前测结果" data-index="pre">
                <template #cell="{ record }">
                  <span v-if="!record.children">{{ record?.pre || '-' }}</span>
                </template>
              </a-table-column>
              <a-table-column title="后测结果" data-index="after">
                <template #cell="{ record }">
                  <span v-if="!record.children">{{ record?.after || '-' }}</span>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="viaCriteria" title="按评测目标查看">
          <a-table
            class="m-2"
            :data="chapter.content.teachingAssessCriteria"
            size="mini"
            :default-expand-all-rows="true"
            :pagination="false"
          >
            <template #columns>
              <a-table-column title="目标" data-index="name" />
              <a-table-column title="评测结果" data-index="criteria">
                <template #cell="{ record }">
                  <a-button
                    v-if="!record.children?.length"
                    size="mini"
                    @click="() => handleViewAssessmentViaCriterion(record)"
                    >查看评测结果
                  </a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
      <a-empty v-if="!chapter.content.teachingAssessCriteria?.length" content="暂无教学目标" />
    </div>
    <div v-else class="mb-4">
      <div class="text-base font-medium"> 三、目标设定</div>
      <a-table
        class="m-2"
        :data="chapter.content.teachingAssessCriteria"
        size="mini"
        :default-expand-all-rows="true"
        :pagination="false"
      >
        <template #columns>
          <a-table-column title="目标" data-index="name" />
        </template>
      </a-table>
    </div>

    <a-modal v-model:visible="viewByCriteriaVisible" :title="currentCriteria?.name" :width="600">
      <a-table :data="assessResultsViaCriteriaMap[currentCriteria?.id]?.students" :pagination="false">
        <template #columns>
          <a-table-column title="班级" data-index="gradeClass" />
          <a-table-column title="学生" data-index="name" />
          <a-table-column title="前测结果" data-index="pre">
            <template #cell="{ record }">
              <span>{{ record?.pre || '-' }}</span>
            </template>
          </a-table-column>
          <a-table-column title="后测结果" data-index="after">
            <template #cell="{ record }">
              <span>{{ record?.after || '-' }}</span>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
