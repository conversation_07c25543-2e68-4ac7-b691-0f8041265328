<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import openapi from '@repo/infrastructure/openapi';
  import { useLoading } from '@repo/infrastructure/hooks';
  import Editor from '../../wps/editor.vue';
  import UploadOrNew from '../../wps/uploadOrNew.vue';

  const { loading, setLoading } = useLoading();

  const props = defineProps({
    course: {
      type: Object,
      required: true,
    },
    chapter: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const chapterInfo = ref<any>();
  const officeType = 'Writer';

  const loadChapter = async () => {
    const { data } = await openapi.rehabCourse.getRehabChapter({
      id: props.chapter.id!,
    });

    chapterInfo.value = data;
  };

  const handleSave = async (fileId: string) => {
    setLoading(true);
    await openapi.rehabCourse.updateRehabChapterContent(
      {
        id: props.chapter.id!,
      },
      {
        teachingDesign: fileId,
      },
    );

    await loadChapter();
    setLoading(false);
  };

  onMounted(async () => {
    setLoading(true);
    await loadChapter();
    setLoading(false);
  });
</script>

<template>
  <a-spin :loading="loading" style="width: 100%">
    <div v-if="!loading && chapterInfo" class="design-wrapper">
      <upload-or-new
        v-if="!chapterInfo.content?.teachingDesign"
        :default-file-name="`教学设计-${chapterInfo.name}`"
        :model-value="chapterInfo.content?.teachingDesign!"
        :office-type="officeType"
        @update:model-value="handleSave"
      />
      <editor
        v-else
        :file-id="chapterInfo.content?.teachingDesign"
        :office-type="officeType"
        element-id="teaching-design"
      />
    </div>
  </a-spin>
</template>

<style lang="less" scoped>
  .design-wrapper {
    position: relative;
    height: calc(100vh - 190px);

    > div {
      height: 100%;
    }
  }
</style>
