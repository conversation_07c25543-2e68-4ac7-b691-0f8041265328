<script setup lang="ts">
  import { computed, PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import {
    deleteChapterAssessment,
    editChapterAssessment,
    getChapterAssessmentList,
  } from '@repo/infrastructure/openapi/rehabChapterAssessmentController';
  import { Message, Modal } from '@arco-design/web-vue';
  import { cloneDeep, isNumber, sum } from 'lodash';
  import GradeClassSelect from '../../student/gradeClassSelect.vue';
  import QuestionTestModal from '../components/questionTestModal.vue';
  import ReferToIepModal from '../components/referToIepModal.vue';

  const props = defineProps({
    chapter: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    chaptersTree: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const type = 'after';
  const { loading: studentLoading, setLoading: setStudentLoading } = useLoading();
  const { loading, setLoading } = useLoading();

  const gradeClass = ref<any>(null);
  const studentsList = ref<Record<string, any>[]>([]);
  const chapterAssessResultMap = ref<Record<number, any>>({});
  const chapterAssessPreResultMap = ref<Record<number, any>>({});
  const currentStudent = ref<Record<string, any>>({});
  const chapterAssessmentQuery = ref<any>({
    chapterId: props.chapter.id,
  });
  const currentResult = ref<any[]>([]);

  const resultData = ref<any>({});
  const currentTotalScore = ref(0);

  const currentCriteria = ref<any>({});
  const questTestVisible = ref(false);

  const referToIepVisible = ref(false);
  const currentCriterion = ref<any>({});

  const handleShowReferToIep = (record: any, preScore: any) => {
    currentCriterion.value = {
      ...record,
      preScore,
      afterScore: record.score,
    };
    referToIepVisible.value = true;
  };
  const handleHideReferToIep = () => {
    referToIepVisible.value = false;
    currentCriterion.value = {};
  };

  const teachingAssessScoresMap = computed(() => {
    return props.course.teachingAssessScores.reduce((acc: Record<number, any>, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});
  });

  const preTotalScore = ref(0);

  const defaultTargets = [...(props.chapter?.content?.teachingAssessCriteria || [])].map((item) => {
    return {
      ...item,
      score: undefined,
      children: item.children?.map((child) => ({
        ...child,
        score: undefined,
      })),
    };
  });

  const loadStudents = async () => {
    setStudentLoading(true);
    try {
      const { data } = await request('/resourceRoom/student', {
        params: {
          gradeClass: gradeClass.value?.id,
          pageSize: 99,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      studentsList.value = (data.items || []).map((item) => {
        item.chapterAssessmentafter = chapterAssessResultMap.value[item.id] || undefined;
        item.chapterAssessmentpre = chapterAssessPreResultMap.value[item.id] || undefined;
        return item;
      });
    } finally {
      setStudentLoading(false);
    }
  };
  const loadResults = async () => {
    const { data } = await getChapterAssessmentList({
      ...chapterAssessmentQuery.value,
    });

    const typesMap = {
      pre: [],
      after: [],
    };
    data.forEach((item) => {
      typesMap[item.type].push(item);
    });

    chapterAssessResultMap.value = typesMap.after.reduce((acc: Record<number, any>, item: any) => {
      acc[item.studentId] = item;
      return acc;
    }, {});

    chapterAssessPreResultMap.value = typesMap.pre.reduce((acc: Record<number, any>, item: any) => {
      acc[item.studentId] = item;
      return acc;
    }, {});
  };

  const handleGradeChange = async (gradeClassId: number, cls: any) => {
    // if (!gradeClassId) {
    //   Message.error('请选择一个班级');
    //   return;
    // }
    gradeClass.value = cls;
    chapterAssessmentQuery.value.gradeClassId = gradeClassId;

    await loadResults();
    await loadStudents();
  };

  const preResult = ref<Record<string, any>>({});
  const handleStudentChange = async (student: any) => {
    if (!student.chapterAssessmentpre?.scores?.length) {
      Message.error('请先完成该学生的前测');
      return;
    }

    const preResultRaw: any[] = [];
    student.chapterAssessmentpre?.scores?.forEach((parent) => {
      if (parent.children?.length) {
        preResultRaw.push(...parent.children);
      } else {
        preResultRaw.push(parent);
      }
    });

    preTotalScore.value = sum(preResultRaw.map((item) => item.score).filter((item) => isNumber(item)));

    preResult.value = preResultRaw.reduce((acc: Record<string, any>, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});
    currentStudent.value = student;
    resultData.value = student[`chapterAssessment${type}`] || {};
    if (resultData.value?.scores?.length) {
      currentResult.value = resultData.value.scores;
    } else {
      currentResult.value = cloneDeep(
        student.chapterAssessmentpre.scores.map((item) => {
          return {
            ...item,
            score: undefined,
            preTestResult: item.testResult,
            testResult: {},
            children:
              item.children?.map((child) => ({
                ...child,
                preTestResult: child.testResult,
                testResult: {},
                score: undefined,
              })) || [],
          };
        }),
      );
    }
  };

  const handleAutoSave = async () => {
    if (!currentResult.value?.length || !currentStudent.value?.id) {
      return;
    }

    setLoading(true);
    try {
      if (!resultData.value?.id) {
        resultData.value = {
          studentId: currentStudent.value?.id,
          chapterId: props.chapter.id,
          gradeClassId: gradeClass.value?.id,
          type,
          scores: currentResult.value,
        };
      }
      const { data } = await editChapterAssessment({
        ...resultData.value,
      });
      resultData.value = data;
      currentStudent.value[`chapterAssessment${type}`] = data;
      currentResult.value = data.scores || [];
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    if (!currentStudent.value?.id || !resultData.value.id) {
      return;
    }
    Modal.confirm({
      title: '请确认',
      content: `确定重置 ${currentStudent.value.name} 的后测目标吗？`,
      onOk: async () => {
        await deleteChapterAssessment({
          id: resultData.value.id,
        });
        Message.success('重置成功');
        resultData.value = {};
        currentResult.value = cloneDeep(defaultTargets);
        currentStudent.value[`chapterAssessment${type}`] = undefined;
      },
    });
  };

  const handleShowTest = (record: any) => {
    currentCriteria.value = record;
    questTestVisible.value = true;
  };

  const handleTestResultUpdated = (criteria: any) => {
    currentResult.value = currentResult.value.map((item) => {
      if (item.id === criteria.id) {
        return criteria;
      }
      if (item.children?.length) {
        item.children = item.children.map((child) => {
          if (child.id === criteria.id) {
            return criteria;
          }
          return child;
        });
      }
      return item;
    });
    currentCriteria.value = null;
    questTestVisible.value = false;
  };

  const screen = () => {
    if (!studentsList.value.length > 0) Message.warning('请先选择班级');
    studentsList.value = studentsList.value.filter(
      (res) => res?.chapterAssessmentpre != null, // 这同时检查了 null 和 undefined
    );
  };
  watch(
    () => currentResult.value,
    () => {
      const allChildren: number[] = [];
      currentResult.value?.forEach((res) => {
        const children: number[] = res?.children?.map((item) => item.score).filter((item) => item !== null) || [];
        allChildren.push(...children);
      });
      currentTotalScore.value = sum(allChildren);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  defineExpose({
    handleAutoSave,
  });
</script>

<template>
  <div class="flex gap-2">
    <div class="w-48 border-r border-r-slate-200 pr-2">
      <grade-class-select @change="handleGradeChange" />
      <a-divider :margin="10" />
      <div class="flex gap-2 text-xs">
        <div class="text-green-500">
          <IconCheck />
          已评测
        </div>
        <div class="text-red-500">
          <IconQuestionCircle />
          未评测
        </div>
        <div style="color: dodgerblue" @click="screen">
          <icon-edit />
          已有前测
        </div>
      </div>
      <a-divider :margin="10" />
      <a-spin class="mx-1 w-full" :loading="studentLoading">
        <div v-for="(student, idx) in studentsList" :key="idx" class="py-2" @click="() => handleStudentChange(student)">
          <div
            class="flex gap-2 items-center cursor-pointer"
            :class="{ 'text-blue-500': currentStudent?.id === student.id }"
          >
            <div :class="student[`chapterAssessment${type}`] ? 'text-green-500' : 'text-red-500'">
              <IconCheck v-if="student[`chapterAssessment${type}`]" />
              <icon-edit v-else-if="student.chapterAssessmentpre != null" style="color: dodgerblue" />
              <IconQuestionCircle v-else />
            </div>
            {{ student.name }}
            <small class="text-xs"> {{ student.age }}岁 {{ student.disorders }} </small>
          </div>
        </div>
        <div v-if="!studentsList?.length">
          <a-empty v-if="!gradeClass?.id" description="请先选择一个班级" />
          <a-empty v-else description="所选班级没有学生" />
        </div>
      </a-spin>
    </div>
    <div class="flex-1">
      <a-empty v-if="!currentStudent?.id" description="从左侧选择一个学生开始评测" />
      <div v-else>
        <div class="flex justify-between items-center mx-1">
          <div class="flex gap-2 items-center">
            <span class="text-base">
              {{ currentStudent.name }}
            </span>
            <span class="text-xs"> {{ currentStudent.age }}岁 {{ currentStudent.disorders }} </span>
          </div>
          <a-space class="actions">
            <a-button size="mini"> 前测得分：{{ preTotalScore || 0 }}</a-button>
            <a-button size="mini"> 后测得分：{{ currentTotalScore || 0 }}</a-button>
            <a-button size="mini" type="primary" :loading="loading" @click="handleAutoSave">
              <template #icon>
                <IconSave />
              </template>
              保存
            </a-button>
            <a-button v-if="resultData?.id" size="mini" type="outline" status="danger" @click="handleReset">
              重置 {{ currentStudent.name }} 的后测
            </a-button>
          </a-space>
        </div>
        <a-divider :margin="10"></a-divider>
        <a-table :data="currentResult" :default-expand-all-rows="true" :pagination="false" size="mini">
          <template #columns>
            <a-table-column data-index="name" title="评测目标" />
            <a-table-column data-index="question" title="试题" :width="80">
              <template #cell="{ record }">
                <a-button
                  v-if="record.questionIds?.length"
                  type="text"
                  size="mini"
                  @click="() => handleShowTest(record)"
                >
                  试题
                </a-button>
              </template>
            </a-table-column>
            <a-table-column data-index="score" title="前测结果">
              <template #cell="{ record }">
                <div v-if="!record.children">
                  <div v-if="isNumber(preResult[record.id]?.score)">
                    <div
                      v-if="
                        teachingAssessScoresMap[preResult[record.id]?.score]?.name !=
                        teachingAssessScoresMap[preResult[record.id]?.score]?.score
                      "
                    >
                      {{ teachingAssessScoresMap[preResult[record.id]?.score]?.name }}
                      ({{ preResult[record.id]?.score }}分)
                    </div>
                    <div v-else> {{ preResult[record.id]?.score }}分</div>
                  </div>
                  <div v-else class="text-red-400"> 未评测</div>
                </div>
              </template>
            </a-table-column>
            <a-table-column data-index="score" title="后测结果">
              <template #cell="{ record }">
                <a-radio-group v-if="!record.children?.length" v-model="record.score" type="button" size="mini">
                  <a-tooltip v-for="(score, idx) in course.teachingAssessScores" :key="idx" :content="score.name">
                    <a-radio :value="score.id">
                      {{ score.id }}
                    </a-radio>
                  </a-tooltip>
                </a-radio-group>
              </template>
            </a-table-column>
            <a-table-column title="康复计划">
              <template #cell="{ record }">
                <a-tooltip content="关联至康复计划教学实施目标">
                  <a-button
                    v-if="!record.children?.length"
                    size="mini"
                    @click="() => handleShowReferToIep(record, preResult[record.id]?.score)"
                  >
                    <template #icon>
                      <IconShareExternal />
                    </template>
                  </a-button>
                </a-tooltip>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>

    <question-test-modal
      v-model:visible="questTestVisible"
      :criteria="currentCriteria"
      :course="course"
      @update:criteria="handleTestResultUpdated"
    />

    <refer-to-iep-modal
      v-model:visible="referToIepVisible"
      :criterion="currentCriterion"
      :student="currentStudent"
      :course="course"
      :chapters-tree="chaptersTree"
      :chapter="chapter"
      @hide="handleHideReferToIep"
    />
  </div>
</template>

<style scoped lang="scss">
  :deep .arco-radio-button.arco-radio-checked {
    background-color: rgb(var(--primary-4));
    color: #fff;
  }
</style>
