<script lang="ts" setup>
  import { onMounted, PropType, ref, watch } from 'vue';
  import { cloneDeep, sum } from 'lodash';
  import {
    getRehabChapterAssessmentList,
    batchEditRehabChapterAssessment,
  } from '@repo/infrastructure/openapi/rehabChapterAssessmentController';
  import { Message } from '@arco-design/web-vue';
  import QuestionTestModal from '../components/questionTestModal.vue';
  import TargetAssessmentCmp from './targets/targetAssessmentCmp.vue';
  import TargetIepTag from './targets/targetIepTag.vue';
  import TargetStudentsAverage from './targets/targetStudentsAverage.vue';

  const props = defineProps({
    chapter: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    chaptersTree: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
  });

  const type = 'pre';
  const loading = ref(false);

  const currentResult = ref<any[]>([]);
  const rawResultDataMap = ref<any>({});
  const currentTotalScore = ref(0);
  const currentCriteria = ref<any>({});
  const questTestVisible = ref(false);
  const resultDataMap = ref<any>({});

  const chapterAssessmentQuery = ref<any>({
    chapterId: props.chapter.id,
    type,
  });

  const handleShowTest = (record: any) => {
    currentCriteria.value = record;
    questTestVisible.value = true;
  };

  const loadResults = async () => {
    const { data } = await getRehabChapterAssessmentList({
      ...chapterAssessmentQuery.value,
    });

    rawResultDataMap.value = (data || []).reduce((prev: any, curr: any) => {
      prev[curr.studentId] = curr;
      return prev;
    }, {});

    const formattedData = {};

    (data || []).forEach((item) => {
      const scoresMap = {};
      (item.scores || []).forEach((score) => {
        if (score.children?.length) {
          score.children.forEach((child: any) => {
            scoresMap[child.id] = child[type];
          });
        } else {
          scoresMap[score.id] = score[type];
        }
      });

      formattedData[item.studentId] = {
        id: item.id,
        scoresMap,
      };
    });

    resultDataMap.value = formattedData;
  };

  const handleTestResultUpdated = (criteria: any) => {
    currentResult.value = currentResult.value.map((item) => {
      if (item.id === criteria.id) {
        return criteria;
      }
      if (item.children?.length) {
        item.children = item.children.map((child) => {
          if (child.id === criteria.id) {
            return criteria;
          }
          return child;
        });
      }
      return item;
    });
    currentCriteria.value = null;
    questTestVisible.value = false;
  };

  const handleSave = async () => {
    const postData: any[] = [];
    Object.keys(resultDataMap.value).forEach((studentId) => {
      let record: any = {};
      record.studentId = Number(studentId);
      record.chapterId = props.chapter.id;
      record.type = type;
      record.gradeClassId = props.course.gradeClassId;
      if (rawResultDataMap.value[studentId]) {
        record = {
          ...rawResultDataMap.value[studentId],
          ...record,
        };
      } else {
        record.scores = [];
      }

      const scoresMap = resultDataMap.value[studentId];
      record.scores = cloneDeep(props.chapter.content?.teachingAssessCriteria || []).map((criterion) => {
        if (criterion.children?.length) {
          criterion.children.map((child) => {
            child[type] = scoresMap.scoresMap[child.id];
            child.students = [];
            return child;
          });
        } else {
          criterion[type] = scoresMap.scoresMap[criterion.id];
          criterion.students = [];
        }

        return criterion;
      });

      postData.push(record);
    });

    loading.value = true;
    try {
      await batchEditRehabChapterAssessment(postData);
      await loadResults();

      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => currentResult.value,
    () => {
      const allChildren: number[] = [];
      currentResult.value?.forEach((res) => {
        const children: number[] = res?.children?.map((item) => item.score).filter((item) => item !== null) || [];
        allChildren.push(...children);
      });
      currentTotalScore.value = sum(allChildren);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  onMounted(async () => {
    await loadResults();
  });
</script>

<template>
  <a-button size="mini" type="primary" :loading="loading" @click="handleSave">
    <template #icon>
      <IconSave />
    </template>
    保存
  </a-button>
  <a-collapse class="mt-2">
    <a-collapse-item
      v-for="(criterion, idx) in chapter.content?.teachingAssessCriteria"
      :key="idx"
      :disabled="!criterion.students?.length"
    >
      <template #header>
        <div class="flex flex-1 w-full gap-2 items-center justify-between">
          <a-space>
            <div>{{ idx + 1 }}、{{ criterion.name }}</div>
            <target-iep-tag
              v-if="criterion?.iepTargetIds?.length || criterion?.rehabTargetIds?.length"
              :node="criterion"
            />
          </a-space>
          <a-space>
            <target-students-average :target="criterion" :result-data-map="resultDataMap" />
            <a-button
              v-if="criterion.questionIds?.length"
              type="text"
              size="mini"
              @click.stop="() => handleShowTest(criterion)"
            >
              试题
            </a-button>
          </a-space>
        </div>
      </template>
      <div v-if="criterion.students?.length">
        <div v-if="criterion.children?.length" class="flex flex-col gap-2">
          <target-assessment-cmp
            v-for="(child, childIdx) in criterion.children"
            :key="childIdx"
            v-model:result-data-map="resultDataMap"
            :sequence="`${idx + 1}.${childIdx + 1}`"
            :target="child"
            :students="criterion.students"
            :course="course"
            :chapter="chapter"
            :chapters-tree="chaptersTree"
          />
        </div>
        <div v-else>
          <target-assessment-cmp
            v-model:result-data-map="resultDataMap"
            :target="criterion"
            :students="criterion.students"
            :course="course"
            :chapter="chapter"
            :chapters-tree="chaptersTree"
          />
        </div>
      </div>
      <div v-else>
        <a-empty description="此目标未关联学生" />
      </div>
    </a-collapse-item>
  </a-collapse>

  <question-test-modal
    v-model:visible="questTestVisible"
    :criteria="currentCriteria"
    :course="course"
    @update:criteria="handleTestResultUpdated"
  />
</template>

<style lang="scss" scoped>
  :deep {
    .arco-collapse-item-header-title {
      flex: 1;
    }
  }
</style>
