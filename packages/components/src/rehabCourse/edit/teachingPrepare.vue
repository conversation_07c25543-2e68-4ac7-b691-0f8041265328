<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import { Modal } from '@arco-design/web-vue';
  import { EditorOrDisplay } from '@repo/rich-editor';
  import { isObject } from 'lodash';
  import AnnotatableBlock from '../../common/annotatableBlock.vue';
  import { AnnotationItem, AnnotationModuleSource } from '../../utils/annotationBlock';
  import AnnotationSidebar from '../../common/annotatable/annotationSidebar.vue';

  const defaultItems = ['训练准备', '训练重难点', '教材分析'];
  const { prompt } = usePrompt();

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const annotationModuleSource = computed<AnnotationModuleSource>(() => {
    return {
      ...props.annotationSource,
      paragraphId: 'TEACHING_PREPARE',
    };
  });

  const rawAnnotations = ref<any[]>();
  const filteredAnnotations = computed({
    get: () => {
      return rawAnnotations.value?.filter((item: any) => item.paragraphId === 'TEACHING_PREPARE') || [];
    },
    set: (value) => {
      emit('update:annotations', {
        ...rawAnnotations.value?.filter((item: any) => item.paragraphId !== 'TEACHING_PREPARE'),
        ...(value || []),
      });
    },
  });

  const contents = computed({
    get: () => props.chapter?.content?.teachingPrepare || [],
    set: (value) => {
      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          teachingPrepare: value,
        },
      });
    },
  });

  const handleAddItem = async () => {
    const name = await prompt({
      title: '添加训练准备',
      placeholder: '请输入训练准备项目名称',
    });

    contents.value.push({
      name,
      udf1: '',
    });

    emit('save');
  };

  const handleUpdateContentName = async (index, content) => {
    const name = await prompt({
      title: '修改训练准备名称',
      placeholder: '请输入训练准备项目名称',
      raw: content.name,
    });

    contents.value[index].name = name;

    emit('save');
  };

  const handleDeleteContentItem = (index, content) => {
    Modal.confirm({
      title: '请确认',
      content: `确定删除该训练准备项目吗(${content.name})？`,
      onOk: () => {
        contents.value.splice(index, 1);
        emit('save');
      },
    });
  };

  const handleSave = () => {
    emit('save');
  };

  onMounted(() => {
    if (props.chapter?.content?.teachingPrepare) {
      contents.value = props.chapter.content.teachingPrepare || [];
    } else {
      contents.value = defaultItems.map((item) => {
        return {
          name: item,
          udf1: '',
        };
      });
    }
  });

  watch(
    () => props.annotations,
    (newVal) => {
      if (!newVal) {
        rawAnnotations.value = [];
        return;
      }
      rawAnnotations.value = isObject(newVal) ? Object.values(newVal) : newVal;
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <div class="content-wrapper flex gap-2">
    <div class="flex-1">
      <a-space>
        <a-button size="mini" type="outline" @click="handleAddItem">
          <template #icon>
            <IconPlus />
          </template>
          添加训练准备
        </a-button>
      </a-space>
      <div v-for="(c, idx) in contents" :key="idx" class="mt-4">
        <a-space>
          <div class="content-title font-medium text-base"> {{ idx + 1 }}、{{ c.name }} </div>
          <a-space>
            <IconEdit class="cursor-pointer" @click="() => handleUpdateContentName(idx, c)" />
            <IconDelete class="cursor-pointer" @click="() => handleDeleteContentItem(idx, c)" />
          </a-space>
        </a-space>
        <editor-or-display
          v-model="c.udf1"
          class="mt-2"
          :editor-style="{ minHeight: '200px', maxHeight: '200px' }"
          @save="handleSave"
        >
          <template #default="{ raw }">
            <annotatable-block
              v-model:annotations="filteredAnnotations"
              :annotation-module-source="annotationModuleSource"
              :related-modules="['student']"
              :initial-text="raw"
              annotation-category="调整"
            />
          </template>
        </editor-or-display>
      </div>
      <a-empty v-if="!contents.length" description="暂无训练准备，请先添加" />
    </div>
    <annotation-sidebar
      v-model:annotations="filteredAnnotations"
      :annotation-module="annotationModuleSource"
      :related-modules="['student']"
      annotation-category="调整"
      :fixed="false"
    />
  </div>
</template>

<style scoped lang="scss"></style>
