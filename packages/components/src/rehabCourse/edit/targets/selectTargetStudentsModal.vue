<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import StudentSelect from '../../../student/studentSelect.vue';

  const props = defineProps({
    target: {
      type: Object,
      required: true,
    },
    chapter: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:target']);
  const selectedStudents = computed({
    get: () => props.target.students || [],
    set: (value) => {
      emit('update:target', {
        ...props.target,
        students: value,
      });
    },
  });

  const removedStudentIds = ref<number[]>([]);

  const handleStudentChange = (studentId: number, student: any) => {
    if (selectedStudents.value.some((s) => s.id === studentId)) {
      return;
    }
    removedStudentIds.value = removedStudentIds.value.filter((id) => id !== studentId);
    selectedStudents.value = [
      ...selectedStudents.value,
      {
        id: studentId,
        name: student.name,
      },
    ];
  };
  const handleRemove = (studentId: number) => {
    if (studentId) removedStudentIds.value.push(studentId);
    selectedStudents.value = selectedStudents.value.filter((student) => student.id !== studentId);
  };
  const handlePreOk = async () => {
    if (removedStudentIds.value.length > 0)
      await request('/course/rehab-chapter-assess', {
        baseURL: PROJECT_URLS.GO_PROJECT_API,
        method: 'put',
        params: {
          questionId: props.target?.id,
          chapterId: props.chapter?.id,
          studentIds: removedStudentIds.value,
        },
      });
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="选择学生" hide-cancel ok-text="完成" :on-before-ok="handlePreOk">
    <student-select :selected-ids="selectedStudents.map((s) => s.id)" @change="handleStudentChange" />
    <div class="flex flex-wrap gap-2 mt-4">
      <a-tag
        v-for="student in selectedStudents"
        :key="student.id"
        color="arcoblue"
        closable
        @close="() => handleRemove(student.id)"
      >
        {{ student.name }}
      </a-tag>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
