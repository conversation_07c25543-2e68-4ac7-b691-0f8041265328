<script setup lang="ts">
  import { computed, PropType, onMounted, ref } from 'vue';

  const props = defineProps({
    resultDataMap: {
      type: Object as PropType<any>,
      required: true,
    },
    target: {
      type: Object,
      required: true,
    },
    type: {
      type: String as PropType<'pre' | 'after'>,
      default: 'pre',
    },
  });

  const studentsMap = computed(() => {
    const map = {};
    props.target.students?.forEach((s) => {
      map[s.id] = s;
    });
    return map;
  });

  const studentsAvgScoreMap = computed(() => {
    const map = {};
    props.target.students?.forEach((s) => {
      let avg;

      if (props.target.children?.length) {
        avg = props.target.children.reduce((acc, child) => {
          // eslint-disable-next-line no-unsafe-optional-chaining
          return acc + (props.resultDataMap[s.id]?.scoresMap[child.id] || 0);
        }, 0);
        avg /= props.target.children.length;
      } else {
        avg = props.resultDataMap[s.id]?.scoresMap[props.target.id];
      }

      map[s.id] = Math.floor(avg);
    });

    return map;
  });

  const studentsPreAvgScoreMap = computed(() => {
    if (props.type === 'pre') {
      return {};
    }
    const preMap = {};
    props.target.students?.forEach((s) => {
      let preAvg;

      if (props.target?.children?.length) {
        preAvg = props.target.children.reduce((acc, child) => {
          // eslint-disable-next-line no-unsafe-optional-chaining
          const preScoresMap = props.resultDataMap[s.id]?.preScoresMap || {};
          return acc + (preScoresMap[child.id] || 0);
        }, 0);
      } else if (Object.keys(props.resultDataMap).includes(String(s.id))) {
        if (props.resultDataMap[s.id]?.preScoresMap !== undefined)
          preAvg = props.resultDataMap[s.id]?.preScoresMap[props.target.id];
      }
      if (!props.target.children?.length) {
        preMap[s.id] = Math.floor(preAvg || 0);
        return preMap;
      }

      preMap[s.id] = Math.floor(preAvg / props.target.children.length);
    });

    return preMap;
  });

  const exposeText = computed(() => {
    const res: any[] = [];
    Object.keys(studentsAvgScoreMap.value).forEach((key) => {
      let item = `${studentsMap.value[key].name}`;
      if (props.type === 'pre') {
        item += `(${Number.isNaN(studentsAvgScoreMap.value[key]) ? '-' : studentsAvgScoreMap.value[key]})`;
      } else {
        item += ` (前测:${studentsPreAvgScoreMap.value[key]} / 后测:${studentsAvgScoreMap.value[key] || '-'})`;
      }
      res.push(item);
    });

    return res.join(' | ');
  });
</script>

<template>
  <a-popover v-if="Object.keys(studentsAvgScoreMap)?.length" trigger="hover">
    <template #content>
      <div class="flex gap-2 flex-col w-48">
        <div
          v-for="(avg, sid) in studentsAvgScoreMap"
          :key="sid"
          class="flex gap-2 justify-around text-center items-center"
        >
          <small class="w-16">{{ studentsMap[sid].name }}: </small>
          <a-space v-if="type === 'after'">
            <small class="w-16">
              <span>前测 {{ Number.isNaN(studentsPreAvgScoreMap[sid]) ? '-' : studentsPreAvgScoreMap[sid] }} </span>
            </small>
            <small class="w-16">后测 {{ Number.isNaN(avg) ? '-' : avg }}</small>
          </a-space>
          <small v-else class="w-16">
            <span>前测 {{ Number.isNaN(avg) ? '-' : avg }} </span>
          </small>
        </div>
      </div>
    </template>
    <small class="max-w-96 truncate text-slate-400"> 平均分：{{ exposeText }} </small>
  </a-popover>
</template>

<style scoped lang="scss"></style>
