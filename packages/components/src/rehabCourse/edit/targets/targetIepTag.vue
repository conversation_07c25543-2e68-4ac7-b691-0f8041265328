<script setup lang="ts">
  const props = defineProps({
    node: { type: Object },
  });
</script>

<template>
  <a-tooltip content="此目标引入自康复计划">
    <a-tag size="mini" color="green" style="transform: scale(0.8)">
      <span v-if="node.iepTargetIds?.length">康复计划</span>
      <span v-else-if="node.rehabTargetIds?.length">IEP</span>
    </a-tag>
  </a-tooltip>
</template>

<style scoped lang="scss"></style>
