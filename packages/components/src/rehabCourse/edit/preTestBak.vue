<script setup lang="ts">
  import { PropType, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import {
    deleteRehabChapterAssessment,
    editRehabChapterAssessment,
    getRehabChapterAssessmentList,
  } from '@repo/infrastructure/openapi/rehabChapterAssessmentController';
  import { Message, Modal } from '@arco-design/web-vue';
  import { cloneDeep, sum } from 'lodash';
  import GradeClassSelect from '../../student/gradeClassSelect.vue';
  import QuestionTestModal from '../components/questionTestModal.vue';

  const props = defineProps({
    chapter: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    course: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });

  const type = 'pre';
  const { loading: studentLoading, setLoading: setStudentLoading } = useLoading();
  const { loading, setLoading } = useLoading();

  const gradeClass = ref<any>(null);
  const studentsList = ref<Record<string, any>[]>([]);
  const chapterAssessResultMap = ref<Record<number, any>>({});
  const currentStudent = ref<Record<string, any>>({});
  const chapterAssessmentQuery = ref<any>({
    chapterId: props.chapter.id,
    type,
  });
  const currentResult = ref<any[]>([]);
  const resultData = ref<any>({});
  const currentTotalScore = ref(0);
  const currentCriteria = ref<any>({});
  const questTestVisible = ref(false);

  const defaultTargets = [...(props.chapter?.content?.teachingAssessCriteria || [])].map((item) => {
    return {
      ...item,
      score: undefined,
      children: item.children?.map((child) => ({
        ...child,
        score: undefined,
      })),
    };
  });

  const loadStudents = async () => {
    setStudentLoading(true);
    try {
      const { data } = await request('/resourceRoom/student', {
        params: {
          gradeClass: gradeClass.value?.id,
          pageSize: 99,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      studentsList.value = (data.items || []).map((item) => {
        item[`chapterAssessment${type}`] = chapterAssessResultMap.value[item.id] || undefined;
        return item;
      });
    } finally {
      setStudentLoading(false);
    }
  };
  const loadResults = async () => {
    const { data } = await getRehabChapterAssessmentList({
      ...chapterAssessmentQuery.value,
    });

    chapterAssessResultMap.value = data.reduce((acc: Record<number, any>, item: any) => {
      acc[item.studentId] = item;
      return acc;
    }, {});
  };

  const handleGradeChange = async (gradeClassId: number, cls: any) => {
    // if (!gradeClassId) {
    //   Message.error('请选择一个班级');
    //   return;
    // }
    gradeClass.value = cls;
    chapterAssessmentQuery.value.gradeClassId = gradeClassId;

    await loadResults();
    await loadStudents();
  };

  const handleStudentChange = async (student: any) => {
    currentStudent.value = student;
    resultData.value = student[`chapterAssessment${type}`] || {};
    if (resultData.value?.scores?.length) {
      currentResult.value = resultData.value.scores;
    } else {
      currentResult.value = cloneDeep(defaultTargets);
    }
  };

  const handleAutoSave = async () => {
    if (!currentResult.value?.length || !currentStudent.value?.id) {
      return;
    }

    setLoading(true);
    if (!resultData.value?.id) {
      resultData.value = {
        studentId: currentStudent.value?.id,
        chapterId: props.chapter.id,
        gradeClassId: gradeClass.value?.id,
        type,
        scores: currentResult.value,
      };
    } else {
      resultData.value.scores = currentResult.value;
    }
    try {
      const { data } = await editRehabChapterAssessment({
        ...resultData.value,
      });
      resultData.value = data;
      currentStudent.value[`chapterAssessment${type}`] = data;
      currentResult.value = data.scores || [];
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    if (!currentStudent.value?.id || !resultData.value.id) {
      return;
    }
    Modal.confirm({
      title: '请确认',
      content: `确定重置 ${currentStudent.value.name} 的前测目标吗？`,
      onOk: async () => {
        await deleteRehabChapterAssessment({
          id: resultData.value.id,
        });
        Message.success('重置成功');
        resultData.value = {};
        currentResult.value = cloneDeep(defaultTargets);
        currentStudent.value[`chapterAssessment${type}`] = undefined;
      },
    });
  };

  const handleShowTest = (record: any) => {
    currentCriteria.value = record;
    questTestVisible.value = true;
  };

  const handleTestResultUpdated = (criteria: any) => {
    currentResult.value = currentResult.value.map((item) => {
      if (item.id === criteria.id) {
        return criteria;
      }
      if (item.children?.length) {
        item.children = item.children.map((child) => {
          if (child.id === criteria.id) {
            return criteria;
          }
          return child;
        });
      }
      return item;
    });
    currentCriteria.value = null;
    questTestVisible.value = false;
  };

  watch(
    () => currentResult.value,
    () => {
      const allChildren: number[] = [];
      currentResult.value?.forEach((res) => {
        const children: number[] = res?.children?.map((item) => item.score).filter((item) => item !== null) || [];
        allChildren.push(...children);
      });
      currentTotalScore.value = sum(allChildren);
    },
    {
      deep: true,
      immediate: true,
    },
  );

  defineExpose({
    handleAutoSave,
  });
</script>

<template>
  <div class="flex gap-2">
    <div class="w-48 border-r border-r-slate-200 pr-2">
      <grade-class-select class="w-48" @change="handleGradeChange" />
      <a-divider :margin="10" />
      <div class="flex gap-2 text-xs">
        <div class="text-green-500">
          <IconCheck />
          已评测
        </div>
        <div class="text-red-500">
          <IconQuestionCircle />
          未评测
        </div>
      </div>
      <a-divider :margin="10" />
      <a-spin class="mx-1 w-full" :loading="studentLoading">
        <div v-for="(student, idx) in studentsList" :key="idx" class="py-2" @click="() => handleStudentChange(student)">
          <div
            class="flex gap-2 items-center cursor-pointer"
            :class="{ 'text-blue-500': currentStudent?.id === student.id }"
          >
            <div :class="student[`chapterAssessment${type}`] ? 'text-green-500' : 'text-red-500'">
              <IconCheck v-if="student[`chapterAssessment${type}`]" />
              <IconQuestionCircle v-else />
            </div>
            {{ student.name }}
            <small class="text-xs"> {{ student.age }}岁 {{ student.disorders }} </small>
          </div>
        </div>
        <div v-if="!studentsList?.length">
          <a-empty v-if="!gradeClass?.id" description="请先选择一个班级" />
          <a-empty v-else description="所选班级没有学生" />
        </div>
      </a-spin>
    </div>
    <div class="flex-1">
      <a-empty v-if="!currentStudent?.id" description="从左侧选择一个学生开始评测" />
      <div v-else>
        <div class="flex justify-between items-center mx-1">
          <div class="flex gap-2 items-center">
            <span class="text-base">
              {{ currentStudent.name }}
            </span>
            <span class="text-xs"> {{ currentStudent.age }}岁 {{ currentStudent.disorders }} </span>
          </div>
          <a-space class="actions">
            <a-button size="mini"> 前测得分：{{ currentTotalScore || 0 }} </a-button>
            <a-button size="mini" type="primary" :loading="loading" @click="handleAutoSave">
              <template #icon>
                <IconSave />
              </template>
              保存
            </a-button>
            <a-button v-if="resultData?.id" size="mini" type="outline" status="danger" @click="handleReset">
              重置 {{ currentStudent.name }} 的前测
            </a-button>
          </a-space>
        </div>
        <a-divider :margin="10"></a-divider>
        <a-table :data="currentResult" :default-expand-all-rows="true" :pagination="false" size="mini">
          <template #columns>
            <a-table-column data-index="name" title="评测目标" />
            <a-table-column data-index="question" title="试题" :width="80">
              <template #cell="{ record }">
                <a-button
                  v-if="record.questionIds?.length"
                  type="text"
                  size="mini"
                  @click="() => handleShowTest(record)"
                >
                  试题
                </a-button>
              </template>
            </a-table-column>
            <a-table-column data-index="score" title="前测结果">
              <template #cell="{ record }">
                <a-radio-group v-if="!record.children?.length" v-model="record.score" type="button" size="mini">
                  <a-tooltip v-for="(score, idx) in course.teachingAssessScores" :key="idx" :content="score.name">
                    <a-radio :value="score.id">
                      {{ score.id }}
                    </a-radio>
                  </a-tooltip>
                </a-radio-group>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>

    <question-test-modal
      v-model:visible="questTestVisible"
      :criteria="currentCriteria"
      :course="course"
      @update:criteria="handleTestResultUpdated"
    />
  </div>
</template>

<style scoped lang="scss">
  :deep .arco-radio-button.arco-radio-checked {
    background-color: rgb(var(--primary-4));
    color: #fff;
  }
</style>
