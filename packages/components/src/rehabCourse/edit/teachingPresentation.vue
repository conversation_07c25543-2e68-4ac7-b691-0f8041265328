<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import openapi from '@repo/infrastructure/openapi';
  import { useLoading } from '@repo/infrastructure/hooks';
  import Editor from '../../wps/editor.vue';
  import UploadOrNew from '../../wps/uploadOrNew.vue';
  import { PPTPage } from '../../wps/types';
  import PresentationResources from '../components/presentationResources.vue';

  const { loading, setLoading } = useLoading();

  const props = defineProps({
    course: {
      type: Object,
      required: true,
    },
    chapter: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const chapterInfo = ref<any>();
  const officeType = 'Presentation';
  const wpsEditorRef = ref<any>();
  const currentSlide = ref<PPTPage>();

  const loadChapter = async () => {
    const { data } = await openapi.rehabCourse.getRehabChapter({
      id: props.chapter.id!,
    });
    return data;
  };

  const handleSave = async (fileId: string) => {
    setLoading(true);
    await openapi.rehabCourse.updateRehabChapterContent(
      {
        id: props.chapter.id!,
      },
      {
        teachingPresentation: fileId,
      },
    );

    chapterInfo.value = await loadChapter();
    setLoading(false);
  };

  const handlePageSwitch = (page: PPTPage) => {
    currentSlide.value = page;
  };

  const handleEditorReady = async (app: any) => {
    const slideId = await app.ActivePresentation.SlideShowWindow.View.Slide.SlideID;
    const slideIndex = await app.ActivePresentation.SlideShowWindow.View.Slide.SlideIndex;

    handlePageSwitch({
      slideId,
      slideIndex: slideIndex - 1,
      finished: true,
    });

    app.Sub.ActiveSlideChange = async (page: { Data: PPTPage }) => {
      if (!page.Data.finished || page.Data.slideId === currentSlide.value?.slideId) {
        return;
      }
      handlePageSwitch(page.Data);
    };
  };

  const handleChapterContentUpdate = async (updated: any, a: any) => {
    setLoading(true);
    try {
      chapterInfo.value = chapterInfo.value || {};
      chapterInfo.value.content = chapterInfo.value.content || {};
      chapterInfo.value.content.presentationItems = updated.content?.presentationItems;
    } finally {
      setLoading(false);
    }
  };

  onMounted(async () => {
    setLoading(true);
    chapterInfo.value = await loadChapter();
    setLoading(false);
  });
</script>

<template>
  <a-spin :loading="loading" style="width: 100%">
    <div v-if="chapterInfo?.id" class="presentation-wrapper">
      <upload-or-new
        v-if="!chapterInfo.content?.teachingPresentation"
        :default-file-name="`教学课件-${chapterInfo.name}`"
        :model-value="chapterInfo.content?.teachingPresentation!"
        :office-type="officeType"
        @update:model-value="handleSave"
      />
      <div v-else class="editor-wrapper">
        <editor
          :ref="wpsEditorRef"
          :file-id="chapterInfo.content?.teachingPresentation"
          :office-type="officeType"
          class="ppt-editor"
          element-id="teaching-presentation"
          @editor-ready="handleEditorReady"
        />

        <presentation-resources
          :slide="currentSlide"
          :chapter="chapterInfo"
          @update:chapter-content="handleChapterContentUpdate"
        />
      </div>
    </div>
  </a-spin>
</template>

<style lang="less" scoped>
  .presentation-wrapper {
    position: relative;

    > div {
      height: 100%;
    }
  }

  .editor-wrapper {
    .ppt-editor {
      height: calc(100vh - 350px);
      max-height: 650px;
    }
  }

  .resource-wrapper {
    margin-top: 10px;
  }
</style>
