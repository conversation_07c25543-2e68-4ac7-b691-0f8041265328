<script lang="ts" setup>
  import { getOssProcessor, IAttachmentProcessor, AttachmentToUpload } from '@repo/infrastructure/upload';
  // import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { onMounted, PropType, ref } from 'vue';
  import openapi from '@repo/infrastructure/openapi';
  import { Modal } from '@arco-design/web-vue';

  const props = defineProps({
    chapter: {
      type: Object as PropType<any>,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
  });

  const items = ref<any[]>(props.chapter.content?.teachingDemo || []);
  const inProgressItem = ref<Record<string, any>>({});
  const ready = ref<any>(false);

  let processor: IAttachmentProcessor;

  const handleSavedItemsChanged = async () => {
    await openapi.rehabCourse.updateRehabChapterContent(
      {
        id: props.chapter.id!,
      },
      {
        teachingDemo: items.value.map((item: any) => {
          return {
            url: item.url,
            name: item.name,
          };
        }),
      },
    );
  };

  const handleDeleteItem = async (item: any, index: number) => {
    Modal.confirm({
      title: '删除确认',
      okButtonProps: {
        status: 'danger',
      },
      content: `确定删除视频[${item.name}]吗？此操作不可恢复`,
      onOk: async () => {
        items.value.splice(index, 1);
        await handleSavedItemsChanged();
      },
    });
  };

  const handleUpload = async (options: any) => {
    if (!options?.fileItem?.file) return;
    const index = options?.fileItem.uid;
    inProgressItem.value[index] = {
      coverImage: '',
      name: options.fileItem.file?.name,
      progress: 0,
    };

    const attachment: AttachmentToUpload = {
      file: options.fileItem?.file,
      saveType: 'course-sys',
      subFolder: `${props.course.id}/teachingDemo`,
      handleUploadProgress: (progress) => {
        options.onProgress?.(progress);
        inProgressItem.value[index].progress = progress;
      },
      handleUploadComplete: async (url) => {
        options.onSuccess?.(url);
        items.value.push({
          url,
          coverImage: processor.videoCoverUrl(url, 160, 90),
          progress: 1,
          name: options.fileItem?.file?.name,
        });
        delete inProgressItem.value[index];
        await handleSavedItemsChanged();
      },
    };

    await processor.uploadLarge(attachment);
  };

  onMounted(async () => {
    processor = await getOssProcessor();
    ready.value = true;
  });
</script>

<template>
  <div>
    <div v-if="ready" class="uploaded-list">
      <div v-for="(item, index) in items" :key="item.url" class="uploaded-item">
        <a-image :src="processor.videoCoverUrl(item.url, 160, 90)" :preview="false" :width="160" :height="90" />
        <div class="name">{{ item.name }}</div>
        <div class="delete-button" @click="() => handleDeleteItem(item, index)">
          <IconDelete />
        </div>
        <div class="play-button">
          <IconPlayCircleFill />
        </div>
      </div>
      <div v-for="(item, k) in inProgressItem" :key="k" class="uploaded-item">
        <a-image :src="item.coverImage" :preview="false" :width="160" :height="90" />
        <div class="name">{{ item.name }}</div>
        <div class="progress-circle">
          <a-progress
            type="circle"
            :percent="item.progress"
            :stroke-width="8"
            :status="item.progress === 1 ? 'success' : 'normal'"
          />
        </div>
      </div>
    </div>
    <a-upload
      draggable
      multiple
      :show-file-list="false"
      :accept="'video/*'"
      :custom-request="handleUpload as any"
      tip="添加演示视频"
    >
    </a-upload>
  </div>
</template>

<style lang="less" scoped>
  .uploaded-list {
    display: flex;
    flex-wrap: wrap;

    .uploaded-item {
      margin-right: 16px;
      margin-bottom: 16px;
      position: relative;
      cursor: pointer;

      &:hover {
        .delete-button {
          display: block;
        }
      }

      .delete-button {
        position: absolute;
        top: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        font-size: 16px;
        padding: 2px 8px;
        border-radius: 0 0 0 4px;
        z-index: 5;
        display: none;

        &:hover {
          background: #000;
        }
      }

      .play-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 160px;
        height: 90px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, 0.3);
        color: rgba(255, 255, 255, 0.8);
        font-size: 32px;

        &:hover {
          background: rgba(0, 0, 0, 0.5);
          color: rgba(255, 255, 255, 1);
        }
      }

      .progress-circle {
        position: absolute;
        background: rgba(0, 0, 0, 0.3);
        top: 0;
        left: 0;
        width: 160px;
        height: 90px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .name {
        margin-top: 8px;
        text-align: center;
        font-size: 12px;
        width: 160px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>
