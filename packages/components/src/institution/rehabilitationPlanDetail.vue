<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import RehabilitationPlanRecord from './rehabilitationPlanRecord.vue';

  const props = defineProps({
    plan: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    justView: {
      type: Boolean,
      default: false,
    },
  });
  const schema = ref(null);
  const emit = defineEmits(['update:visible']);
  const currentRow = ref(null);
  const recordVisible = ref(false);

  const handleRowAction = (action, row) => {
    if (action.key === 'records') {
      currentRow.value = row;
      recordVisible.value = true;
    }
  };

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const queryParams = {
    sort: '-id',
    rehabilitationPlanId: props.plan.id,
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationPlanDetail');
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" :width="1000" hide-cancel fullscreen title="康复计划明细">
    <table-with-modal-form
      v-if="schema"
      :just-view="justView"
      module-name="康复计划明细"
      :schema="schema"
      :default-query-params="queryParams"
      :default-edit-value="{ rehabilitationPlanId: plan.id }"
      :visible-columns="['domain', 'target', 'content']"
      @row-action="handleRowAction"
    />
    <rehabilitation-plan-record
      v-if="recordVisible"
      v-model:visible="recordVisible"
      :plan="plan"
      :detail="currentRow"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
