<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const props = defineProps({
    plan: {
      type: Object,
      required: true,
    },
    detail: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  });
  const schema = ref(null);
  const emit = defineEmits(['update:visible']);
  const ready = ref(false);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const queryParams = {
    sort: '-id',
    rehabilitationPlanDetailId: props.detail.id,
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationPlanTrainingRecord');
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" simple :width="1000" hide-cancel fullscreen>
    <table-with-modal-form
      v-if="ready && schema"
      module-name="康复训练记录"
      :schema="schema"
      :default-query-params="queryParams"
      :default-edit-value="{ rehabilitationPlanId: plan.id }"
      :visible-columns="['date', 'employee', 'guardian', 'content']"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
