<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { IconSwap, IconClose } from '@arco-design/web-vue/es/icon';
  import { collaborateActionOptions } from '@repo/infrastructure/data/collaborate';
  import useCommonStore from '@repo/infrastructure/utils/store';
  // import SubgroupCollaborator from "./subgroupCollaborator.vue";

  const switchLoading = ref(false);

  const notStudentRelatedModules = ['ResourceRoomEvent', 'TopicResearch', 'AwardRecord'];

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
      required: true,
    },
    record: {
      type: Object,
      default: () => ({}),
    },
    module: {
      type: String,
      required: true,
    },
  });

  const currentSelectTeacher = ref(null);
  const emit = defineEmits(['update:modelValue', 'update:record']);
  const { userInfo } = useUserStore();

  const defaultTeacherQueryParams = {
    branchOfficeId: userInfo.branchOffice.id,
  };

  const selectedCollaborator = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const owner = computed(() => selectedCollaborator.value?.find((item) => item.action === 'Owner') || {});

  const selectedTeacherIds = computed(() => selectedCollaborator.value?.map((item) => item.userId) || []);

  const handleTeacherSelected = (id: number) => {
    if (selectedTeacherIds.value.includes(id)) {
      return;
    }
    selectedCollaborator.value = [
      ...selectedCollaborator.value,
      {
        userId: id,
        recordId: props.record.id,
        action: 'View',
      },
    ];
  };

  const recordData = computed({
    get: () => props.record,
    set: (value) => {
      emit('update:record', value);
    },
  });

  const allTeachers = ref<any[]>([]);
  const teachersMap = ref<any>({});
  const myGradeClass = ref<any>([]);
  const teacherGroup = ref<any>([]);

  const handleRemoveCollaborator = (idx: number) => {
    selectedCollaborator.value.splice(idx, 1);
  };

  const handleSwitchOwner = async (idx: number) => {
    const to = selectedCollaborator.value[idx];
    selectedCollaborator.value = selectedCollaborator.value.map((item) => {
      if (item.action === 'Owner') {
        item.action = 'Edit';
      }
      if (item.userId === to.userId) {
        item.action = 'Owner';
      }
      return item;
    });
  };

  const handleSameClassAutoChange = (value: boolean) => {
    recordData.value.recordCollaborationConfig.enableSameClassAuto = value;
    if (value) {
      recordData.value.recordCollaborationConfig.sameClassAutoAction = 'View';
      recordData.value.recordCollaborationConfig.enableSameOrgAuto = false;
    }
  };

  const handleSameOrgAutoChange = (value: boolean) => {
    recordData.value.recordCollaborationConfig.enableSameOrgAuto = value;
    if (value) {
      recordData.value.recordCollaborationConfig.sameOrgAutoAction = 'View';
      recordData.value.recordCollaborationConfig.enableSameClassAuto = false;
    }
  };
  const handleSameGroupAutoChange = (value: boolean, group: any, index: number) => {
    recordData.value.recordCollaborationConfig.groupsCollaborationConfig[index] = {
      groupId: group.id,
      groupName: group.name,
      enableSameGroupAuto: value,
      sameGroupAutoAction: 'View',
    };
  };
  const handleGroupAutoChange = (value: string, index: number) => {
    recordData.value.recordCollaborationConfig.groupsCollaborationConfig[index].sameGroupAutoAction = value;
  };
  const isMySubgroupStudent = computed((): boolean => {
    /*    return (
      myGradeClass.value?.length > 0 &&
      !myGradeClass.value.some((item: number) => item === props.record?.student?.gradeClass?.id)
    ); */
    return props.record?.student?.clusterIds?.length > 0;
  });

  const loadGroupInfo = async () => {
    const teacherStore = useCommonStore({
      api: `/resourceRoom/subgroup/findTeacherGroup/${props.record?.student?.id || -1}`,
      queryParams: {
        ...defaultTeacherQueryParams,
      },
    });
    teacherGroup.value = await teacherStore.getList();

    if (teacherGroup.value.length) {
      const uniqueTeachers = new Map();
      teacherGroup.value.forEach((item) => {
        if (item?.teacherList && item?.teacherList.length) {
          item?.teacherList.forEach((teacher) => {
            if (!uniqueTeachers.has(teacher.id)) {
              uniqueTeachers.set(teacher.id, { ...teacher, groups: [{ name: item.name, id: item.id }] });
            } else {
              uniqueTeachers.get(teacher.id).groups.push({ name: item.name, id: item.id });
            }
          });
        }
      });
      allTeachers.value = Array.from(uniqueTeachers.values());
      teachersMap.value = allTeachers.value.reduce((acc, item) => {
        acc[item.id] = item;
        return acc;
      }, {});
    }
  };
  // 如果是专委会的专家协作只能选择对应单位的老师
  const isSpacialCommittee = computed(() => {
    return userInfo.branchOffice.school.nature === '特教专委会';
  });

  onMounted(async () => {
    const store = useCommonStore({
      api: '/org/companyUser/allTeachers',
      queryParams: {
        ...defaultTeacherQueryParams,
      },
    });
    const gradeClassStore = useCommonStore({
      api: '/resourceRoom/gradeClass',
      queryParams: {
        ...defaultTeacherQueryParams,
      },
    });

    allTeachers.value = await store.getList();
    teachersMap.value = await store.getMap();
    myGradeClass.value = (await gradeClassStore.getList()).map((item) => item.id);
    await loadGroupInfo();
  });

  const ownerGroupIntersectionStudent = computed(() => {
    const groupsData = teachersMap.value[owner.value.userId] || {};
    const teacherGroups = groupsData.groups || []; // 确保 `groups` 存在
    const collaborationConfig = recordData.value.recordCollaborationConfig.groupsCollaborationConfig;

    if (teacherGroups.length > collaborationConfig.length) {
      const groupIds = collaborationConfig.map((item) => item.groupId);
      const filterGroups = teacherGroups.filter((item: any) => !groupIds.includes(item.groupId));

      filterGroups.forEach((item: any) => {
        collaborationConfig.push({
          groupId: item.id,
          groupName: item.name,
          enableSameGroupAuto: false,
          sameGroupAutoAction: 'View',
        });
      });
    }

    if (props.record?.student?.clusterIds?.length) {
      return teacherGroups.filter((item) => props.record?.student?.clusterIds?.includes(item.id));
    }
    return teacherGroups;
  });
</script>

<template>
  <!--非分组学生-->
  <div>
    <a-space v-if="userInfo.id === owner.userId" class="items-center w-full">
      <div>分享教师</div>
      <a-select v-model="currentSelectTeacher" allow-search clss="flex-1" size="mini">
        <a-option
          v-for="teacher in allTeachers"
          :key="teacher.id"
          :value="teacher.id"
          :label="teacher.name"
          :disabled="selectedTeacherIds.includes(teacher.id)"
          @click="() => handleTeacherSelected(teacher.id)"
        />
      </a-select>
    </a-space>
    <a-divider v-if="userInfo.id === owner.userId" :margin="10" />
    <div
      v-for="(collaborator, idx) in selectedCollaborator"
      v-show="collaborator.userSet !== false"
      :key="collaborator.id"
      class="flex mt-2 gap-2 items-center"
    >
      <a-popover
        v-if="notStudentRelatedModules.indexOf(module) < 0 && isMySubgroupStudent"
        :content="teachersMap[collaborator.userId]?.groups?.map((item) => item.name).join(', ')"
      >
        <div class="text-sm">
          {{ teachersMap[collaborator.userId]?.name ?? collaborator?.name }}
        </div>
      </a-popover>
      <div v-else class="w-20 text-sm"> {{ teachersMap[collaborator.userId]?.name }}</div>

      <a-space v-if="userInfo.id === owner.userId" class="flex-1 flex items-center gap-2">
        <a-select
          v-if="collaborator.action !== 'Owner'"
          v-model="collaborator.action"
          size="mini"
          :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
        />
        <a-tag v-else color="green">数据所有者</a-tag>

        <a-popconfirm
          v-if="collaborator.action !== 'Owner'"
          content="确定要取消这个老师的协作权限吗？"
          @ok="() => handleRemoveCollaborator(idx)"
        >
          <a-button size="mini">
            <template #icon>
              <IconClose />
            </template>
            取消分享</a-button
          >
        </a-popconfirm>

        <a-popconfirm
          v-if="collaborator.action !== 'Owner'"
          :content="`确定要将此数据所有权转移给 ${teachersMap[collaborator.userId]?.name} 吗？`"
          @ok="() => handleSwitchOwner(idx)"
        >
          <a-button size="mini" :loading="switchLoading">
            <template #icon>
              <IconSwap />
            </template>
            转移给 Ta</a-button
          >
        </a-popconfirm>
      </a-space>
      <a-space v-else>
        <a-tag v-if="collaborator.action === 'Owner'" color="green">数据所有者</a-tag>
        <a-tag v-else-if="collaborator.action === 'Edit'" color="blue">可修改</a-tag>
        <a-tag v-else-if="collaborator.action === 'View'" color="gray">仅查看</a-tag>
      </a-space>
    </div>
    <div v-if="notStudentRelatedModules.indexOf(module) < 0" class="flex flex-col gap-2">
      <!--专委会单独选择老师就行了-->
      <div v-if="!isSpacialCommittee">
        <a-divider :margin="10" />
        <a-space>
          <a-switch
            v-model="recordData.recordCollaborationConfig.enableSameClassAuto"
            size="small"
            type="round"
            @change="handleSameClassAutoChange"
          />
          学生所在班级老师自动加入分享
          <a-select
            v-model="recordData.recordCollaborationConfig.sameClassAutoAction"
            size="mini"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
          />
        </a-space>
        <a-space>
          <a-switch
            v-model="recordData.recordCollaborationConfig.enableSameOrgAuto"
            size="small"
            type="round"
            @change="handleSameOrgAutoChange"
          />
          学生所在学校老师自动加入分享
          <a-select
            v-model="recordData.recordCollaborationConfig.sameOrgAutoAction"
            size="mini"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
          />
        </a-space>
      </div>
      <div v-if="isMySubgroupStudent" class="flex flex-col gap-2">
        <!--设置教师学生共有的分组协作权限-->
        <a-divider :margin="10" />
        <a-space v-for="(group, index) in ownerGroupIntersectionStudent" :key="index">
          <a-switch
            v-model="recordData.recordCollaborationConfig.groupsCollaborationConfig[index].enableSameGroupAuto"
            size="small"
            type="round"
            @change="(value: boolean) => handleSameGroupAutoChange(value, group, index)"
          />
          学生所在分组老师自动加入分享
          <!--v-model="recordData.recordCollaborationConfig.groupsCollaborationConfig[index].sameGroupAutoAction"-->
          <a-select
            v-model="recordData.recordCollaborationConfig.groupsCollaborationConfig[index].sameGroupAutoAction"
            size="mini"
            style="width: 90px"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
            @change="(value) => handleGroupAutoChange(value, index)"
          />
          <span>{{ group.name }}</span>
        </a-space>
      </div>
    </div>
    <a-divider :margin="10" />
    <div class="text-sm text-gray-400"> * 调整后，请点击确定后保存生效 </div>
  </div>
</template>

<style scoped lang="scss"></style>
