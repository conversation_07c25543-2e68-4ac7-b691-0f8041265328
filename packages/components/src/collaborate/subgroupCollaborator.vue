<!--
<script setup lang="ts">
import { computed, onMounted, PropType, ref } from 'vue';
import { useUserStore } from '@repo/infrastructure/store';
import { IconSwap, IconClose } from '@arco-design/web-vue/es/icon';
import { collaborateActionOptions } from '@repo/infrastructure/data/collaborate';
import useCommonStore from '@repo/infrastructure/utils/store';

const switchLoading = ref(false);

const notStudentRelatedModules = ['ResourceRoomEvent', 'TopicResearch', 'AwardRecord'];

const props = defineProps({
  modelValue: {
    type: Array as PropType<any[]>,
    required: true,
  },
  record: {
    type: Object,
    default: () => ({}),
  },
  module: {
    type: String,
    required: true,
  },
});

const currentSelectTeacher = ref(null);
const emit = defineEmits(['update:modelValue', 'update:record']);
const { userInfo } = useUserStore();

const defaultTeacherQueryParams = {
  branchOfficeId: userInfo.branchOffice.id,
};

const selectedCollaborator = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
  },
});

const owner = computed(() => selectedCollaborator.value?.find((item) => item.action === 'Owner') || {});

const selectedTeacherIds = computed(() => selectedCollaborator.value?.map((item) => item.userId) || []);

const handleTeacherSelected = (id: number) => {
  if (selectedTeacherIds.value.includes(id)) {
    return;
  }
  selectedCollaborator.value = [
    ...selectedCollaborator.value,
    {
      userId: id,
      recordId: props.record.id,
      action: 'View',
    },
  ];
};

const recordData = computed({
  get: () => props.record,
  set: (value) => {
    emit('update:record', value);
  },
});

const allTeachers = ref<any[]>([]);
const teachersMap = ref<any>({});
const myGradeClass = ref<any>([])

const handleRemoveCollaborator = (idx: number) => {
  selectedCollaborator.value.splice(idx, 1);
};

const handleSwitchOwner = async (idx: number) => {
  const to = selectedCollaborator.value[idx];
  selectedCollaborator.value = selectedCollaborator.value.map((item) => {
    if (item.action === 'Owner') {
      item.action = 'Edit';
    }
    if (item.userId === to.userId) {
      item.action = 'Owner';
    }
    return item;
  });
};

const handleSameClassAutoChange = (value: boolean) => {
  recordData.value.recordCollaborationConfig.enableSameClassAuto = value;
  if (value) {
    recordData.value.recordCollaborationConfig.sameClassAutoAction = 'View';
    recordData.value.recordCollaborationConfig.enableSameOrgAuto = false;
  }
};

const handleSameOrgAutoChange = (value: boolean) => {
  recordData.value.recordCollaborationConfig.enableSameOrgAuto = value;
  if (value) {
    recordData.value.recordCollaborationConfig.sameOrgAutoAction = 'View';
    recordData.value.recordCollaborationConfig.enableSameClassAuto = false;
  }
};
const isSubgroupStudent = computed((): boolean => {
  return (
      myGradeClass.value?.length > 0 &&
      !myGradeClass.value.some((item:number) => item === props.record?.student?.gradeClass?.id)
  );
});

onMounted(async () => {
  const store = useCommonStore({
    api: '/org/companyUser/allTeachers',
    queryParams: {
      ...defaultTeacherQueryParams,
    },
  });
  const gradeClassStore = useCommonStore({
    api: '/resourceRoom/gradeClass',
    queryParams: {
      ...defaultTeacherQueryParams,
    },
  });
  allTeachers.value = await store.getList();
  teachersMap.value = await store.getMap();
  myGradeClass.value = (await gradeClassStore.getList()).map(item=>item.id);
});
</script>

<template>
  &lt;!&ndash;非分组学员&ndash;&gt;
    <a-space v-if="userInfo.id === owner.userId" class="items-center w-full">
      <div>协作教师</div>
      <a-select v-model="currentSelectTeacher" allow-search clss="flex-1" size="mini">
        <a-option
            v-for="teacher in allTeachers"
            :key="teacher.id"
            :value="teacher.id"
            :label="teacher.name"
            :disabled="selectedTeacherIds.includes(teacher.id)"
            @click="() => handleTeacherSelected(teacher.id)"
        />
      </a-select>
    </a-space>
    <a-divider v-if="userInfo.id === owner.userId" :margin="10" />
    <div
        v-for="(collaborator, idx) in selectedCollaborator"
        v-show="collaborator.userSet !== false"
        :key="collaborator.id"
        class="flex mt-2 gap-2 items-center"
    >
      <div class="w-20 text-sm"> {{ teachersMap[collaborator.userId]?.name }}</div>
      <a-space v-if="userInfo.id === owner.userId" class="flex-1 flex items-center gap-2">
        <a-select
            v-if="collaborator.action !== 'Owner'"
            v-model="collaborator.action"
            size="mini"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
        />
        <a-tag v-else color="green">数据所有者</a-tag>

        <a-popconfirm
            v-if="collaborator.action !== 'Owner'"
            content="确定要取消这个老师的协作权限吗？"
            @ok="() => handleRemoveCollaborator(idx)"
        >
          <a-button size="mini">
            <template #icon>
              <IconClose />
            </template>
            取消协作</a-button
          >
        </a-popconfirm>

        <a-popconfirm
            v-if="collaborator.action !== 'Owner'"
            :content="`确定要将此数据所有权转移给 ${teachersMap[collaborator.userId]?.name} 吗？`"
            @ok="() => handleSwitchOwner(idx)"
        >
          <a-button size="mini" :loading="switchLoading">
            <template #icon>
              <IconSwap />
            </template>
            转移给 Ta</a-button
          >
        </a-popconfirm>
      </a-space>
      <a-space v-else>
        <a-tag v-if="collaborator.action === 'Owner'" color="green">数据所有者</a-tag>
        <a-tag v-else-if="collaborator.action === 'Edit'" color="blue">可修改</a-tag>
        <a-tag v-else-if="collaborator.action === 'View'" color="gray">仅查看</a-tag>
      </a-space>
    </div>
    <div v-if="notStudentRelatedModules.indexOf(module) < 0" class="flex flex-col gap-2">
      <a-divider :margin="10" />
      <a-space>
        <a-switch
            v-model="recordData.recordCollaborationConfig?.enableSameClassAuto"
            size="small"
            type="round"
            @change="handleSameClassAutoChange"
        />
        学生所在班级老师自动加入协作
        <a-select
            v-model="recordData.recordCollaborationConfig.sameClassAutoAction"
            size="mini"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
        />
      </a-space>
      <a-space>
        <a-switch
            v-model="recordData.recordCollaborationConfig.enableSameOrgAuto"
            size="small"
            type="round"
            @change="handleSameOrgAutoChange"
        />
        学生所在学校老师自动加入协作
        <a-select
            v-model="recordData.recordCollaborationConfig.sameOrgAutoAction"
            size="mini"
            :options="collaborateActionOptions.filter((item) => item.value !== 'Owner')"
        />
      </a-space>
    </div>
    <a-divider :margin="10" />
    <div class="text-sm text-gray-400"> * 调整后，请点击确定后保存生效 </div>
</template>

<style scoped lang="scss"></style>
-->
