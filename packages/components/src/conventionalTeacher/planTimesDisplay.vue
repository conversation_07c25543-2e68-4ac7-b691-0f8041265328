<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    raw: {
      type: String,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
  });
  const raw = ref<any>(props.raw);
  const orders = ref();
  const loadOrderInfo = async () => {
    const { data: res } = await request(`/resourceCenter/normalTourGuideOrder/findByGuidPlanId/${props.record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    orders.value = res.items;
  };
  const getPeriod = (timeRange: number) => {
    switch (timeRange) {
      case -1:
        return '全天';
      case 1:
        return '上午';
      case 2:
        return '下午';
    }
  };
  const getOrdersInfo = (record: any) => {
    const orderList = orders.value?.filter((item) => item.planGuideDate === record.planGuideDate);
    if (orderList?.length > 0) {
      return orderList
        ?.map((item: any) => {
          return `${item?.operator.name}(${getPeriod(item.timeRange)})`;
        })
        .join(', ');
    }
    return '-';
  };
  onMounted(async () => {
    await loadOrderInfo();
    raw.value?.sort((a, b) => {
      if (a.planGuideDate < b.planGuideDate) {
        return 1;
      }
      if (a.planGuideDate > b.planGuideDate) {
        return -1;
      }
      return 0;
    });
  });
</script>

<template>
  <a-table :data="raw">
    <template #columns>
      <a-table-column data-index="planGuideDate" title="时间"></a-table-column>
      <a-table-column data-index="timeRange" title="时间段">
        <template #cell="{ record }">
          <span>{{ getPeriod(record.timeRange) }}</span>
        </template>
      </a-table-column>
      <a-table-column data-index="teacher" title="预约教师">
        <template #cell="{ record }">
          <span>{{ getOrdersInfo(record) }}</span>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
