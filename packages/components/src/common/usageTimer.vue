<template>
  <slot :total-time="totalTime" :is-active="isActive" :total-time-display="totalTimeDisplay">
    <a-tag :color="tagColor" size="small"> 已用时：{{ totalTimeDisplay }} </a-tag>
  </slot>
</template>

<script setup lang="ts">
  import { onMounted, onBeforeUnmount, computed, ref } from 'vue';
  import { onBeforeRouteLeave } from 'vue-router';
  import { secondsFormatter } from '@repo/ui/components/utils/utils';

  const props = defineProps({
    modelValue: {
      type: Number,
      default: 0,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const totalTime = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  }); // 总的阅读时长（秒）
  const totalTimeDisplay = computed(() => {
    return secondsFormatter(totalTime.value);
  }); // 格式化后的阅读时长 HH:mm:ss
  const isActive = ref(true); // 用户是否活跃
  const isFocus = ref(true); // 页面是否聚焦
  let idleTimeout = null; // 记录闲置时间的定时器
  let interval = null; // 计时器，用来每秒更新阅读时长
  const idleLimit = 60 * 1000; // 用户无操作的闲置时间限制（1分钟）

  const tagColor = computed(() => {
    if (!isActive.value || !isFocus.value || document.visibilityState !== 'visible') {
      return 'red';
    }
    return '#165dff';
  }); // 根据阅读时长设置标签颜色

  // 更新阅读时长，每秒调用一次
  const updateReadingTime = () => {
    if (isFocus.value && isActive.value && document.visibilityState === 'visible') {
      totalTime.value += 1;
    }
  };

  // 重置用户活动状态和闲置计时器
  const resetIdleTimer = () => {
    clearTimeout(idleTimeout);
    if (!isActive.value && document.visibilityState === 'visible') {
      isActive.value = true;
    }
    idleTimeout = setTimeout(() => {
      isActive.value = false; // 标记用户为不活跃
    }, idleLimit);
  };

  // 初始化用户活动监听器
  const setupActivityListeners = () => {
    document.addEventListener('mousemove', resetIdleTimer); // 鼠标移动
    document.addEventListener('keydown', resetIdleTimer); // 键盘按下
    document.addEventListener('click', resetIdleTimer); // 鼠标点击
  };

  // 移除监听器
  const removeActivityListeners = () => {
    document.removeEventListener('mousemove', resetIdleTimer);
    document.removeEventListener('keydown', resetIdleTimer);
    document.removeEventListener('click', resetIdleTimer);
  };

  // 初始化时设置计时器并开始计算时间
  onMounted(() => {
    setupActivityListeners(); // 监听用户活动

    interval = setInterval(updateReadingTime, 1000); // 每秒更新阅读时长

    // 监听页面可见性
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        isActive.value = false; // 页面不可见时暂停计时
      } else if (document.visibilityState === 'visible') {
        isActive.value = true; // 页面可见时重新开始计时
      }
    });

    // blur
    window.addEventListener('blur', () => {
      isFocus.value = false; // 页面不可见时暂停计时
    });

    // focus
    window.addEventListener('focus', () => {
      isFocus.value = true; // 页面可见时重新开始计时
    });
  });

  // 离开路由前停止计时并清除所有事件
  onBeforeRouteLeave(() => {
    clearInterval(interval); // 清除计时器
  });

  // 页面卸载时清除监听器和计时器
  onBeforeUnmount(() => {
    clearInterval(interval); // 清除计时器
    clearTimeout(idleTimeout); // 清除闲置计时器
    removeActivityListeners(); // 移除用户活动监听
  });
</script>
