<script setup lang="ts">
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import { setToken } from '@repo/infrastructure/auth';
  import { Message } from '@arco-design/web-vue';

  const accounts = ref<any[]>([]);

  const userStore = useUserStore();
  const toUserId = ref<number>();

  const handleOpen = async () => {
    const { data } = await request('/org/companyUser/myAvatars', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    const { userInfo } = userStore;
    accounts.value = data;
    toUserId.value = userInfo.id;
  };

  const handleSwitch = async () => {
    Message.info('正在切换账户，请稍后...');

    const { data } = await request(`/org/companyUser/switchTo/${toUserId.value}`, {
      method: 'PUT',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    if (data) {
      setToken(data);
      window.location.href = `${PROJECT_URLS.MAIN_PROJECT}?token=${data}`;
    }
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="帐号切换" simple :footer="false" @open="handleOpen">
    <div class="flex gap-2 items-center w-full flex-1">
      <a-select v-model="toUserId" class="flex-1">
        <a-option
          v-for="account in accounts"
          :key="account.id"
          :label="`${account.name} [${account.udf1}]`"
          :value="account.id"
        >
          <div class="leading-8">
            <div>{{ account.name }} [{{ account.udf1 }}]</div>
            <small>{{ account.udf2 }} </small>
          </div>
        </a-option>
      </a-select>
      <a-popconfirm content="确定要切换到此账户吗？" @ok="handleSwitch">
        <a-button type="primary" :disabled="toUserId === userStore.userInfo.id">
          <template #icon>
            <IconSwap />
          </template>
          切换
        </a-button>
      </a-popconfirm>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
