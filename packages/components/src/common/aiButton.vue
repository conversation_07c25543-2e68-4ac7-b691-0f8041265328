<script setup lang="ts">
  import { IconLoading } from '@arco-design/web-vue/es/icon';

  const props = defineProps({
    tooltip: {
      type: String,
      default: 'AI 分析',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: 'top',
    },
  });

  const emits = defineEmits(['ok']);

  const handleClick = () => {
    if (props.disabled || props.loading) {
      return;
    }
    emits('ok');
  };
</script>

<template>
  <a-tooltip :content="tooltip" :position="position">
    <button class="ai-btn px-2 py-1.5 ml-4 hover:shadow" v-bind="$attrs" :disabled="disabled" @click="handleClick">
      <div class="btn-content text-xs flex justify-center items-center">
        <div class="ai-icon">
          <IconLoading v-if="loading" />
          <!-- 神经网络核心图标 -->
          <svg v-else viewBox="0 0 24 24" class="core">
            <path d="M12 2L4 8V16L12 22L20 16V8L12 2Z" />
            <path d="M12 12L4 8" />
            <path d="M12 12L20 8" />
            <path d="M12 12L4 16" />
            <path d="M12 12L20 16" />
            <circle cx="12" cy="12" r="3" />
          </svg>

          <!-- 脉冲效果 -->
          <div class="pulse"></div>

          <!-- 动态节点 -->
          <div class="ai-style-nodes">
            <div class="node" style="top: 10%; left: 30%; animation: nodeBlink 1.5s infinite 0.1s"></div>
            <div class="node" style="top: 20%; left: 70%; animation: nodeBlink 1.8s infinite 0.3s"></div>
            <div class="node" style="top: 70%; left: 20%; animation: nodeBlink 2.1s infinite 0.5s"></div>
            <div class="node" style="top: 80%; left: 60%; animation: nodeBlink 1.7s infinite 0.2s"></div>
          </div>
        </div>
        <slot></slot>
      </div>

      <slot name="customize"> </slot>

      <div id="extraAction" class="absolute -right-5 top-0 h-full">
        <slot name="extraAction"> </slot>
      </div>
    </button>
  </a-tooltip>
</template>

<style scoped lang="scss">
  .ai-btn {
    position: relative;
    color: white;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    //overflow: hidden;
    z-index: 1;
    transform-style: preserve-3d;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    //box-shadow:
    //  0 0 15px rgba(59, 130, 246, 0.6),
    //  0 0 30px rgba(139, 92, 246, 0.4);
  }

  /* 悬浮效果 */
  .ai-btn:hover {
    transform: scale(1.02) rotateX(10deg);
    //box-shadow:
    //  0 0 25px rgba(59, 130, 246, 0.8),
    //  0 0 40px rgba(139, 92, 246, 0.6),
    //  0 10px 30px rgba(0, 0, 0, 0.3);
  }

  /* 光效层 */
  .ai-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);
    transform: rotate(30deg);
    z-index: -1;
    transition: all 0.6s ease;
  }

  .ai-btn:hover::before {
    animation: shine 2s infinite;
  }

  /* 数据粒子效果 */
  .ai-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    //background: radial-gradient(circle at 20% 30%, rgba(100, 200, 255, 0.8) 0%, transparent 50%),
    //  radial-gradient(circle at 80% 70%, rgba(255, 100, 200, 0.8) 0%, transparent 50%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
  }

  .ai-btn:hover::after {
    opacity: 0.6;
    animation:
      particles 3s infinite linear,
      dataFlow 4s infinite linear;
  }

  /* 按钮内容 */
  .btn-content {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  /* AI 专用图标 */
  .ai-icon {
    width: 18px;
    height: 18px;
    position: relative;
  }

  .ai-icon .core {
    fill: none;
    stroke: white;
    stroke-width: 2;
    stroke-dasharray: 30;
    stroke-dashoffset: 30;
    animation: draw 1.5s infinite 0.3s;
  }

  .ai-icon .pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(0);
    animation: pulse 2s infinite;
  }

  .ai-icon .nodes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  .ai-icon .node {
    position: absolute;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    opacity: 0;
  }

  /* 禁用状态样式 */
  .ai-btn:disabled {
    /* 视觉降级 */
    opacity: 0.7;
    background: linear-gradient(45deg, #64748b, #94a3b8, #cbd5e1);

    /* 禁用交互效果 */
    cursor: not-allowed;
    transform: none !important;
    box-shadow:
      0 0 10px rgba(100, 116, 139, 0.4),
      0 0 0 1px inset rgba(255, 255, 255, 0.1) !important;

    /* 禁用所有动画 */
    animation: none !important;
  }

  /* 禁用状态下的图标变化 */
  .ai-btn:disabled .ai-icon .core {
    stroke: #e2e8f0;
    stroke-dasharray: none;
    animation: none;
  }

  .ai-btn:disabled .ai-icon .pulse,
  .ai-btn:disabled .ai-icon .node {
    display: none;
  }

  /* 添加禁用状态提示符号 */
  .ai-btn:disabled .btn-content::after {
    content: '🚫';
    position: absolute;
    right: -25px;
    top: 50%;
    transform: translateY(-50%);
    filter: grayscale(0.8);
  }

  /* 文字颜色变灰 */
  .ai-btn:disabled span {
    color: #e2e8f0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  /* 动画定义 */
  /*  @keyframes shine {
    0% {
      transform: rotate(30deg) translate(-10%, -10%);
    }
    100% {
      transform: rotate(30deg) translate(10%, 10%);
    }
  }*/

  //@keyframes particles {
  //  0% {
  //    transform: scale(1);
  //    opacity: 0.6;
  //  }
  //  50% {
  //    transform: scale(1.1);
  //    opacity: 0.3;
  //  }
  //  100% {
  //    transform: scale(1);
  //    opacity: 0.6;
  //  }
  //}

  //@keyframes dataFlow {
  //  0% {
  //    background-position:
  //      0% 0%,
  //      100% 100%;
  //  }
  //  100% {
  //    background-position:
  //      100% 100%,
  //      0% 0%;
  //  }
  //}

  @keyframes draw {
    to {
      stroke-dashoffset: 0;
    }
  }

  //@keyframes pulse {
  //  0% {
  //    transform: scale(0);
  //    opacity: 1;
  //  }
  //  70% {
  //    transform: scale(1.3);
  //    opacity: 0;
  //  }
  //  100% {
  //    transform: scale(1.3);
  //    opacity: 0;
  //  }
  //}

  //@keyframes nodeBlink {
  //  0%,
  //  100% {
  //    opacity: 0.2;
  //    transform: scale(0.8);
  //  }
  //  50% {
  //    opacity: 1;
  //    transform: scale(1.2);
  //  }
  //}

  .ai-processor {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  }

  .console-output {
    background: rgba(#f9f9f9, 0.89);
    padding: 1rem;
    border-radius: 0.25rem;
    color: #e2e8f0;
    overflow: hidden;
  }

  .message {
    margin: 0.5rem 0;
    opacity: 0.7;
    transition: all 0.3s ease;
    line-height: 1.6;
  }

  .message.active {
    opacity: 1;
    font-weight: bold;
    color: #38bdf8;
  }

  .message.completed {
    color: #4ade80;
  }

  .prefix {
    color: #94a3b8;
    margin-right: 0.2rem;
  }

  .ellipsis {
    color: #cbd5e1;
  }

  .completed-mark {
    color: #4ade80;
    margin-left: 0.25rem;
  }

  /* 过渡动画 */
  .message-enter-active,
  .message-leave-active {
    transition: all 0.3s ease;
  }

  .message-enter-from,
  .message-leave-to {
    opacity: 0;
    transform: translateX(10px);
  }
</style>
