<script lang="ts" setup>
  import Highlighter from 'web-highlighter';
  import { computed, nextTick, onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { debouncedWatch } from '@vueuse/core';
  import { AnnotationItem, AnnotationModuleSource, HighlightedSection } from '../utils/annotationBlock';
  import AnnotationModal from './annotatable/annotationModal.vue';

  const props = defineProps({
    initialText: {
      type: String,
      required: true,
    },
    relatedModules: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    annotationModuleSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotationCategory: {
      type: String,
      default: '想法',
    },
    annotations: {
      type: Array as PropType<AnnotationItem[]>,
      default: () => [],
    },
    /**
     * 用于 annotatable block 高亮显示，但不同位置创建的 在其他位置时不能准确定位，需要微调 startMeta 和 endMeta 的 parentId
     */
    sectionParentIdPlus: {
      type: Number,
      default: 1,
    },
  });

  const raw = ref(props.initialText);
  const emit = defineEmits(['update:annotations']);

  const annotationItems = computed<AnnotationItem[]>({
    get: () => props.annotations || [],
    set: (value) => emit('update:annotations', value),
  });

  const highlighter = new Highlighter({
    exceptSelectors: ['.ignore-selection', 'pre', 'code'],
    style: {
      className: 'highlight-wrap-text',
    },
  });
  highlighter.stop();

  const createTag = (top, left, id) => {
    const $span = document.createElement('span');
    $span.style.left = `${left - 5}px`;
    $span.style.top = `${top - 10}px`;
    $span.dataset.id = id;
    $span.textContent = 'delete';
    $span.classList.add('my-remove-tip');
    document.body.appendChild($span);
  };

  function getPosition($node) {
    const offset = {
      top: 0,
      left: 0,
    };
    while ($node) {
      offset.top += $node.offsetTop;
      offset.left += $node.offsetLeft;
      $node = $node.offsetParent;
    }

    return offset;
  }

  const init = () => {
    highlighter
      .on('selection:hover', ({ id }) => {
        // 通过添加 class，实现类似 hover 效果
        highlighter.addClass('highlight-wrap-hover', id);
      })
      .on('selection:hover-out', ({ id }) => {
        // 鼠标离开时清除悬停样式
        highlighter.removeClass('highlight-wrap-hover', id);
      });
    // .on('selection:create', ({ sources }) => {
    //   sources.forEach((s) => {
    //     const position = getPosition(highlighter.getDoms(s.id)[0]);
    //     createTag(position.top, position.left, s.id);
    //   });
    // });

    // highlighter.run();
  };

  const createButtonVisible = ref(false);
  const createButtonStyle = ref({
    top: '0',
    left: '0',
  });

  const findRelativeParent = (element) => {
    while (element && element !== document.body) {
      const style = window.getComputedStyle(element);
      if (['relative', 'absolute', 'fixed'].includes(style.position)) {
        return element; // 找到最近的定位父元素
      }
      element = element.parentElement; // 继续向上查找
    }
    return document.body; // 如果没有找到，默认为 body
  };

  const handleShowButton = () => {
    const selection = window.getSelection(); // 获取选中的文本
    if (!selection) {
      createButtonVisible.value = false;
      return;
    }
    if (!selection.isCollapsed) {
      const range = selection.getRangeAt(0); // 获取选中的范围
      const rect = range.getBoundingClientRect(); // 获取选区的矩形信息

      // 找到最近的 relative 父元素
      const relativeElement = findRelativeParent(
        range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer
          : range.commonAncestorContainer.parentElement,
      );

      const relativeRect = relativeElement.getBoundingClientRect(); // 获取父元素的位置

      // 计算相对于 relative 元素的位置
      const top = rect.top - relativeRect.top;
      const left = rect.left - relativeRect.left;

      // 设置按钮的位置
      createButtonStyle.value = {
        top: `${top - 45}px`, // 距离选区上方一定高度
        left: `${left + rect.width / 2 - 20}px`, // 水平居中
      };

      createButtonVisible.value = true;
    } else {
      createButtonVisible.value = false;
    }
  };

  const handleNewAnnotationCreated = async (selection, callback) => {
    if (!selection) {
      return;
    }
    const source = highlighter.fromRange(selection);
    createButtonVisible.value = false;

    if (typeof callback === 'function') {
      const newItem = await callback(source);
      annotationItems.value = [...annotationItems.value, newItem];
    }
  };

  onMounted(async () => {
    // highlighter.stop();
    init();
  });

  const tryGetDomList = (tryNumber: number, section: HighlightedSection) => {
    section = {
      ...section,
      startMeta: {
        ...section.startMeta,
        parentIndex: section.startMeta.parentIndex + tryNumber,
      },
      endMeta: {
        ...section.endMeta,
        parentIndex: section.endMeta.parentIndex + tryNumber,
      },
    };

    const source = highlighter.fromStore(section.startMeta, section.endMeta, section.text, section.id);
    const { id, endMeta, startMeta } = source || {};

    // 获取当前高亮的 DOM 元素
    const domList = highlighter.getDoms(id).map((item) => {
      return item.parentElement;
    });

    return {
      domList,
      source,
      id,
      startMeta,
      endMeta,
    };
  };

  debouncedWatch(
    () => props.annotations,
    async (newVal) => {
      const unavailableSections: string[] = [];

      // highlighter.removeAll();
      newVal.map(async ({ section }: AnnotationItem) => {
        const source = highlighter.fromStore(section.startMeta, section.endMeta, section.text, section.id);
        const { id, endMeta, startMeta } = source;

        // 获取当前高亮的 DOM 元素
        const domList = highlighter.getDoms(id).map((item) => {
          console.log(item);
          return item.parentElement;
        });

        if (!domList.length) {
          return;
          // for (let i = -2; i < 3; i += 1) {
          //   const tryResult = tryGetDomList(i, section);
          //   if (tryResult.domList?.length) {
          //     id = tryResult.id;
          //     startMeta = tryResult.startMeta;
          //     endMeta = tryResult.endMeta;
          //     domList = tryResult.domList;
          //     source = tryResult.source;
          //     break;
          //   }
          // }
          // console.log(id, section.text, startMeta, endMeta, domList, newVal);
        }

        if (!domList.length || !id || !startMeta || !endMeta) {
          // 如果高亮区域已经不存在，直接判定为不可用
          // unavailableSections.push(id);
          return;
        }

        const startLineElement = domList[0];
        const endLineElement = domList[endMeta.parentIndex - startMeta.parentIndex];

        if (!startLineElement || !endLineElement) {
          unavailableSections.push(id);
          return;
        }

        const startLineLength = startLineElement.textContent?.length || 0;
        const endLineLength = endLineElement.textContent?.length || 0;

        // 如果开始行和结束行一致，直接判断dom中这一行 是否存在  endMeta.textOffset长度的文本
        if (startMeta.parentIndex === endMeta.parentIndex && startLineLength < endMeta.textOffset) {
          unavailableSections.push(id);
        } else if (
          // 如果不在一行，判断开始行长度是否大于 startMeta.textOffset 并且结束行长度是否大于 endMeta.textOffset
          startLineLength < startMeta.textOffset ||
          endLineLength < endMeta.textOffset
        ) {
          unavailableSections.push(id);
        }

        if (unavailableSections.length) {
          // 移除不可用的高亮区域
          try {
            annotationItems.value = newVal.filter(({ sec }) => !unavailableSections.includes(sec.id));

            // await request({
            //   url: '/document/selectedTextAnnotation',
            //   method: 'delete',
            //   baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            //   data: {
            //     ...props.annotationModuleSource,
            //     ids: unavailableSections,
            //   },
            // });
            //
            // if (!annotationItems.value?.length) {
            //   Message.info('所有标注区域均已失效，页面将自动刷新');
            //   window.location.reload();
            // }
          } catch (e) {
            console.error(e);
          }
        }
      });
    },
    { immediate: true, deep: true, debounce: 1000 },
  );
</script>

<template>
  <div @mouseup="handleShowButton" v-html="raw" />
  <div v-if="createButtonVisible" class="p-1 bg-white absolute create-annotation-wrapper" :style="createButtonStyle">
    <a-tooltip :content="`创建${annotationCategory}`">
      <annotation-modal
        :category="annotationCategory"
        :highlighter="highlighter"
        :related-modules="relatedModules"
        :annotation-module-source="annotationModuleSource"
        @create-range="handleNewAnnotationCreated"
      >
        <a-button size="small" shape="square" type="outline">
          <template #icon>
            <IconBulb />
          </template>
        </a-button>
      </annotation-modal>
    </a-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  :deep {
    .highlight-wrap-text {
      cursor: pointer;
      transition: all linear 0.2s;
      //text-decoration: underline;
      //// dashed
      //text-decoration-color: rgba(var(--arcoblue-5), 0.8);
      //// wavy
      //text-decoration-style: dashed;
      //text-decoration-thickness: 1.5px;
      //text-decoration-skip-ink: none;
      //text-underline-offset: 4px;
      background: rgba(var(--arcoblue-4), 0.2);

      &.highlight-wrap-hover {
        transition: all linear 0.2s;
        //text-decoration-style: solid;
        //text-decoration-thickness: 2px;
        //text-decoration-color: rgba(var(--arcoblue-8), 0.8);
        background: rgba(var(--arcoblue-4), 0.4);
      }
    }
  }
  .create-annotation-wrapper {
    position: absolute;
  }
</style>
