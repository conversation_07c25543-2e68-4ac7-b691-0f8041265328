<template>
  <a-button
    class="cursor-pointer ml-2 px-4 py-1"
    v-bind="$attrs"
    size="large"
    :disabled="disabled"
    @click="() => handleClick()"
  >
    <template #icon>
      <IconSend />
    </template>
    {{ verifyCodeSend > 0 ? verifyCodeSend + 's 后重发' : '发送' }}
  </a-button>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    mobile: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    handler: {
      type: Function,
    },
  });

  const mobileChecked = computed(() => {
    return /^1[3456789]\d{9}$/.test(props.mobile);
  });
  const sendingSMS = ref(false);
  const verifyCodeSend = ref(0);

  const disabled = computed(() => {
    return !mobileChecked.value || verifyCodeSend.value > 0 || sendingSMS.value;
  });

  const sendVerifyCode = async () => {
    if (disabled.value) {
      return;
    }
    sendingSMS.value = true;

    const { type, mobile } = props;
    await request(`/common/verifyCode/sendSms/${type}/${mobile}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.success('验证码发送成功，请注意查收');
    verifyCodeSend.value = 60;
    sendingSMS.value = false;

    const t = setInterval(() => {
      verifyCodeSend.value -= 1;
      if (verifyCodeSend.value <= 0) {
        clearInterval(t);
      }
    }, 1000);
  };

  const handleClick = async () => {
    try {
      if (props.handler) {
        await props.handler();
      } else {
        await sendVerifyCode();
      }
    } finally {
      sendingSMS.value = false;
    }
  };

  defineExpose({
    sendVerifyCode,
  });
</script>

<style scoped lang="scss"></style>
