<script setup lang="ts">
  import { computed, defineProps, defineEmits, ref, PropType, watch, onMounted, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { marked } from 'marked';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    streamCall: {
      type: Boolean,
      default: false,
      required: false,
    },
    response: {
      type: Object as PropType<any>,
    },
    sessionId: {
      type: String,
      default: 'default',
    },
  });

  const emits = defineEmits(['update:visible', 'send', 'stop']);
  const chatContainer = ref<HTMLElement | null>(null);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emits('update:visible', value),
  });
  const isReply = ref(false);
  const isLoading = ref(false);
  const msg = ref(null);

  const loadChatHistory = () => {
    const history = localStorage.getItem(`chat_history_${props.sessionId}`);
    return history ? JSON.parse(history) : [];
  };

  const chatCatch = ref<any[]>(loadChatHistory());

  const saveChatHistory = () => {
    localStorage.setItem(`chat_history_${props.sessionId}`, JSON.stringify(chatCatch.value));
  };

  // 初始化时加载历史记录
  onMounted(() => {
    chatCatch.value = loadChatHistory();
  });

  const handleSend = () => {
    if (!msg.value || msg.value === '') {
      Message.clear('top');
      Message.error('发送消息不能为空');
      return;
    }
    isReply.value = true;
    isLoading.value = true;
    chatCatch.value.push({ role: 'user', msg: msg.value });
    saveChatHistory();
    emits('send', msg.value);
  };

  const handleStop = () => {
    isReply.value = false;
    isLoading.value = false;
    emits('stop');
  };

  const handleCopy = (message: any) => {
    navigator.clipboard.writeText(message).then(() => {
      Message.clear('top');
      Message.success('已复制');
    });
  };

  const clearChatHistory = () => {
    chatCatch.value = [];
    localStorage.removeItem(`chat_history_${props.sessionId}`);
  };
  const handleScrollToBottom = async () => {
    await nextTick();

    if (chatContainer.value) {
      chatContainer.value.scrollTo({
        top: chatContainer.value.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  watch(
    () => props.response,
    async (newVal: any) => {
      if (newVal?.rawResponse) {
        chatCatch.value.push({ role: 'robot', msg: newVal.rawResponse });
        saveChatHistory();
      }
      isLoading.value = false;
      isReply.value = false;
      msg.value = null;
      await handleScrollToBottom();
    },
    { deep: true, immediate: true },
  );

  watch(
    () => props.sessionId,
    (newVal) => {
      localStorage.setItem(`behavior_chat_sessionId`, newVal);
      chatCatch.value = loadChatHistory();
    },
  );
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    width="50%"
    :closable="false"
    :draggable="true"
    v-bind="$attrs"
    :body-style="{ padding: 0, margin: 0 }"
  >
    <template #title>
      <slot name="title">
        <div class="flex justify-between items-center w-full">
          <span class="font-bold">{{ '行为分析小诸葛' }}</span>
          <a-button size="mini" type="text" status="danger" @click="clearChatHistory">
            <template #icon>
              <icon-delete />
            </template>
            清空记录
          </a-button>
        </div>
      </slot>
    </template>
    <slot name="content">
      <a-spin :loading="isLoading" class="w-full">
        <div ref="chatContainer" class="w-full h-[50vh] overflow-auto px-4 py-2 bg-white rounded border space-y-4">
          <div
            v-for="(message, index) in chatCatch"
            :key="index"
            class="flex items-start"
            :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
          >
            <!-- 助手头像 -->
            <div
              v-if="message.role !== 'user'"
              class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-xs mr-2"
            >
              <icon-robot size="15" />
            </div>

            <div
              class="max-w-[70%] px-4 py-2 rounded-lg shadow text-sm relative group"
              :class="
                message.role === 'user'
                  ? 'bg-blue-500 text-white rounded-br-none'
                  : 'bg-gray-200 text-gray-800 rounded-bl-none'
              "
            >
              <icon-copy
                v-if="message.role !== 'user'"
                class="absolute right-1 top-1 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                @click="handleCopy(message.msg)"
              />
              <div class="px-2 py-1" v-html="marked(message?.msg || '')"></div>
            </div>

            <!-- 用户头像 -->
            <div
              v-if="message.role === 'user'"
              class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs ml-2"
            >
              U
            </div>
          </div>
          <div class="absolute bottom-6 right-10">
            <slot name="moreAction">
              <div class="flex justify-center items-center space-x-2">
                <div
                  class="rounded-full cursor-pointer bg-gray-100 text-black flex flex-justify-center items-center p-2"
                  @click="handleScrollToBottom"
                >
                  <icon-down size="20" />
                </div>
              </div>
            </slot>
          </div>
        </div>
      </a-spin>
    </slot>
    <template #footer>
      <slot name="footer">
        <div class="bg-gray-100 rounded-[8px] border">
          <a-textarea
            v-model="msg"
            class="max-h-[50px]"
            style="border-radius: 8px; border: none"
            placeholder="问一问ai"
          />
          <div class="flex justify-end items-center mb-3 mr-2">
            <a-tooltip :content="isReply ? '等待回复' : '发送'">
              <a-button
                v-if="!isReply"
                shape="round"
                style="background-color: #d5ecff; color: #0468b5"
                @click="handleSend"
              >
                <template #icon>
                  <icon-send />
                </template>
              </a-button>
              <a-button v-else shape="round" style="background-color: #ffdddf; color: #ff121e" @click="handleStop">
                <template #icon>
                  <icon-record-stop />
                </template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </slot>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  :deep(.arco-textarea) {
    background-color: #f3f3f3 !important;
  }
</style>
