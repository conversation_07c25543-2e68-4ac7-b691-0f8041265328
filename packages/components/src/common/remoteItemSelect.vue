<script setup lang="ts">
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { computed, onMounted, PropType, ref } from 'vue';

  const { loading: searchLoading, setLoading: setSearchLoading } = useLoading();
  const dataList = ref([]);
  const rawDataList = ref([]);

  const emit = defineEmits(['change']);
  const props = defineProps({
    modelValue: {
      type: [String, Number, Array, Object] as PropType<any>,
    },
    api: {
      type: String,
      required: true,
    },
    searchFields: {
      type: Array as PropType<string[]>,
      default: () => ['name'],
    },
    baseURL: {
      type: String,
      default: PROJECT_URLS.MAIN_PROJECT_API,
    },
    labelField: {
      type: String,
      default: 'name',
    },
    preload: {
      type: Boolean,
      default: false,
    },
    groupBy: {
      type: [String, Function] as PropType<any>,
      default: '',
    },
    defaultQueryParams: {
      type: Object,
      default: () => {},
    },
  });

  const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('change', value);
    },
  });

  const handleSearch = async (value, queryParams?: any) => {
    setSearchLoading(true);
    try {
      const params = {
        ...props.defaultQueryParams,
        ...(queryParams || {}),
      };
      params[props.searchFields?.join('|')] = `%${value}%`;
      const { data } = await request(props.api, {
        baseURL: props.baseURL,
        params,
      });
      rawDataList.value = data.items || [];
      if (props.groupBy) {
        const rawData = data.items?.map((item) => {
          let group;
          if (typeof props.groupBy === 'function') {
            group = props.groupBy(item);
          } else {
            group = item[props.groupBy];
          }
          return {
            value: item.id,
            label: item[props.labelField],
            group,
          };
        });

        dataList.value = rawData.reduce((acc, cur) => {
          const group = acc.find((item) => item.label === cur.group);
          if (group) {
            group.options.push(cur);
          } else {
            acc.push({
              label: cur.group,
              isGroup: true,
              options: [cur],
            });
          }
          return acc;
        }, []);
      } else {
        dataList.value = data.items?.map((item) => {
          return {
            value: item.id,
            label: item[props.labelField],
          };
        });
      }
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSelect = (id) => {
    const raw: any = rawDataList.value.find((i: any) => i.id === id);
    emit('change', raw.id, raw);
  };

  defineExpose({
    handleSearch,
  });

  onMounted(async () => {
    if (props.preload) {
      await handleSearch('');
    }
  });
</script>

<template>
  <a-select
    v-bind="{
      placeholder: '选择或输入名称搜索',
      ...$attrs,
    }"
    v-model="selected"
    :loading="searchLoading"
    :filter-option="false"
    allow-search
    :options="dataList"
    @search="handleSearch"
    @change="handleSelect"
  />
</template>

<style scoped lang="scss"></style>
