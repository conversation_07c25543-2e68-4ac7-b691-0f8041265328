<template>
  <a-modal v-bind="$attrs" v-model:visible="visible" :title="title" :on-before-ok="handleSubmit" @close="handleClose">
    <div class="selected-items">
      <slot name="selected-items">
        <slot name="selected-items-title"> 将批量操作项目： </slot>{{ itemNames.join(', ') }}
      </slot>
    </div>
    <slot :submit-value="submitValue"></slot>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      required: true,
    },
    selectedItems: {
      required: true,
      type: Array,
    },
    toDisplay: {
      type: Function as PropType<(item: any) => string>,
      default: (item) => {
        return item.name;
      },
    },
    submitValue: {
      type: [String, Number, Boolean, Array, Object],
      default: undefined,
    },
  });

  const emit = defineEmits(['update:modelValue', 'submit']);
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const itemNames = computed(() => {
    return props.selectedItems.map(props.toDisplay);
  });

  const submittedVal = computed({
    get: () => props.submitValue,
    set: (val) => emit('submit', val),
  });

  const handleClose = () => {
    emit('update:modelValue', false);
  };

  const handleSubmit = () => {
    if (submittedVal.value === undefined) {
      Message.error('请先选择操作');
      return;
    }
    Message.success('正在处理，请稍后...');
    emit('submit', props.selectedItems, submittedVal.value);
    handleClose();
  };
</script>

<style lang="scss" scoped>
  .selected-items {
    margin: 16px 0;
  }
</style>
