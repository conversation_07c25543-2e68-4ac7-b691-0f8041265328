import { CustomSchema, DataTypes } from '@repo/infrastructure/types';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { getGradesList, getGradesListOptions } from '@repo/infrastructure/data/schoolTypes';
import { PROJECT_URLS } from '@repo/env-config';
import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
import useSchoolCourseStore from '../store/schoolCourseStore';

const course: CustomSchema = {
  api: '/course/course',
  baseURL: PROJECT_URLS.GO_PROJECT_API,
  listViewProps: {
    rowActionWidth: 320,
  },
  rowActions: [
    { key: 'archive', label: '教学档案', expose: true, handler() {} },
    { key: 'mission', label: '教学任务', expose: true, handler() {} },
    { key: 'questionLib', label: '题库', expose: true, handler() {} },
    // { key: 'behavior', label: '行为管理', expose: true, handler() {} },
    // { key: 'assessment', label: '课后评测', expose: true, handler() {} },
    // { key: 'question', label: '综合试题', expose: true, handler() {} },
  ],
  fieldsMap: {
    category: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        valueField: 'id',
        labelField: 'name',
        getOptions() {
          const store = useSchoolCourseStore();
          return store.getSchoolCourses();
        },
      },
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        async toDisplay(value) {
          if (!value) return '';
          const store = useSchoolCourseStore();
          const map = await store.getSchoolCoursesMap();
          return map[value]?.name;
        },
      },
    },
    gradePeriod: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: getPeriodsList(),
      },
      displayProps: {
        setStyle: (option: any) => {
          if (option.label.includes('春')) return { color: 'green' };
          if (option.label.includes('秋')) return { color: 'red' };
          return null;
        },
      },
    },
    grade: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        getOptions: async () => {
          const menuStore = useUserMenuStore();
          const menuInfo = await menuStore.getCurrentMenuInfo();
          let orgNature = menuInfo?.app?.label;
          if (!orgNature) {
            const userStore = useUserStore();
            orgNature = userStore.getUserNature();
          }

          return getGradesListOptions(orgNature);
        },
      },
      listProps: {
        columnWidth: 140,
      },
    },
    period: {
      valueType: DataTypes.Enum.name,
      inputWidgetProps: {
        allowCreate: true,
        options: ['上册', '下册'],
      },
      listProps: {
        columnWidth: 80,
      },
    },
    coverImage: {
      visibleInForm: false,
      inputWidget: 'uploadInput',
    },
    orgNature: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    teachingAssessScores: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
  },
};

export default { courseSchema: course };
