import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';
import { questionnaireFormScopeOptions } from '../src/constants';

const questionnaireForm: CustomSchema = {
  api: '/questionnaire/form',
  listViewProps: { rowActionWidth: 280 },
  rowActions: [
    { key: 'view', visible: false },
    {
      key: 'investigationResult',
      label: '调查结果',
      icon: 'icon-fire',
      visible(record) {
        return record.scene === 'investigation' && record.published;
      },
      handler() {},
      expose: true,
    },
    {
      key: 'testResult',
      icon: 'icon-subscribed',
      label: '考试结果',
      visible(record) {
        return record.scene === 'examination' && record.published;
      },
      handler() {},
      expose: true,
    },
    {
      key: 'questionnaire',
      label: '问卷管理',
      handler() {},
      expose: true,
      visible(record) {
        return !record.published && record.scene === 'investigation';
      },
    },
    {
      key: 'testManagement',
      label: '试题管理',
      handler() {},
      expose: true,
      visible(record) {
        return !record.published && record.scene === 'examination';
      },
    },
    {
      key: 'preview',
      label: '预览',
      handler() {},
      expose: true,
      visible(record) {
        return record.published;
      },
    },
    {
      key: 'recordList',
      label: '答卷列表',
      handler() {},
      visible(record) {
        return record.published;
      },
    },
  ],
  formViewProps: {
    defaultData: { published: false, questionIds: [], branchOfficeIds: [] },
  },
  fieldsMap: {
    scene: {
      inputWidget: 'selectInput',
      listProps: { columnWidth: 100 },
      inputWidgetProps: {
        options: [
          { value: 'examination', label: '考试' },
          { value: 'investigation', label: '调查' },
        ],
      },
    },
    uuid: { visibleInForm: false, visibleInTable: false, visibleInDetail: false },
    description: { inputWidget: 'richInput' },
    questionIds: { defaultValue: false, visibleInForm: false, visibleInTable: false, visibleInDetail: false },
    questions: { defaultValue: [], visibleInForm: false, visibleInTable: false, visibleInDetail: false },
    questionConfig: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    published: {
      visibleInForm: false,
      visibleInDetail: false,
      listProps: {
        columnWidth: 180,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/questionnaireForm/components/publishStatus.vue')),
      },
    },
    publishScope: {
      visibleInForm: false,
      visibleInDetail: false,
      inputWidgetProps: { options: questionnaireFormScopeOptions },
      listProps: { columnWidth: 140 },
    },
    relatedId: { visibleInForm: false, visibleInTable: false, visibleInDetail: false },
    branchOfficeIds: { visibleInForm: false, visibleInTable: false, visibleInDetail: false },
    startTime: { inputWidgetProps: { showTime: true } },
    endTime: { inputWidgetProps: { showTime: true } },
  },
};

const questionnaire: CustomSchema = {
  api: '/question/questionnaire',
};

export default {
  questionnaire,
  questionnaireForm,
};
