import { CustomSchema } from '@repo/infrastructure/types';
import { getItemAttachmentsColumns } from '@repo/ui/components/utils/extraContent';
import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';

const schoolItemAttachmentsColumns = getItemAttachmentsColumns({
  defaultOptions: ['营业执照'],
});

const SchoolSchema: CustomSchema = {
  api: '/org/branchOffice',
  requestApi: {
    list: '/org/branchOffice/getTree',
  },
  rowActions: [
    {
      key: 'resettlementQrCode',
      label: '学生安置二维码',
      handler() {},
      visible(row) {
        return row?.school?.nature === UNIT_NATURES_MAP.SpecialEduCommittee;
      },
    },
  ],
  listViewProps: {
    showIndex: false,
    pagination: false,
    size: 'mini',
  },
  quickSearchProps: {
    enabled: true,
    fields: ['name'],
  },
  modalEdit: true,
  fieldsMap: {
    'itemAttachments': {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: schoolItemAttachmentsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: schoolItemAttachmentsColumns,
      },
    },
    'name': {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    'leaf': {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    'children': {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    'allDescendantIds': {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    'school': {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    'school.name': {
      key: 'school.name',
      label: '学校名称',
      listProps: {
        columnWidth: 200,
      },
    },
    'school.nature': {
      key: 'school.nature',
      label: '单位性质',
      visibleInForm: false,
      listProps: {
        columnWidth: 140,
      },
    },
    'school.operationType': {
      key: 'school.operationType',
      label: '办学类型',
      visibleInForm: false,
      listProps: {
        columnWidth: 100,
      },
    },
    'school.type': {
      key: 'school.type',
      label: '学校类型',
      visibleInForm: false,
      listProps: {
        columnWidth: 100,
      },
    },
    'school.institutionType': {
      key: 'school.institutionType',
      label: '机构类型',
      visibleInForm: false,
      listProps: {
        columnWidth: 120,
      },
    },
    'school.hasResourceRoom': {
      key: 'school.hasResourceRoom',
      label: '有资源教室',
      visibleInForm: false,
      displayProps: {
        component: 'BooleanDisplay',
      },
      listProps: {
        columnWidth: 100,
      },
    },
    'parentId': {
      visibleInDetail: false,
    },
  },
};

export default { SchoolSchema };
