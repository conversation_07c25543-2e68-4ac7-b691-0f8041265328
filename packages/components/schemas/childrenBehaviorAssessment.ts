import { CustomSchema } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';

const placementReport: CustomSchema = {
  api: '/specialCommittee/childrenBehaviorAssessment',
  listViewProps: {},
  modalEdit: true,
  rowActions: [
    {
      key: 'report',
      label: '报告',
      handler: () => {},
      expose: true,
    },
    getDataSubmitAction('/specialCommittee/childrenBehaviorAssessment'),
  ],
  formViewProps: {},
  fieldsMap: {
    student: {
      key: 'student',
      label: '学生',
    },
    age: {
      key: 'student.age',
      label: '年龄',
    },
    nation: {
      key: 'student.nation',
      label: '民族',
    },
    createdDate: {
      key: 'createdDate',
      label: '创建时间',
    },
    general: {
      key: 'general.filledBy',
      label: '填写人',
      displayProps: {
        toDisplay: (val) => {
          switch (val) {
            case 'father':
              return '父亲';
            case 'mother':
              return '母亲';
            default:
              return '其他人';
          }
        },
      },
    },
    status: {
      key: 'student.status',
      label: '状态',
      visibleInForm: false,
      displayProps: {
        toDisplay: (val) => {
          switch (val) {
            case 'Normal':
              return '正常';
            case 'TransferOut':
              return '转出';
            case 'Suspension':
              return '休学';
            case 'Graduation':
              return '毕业';
            case 'Other':
              return '其他';
            case 'WaitingResettlement':
              return '待安置';
            default:
              return '';
          }
        },
      },
    },
  },
};

export default {
  placementReport,
};
