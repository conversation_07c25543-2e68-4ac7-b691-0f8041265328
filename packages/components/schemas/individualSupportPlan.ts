import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { getCurrentPeriod, getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import useCommonStore from '@repo/infrastructure/utils/store';

const diagnosisInputColumns = [
  { key: 'content', label: '评估内容' },
  { key: 'evaluateTime', label: '评估时间', inputWidget: 'dateInput', listProps: { columnWidth: 120 } },
  { key: 'evaluator', label: '评估人', listProps: { columnWidth: 100 } },
  { key: 'results', label: '评估结果概况' },
];
const onlyContentColumns = [{ key: 'content', label: '内容' }];
const settlementColumns = [
  { key: 'subject', label: '安置' },
  { key: 'content', label: '内容' },
  { key: 'dateRange', label: '时间', inputWidget: 'dateRangeInput' },
  { key: 'personInCharge', label: '主要负责人' },
];
const supportsColumns = [
  { key: 'item', label: '项目', width: 150 },
  { key: 'content', label: '内容' },
  { key: 'personInCharge', label: '主要负责人', width: 150 },
];

const ispSchema: CustomSchema = {
  api: '/resourceRoom/individualizedSupportPlan',
  collaborate: true,
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  rowActions: [
    { key: 'toIEP', label: '个别化', handler() {}, expose: true, collaborate: 'Edit' },
    { key: 'assessment', label: '评估', handler() {}, collaborate: 'Edit' },
    getCollaborateRowAction('ISP'),
    getDataSubmitAction('/resourceRoom/individualizedSupportPlan'),
  ],
  listViewProps: {
    size: 'mini',
    rowActionWidth: 240,
  },
  detailViewProps: {
    showFresh: true,
    showOthers: true,
    columns: 1,
    title: (raw: any) => {
      return `${raw.student?.gradeClass?.name} ${raw.student.name} ${raw.gradePeriod} 的支持计划`;
    },
    fieldsGrouping: [
      {
        columns: 4,
        label: '学生基本信息',
        fields: [
          // 'gradeClass',
          'student',
          'studentSymbol',
          'basicInfo',
          'studentAddress',
          'familyStatus',
          'historyOfDevelopment',
          'historyOfEducation',
          'familyMembers',
        ],
      },
      {
        label: '计划信息',
        columns: 2,
        fields: ['gradePeriod', 'meetingDate', 'participantIds'],
      },
      {
        columns: 1,
        fields: [
          'educationDiagnosisOfLesson',
          'educationDiagnosisOfPhysical',
          'educationDiagnosisOfPsychological',
          'educationDiagnosisOfOthers',
        ],
      },
      {
        columns: 1,
        fields: ['advantage', 'vulnerability'],
      },
      {
        columns: 1,
        fields: ['resettlementAdvices'],
      },
      {
        columns: 1,
        fields: ['supports'],
      },
    ],
  },
  formViewProps: {
    layout: 'horizontal',
    size: 'mini',
    formatData: async (data) => {
      if (!data) {
        return {};
      }
      if (!data.gradeClass) {
        data.gradeClass = data?.student?.gradeClass?.id;
      }
      data.advantage = data.advantage?.map((item: any) => {
        return { content: item };
      });
      data.vulnerability = data.vulnerability?.map((item: any) => {
        return { content: item };
      });
      data.gradePeriod = data.gradePeriod?.name || data.gradePeriod;
      return data;
    },
    fieldsGrouping: [
      {
        label: '学生基本信息',
        fields: [
          // 'gradeClass',
          'student',
          'studentSymbol',
          'basicInfo',
          'studentAddress',
          'familyStatus',
          'historyOfDevelopment',
          'historyOfEducation',
          'familyMembers',
        ],
      },
      {
        label: '计划信息',
        fields: ['gradePeriod', 'meetingDate', 'participantIds'],
      },
      {
        colSpan: 24,
        fields: [
          'educationDiagnosisOfLesson',
          'educationDiagnosisOfPhysical',
          'educationDiagnosisOfPsychological',
          'educationDiagnosisOfOthers',
        ],
      },
      {
        colSpan: 24,
        fields: ['advantage', 'vulnerability'],
      },
      {
        fields: ['resettlementAdvices'],
      },
      {
        fields: ['supports'],
      },
    ],
  },
  fieldsMap: {
    ...getCollaboratorField(),
    participantIds: {
      inputWidget: 'selectInput',
      required: true,
      inputWidgetProps: {
        colSpan: 12,
        multiple: true,
        getOptions: async () => {
          const store = useCommonStore({
            api: '/org/companyUser',
          });
          const raw = await store.getList();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (value) => {
          const store = useCommonStore({
            api: '/org/companyUser',
          });
          const raw = await store.getList();
          return raw
            ?.filter((item) => value?.indexOf(item.id) >= 0)
            .map((item) => {
              return item.name;
            })
            .join('、');
        },
      },
    },
    student: {
      valueType: 'Foreign',
      foreignField: {
        api: '/resourceRoom/student',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      inputWidgetProps: {
        allowClear: true,
        allowSearch: true,
        placeholder: '请选择学生',
        // disabled: true,
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.id === value);
          if (!selected) {
            return undefined;
          }
          return {
            ...selected.raw,
          };
        },
        // queryDepends: {
        //   async onGradeClassChange(gradeClass) {
        //     const { data } = await request('/resourceRoom/student', {
        //       params: {
        //         gradeClass,
        //         pageSize: 999,
        //       },
        //       baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        //     });
        //     const options = data.items?.map((item) => {
        //       return {
        //         value: item.id,
        //         label: item.name,
        //         ...item,
        //       };
        //     });
        //     return {
        //       inputWidgetProps: {
        //         disabled: false,
        //         options,
        //       },
        //     };
        //   },
        // },
      },
    },
    gradePeriod: {
      inputWidget: 'selectInput',
      defaultValue: getCurrentPeriod(),
      inputWidgetProps: {
        options: getPeriodsList(),
      },
    },
    studentSymbol: {
      label: '系统证号',
      key: 'studentSymbol',
      inputWidget: 'displayInput',
      displayProps: {
        toDisplay(val, record) {
          return record?.student?.symbol;
        },
      },
    },

    baseInfo: {
      label: '基本信息',
      key: 'basicInfo',
      inputWidget: 'displayInput',
      displayProps: {
        toDisplay(val, record) {
          if (!record?.student) return '';
          return [record.student?.gender, `${record.student?.age}岁`, record.student?.disorders]
            .filter(Boolean)
            .join(' / ');
        },
      },
    },
    studentAddress: {
      visibleInForm: false,
      visibleInDetail: false,
      label: '家庭住址',
      key: 'studentAddress',
      inputWidgetProps: {
        colSpan: 24,
      },
      inputWidget: 'displayInput',
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.address;
        },
      },
    },
    familyStatus: {
      label: '家庭情况',
      key: 'familyStatus',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.familyStatus;
        },
      },
    },
    historyOfDevelopment: {
      label: '发展史',
      key: 'historyOfDevelopment',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.historyOfDevelopment;
        },
      },
    },
    historyOfEducation: {
      label: '教育史',
      key: 'historyOfEducation',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.historyOfEducation;
        },
      },
    },
    familyMembers: {
      inputWidget: defineAsyncComponent(() => import('@repo/components/student/studentFamilyInput.vue')),
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentFamilyDisplay.vue')),
      },
      inputWidgetProps: {
        colSpan: 24,
        queryDepends: {
          onStudentChange(student, inputValue, formData) {
            formData.value.familyMembers = student?.familyMembers || [];
            return {};
          },
        },
      },
    },
    educationDiagnosisOfLesson: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: diagnosisInputColumns,
      },
    },
    educationDiagnosisOfPhysical: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: diagnosisInputColumns,
      },
    },
    educationDiagnosisOfPsychological: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: diagnosisInputColumns,
      },
    },
    educationDiagnosisOfOthers: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: diagnosisInputColumns,
      },
    },
    advantage: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: onlyContentColumns,
        formatValue: (value) => {
          return value
            ?.filter((item) => item.content?.trim())
            ?.map((item) => {
              return item.content;
            });
        },
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: onlyContentColumns,
      },
    },
    vulnerability: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: onlyContentColumns,
        formatValue: (value) => {
          return value
            ?.filter((item) => item.content?.trim())
            ?.map((item) => {
              return item.content;
            });
        },
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: onlyContentColumns,
      },
    },
    resettlementAdvices: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: settlementColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: settlementColumns,
      },
    },
    supports: {
      inputWidget: 'listTableInput',
      defaultValue: [
        { item: '辅具' },
        { item: '无障碍设施' },
        { item: '助教' },
        { item: '课程和教材' },
        { item: '作业和考试' },
        { item: '家庭教育' },
        { item: '其他' },
      ] as any,
      inputWidgetProps: {
        colSpan: 24,
        columns: supportsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: supportsColumns,
      },
    },
    participants: {
      key: 'participants',
      visibleInForm: false,
      inputWidgetProps: {
        colSpan: 12,
      },
    },
  },
};

export default { ispSchema };
