import { ref } from 'vue';
import { PROJECT_URLS } from '@repo/env-config';
import { CustomSchema } from '@repo/infrastructure/types';
import { request } from '@repo/infrastructure/request';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { getAdminClientNature } from '../src/utils/utils';

const scheduleToolSchema: CustomSchema = {
  api: '/teacher/scheduleTool',
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  rowActions: [
    {
      key: 'enterSys',
      label: '进入系统',
      handler: () => {},
      expose: true,
      btnProps: {
        type: 'outline',
      },
    },
    {
      key: 'copy',
      label: '复制一份',
      handler: () => {},
    },
  ],
  detailViewProps: {},
  fieldsMap: {
    name: {
      key: 'name',
      label: '名称',
      inputWidget: 'textInput',
    },
    boId: {
      key: 'boId',
      label: '所属单位',
      required: true,
      inputWidget: 'selectInput',
      inputWidgetProps: {
        valueField: 'id',
        labelField: 'name',
        allowSearch: true,
        async getOptions() {
          const { data } = await request('/org/branchOffice', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            params: {
              name: '%_%',
              nature: getAdminClientNature(),
            },
          });

          return data.items || [];
        },
      },
      displayProps: {
        toDisplay: (val, record) => {
          return record?.branchOfficeName || '';
        },
      },
    },
    period: {
      key: 'period',
      label: '学期',
      inputWidget: 'selectInput',
      required: true,
      inputWidgetProps: {
        options: getPeriodsList(),
      },
      displayProps: {
        setStyle: (option: any) => {
          if (option.label.includes('春')) return { color: 'green' };
          if (option.label.includes('秋')) return { color: 'red' };
          return null;
        },
        toDisplay: (record: string) => {
          return record;
        },
      },
    },
  },
};

export default { scheduleToolSchema };
