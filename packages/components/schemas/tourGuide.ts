import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';
import useCommonStore from '@repo/infrastructure/utils/store';

const tourGuide: CustomSchema = {
  api: '/resourceRoom/tourGuide',
  detailViewProps: {
    type: 'modal',
    fieldsGrouping: [
      {
        label: '申请信息',
        fields: [
          'subject',
          'status',
          'symbol',
          'applier',
          'applierPhone',
          'applyDate',
          'serviceProvider',
          'submitRemark',
          'applyReason',
          'problemDescription',
          'students',
        ],
      },
      {
        label: '学校意见',
        fields: ['schoolOpinion', 'schoolOpinionDate'],
      },
    ],
  },
  rowActions: [
    {
      key: 'guidanceRecord',
      label: '指导记录',
      expose: true,
    },
    {
      key: 'followUp',
      label: '回访记录',
      handler() {},
    },
    { key: 'edit', visible: false },
  ],
  fieldsMap: {
    branchOfficeId: {
      key: 'branchOfficeId',
      label: '申请学校',
      displayProps: {
        async toDisplay(val) {
          const boStore = useCommonStore({
            api: '/org/branchOffice/simpleList',
          });
          const res = await boStore.toDisplay(val);
          return res;
        },
      },
    },
    submitDirectly: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
    serviceProvider: {
      displayProps: {
        detailSpan: 2,
      },
    },
    subject: {
      displayProps: {
        detailSpan: 2,
      },
    },
    submitRemark: {
      displayProps: {
        detailSpan: 2,
      },
    },
    applyDate: {
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    applyReason: {
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/tourGuide/applyReason.vue')),
      },
    },
    problemDescription: {
      displayProps: {
        detailSpan: 2,
      },
    },
    students: {
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/tourGuide/studentSimpleList.vue')),
      },
    },
    operationRecords: {
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/tourGuide/operationLog.vue')),
      },
    },
  },
};

export default { tourGuide };
