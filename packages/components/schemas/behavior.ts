import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';

const diagnosisStatuses = ['2', '3', '4', '5', '6'];

const behaviorSchema: CustomSchema = {
  api: '/resourceRoom/behaviorRecordAnalysis',
  modalEdit: true,
  // collaborate: true,
  quickSearchProps: {
    enabled: true,
    fields: ['keywords'],
    multiple: false,
    placeholder: '按学生/评测老师/学校搜索',
  },
  detailViewProps: {
    showOthers: true,
    showFresh: true,
    columns: 2,
    title: (raw: any) => {
      if (!raw) {
        return '行为管理';
      }
      return `${raw?.studentName} 的行为分析`;
    },
    fieldsGrouping: [
      {
        columns: 2,
        fields: ['studentName', 'createdDate', 'event'],
      },
      {
        columns: 1,
        fields: ['behaviorRecordAnalysis', 'diagnosis', 'analysis', 'strategy'],
      },
    ],
  },
  listViewProps: {
    rowActionWidth: 420,
    searchType: 'advance',
    filterFields: ['id', 'studentName', 'event', 'createdDate', 'status'],
  },
  formViewProps: {
    layout: 'horizontal',
    size: 'mini',
    formatData: async (data) => {
      if (!data) {
        return {};
      }
      if (!data.gradeClass) {
        data.gradeClass = data?.student?.gradeClass?.id;
      }
      data.gradePeriod = data.gradePeriod?.name || data.gradePeriod;
      return data;
    },
    fieldsGrouping: [
      {
        label: '学生基本信息',
        fields: ['event'],
      },
    ],
  },
  rowActions: [
    /*    {
      key: 'view',
      label: '查看',
      handler() {},
    }, */
    {
      key: 'addEvent',
      label: '添加事件',
      expose: true,
      handler() {},
    },
    /*    {
      key: 'edit',
      // visible: false,
    }, */
    {
      key: 'startDiagnosis',
      label: '开始诊断',
      expose: true,
      handler() {},
      visible(record) {
        return record.status === '1';
      },
    },
    {
      key: 'continueDiagnosis',
      label: '继续诊断',
      expose: true,
      handler() {},
      visible(record) {
        return diagnosisStatuses.includes(record.status);
      },
    },
    {
      key: 'executePolicy',
      label: '策略执行',
      expose: true,
      handler() {},
      visible(record) {
        return record.status === '6';
      },
    },
    getCollaborateRowAction('BehaviorAnalysis'),
    getDataSubmitAction('/resourceRoom/behaviorRecordAnalysis', {
      disabled: (record: any) => {
        return (record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected') || Number(record.status) < 6;
      },
    }),
  ],
  fieldsMap: {
    ...getCollaboratorField(),
    behaviorRecordAnalysis: {
      key: 'behaviorRecordAnalysis',
      label: '行为事件',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/student/components/behaviorEventDisplay.vue')),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    diagnosis: {
      key: 'diagnosis',
      label: '诊断信息',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/student/components/behaviorDiagnosisDisplay.vue')),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    analysis: {
      key: 'analysis',
      label: '症解',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/student/components/behaviorAnalysisDisplay.vue')),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    strategy: {
      key: 'strategy',
      label: '策略',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/student/components/behaviorStrategyDisplay.vue')),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    createdDate: {
      inputWidgetProps: {
        maxLength: 18,
      },
    },
    studentName: {},
    studentId: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
    status: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
    modifiedDate: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
  },
};

export default { behaviorSchema };
