import { CustomSchema } from '@repo/infrastructure/types';

const rehabilitationInstitution: CustomSchema = {
  api: '/rehabilitation/rehabilitationPlanDetail',
  rowActions: [{ key: 'records', label: '康复训练记录', expose: true, handler() {} }],
  listViewProps: {
    rowActionWidth: 220,
  },
  fieldsMap: {
    rehabilitationPlanId: {
      visibleInDetail: false,
      visibleInTable: false,
      visibleInForm: false,
    },
    domain: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['物理治疗', '作业治疗', '心理治疗', '语言治疗', '感统', '其他'],
        allowCreate: true,
      },
      listProps: {
        columnWidth: 120,
      },
    },
    target: {
      inputWidget: 'textareaInput',
    },
    // content: {
    //   inputWidget: 'textareaInput',
    // },
  },
};

export default { rehabilitationInstitution };
