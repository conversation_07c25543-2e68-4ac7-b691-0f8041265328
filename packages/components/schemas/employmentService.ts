import { CustomSchema, SchemaField } from '@repo/infrastructure/types';

const employmentService: CustomSchema = {
  api: '/resourceRoom/employmentService',
  modalEdit: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student.name'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 120,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '就业服务';
      }
      return `${raw?.student.name}的就业服务`;
    },
  },

  rowActions: [
    {
      key: 'viewRecord',
      label: '查看',
      expose: true,
      handler() {},
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'tutoringProgram',
      label: '辅导计划',
      handler() {},
    },
    {
      key: 'record',
      label: '辅导记录',
      handler() {},
    },
  ],
  fieldsMap: {
    student: {},
    dateRange: {},
    supervisionPlan: {},
    target: {},
    guideTeacher: {},
    companyName: {},
    createdDate: {},
    age: {},
    disorders: {},
  },
};

export default { employmentService };
