import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { getCurrentPeriod, getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import useCommonStore from '@repo/infrastructure/utils/store';

const diagnosisInputColumns: SchemaField[] = [
  { key: 'content', label: '评估内容' },
  { key: 'evaluateTime', label: '评估时间', inputWidget: 'dateInput', listProps: { columnWidth: 140 } },
  { key: 'evaluator', label: '评估人', listProps: { columnWidth: 160 } },
  { key: 'results', label: '评估结果概况', inputWidget: 'textareaInput' },
];

const lessonDiagnosisInputColumns: SchemaField[] = [
  { key: 'content', label: '评估内容', listProps: { columnWidth: 200 }, inputWidget: 'textareaInput' },
  { key: 'evaluateTime', label: '评估时间', inputWidget: 'dateInput', listProps: { columnWidth: 140 } },
  { key: 'evaluator', label: '评估人', listProps: { columnWidth: 160 } },
  { key: 'results', label: '评估结果概况', inputWidget: 'textareaInput' },
];

const settlementColumns = [
  { key: 'subject', label: '安置' },
  { key: 'content', label: '内容' },
  { key: 'dateRange', label: '时间', inputWidget: 'dateRangeInput' },
  { key: 'personInCharge', label: '主要负责人' },
];
const supportsColumns = [
  { key: 'item', label: '项目', width: 150 },
  { key: 'content', label: '内容' },
  { key: 'personInCharge', label: '主要负责人', width: 150 },
];

const iepSchema: CustomSchema = {
  api: '/resourceRoom/individualizedEducation',
  collaborate: true,
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  quickSearchProps: {
    enabled: true,
    fields: ['student.name', 'student.gradeClass.name', 'gradePeriod'],
    placeholder: '按学生姓名、班级、学期搜索',
  },
  rowActions: [
    { key: 'targetEdit', label: '计划目标', handler() {}, expose: true, collaborate: 'Edit' },
    { key: 'viewDetails', label: '查看', handler() {}, expose: true },
    {
      key: 'submitBtn',
      label: '提交',
      handler() {},
      expose: false,
      icon: 'icon-check',
      disabled: (record: any) => {
        return record?.statisticsStatus === 'Enable';
      },
    },
    { key: 'export', multiple: true, label: '导出IEP' },
    { key: 'view', visible: false },
    getCollaborateRowAction('IEP'),
    getDataSubmitAction('/resourceRoom/individualizedEducation'),
  ],
  listViewProps: {
    size: 'mini',
    rowActionWidth: 280,
  },
  detailViewProps: {
    showFresh: true,
    showOthers: true,
    columns: 1,
    title: (raw: any) => {
      if (!raw) {
        return '个别化教育计划';
      }
      return `${raw?.student?.gradeClass?.name || ''} ${raw?.student.name} ${raw?.gradePeriod} 的个别化教育计划`;
    },
    fieldsGrouping: [
      {
        columns: 4,
        label: '学生基本信息',
        fields: [
          // 'gradeClass',
          'student',
          'studentSymbol',
          'basicInfo',
          'studentAddress',
          'familyStatus',
          'historyOfDevelopment',
          'historyOfEducation',
          'familyMembers',
        ],
      },
      {
        label: '计划信息',
        columns: 2,
        fields: ['gradePeriod', 'meetingDate', 'participants'],
      },
      {
        columns: 1,
        fields: [
          'educationDiagnosisOfLesson',
          'educationDiagnosisOfPhysical',
          'educationDiagnosisOfPsychological',
          'educationDiagnosisOfOthers',
        ],
      },
      {
        columns: 1,
        fields: ['advantage', 'vulnerability'],
      },
      {
        columns: 1,
        fields: ['resettlementAdvices'],
      },
      {
        label: '计划目标',
        columns: 1,
        fields: ['targets'],
      },
      {
        columns: 1,
        fields: ['supports'],
      },
    ],
  },
  formViewProps: {
    layout: 'horizontal',
    size: 'mini',
    formatData: async (data) => {
      if (!data) {
        return {};
      }
      if (!data.gradeClass) {
        data.gradeClass = data?.student?.gradeClass?.id;
      }
      // data.advantage = data.advantage?.map((item: any) => {
      //   return { content: item };
      // });
      // data.vulnerability = data.vulnerability?.map((item: any) => {
      //   return { content: item };
      // });
      data.gradePeriod = data.gradePeriod?.name || data.gradePeriod;
      return data;
    },
    fieldsGrouping: [
      {
        label: '学生基本信息',
        fields: [
          // 'gradeClass',
          'student',
          'studentSymbol',
          'basicInfo',
          'studentAddress',
          'familyStatus',
          'historyOfDevelopment',
          'historyOfEducation',
          'familyMembers',
        ],
      },
      {
        label: '计划信息',
        fields: ['gradePeriod', 'meetingDate', 'participantIds', 'participants'],
      },
      {
        colSpan: 24,
        fields: [
          'educationDiagnosisOfLesson',
          'educationDiagnosisOfPhysical',
          'educationDiagnosisOfPsychological',
          'educationDiagnosisOfOthers',
        ],
      },
      {
        colSpan: 24,
        fields: ['advantage', 'vulnerability'],
      },
      {
        fields: ['resettlementAdvices'],
      },
      {
        fields: ['supports'],
      },
    ],
  },
  fieldsMap: {
    additionalData: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    ...getCollaboratorField(),
    gradeClass: {
      visibleInForm: false,
    },
    targets: {
      key: 'targets',
      visibleInTable: false,
      visibleInForm: false,
      label: '',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/iep/targetsInIepDetail.vue')),
      },
    },
    student: {
      valueType: 'Foreign',
      foreignField: {
        api: '/resourceRoom/student',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
        foreignOptionsHandler: async (rawOptions: any) => {
          // 过滤了当前账号负责的学生
          // try {
          //   const { data: res } = await request('/org/companyUser/getMyResponsibleStudents', {
          //     method: 'GET',
          //     baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          //     params: {
          //       nature: getOrgNature(),
          //     },
          //   });
          //   const studentIds = res.items.map((item: any) => {
          //     return item.id;
          //   });
          //   return rawOptions.filter((item) => studentIds.includes(item.id));
          // } catch (e) {
          //   console.log(e);
          // }
          return rawOptions;
        },
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      inputWidgetProps: {
        allowClear: true,
        allowSearch: true,
        placeholder: '请选择学生',
        // disabled: true,
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.id === value);
          if (!selected) {
            return undefined;
          }
          return {
            ...selected.raw,
          };
        },
        onValueChange: (val: any, record: any) => {
          record.value.participantIds = val.gradeClass.teacherList?.map((t) => t.id) || [];
        },
        /* queryDepends: {
          async onGradeClassChange(gradeClass) {
            const { data } = await request('/resourceRoom/student', {
              params: {
                gradeClass,
                pageSize: 999,
              },
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });
            const options = data.items?.map((item) => {
              return {
                value: item.id,
                label: `${item.name}`,
                ...item,
              };
            });
            return {
              inputWidgetProps: {
                disabled: false,
                options,
              },
            };
          },
        }, */
      },
    },
    gradePeriod: {
      inputWidget: 'selectInput',
      defaultValue: null, // getCurrentPeriod(),
      inputWidgetProps: {
        options: getPeriodsList(),
      },
      displayProps: {
        setStyle: (option: any) => {
          if (option.label.includes('春')) return { color: 'green' };
          if (option.label.includes('秋')) return { color: 'red' };
          return null;
        },
      },
    },
    studentSymbol: {
      label: '系统证号',
      key: 'studentSymbol',
      inputWidget: 'displayInput',
      displayProps: {
        toDisplay(val, record) {
          return record?.student?.symbol;
        },
      },
    },

    baseInfo: {
      label: '基本信息',
      key: 'basicInfo',
      inputWidget: 'displayInput',
      displayProps: {
        toDisplay(val, record) {
          if (!record?.student) return '';
          return [record.student?.gender, `${record.student?.age}岁`, record.student?.disorders]
            .filter(Boolean)
            .join(' / ');
        },
      },
    },
    studentAddress: {
      visibleInForm: false,
      visibleInDetail: false,
      label: '家庭住址',
      key: 'studentAddress',
      inputWidgetProps: {
        colSpan: 24,
      },
      inputWidget: 'displayInput',
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.address;
        },
      },
    },

    familyStatus: {
      label: '家庭情况',
      key: 'familyStatus',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.familyStatus;
        },
      },
    },
    historyOfDevelopment: {
      label: '发展史',
      key: 'historyOfDevelopment',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.historyOfDevelopment;
        },
      },
    },
    historyOfEducation: {
      label: '教育史',
      key: 'historyOfEducation',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 4,
        toDisplay(val, record) {
          return record?.student?.historyOfEducation;
        },
      },
    },
    familyMembers: {
      inputWidget: defineAsyncComponent(() => import('@repo/components/student/studentFamilyInput.vue')),
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentFamilyDisplay.vue')),
      },
      inputWidgetProps: {
        colSpan: 24,
        queryDepends: {
          onStudentChange(student, inputValue, formData) {
            formData.value.familyMembers = student?.familyMembers || [];
            return {};
          },
        },
      },
    },
    educationDiagnosisOfLesson: {
      inputWidget: defineAsyncComponent(() => import('../src/iep/components/diagnosisInput.vue')),
      inputWidgetProps: {
        colSpan: 24,
        columns: lessonDiagnosisInputColumns,
        diagnosisType: 'LessonDiagnosis',
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/iep/components/diagnosisDisplay.vue')),
        columns: lessonDiagnosisInputColumns,
      },
    },
    educationDiagnosisOfPhysical: {
      inputWidget: defineAsyncComponent(() => import('../src/iep/components/diagnosisInput.vue')),
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
        diagnosisType: 'PhysiologicalDiagnosis',
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/iep/components/diagnosisDisplay.vue')),
        columns: diagnosisInputColumns,
      },
    },
    educationDiagnosisOfPsychological: {
      inputWidget: defineAsyncComponent(() => import('../src/iep/components/diagnosisInput.vue')),
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
        diagnosisType: 'PsychologicalDiagnosis',
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/iep/components/diagnosisDisplay.vue')),
        columns: diagnosisInputColumns,
      },
    },
    educationDiagnosisOfOthers: {
      inputWidget: defineAsyncComponent(() => import('../src/iep/components/diagnosisInput.vue')),
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
        diagnosisType: 'OtherDiagnosis',
      },
      displayProps: {
        component: defineAsyncComponent(() => import('../src/iep/components/diagnosisDisplay.vue')),
        columns: diagnosisInputColumns,
      },
    },
    advantage: {
      inputWidget: 'stringListInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        component: 'StringListDisplay',
      },
    },
    vulnerability: {
      inputWidget: 'stringListInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        component: 'StringListDisplay',
      },
    },
    resettlementAdvices: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: settlementColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: settlementColumns,
      },
    },
    supports: {
      inputWidget: 'listTableInput',
      defaultValue: [
        { item: '辅具' },
        { item: '无障碍设施' },
        { item: '助教' },
        { item: '课程和教材' },
        { item: '作业和考试' },
        { item: '家庭教育' },
        { item: '其他' },
      ] as any,
      inputWidgetProps: {
        colSpan: 24,
        columns: supportsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: supportsColumns,
      },
    },
    participants: {
      key: 'participants',
      visibleInForm: false,
      inputWidgetProps: {
        colSpan: 6,
      },
    },
    participantIds: {
      inputWidget: 'selectInput',
      required: true,
      inputWidgetProps: {
        allowCreate: true,
        colSpan: 12,
        multiple: true,
        getOptions: async () => {
          const store = useCommonStore({
            api: '/org/companyUser',
            queryParams: {},
          });
          const raw = await store.getList();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (value) => {
          const store = useCommonStore({
            api: '/org/companyUser/allTeachers',
          });
          const raw = await store.getList();
          if (typeof value === 'string') return value;

          if (Array.isArray(value)) {
            const names = value.map((item) => {
              if (typeof item === 'string') {
                return item;
              }
              const found = raw?.find((user) => user.id === item);
              return found ? found.name : '';
            });
            return names.filter(Boolean).join('、');
          }
          return '';
        },
      },
      /* displayProps: {
        toDisplay: async (value) => {
          const store = useCommonStore({
            api: '/org/companyUser',
          });
          const raw = await store.getList();
          return raw
            .filter((item) => value?.indexOf(item.id) >= 0)
            .map((item) => {
              return item.name;
            })
            .join('、');
        },
      }, */
    },
    guardianConfirmed: {
      listProps: {
        columnWidth: 120,
      },
    },
  },
};

export default { iepSchema };
