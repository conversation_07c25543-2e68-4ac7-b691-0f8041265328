import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const attentionTrainingSchema: CustomSchema = {
  api: '/resourceRoom/attentionTrainingRecord',
  modalEdit: true,
  // collaborate: true,
  quickSearchProps: {
    enabled: true,
    fields: ['keywords'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 160,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    title: (raw: any) => {
      if (!raw) {
        return '训练记录';
      }
      return `${raw?.student.name} 的${raw.trainingType}记录`;
    },
    fieldsGrouping: [
      {
        columns: 1,
        fields: ['attentionTrainingDisplay'],
      },
    ],
  },
  formViewProps: {},
  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
    {
      key: 'assessment',
      label: '评估',
      expose: false,
      handler() {},
      visible(record) {
        return record?.assessmentDetails.length > 0;
      },
    },
    {
      key: 'training',
      label: '训练',
      expose: true,
      handler() {},
      visible(record) {
        return record?.trainingDetails.length > 0;
        // return record.trainingDetails.length > 0 || record?.assessmentDetails.length > 0;
      },
    },
  ],
  fieldsMap: {
    attentionTrainingDisplay: {
      key: 'attentionTrainingDisplay',
      label: '注意力训练',
      displayProps: {
        component: defineAsyncComponent(
          () => import('../src/student/components/attentionTraining/attentionTrainingDisplay.vue'),
        ),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    trainingType: {},
    ageBracket: {},
    setting: {},
    trainingDetails: {},
    assessmentDetails: {},
    trainingTimes: {},
    assessmentTimes: {},
    disorders: {},
    attentionTrainingEvaluationCriteria: {
      visibleInTable: false,
    },
    normativeReference: {
      label: '常模参照',
      key: 'normativeReference',
    },
    modifiedDate: {
      visibleInTable: false,
      visibleInForm: false,
      visibleInDetail: false,
    },
    gridSize: {},
  },
};

export default { attentionTrainingSchema };
