import { CustomSchema } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { useUserStore } from '@repo/infrastructure/store';

const CustomCriterionResultSchema: CustomSchema = {
  api: '/evaluation/customCriterionResult',
  modalEdit: true,
  collaborate: true,
  quickSearchProps: {
    enabled: true,
    fields: ['keywords'],
    multiple: false,
    placeholder: '按学生/评测老师/学校搜索',
  },
  listViewProps: {
    rowActionWidth: 210,
    searchType: 'advance',
    filterFields: [
      'gradeClass',
      'student',
      'times',
      'customCriterionId',
      'evaluationDate',
      'assessmentTeachers',
      'finished',
    ],
  },
  rowActions: [
    {
      label: '批量提交',
      key: 'batchSubmit',
      icon: 'icon-double-up',
      hideInRow: true,
      multiple: true,
      handler() {},
    },
    {
      label: '批量导出',
      key: 'batchExport',
      icon: 'icon-export',
      hideInRow: true,
      multiple: true,
      handler() {},
    },
    {
      key: 'delete',
      visible: false,
    },
    {
      label: '删除',
      key: 'batchDel',
      icon: 'icon-delete',
      multiple: true,
      handler() {},
      btnProps: {
        status: 'danger',
      },
      disabled: (record: any) => {
        if (Array.isArray(record)) {
          return false;
        }
        if (typeof record === 'object') {
          const userStore = useUserStore();
          const { userInfo } = userStore;
          const ownerId = record.collaborators.find((c) => c.action === 'Owner')?.userId;
          const isOwner = ownerId === userInfo?.id;
          const isSubmitted = record.submitStatus === 'Submitted';
          return !isOwner || isSubmitted;
        }
        return true;
      },
    },
    {
      label: '查看',
      key: 'criterionResultView',
      expose: true,
      visible(record) {
        return record.finished && record.type === 'Criterion';
      },
      handler() {},
    },
    {
      label: '查看',
      key: 'diagnosisResultView',
      expose: true,
      visible(record) {
        return record.type === 'Diagnosis';
      },
      handler() {},
    },
    {
      label: '修改',
      key: 'diagnosisResultEdit',
      icon: 'icon-edit',
      collaborate: 'Edit',
      expose: false,
      visible(record) {
        return record.type === 'Diagnosis' && (record.submitStatus !== 'Draft' || record.submitStatus !== 'Rejected');
      },
      handler() {},
    },
    {
      key: 'edit',
      visible: false,
    },
    // {
    //   key: 'aiAnalysis',
    //   label: 'AI 分析',
    //   handler() {},
    // },
    {
      label: '继续评估',
      collaborate: 'Edit',
      key: 'continue',
      expose: true,
      btnProps: {
        type: 'outline',
        status: 'success',
      },
      visible(record) {
        return !record.finished && record.type !== 'Diagnosis';
      },
      handler(record: any) {},
    },
    getCollaborateRowAction('Assessment'),
    getDataSubmitAction('/evaluation/customCriterionResult', {
      disabled: (record: any) => {
        return !record.finished || (record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected');
      },
    }),
    {
      key: 'analysis',
      label: '能力评估图',
      handler() {},
      expose: true,
      visible: false,
    },
    // {
    //   key: 'export',
    //   label: '导出',
    //   handler() {},
    // },
    {
      key: 'edit',
      visible: false,
    },
    {
      key: 'view',
      visible: false,
    },
  ],
  fieldsMap: {
    student: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    gradeClass: {
      label: '班级',
      key: 'gradeClass',
      displayProps: {
        toDisplay(val, record) {
          return record?.student?.gradeClass?.name;
        },
      },
      foreignField: {
        api: '/resourceRoom/gradeClass',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      filterProps: {
        targetField: 'student.gradeClass',
      },
    },
    customCriterionId: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
      label: '评估项目',
      inputWidget: 'select',
      valueType: 'Foreign',
      dataType: 'Foreign',
      foreignField: {
        api: '/evaluation/customCriterion',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      filterProps: {
        supportOperators: ['In', 'NotIn'],
        defaultOperator: 'In',
      },
    },
    ...getCollaboratorField(),
    evaluator: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
    },

    maxScore: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    scoresMap: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    evaluationDetails: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    times: {
      visibleInTable: false,
      visibleInDetail: false,
      filterable: true,
    },
    timesLabel: {
      visibleInForm: false,
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        toDisplay(val) {
          return val ? `第${val}` : '';
        },
      },
    },
    finished: {
      listProps: {
        columnWidth: 80,
      },
    },
    evaluationDate: {
      listProps: {
        columnWidth: 120,
      },
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    resultSnapshot: {
      visibleInTable: false,
    },
  },
};

export default { CustomCriterionResultSchema };
