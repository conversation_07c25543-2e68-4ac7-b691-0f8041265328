import { CustomSchema } from '@repo/infrastructure/types';
// 专委会安置报告
const placementReport: CustomSchema = {
  api: '/specialCommittee/placementReport',
  listViewProps: {},
  modalEdit: true,
  rowActions: [
    { key: 'viewReport', label: '查看', handler: () => {}, expose: true },
    { key: 'view', visible: false },
  ],
  formViewProps: {},
  fieldsMap: {
    student: {
      key: 'student',
      label: '学生',
    },
    age: {
      key: 'student.age',
      label: '年龄',
    },
    nation: {
      key: 'student.nation',
      label: '民族',
    },
    createdDate: {
      key: 'createdDate',
      label: '创建时间',
    },
    status: {
      key: 'student.status',
      label: '状态',
      visibleInForm: false,
      displayProps: {
        toDisplay: (val) => {
          switch (val) {
            case 'Normal':
              return '正常';
            case 'TransferOut':
              return '转出';
            case 'Suspension':
              return '休学';
            case 'Graduation':
              return '毕业';
            case 'Other':
              return '其他';
            case 'WaitingResettlement':
              return '待安置';
            default:
              return '';
          }
        },
      },
    },
  },
};

export default {
  placementReport,
};
