import { CustomSchema } from '@repo/infrastructure/types';
import useCommonStore from '@repo/infrastructure/utils/store';
import { defineAsyncComponent, computed } from 'vue';
import { getClientRole } from '@repo/env-config';
import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
import { omit } from 'lodash';

const timeRanges = {
  '-1': '全天',
  '1': '上午',
  '2': '下午',
};

const status = {
  Reject: '未通过',
  PendingApproval: '待审核',
  Pass: '通过',
};

// 可约日期及时间
interface PlanTimes {
  planGuideDate: string;
  timeRange: number;
}

// 巡回老师
interface PlanTeacher {
  planTimes: PlanTimes[]; // 假设planTimes是一个包含Date对象的数组
  major: string;
}

const planTeacher: PlanTeacher = {
  planTimes: [],
  major: '21',
};

/**
 * 更新教师信息
 *
 * @param value 教师信息对象，包含 planTimes 和 major 属性
 */
function updateTeacher(value: any) {
  planTeacher.planTimes = value?.planTimes;
  planTeacher.major = value?.major;
}

const isTeacherClient = computed(() => {
  const role = getClientRole();
  return role === 'Manager';
});

/**
 * 获取可用日期列表
 *
 * @returns 返回可用日期列表，如果获取失败则返回一个空数组
 */
function getAvailableDates() {
  return planTeacher.planTimes?.map((item) => item.planGuideDate) || [];
}

const rawPermissions = ['dailyWork:normalTourGuide:'];

const conventionalAppointment: CustomSchema = {
  api: '/resourceCenter/normalTourGuideOrder',
  // 权限控制  及时menu.ts里的路径与这里保持一致
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: rawPermissions.map((item) => `${item}edit`),
    delete: rawPermissions.map((item) => `${item}delete`),
  },
  listViewProps: {
    rowActionWidth: 180,
  },
  rowActions: [
    /*    {
              label: '驳回',
              key: 'reject',
              expose: true,
              handler: () => {},
            }, */
  ],
  detailViewProps: {
    columns: 1,
  },
  fieldsMap: {
    status: {
      visibleInForm: isTeacherClient,
      displayProps: {
        toDisplay: (value: any, record: any) => {
          return status[value];
        },
      },
    },
    // 巡回老师id  为了给实体传值
    normalTourGuideScheduling: {
      inputWidget: 'numberInput',
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    operator: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
    },
    teacher: {
      labelField: 'name',
      valueField: 'id',
      inputWidget: 'selectInput',
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
      inputWidgetProps: {
        placeholder: '请选择巡回指导老师',
        allowSearch: true,
        async getOptions() {
          const teacherStore = useCommonStore({
            api: '/resourceCenter/normalTourGuidePlan/allPlanTeachers',
          });
          const raw = await teacherStore.getList();
          return raw.map((item) => {
            return {
              label: item.teacher.name,
              value: item.id,
              planTimes: item.planTimes,
              major: item.major,
            };
          });
        },
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.value === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.raw?.value,
            name: selected.raw?.label,
            planTimes: selected.raw?.planTimes,
            major: selected.raw?.major,
          };
        },
        /**
         * 当值改变时触发的函数
         *
         * @param value 改变后的值对象
         * @param record 相关的记录对象
         */
        onValueChange(value, record) {
          updateTeacher(value);
        },
      },
    },
    planGuideDate: {
      // todo  这里不知道怎么搞  需要限制只能是planTimes里的日期
      inputWidget: 'selectInput',
      inputWidgetProps: {
        disabled: true,
        placeholder: '请先选择教师',
        queryDepends: {
          onTeacherChange(value, inputValue, formData) {
            const planTimes = value?.planTimes || [];
            const options = planTimes.map((item) => item.planGuideDate).sort((a, b) => new Date(a) - new Date(b));
            return {
              inputWidgetProps: {
                placeholder: planTimes.length ? '请选择预约指导日期' : '当前老师无可预约日期',
                disabled: false,
                options,
              },
            };
          },
        },
      },
    },
    major: {
      defaultValue: '请先选择老师',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onTeacherChange(value, inputValue, formData) {
            formData.value.major = value?.major || '';
            return {};
          },
        },
      },
    },

    timeRange: {
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        toDisplay(val) {
          // 返回 0=全天、1=上午、2=下午
          return timeRanges[val] || '';
        },
      },

      inputWidget: 'selectInput',
      inputWidgetProps: {
        disabled: true,
        placeholder: '请先选择预约指导日期',
        queryDepends: {
          onPlanGuideDateChange(value, inputValue, formData) {
            const planTime = formData.value.teacher?.planTimes?.find((item) => item.planGuideDate === value);
            if (!planTime) {
              return {
                inputWidgetProps: {
                  disabled: true,
                  placeholder: '请先选择计划日期',
                },
              };
            }

            let options = [];
            let defaultValue;
            switch (planTime.timeRange) {
              case -1:
                options = [
                  { label: '全天', value: -1 },
                  { label: '上午', value: 1 },
                  { label: '下午', value: 2 },
                ];
                defaultValue = -1;
                break;
              case 1:
                options = [{ label: '上午', value: 1 }];
                defaultValue = 1;
                break;
              case 2:
                options = [{ label: '下午', value: 2 }];
                defaultValue = 2;
                break;
              default:
                break;
            }
            return {
              defaultValue,
              inputWidgetProps: {
                placeholder: '请选择时段',
                disabled: false,
                options,
              },
            };
          },
        },
      },
      // inputWidget: 'selectInput',
      // inputWidgetProps: {
      //   placeholder: '请选择课时',
      //   allowSearch: true,
      //   // todo  这里不知道怎么搞  需要根据上面选择的plantimes里的数据限制，全天的时候可以有上午下午，否则只能选择上午下午
      //
      //   options: [
      //     { label: '全天', value: 0 },
      //     { label: '上午', value: 1 },
      //     { label: '下午', value: 2 },
      //   ],
      // },
    },
  },
};

export default {
  conventionalAppointment,
};
