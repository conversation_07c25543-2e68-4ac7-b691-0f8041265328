import { CustomSchema } from '@repo/infrastructure/types';
import { secondsFormatter } from '@repo/ui/components/utils/utils';

const questionnaireRecordSchema: CustomSchema = {
  api: '/questionnaire/record',
  rowActions: [
    { key: 'view', visible: false },
    { key: 'edit', visible: false },
    { key: 'delete', visible: false },
    { key: 'detail', label: '查看', expose: true },
    { key: 'reject', label: '答卷驳回', expose: true },
  ],
  fieldsMap: {
    author: {
      key: 'author',
      label: '提交人',
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        toDisplay(val, record: any) {
          return record?.createdBy?.name || record?.additionalInfo.authorName || '';
        },
      },
    },
    boName: {
      key: 'boName',
      label: '单位信息',
    },
    phoneNumber: {
      key: 'phoneNumber',
      label: '手机号',
    },
    totalScore: {
      listProps: {
        columnWidth: 80,
      },
    },
    fullScore: {
      listProps: {
        columnWidth: 70,
      },
    },
    timeCost: {
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        toDisplay(val) {
          return secondsFormatter(val);
        },
      },
    },
  },
};

export default { questionnaireRecordSchema };
