import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const neuralDiscriminationSchema: CustomSchema = {
  api: '/resourceRoom/learningDisabilitiesScreeningScale',
  modalEdit: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 150,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '学习障碍儿童筛查量表(PRS)';
      }
      return `${raw?.student.name}的记录`;
    },
  },

  rowActions: [
    {
      key: 'viewRecords',
      label: '查看结果',
      expose: true,
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    student: {},
    totalScore: {},
    speechAbility: {},
    nonverbalAbility: {},
    gender: {},
    createdDate: {},
    age: {},
    disorders: {
      visibleInForm: false,
    },
  },
};

export default { neuralDiscriminationSchema };
