import { CustomSchema, SchemaField } from '@repo/infrastructure/types';

const behaviorSchema: CustomSchema = {
  api: '/resourceRoom/application',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    fields: ['applier'],
    multiple: false,
    placeholder: '搜索',
  },
  detailViewProps: {
    showOthers: true,
    showFresh: true,
    columns: 2,
    title: (raw: any) => {
      if (!raw) {
        return '';
      }
      return `${raw?.studentName} `;
    },
    fieldsGrouping: [
      {
        columns: 4,
        label: '申请基本信息',
        fields: ['applier', 'applierPhone', 'applyDate', 'subject', 'applicationType'],
      },
    ],
  },
  listViewProps: {},
  formViewProps: {
    fullscreen: true,
  },
  rowActions: [
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'details',
      label: '详情',
      expose: true,
    },
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    branchOffice: {
      label: '学校',
      key: 'branchOffice',
      visibleInForm: false,
      visibleInTable: false,
    },
    applier: {
      inputWidget: 'textareaInput',
      label: '申请人',
      key: 'applier',
    },
    applierPhone: {
      inputWidget: 'textInput',
      label: '电话',
      key: 'applierPhone',
    },
    applyDate: {
      inputWidget: 'dateInput',
      label: '申请日期',
      key: 'applyDate',
    },
    subject: {
      inputWidget: 'richInput',
      label: '主题',
      key: 'subject',
    },
    applicationType: {
      inputWidget: 'selectInput',
      dataType: 'Enum',
      label: '类型',
      key: 'applicationType',
      inputWidgetProps: {
        options: [
          { label: '巡回指导', value: 'TourGuide' },
          { label: '送教上门', value: 'D2dService' },
          { label: '个案回访', value: 'TourGuideIndividual' },
        ],
      },
    },
    status: {
      listProps: {
        visible: true,
        ellipsis: true,
        columnWidth: 120,
      },
      inputWidgetProps: {
        showWordLimit: true,
        allowClear: true,
        disabled: false,
        allowUpdate: true,
        options: [
          { label: '待提交', value: 'NotSubmitted' },
          { label: '待审核', value: 'Waiting' },
          { label: '已通过', value: 'Approved' },
          { label: '已拒绝', value: 'Rejected' },
          { label: '已完成', value: 'Finished' },
        ],
      },
      unique: false,
      ellipsis: true,
      filterProps: {
        inputWidgetProps: {},
      },
      key: 'status',
      label: '状态',
      dataType: 'Enum',
      valueType: 'Enum',
      defaultValue: 'NotSubmitted',
      editable: true,
      required: false,
      readOnly: true,
      inputWidget: 'selectInput',
      dataIndex: 'status',
      visibleInForm: false,
    },
  },
};

export default { behaviorSchema };
