import { CustomSchema } from '@repo/infrastructure/types';
import { getClientRole } from '@repo/env-config';
import useSchoolCourseStore from '../store/schoolCourseStore';

const courseLibrarySchema: CustomSchema = {
  api: '/course/rehab-chapter-library',
  listViewProps: {
    size: 'mini',
  },
  permissions: {
    delete: [
      'specialSchool:teacher:courseLibrary:manage',
      'kindergarten:teacher:courseLibrary:manage',
      'compulsoryEducation:teacher:courseLibrary:manage',
      'vocationalEducation:teacher:courseLibrary:manage',
    ],
  },
  rowActions: [
    {
      key: 'refer',
      label: '引用',
      expose: true,
      icon: 'icon-share-internal',
      visible: () => {
        const client = getClientRole();
        return client === 'Company';
      },
      btnProps: {
        type: 'outline',
        size: 'mini',
      },
    },
    {
      key: 'preview',
      label: '查看',
      expose: true,
      icon: 'icon-eye',
      btnProps: {
        size: 'mini',
      },
    },
    { key: 'view', visible: false, permNode: 'NO_ONE' },
    { key: 'edit', visible: false, permNode: 'NO_ONE' },
  ],
  fieldsMap: {
    period: {
      listProps: {
        columnWidth: 80,
      },
    },
    referenceCount: {
      listProps: {
        columnWidth: 90,
      },
    },
    category: {
      label: '训练科目',
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        async toDisplay(value) {
          if (!value) return '';
          const store = useSchoolCourseStore();
          const map = await store.getSchoolCoursesMap();
          return map[value]?.name;
        },
      },
    },
  },
};

export default {
  courseLibrarySchema,
};
