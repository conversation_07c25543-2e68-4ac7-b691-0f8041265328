import { CustomSchema, SchemaField } from '@repo/infrastructure/types';

const effectEvaluationColumns: SchemaField = [
  { key: 'target', label: '教学目标', inputWidget: 'textareaInput' },
  {
    key: 'preTest',
    label: '前测',
    inputWidget: 'numberInput',
    inputWidgetProps: { mode: 'button' },
    listProps: { columnWidth: 150 },
  },
  {
    key: 'afterTest',
    label: '后测',
    inputWidget: 'numberInput',
    inputWidgetProps: { mode: 'button' },
    listProps: { columnWidth: 150 },
  },
];

const sendEduRecord: CustomSchema = {
  api: '/resourceRoom/sendEducationRecord',
  rowActions: [
    { key: 'view', visible: false },
    {
      key: 'viewDetail',
      label: '查看',
      expose: true,
      handler() {},
    },
  ],
  formViewProps: {
    layout: 'horizontal',
    fullscreen: true,
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: ['type', 'teacher', 'date', 'classHour', 'school'],
      },
      {
        colSpan: 24,
        label: '送教内容',
        fields: ['teachingPlan', 'effectEvaluation', 'nextSendPoint', 'attachments'],
      },
    ],
  },
  detailViewProps: {
    fullscreen: true,
    fieldsGrouping: [
      {
        label: '基本信息',
        columns: 4,
        fields: ['type', 'teacher', 'date', 'classHour', 'school'],
      },
      {
        columns: 1,
        label: '送教内容',
        fields: ['teachingPlan', 'effectEvaluation', 'nextSendPoint', 'attachments'],
      },
    ],
  },
  fieldsMap: {
    sendEducationPlan: {
      visibleInForm: false,
      displayProps: {
        toDisplay: (value: any) => {
          if (!value) return '';
          return `${value.student?.name} ${value.period}`;
        },
      },
    },
    type: {
      inputWidget: 'selectInput',
      allowCreate: true,
      options: [
        { label: '送教', value: '送教' },
        { label: '送康', value: '送康' },
      ],
      displayProps: {
        toDisplay: (value: any) => {
          return value || '送教';
        },
      },
    },
    // content: {
    //   inputWidget: 'textareaInput',
    // },
    teachingPlan: {
      inputWidget: 'richInput',
    },
    effectEvaluation: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        columns: effectEvaluationColumns,
      },
    },
    nextSendPoint: {
      inputWidget: 'textareaInput',
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
        detailSpan: 2,
      },
    },
    guardianTraining: {
      inputWidget: 'richInput',
      inputWidgetProps: {
        colSpan: 24,
      },
      displayProps: {
        detailSpan: 24,
      },
    },
    additionalData: {
      visibleInDetail: false,
    },
    parentSignature: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
      key: 'parentSignature',
      label: '家长签字',
      inputWidget: 'signatureInput',
      inputWidgetProps: {
        width: '100%',
        height: 200,
        actionDirection: 'right',
      },
    },
  },
};

export default { sendEduRecord };
