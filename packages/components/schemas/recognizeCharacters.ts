import { CustomSchema } from '@repo/infrastructure/types';
import useSchoolCourseStore from '../store/schoolCourseStore';

const readingDisabilitySchema: CustomSchema = {
  api: '/resourceRoom/recognizeCharactersResult',
  modalEdit: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student.name'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 150,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '';
      }
      return `${raw?.student.name}的记录`;
    },
  },

  rowActions: [
    {
      key: 'viewRecords',
      label: '查看报告',
      expose: true,
    },
    {
      key: 'viewThesaurus',
      label: '查看字本',
      handler: () => {},
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    student: { label: '姓名', key: 'student' },
    spendTime: {
      label: '耗时',
      key: 'spendTime',
      displayProps: {
        toDisplay: (val: any, record: any) => {
          return `${(val / 1000).toFixed(1)}s`;
        },
      },
    },
    gender: {
      label: '性别',
      key: 'student.gender',
    },
    age: {
      label: '年龄',
      key: 'student.age',
    },
    disorders: {
      label: '障碍类型',
      key: 'student.disorders',
    },
    chapterId: {
      visibleInTable: false,
      label: '章节',
      key: 'chapterId',
      toDisplay: (val: any, record: any) => {
        return record.chapterId;
      },
    },
    category: {
      label: '课程',
      key: 'category',
      displayProps: {
        async toDisplay(value) {
          if (!value) return '';
          const store = useSchoolCourseStore();
          const map = await store.getSchoolCoursesMap();
          return map[value]?.name;
        },
      },
    },
  },
};

export default { readingDisabilitySchema };
