import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const readingDisabilitySchema: CustomSchema = {
  api: '/resourceRoom/readingDisability',
  modalEdit: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 150,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '儿童汉语阅读障碍量表(DCCC)';
      }
      return `${raw?.student.name}的记录`;
    },
  },

  rowActions: [
    {
      key: 'viewRecords',
      label: '查看结果',
      expose: true,
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    student: { label: '姓名', key: 'student' },
    total: {},
    result: {},
    gender: {
      label: '性别',
      key: 'student.gender',
    },
    age: {
      label: '年龄',
      key: 'student.age',
    },
    disorders: {
      label: '障碍类型',
      key: 'student.disorders',
    },
    createdDate: {},
  },
};

export default { readingDisabilitySchema };
