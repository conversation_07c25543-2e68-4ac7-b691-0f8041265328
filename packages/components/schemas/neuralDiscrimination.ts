import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const neuralDiscriminationSchema: CustomSchema = {
  api: '/resourceRoom/neuralDiscrimination',
  modalEdit: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 120,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '神经甄别';
      }
      return `${raw?.student.name}的记录`;
    },
    fieldsGrouping: [
      {
        columns: 2,
        fields: ['student', 'gender', 'totalScore', 'score'],
      },
      {
        columns: 3,
        fields: ['hands', 'eyes', 'feet'],
      },
      {
        columns: 1,
        fields: ['neuralDiscriminationDisplay'],
      },
    ],
  },

  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    neuralDiscriminationDisplay: {
      key: 'neuralDiscriminationDisplay',
      label: '神经甄别',
      displayProps: {
        component: defineAsyncComponent(
          () => import('../src/student/components/neuralDiscrimination/neuralDiscriminationDisplay.vue'),
        ),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
    student: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    details: {
      visibleInDetail: false,
      visibleInForm: false,
      visibleInTable: false,
    },
    gender: {},
    totalScore: {},
    score: {},
    state: {},
    createdDate: {},
    age: {},
    disorders: {
      visibleInForm: false,
    },
  },
};

export default { neuralDiscriminationSchema };
