import { CustomSchema } from '@repo/infrastructure/types';
import { getClientRole, PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';
import { defineAsyncComponent } from 'vue';

const clientRole = getClientRole();

const rehabilitationPlanSchema: CustomSchema = {
  api: '/rehabilitation/rehabilitationDesign',
  // rowActions: [{ key: 'assessment', label: '康复评测', expose: true, handler() {} }],
  listViewProps: {
    rowActionWidth: 200,
  },
  fieldsMap: {
    institution: {
      visibleInForm: false,
      foreignField: {
        api: '/rehabilitation/rehabilitationInstitution',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      inputWidgetProps: {
        disabled: clientRole === 'Company',
        valueType: (value, options) => {
          return options.find((item) => item.value === value)?.raw;
        },
      },
    },
    employee: {
      listProps: {
        columnWidth: 120,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
      foreignField: {
        api: '/org/companyUser',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      inputWidgetProps: {
        valueType: (value, options) => {
          const selected = options.find((item) => item.value === value);
          return {
            id: selected.value,
            name: selected.label,
          };
        },
      },
    },
    // rehabilitationEmployee: {
    //   listProps: {
    //     columnWidth: 120,
    //   },
    //   inputWidgetProps: {
    //     options: [],
    //     valueType: (value, options) => {
    //       const selected = options.find((item) => item.value === value)?.raw;
    //       return {
    //         id: selected.value,
    //         name: selected.label,
    //       };
    //     },
    //     disabled: true,
    //     queryDepends: {
    //       onRehabilitationInstitutionChange: async (value, a, formData, isInit) => {
    //         if (!isInit) {
    //           formData.value.rehabilitationEmployee = undefined;
    //         }
    //
    //         const { data } = await request('/rehabilitation/rehabilitationEmployee', {
    //           params: {
    //             rehabilitationInstitution: value.id,
    //           },
    //           baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    //         });
    //
    //         const options = data.items.map((item: any) => ({
    //           label: item.name,
    //           value: item.id,
    //         }));
    //
    //         return {
    //           inputWidgetProps: {
    //             options,
    //             disabled: false,
    //           },
    //         };
    //       },
    //     },
    //   },
    // },
    classDate: {
      inputWidgetProps: {
        showTime: true,
      },
    },
    subject: {
      displayProps: {
        detailSpan: 24,
      },
    },
    preparation: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 24,
      },
    },
    targetContent: {
      inputWidget: 'stringListInput',
      displayProps: {
        detailSpan: 24,
      },
    },
    steps: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 24,
      },
    },
    reflection: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 24,
      },
    },
  },
};

export default { rehabilitationPlanSchema };
