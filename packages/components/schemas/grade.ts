import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { getAdminClientNature } from '../src/utils/utils';

const getDisplayName = (enrollmentYear, gradeName) => {
  const res = [];
  if (enrollmentYear) {
    res.push(`[${enrollmentYear}届]`);
  }
  if (gradeName) {
    res.push(gradeName);
  }
  return res.join('');
};

const grade: CustomSchema = {
  api: '/teacher/schoolGrade',
  // rowActions: [{ key: 'periods', label: '学期管理', handler() {} }],
  formViewProps: {},
  fieldsMap: {
    school: {
      dataType: 'Foreign',
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: async () => {
          const nature = getAdminClientNature();
          return {
            nature,
          };
        },
      },
      inputWidgetProps: {
        allowSearch: true,
        valueType: (value, computedOptions) => {
          const school = computedOptions.find((item) => item.raw?.id === value);
          return {
            id: school.value,
            name: school.label,
          };
        },
      },
    },
    name: {
      inputWidgetProps: {
        queryDepends: {
          onGradeNameChange(gradeName, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.enrollmentYear, formData.value.gradeName);
            return {};
          },
          onEnrollmentYearChange(gradeName, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.enrollmentYear, formData.value.gradeName);
            return {};
          },
        },
      },
    },
    enrollmentYear: {
      listProps: {
        columnWidth: 100,
      },
    },
    graduateYear: {
      listProps: {
        columnWidth: 100,
      },
    },
    currentPeriod: {
      visibleInForm: false,
      displayProps: {
        detailSpan: 2,
        toDisplay(value) {
          return value?.name || '无法确定当前学期';
        },
      },
    },
    gradePeriods: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/teacher/schoolGradePeriodsDisplay.vue')),
      },
    },
    orgNature: {
      defaultValue: getAdminClientNature,
      visibleInForm: false,
    },
  },
};

export default { grade };
