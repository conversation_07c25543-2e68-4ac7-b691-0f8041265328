import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { defineAsyncComponent } from 'vue';

const sendEducationArchives: CustomSchema = {
  api: '/resourceRoom/d2dEducation',
  modalEdit: true,
  collaborate: true,
  quickSearchProps: {
    enabled: false,
    fields: ['student.name', 'personInCharge', 'period', 'cbName'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 280,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    width: 1000,
    title: (raw: any) => {
      if (!raw) {
        return '送教档案管理';
      }
      return `${raw?.student.name}的送教档案`;
    },
  },
  rowActions: [
    {
      key: 'viewRecord',
      label: '查看',
      expose: false,
      visible: false,
      handler() {},
    },
    {
      key: 'view',
      visible: false,
    },
    getCollaborateRowAction('D2dEducation'),
    getDataSubmitAction('/resourceRoom/d2dEducation', {
      disabled: (record: any) => {
        return !record.finished || (record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected');
      },
    }),
    {
      key: 'editEducationArchive',
      label: '编辑档案',
      handler() {},
      expose: true,
      btnProps: { type: 'outline', status: 'success' },
    },
    {
      key: 'placeOnFile',
      label: '归档',
      expose: true,
      visible: (record: any) => {
        return !record.finished;
      },
      handler() {},
    },
    {
      key: 'removeArchive',
      label: '解除归档',
      visible: (record: any) => {
        return record.finished;
      },
      expose: true,
    },
    {
      key: 'exportArchive',
      label: '导出',
      handler() {},
    },
    {
      key: 'exportArchiveMerge',
      label: '合并导出',
      multiple: true,
      handler() {},
    },
  ],
  fieldsMap: {
    ...getCollaboratorField(),
    student: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    finished: {
      visibleInForm: false,
    },
    chargeUser: {
      visibleInForm: false,
      displayProps: {
        toDisplay: (val) => {
          return val.map((i) => i?.name).join(', ');
        },
      },
    },
    period: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: getPeriodsList(),
      },
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
  },
};

export default { sendEducationArchives };
