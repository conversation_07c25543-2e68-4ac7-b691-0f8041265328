import { CustomSchema, DataTypes } from '@repo/infrastructure/types';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
// import { grades } from '@repo/infrastructure/data/schoolTypes';
import { PROJECT_URLS } from '@repo/env-config';
import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
import { getGradesListOptions } from '@repo/infrastructure/data/schoolTypes';
import useSchoolCourseStore from '../store/schoolCourseStore';

const rehabCourse: CustomSchema = {
  api: '/course/rehab-course',
  baseURL: PROJECT_URLS.GO_PROJECT_API,
  listViewProps: {
    rowActionWidth: 320,
  },
  rowActions: [
    { key: 'archive', label: '康复档案', expose: true, handler() {} },
    { key: 'mission', label: '康复任务', expose: true, handler() {} },
    { key: 'questionLib', label: '题库', expose: true, handler() {} },
    // { key: 'behavior', label: '行为管理', expose: true, handler() {} },
    // { key: 'assessment', label: '课后评测', expose: true, handler() {} },
    // { key: 'question', label: '综合试题', expose: true, handler() {} },
  ],
  fieldsMap: {
    category: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        valueField: 'id',
        labelField: 'name',
        getOptions() {
          const store = useSchoolCourseStore();
          return store.getSchoolCourses();
        },
      },
      listProps: {
        columnWidth: 100,
      },
      displayProps: {
        async toDisplay(value) {
          if (!value) return '';
          const store = useSchoolCourseStore();
          const map = await store.getSchoolCoursesMap();
          return map[value]?.name;
        },
      },
    },
    gradePeriod: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: getPeriodsList(),
      },
      displayProps: {
        setStyle: (option: any) => {
          if (option.label.includes('春')) return { color: 'green' };
          if (option.label.includes('秋')) return { color: 'red' };
          return null;
        },
      },
    },
    // grade: {
    //   inputWidget: 'selectInput',
    //   inputWidgetProps: {
    //     valueField: 'id',
    //     labelField: 'name',
    //     allowCreate: true,
    //     getOptions: async () => {
    //       const menuStore = useUserMenuStore();
    //       const menuInfo = await menuStore.getCurrentMenuInfo();
    //       let orgNature = menuInfo?.app?.label;
    //       if (!orgNature) {
    //         const userStore = useUserStore();
    //         orgNature = userStore.getUserNature();
    //       }
    //       return getGradesListOptions(orgNature);
    //     },
    //   },
    //   listProps: {
    //     columnWidth: 140,
    //   },
    // },
    trainingForm: {
      valueType: DataTypes.Enum.name,
      inputWidgetProps: {
        allowCreate: true,
        options: ['个训', '小组'],
      },
    },
    coverImage: {
      visibleInForm: false,
      inputWidget: 'uploadInput',
    },
    orgNature: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    teachingAssessScores: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
  },
};

export default { rehabCourse };
