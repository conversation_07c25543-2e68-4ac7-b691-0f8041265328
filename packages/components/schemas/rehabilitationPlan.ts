import { CustomSchema } from '@repo/infrastructure/types';
import { getClientRole, PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';
import { defineAsyncComponent } from 'vue';
import { getCurrentPeriod, getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { getCollaborateRowAction, getCollaboratorField, getDataSubmitAction } from '../src/utils/collaborate';

const clientRole = getClientRole();

const studentFieldInTeacher = {
  dataType: 'Foreign',
  foreignField: {
    api: '/resourceRoom/student',
    preload: true,
    apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
    loadPageSize: 50,
  },
  inputWidgetProps: {
    valueType: (val) => {
      return { id: val };
    },
    allowSearch: true,
  },
  displayProps: {
    component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
  },
};

const studentInAdmin = {
  listProps: {
    columnWidth: 120,
  },
  displayProps: {
    component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
  },
  inputWidgetProps: {
    options: [],
    valueType: (value) => {
      return { id: value };
    },
    disabled: true,
    queryDepends: {
      onRehabilitationInstitutionChange: async (value, a, formData, isInit) => {
        if (!isInit) {
          formData.value.student = undefined;
        }
        const { data } = await request('/resourceRoom/student', {
          params: {
            'rehabilitationInstitutionId|homeDeliveryInstitutionId': value.id,
          },
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });

        const options = data.items.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));

        return {
          inputWidgetProps: {
            options,
            disabled: false,
          },
        };
      },
    },
  },
};

const rehabilitationPlanSchema: CustomSchema = {
  api: '/rehabilitation/rehabilitationPlan',
  rowActions: [
    { key: 'targets', label: '计划目标', expose: true, handler() {} },
    getCollaborateRowAction('RehabilitationPlan'),
    getDataSubmitAction('/rehabilitation/rehabilitationPlan'),
  ],
  listViewProps: {
    rowActionWidth: 250,
  },
  detailViewProps: {
    columns: 2,
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: [
          'rehabilitationInstitution',
          // 'rehabilitationEmployee',
          'student',
          'dateRange',
          'expectResult',
          'advice',
          'remark',
        ],
      },
      {
        label: '计划目标',
        columns: 1,
        fields: ['targets'],
      },
    ],
  },
  fieldsMap: {
    ...getCollaboratorField(),
    studentIds: {
      visibleInForm: false,
    },
    targets: {
      key: 'targets',
      visibleInTable: false,
      visibleInForm: false,
      label: '',
      displayProps: {
        component: defineAsyncComponent(() => import('../src/rehabilitation/targetsInRehabDetail.vue')),
      },
    },
    institution: {
      visibleInForm: false,
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
    },
    // employee: {
    //   listProps: {
    //     columnWidth: 120,
    //   },
    //   displayProps: {
    //     component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
    //     mode: 'capsule',
    //   },
    //   foreignField: {
    //     api: '/org/companyUser',
    //     apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
    //     preload: true,
    //   },
    //   inputWidgetProps: {
    //     valueType: (value, options) => {
    //       const selected = options.find((item) => item.value === value);
    //       return {
    //         id: selected.value,
    //         name: selected.label,
    //       };
    //     },
    //   },
    //   // inputWidgetProps: {
    //   //   options: [],
    //   //   valueType: (value) => {
    //   //     return { id: value };
    //   //   },
    //   //   disabled: true,
    //   //   queryDepends: {
    //   //     onRehabilitationInstitutionChange: async (value, a, formData, isInit) => {
    //   //       if (!isInit) {
    //   //         formData.value.rehabilitationEmployee = undefined;
    //   //       }
    //   //
    //   //       const { data } = await request('/rehabilitation/rehabilitationEmployee', {
    //   //         params: {
    //   //           rehabilitationInstitution: value.id,
    //   //         },
    //   //         baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    //   //       });
    //   //
    //   //       const options = data.items.map((item: any) => ({
    //   //         label: item.name,
    //   //         value: item.id,
    //   //       }));
    //   //
    //   //       return {
    //   //         inputWidgetProps: {
    //   //           options,
    //   //           disabled: false,
    //   //         },
    //   //       };
    //   //     },
    //   //   },
    //   // },
    // },
    student: clientRole === 'Manager' ? studentInAdmin : studentFieldInTeacher,
    dateRange: {
      displayProps: {
        toDisplay: (value) => {
          return value ? value.map((item) => item.substring(0, 10)).join(' 至 ') : '';
        },
      },
      inputWidget: 'dateRangeInput',
    },
    gradePeriod: {
      inputWidget: 'selectInput',
      defaultValue: getCurrentPeriod(),
      inputWidgetProps: {
        options: getPeriodsList(),
      },
    },
    expectResult: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    advice: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    remark: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
  },
};

export default { rehabilitationPlanSchema };
