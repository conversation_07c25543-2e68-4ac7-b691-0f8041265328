import { defineAsyncComponent } from 'vue';
import { CustomSchema } from '@repo/infrastructure/types';
import dayjs from 'dayjs';
import useCommonStore from '@repo/infrastructure/utils/store';

const AssetApplicationSchema: CustomSchema = {
  api: '/asset/assetBorrowApplication',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    placeholder: '按借用人搜索',
    fields: ['cbName'],
  },
  listViewProps: {
    searchType: 'column',
  },
  rowActions: [
    {
      key: 'submit',
      label: '提交审核',
      handler: () => {},
      visible(record) {
        return record.status === 'NotSubmitted';
      },
    },
    {
      key: 'withdraw',
      label: '撤回申请',
      handler: () => {},
      visible(record) {
        return record.status === 'Waiting';
      },
    },
    {
      key: 'approve',
      label: '同意申请',
      permNode: 'center:dailyWork:asset:applicationVerify',
      handler: () => {},
      visible(record) {
        return record.status === 'Waiting';
      },
    },
    {
      key: 'reject',
      label: '拒绝申请',
      permNode: 'center:dailyWork:asset:applicationVerify',
      handler: () => {},
      visible(record) {
        return record.status === 'Waiting';
      },
    },
    {
      key: 'outbound',
      label: '领用出库',
      permNode: 'center:dailyWork:asset:applicationVerify',
      handler: () => {},
      visible(record) {
        return record.status === 'Approved';
      },
    },
  ],
  fieldsMap: {
    planBorrowDate: {
      defaultValue: dayjs().format('YYYY-MM-DD'),
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    details: {
      visibleInTable: false,
      // eslint-disable-next-line import/no-unresolved
      // @ts-ignore
      inputWidget: defineAsyncComponent(() => import('../src/asset/assetBorrowDetailEdit.vue')),
      displayProps: {
        // eslint-disable-next-line import/no-unresolved
        // @ts-ignore
        component: defineAsyncComponent(() => import('../src/asset/assetBorrowDetailDisplay.vue')) as any,
        detailSpan: 2,
      },
    },
    borrowCount: {
      listProps: {
        columnWidth: 100,
      },
    },
    operator: {
      displayProps: {
        detailSpan: 2,
      },
    },
    status: {
      listProps: {
        columnWidth: 80,
      },
    },
    unreturnedCount: {
      listProps: {
        columnWidth: 120,
      },
    },
    cbUserBoName: {
      listProps: {
        columnWidth: 220,
      },
    },
  },
};

export default { AssetApplicationSchema };
