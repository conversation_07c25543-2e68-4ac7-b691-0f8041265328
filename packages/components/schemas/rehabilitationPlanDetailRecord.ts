import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const rehabilitationInstitution: CustomSchema = {
  api: '/rehabilitation/rehabilitationPlanTrainingRecord',
  fieldsMap: {
    rehabilitationPlanDetailId: {
      visibleInDetail: false,
      visibleInTable: false,
      visibleInForm: false,
    },
    content: {
      inputWidget: 'textareaInput',
    },
    employee: {
      inputWidget: 'selectInput',
      dataType: 'Number',
      valueType: 'Number',
      foreignField: undefined,
      inputWidgetProps: {
        valueType: (value) => {
          return { id: value };
        },
        getOptions: async (formData) => {
          console.log(formData);
        },
        allowSearch: true,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
    },
  },
};

export default { rehabilitationInstitution };
