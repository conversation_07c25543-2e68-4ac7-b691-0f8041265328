import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const displaySetting = (item: any) => {
  switch (item) {
    case 'easy':
      return '初级';
    case 'medium':
      return '中级';
    case 'hard':
      return '高级';
    default:
      return '';
  }
};

const attentionTrainingSchema: CustomSchema = {
  api: '/attentionTraining/stopSignal',
  modalEdit: true,
  // collaborate: true,
  quickSearchProps: {
    enabled: true,
    fields: ['student.name'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 160,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    title: (raw: any) => {
      if (!raw) {
        return '训练记录';
      }
      return `${raw?.student.name}的记录（停止信号${displaySetting(raw.setting?.difficulty)}）`;
    },
    fieldsGrouping: [
      {
        columns: 1,
        fields: ['stroopTaskDisplay'],
      },
    ],
  },
  formViewProps: {},
  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
    /* {
      key: 'assessment',
      label: '历史数据',
      expose: false,
      handler() {},
    }, */
    {
      key: 'training',
      label: '训练',
      expose: true,
      handler() {},
    },
  ],
  fieldsMap: {
    setting: {
      key: 'setting',
      label: '次数/停止信号频率/声音/最小间隔/最大间隔/Go刺激呈现时间(ms)',
    },
    trainingRecord: {},
    student: {
      listProps: {
        columnWidth: 150,
      },
    },
    disorders: {
      key: 'student.disorders',
      label: '障碍类型',
    },
    age: {
      label: '年龄',
      key: 'student.age',
    },
    gender: { key: 'student.gender', label: '性别' },
    stroopTaskDisplay: {
      key: 'stroopTaskDisplay',
      label: '',
      displayProps: {
        component: defineAsyncComponent(
          () => import('../src/student/components/attentionTraining/stopSignalDisplay.vue'),
        ),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
  },
};

export default { attentionTrainingSchema };
