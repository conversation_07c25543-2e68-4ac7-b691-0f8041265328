import { CustomSchema, SchemaField } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const displaySetting = (item: any) => {
  switch (item) {
    case 'easy':
      return '初级';
    case 'medium':
      return '中级';
    case 'hard':
      return '高级';
    default:
      return '';
  }
};

const attentionTrainingSchema: CustomSchema = {
  api: '/attentionTraining/stroopEffect',
  modalEdit: true,
  // collaborate: true,
  quickSearchProps: {
    enabled: true,
    fields: ['student.name'],
    multiple: false,
    placeholder: '输入搜索关键字',
  },
  listViewProps: {
    rowActionWidth: 160,
  },
  detailViewProps: {
    showOthers: false,
    showFresh: true,
    columns: 2,
    title: (raw: any) => {
      if (!raw) {
        return '训练记录';
      }
      return `${raw?.student.name}的记录（斯特鲁普${displaySetting(raw.setting.difficulty)}）`;
    },
    fieldsGrouping: [
      {
        columns: 1,
        fields: ['stroopTaskDisplay'],
      },
    ],
  },
  formViewProps: {},
  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
    /* {
      key: 'assessment',
      label: '历史数据',
      expose: false,
      handler() {},
    }, */
    {
      key: 'training',
      label: '训练',
      expose: true,
      handler() {},
      visible(record) {
        return record?.trainingRecord.length > 0;
      },
    },
  ],
  fieldsMap: {
    setting: {
      key: 'setting',
      label: '模式/难度/数量/显示时间/对比度',
    },
    trainingRecord: {},
    student: {
      listProps: {
        columnWidth: 150,
      },
    },
    disorders: {
      key: 'student.disorders',
      label: '障碍类型',
    },
    age: {
      label: '年龄',
      key: 'student.age',
    },
    gender: { key: 'student.gender', label: '性别' },
    stroopTaskDisplay: {
      key: 'stroopTaskDisplay',
      label: '',
      displayProps: {
        component: defineAsyncComponent(
          () => import('../src/student/components/attentionTraining/stroopTaskDisplay.vue'),
        ),
      },
      visibleInTable: false,
      visibleInForm: false,
    },
  },
};

export default { attentionTrainingSchema };
