import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';

const rehabilitationAssessment: CustomSchema = {
  api: '/rehabilitation/rehabilitationAssessment',
  fieldsMap: {
    institution: {
      // dataType: 'Foreign',
      // foreignField: {
      //   api: '/rehabilitation/fusionSchool',
      //   preload: true,
      //   apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
      // },
      // inputWidgetProps: {
      //   disabled: true,
      //   valueType: (val) => {
      //     return { id: val };
      //   },
      //   allowSearch: true,
      // },
      visibleInForm: false,
    },
    assessmentDate: {
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    participants: {
      displayProps: {
        detailSpan: 2,
      },
    },
    student: {
      dataType: 'Foreign',
      foreignField: {
        api: '/resourceRoom/student',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        loadPageSize: 50,
      },
      inputWidgetProps: {
        valueType: (val) => {
          return { id: val };
        },
        allowSearch: true,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    results: {
      inputWidget: 'textareaInput',
      maxLength: 9999,
      displayProps: {
        detailSpan: 2,
      },
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
        detailSpan: 2,
      },
    },
  },
};

export default { rehabilitationAssessment };
