import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent, Ref } from 'vue';
import { request } from '@repo/infrastructure/request';
import { CustomSchema } from '@repo/infrastructure/types';
import { useTeacherStore } from '@repo/infrastructure/store';
import { getAdminClientNature } from '../src/utils/utils';

const getDisplayName = (grade, className) => {
  const res = [];
  if (grade) {
    res.push(grade.name);
  }
  if (className) {
    res.push(className);
  }
  return res.join('');
};

const gradeClassSchema: CustomSchema = {
  api: '/resourceRoom/gradeClass',
  modalEdit: true,
  rowActions: [
    {
      key: 'teacherListMaintain',
      label: '任课教师维护',
      handler() {},
    },
  ],
  fieldsMap: {
    grade: {
      inputWidgetProps: {
        disabled: true,
        labelField: 'name',
        valueField: 'id',
        valueType(value, computedOptions) {
          const raw = computedOptions?.find((item) => item.raw?.id === value);
          return {
            id: value,
            name: raw.raw.name,
            school: raw.raw.school,
          };
        },
        queryDepends: {
          async onBoIdChange(boId: any, inputValue: Ref<any>, formData: any, isInit: boolean) {
            if (!isInit) {
              inputValue.value = undefined;
            }

            if (boId) {
              const { data: school } = await request('/resourceCenter/fusionSchool', {
                params: {
                  branchOfficeId: boId,
                },
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });
              if (!school.items?.length) {
                return {
                  disabled: true,
                  options: [],
                };
              }

              const { data } = await request('/teacher/schoolGrade', {
                params: {
                  school: school.items[0].id,
                  orgNature: getAdminClientNature(),
                },
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              return {
                inputWidgetProps: {
                  placeholder: data.items?.length ? '请选择年级' : '未找到年级信息',
                  disabled: false,
                  options: data.items || [],
                },
              };
            }
            return {
              inputWidgetProps: {
                placeholder: '',
                disabled: true,
                options: [],
              },
            };
          },
        },
      },
    },
    boId: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        valueField: 'id',
        labelField: 'name',
        allowSearch: true,
        async getOptions() {
          const { data } = await request('/org/branchOffice', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            params: {
              name: '%_%',
              nature: getAdminClientNature(),
            },
          });

          return data.items || [];
        },
      },
    },
    fusionSchool: {
      visibleInForm: false,
    },
    graduated: {
      key: 'graduated',
      label: '毕业情况',
      visibleInForm: false,
      inputWidgetProps: {
        options: [
          {
            label: '未毕业',
            value: true,
          },
          {
            label: '已毕业',
            value: false,
          },
        ],
      },
      displayProps: {
        component: 'BooleanDisplay',
        toDisplay(val, record) {
          return !record?.grade?.graduate;
        },
      },
    },
    managerUser: {
      foreignField: {
        api: undefined,
      },
      inputWidgetProps: {
        labelField: 'name',
        valueField: 'id',
        allowSearch: true,
        valueType(id) {
          return {
            id,
          };
        },
        queryDepends: {
          async onBoIdChange(boId: any) {
            if (boId) {
              const teacherStore = useTeacherStore();
              const teachers = await teacherStore.getTeachersList();

              return {
                inputWidgetProps: {
                  placeholder: teachers.length ? '请选择班主任' : '当前学校无用户',
                  disabled: false,
                  options: teachers || [],
                },
              };
            }
            return {
              inputWidgetProps: {
                placeholder: '',
                disabled: true,
                options: [],
              },
            };
          },
        },
      },
      displayProps: {
        detailSpan: 2,
        toDisplay: (val, record) => {
          if (record.teacherList?.length) {
            const teachers = record.teacherList
              .filter((item) => item?.name && item?.fixed && item?.name !== val?.name)
              .map((teacher) => teacher.name)
              .join(' ');
            return `${val?.name} (${teachers})`;
          }
          return val?.name;
        },
      },
    },
    teacherList: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/student/gradeClassTeacherList.vue')),
      },
    },
    name: {
      inputWidgetProps: {
        queryDepends: {
          onGradeChange(grade, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.grade, formData.value.className);
          },
          onClassNameChange(grade, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.grade, formData.value.className);
          },
        },
      },
    },
    orgNature: {
      defaultValue: getAdminClientNature,
      visibleInForm: false,
    },
  },
};

export default { gradeClassSchema };
