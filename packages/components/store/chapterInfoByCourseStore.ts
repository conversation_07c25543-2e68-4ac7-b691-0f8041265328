import { defineStore } from 'pinia';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

const useChapterStore = defineStore('chapter', {
  state: (): any => ({
    // 缓存章节信息，以 courseId 为键
    chaptersMap: {},
  }),
  actions: {
    /**
     * 根据 courseId 获取章节信息
     * @param courseId - 课程 ID
     * @param fresh - 是否强制刷新缓存
     * @returns 章节列表
     */
    async getChaptersByCourseId(courseId: string, fresh?: boolean) {
      // 如果缓存中存在且不需要强制刷新，则直接返回缓存数据
      if (!fresh && this.chaptersMap[courseId]) {
        return this.chaptersMap[courseId];
      }
      // 从接口获取章节信息
      const { data } = await request('/course/chapter', {
        method: 'GET',
        params: {
          courseId,
        },
      });

      // 更新缓存
      this.chaptersMap[courseId] = data || [];

      return this.chaptersMap[courseId];
    },

    /**
     * 获取章节信息的映射（以 chapterId 为键）
     * @param courseId - 课程 ID
     * @param fresh - 是否强制刷新缓存
     * @returns 章节信息的映射
     */
    async getChaptersMapByCourseId(courseId: string, fresh?: boolean) {
      const chapters = await this.getChaptersByCourseId(courseId, fresh);
      return chapters.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {});
    },

    /**
     * 刷新缓存
     * @param courseId - 课程 ID
     */
    async refresh(courseId: string) {
      await this.getChaptersByCourseId(courseId, true);
    },

    /**
     * 添加章节信息
     * @param courseId - 课程 ID
     * @param chapter - 章节信息
     */
    async addChapter(courseId: string, chapter: any) {
      if (this.chaptersMap[courseId]) {
        this.chaptersMap[courseId].push(chapter);
      } else {
        this.chaptersMap[courseId] = [chapter];
      }
    },

    /**
     * 清空缓存
     */
    clearCache() {
      this.chaptersMap = {};
    },
  },
});

export default useChapterStore;
