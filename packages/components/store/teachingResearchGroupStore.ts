import { defineStore } from 'pinia';
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';

const useTeachingResearchGroupStore = defineStore('teachingResearchGroup', {
  state: () => ({
    teachingResearchGroups: [],
  }),
  actions: {
    async getTeachingResearchGroups() {
      if (!this.teachingResearchGroups.length) {
        await this.init();
      }
      return this.teachingResearchGroups;
    },
    async getTeachingResearchGroupsMap() {
      const lists = await this.getTeachingResearchGroups();
      return lists.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {});
    },
    async init() {
      const { data: teachingResearchGroups } = await request('/teacher/teachingResearchGroup', {
        method: 'GET',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          pageSize: 999,
        },
      });

      this.$patch({ teachingResearchGroups: teachingResearchGroups.items || [] });
    },
    async onAdd(data: any) {
      this.teachingResearchGroups.shift(data);
    },
    async refresh() {
      await this.init();
    },
  },
});

export default useTeachingResearchGroupStore;
