import { defineStore } from 'pinia';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';

const useSchoolCourseStore = defineStore('schoolCourse', {
  state: (): any => ({
    schoolCoursesMap: {},
  }),
  actions: {
    async getSchoolCourses(fresh?: boolean) {
      const menuStore = useUserMenuStore();
      const menuInfo = menuStore.getCurrentMenuInfo();
      let orgNature = '';
      if (menuInfo.app?.label) {
        orgNature = menuInfo.app.label;
      } else {
        const userStore = useUserStore();
        orgNature = userStore.getUserNature();
      }

      if (fresh === true || !this.schoolCoursesMap[orgNature]) {
        await this.init(orgNature);
      }
      return this.schoolCoursesMap[orgNature];
    },
    async getSchoolCoursesMap(fresh?: boolean) {
      const lists = await this.getSchoolCourses(fresh);
      return lists.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {});
    },
    async init(orgNature: string) {
      const { data: courses } = await request('/teacher/schoolCourse', {
        method: 'GET',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          pageSize: 999,
          orgNature,
        },
      });

      const map = this.schoolCoursesMap;
      map[orgNature] = courses.items || [];

      this.$patch({
        schoolCoursesMap: map,
      });
    },
    async getAll() {
      const data = await this.getSchoolCourses();
      return data;
    },
    async onAdd(data: any) {
      if (data.orgNature) {
        this.schoolCoursesMap[data.orgNature] = [...(this.schoolCoursesMap[data.orgNature] || []), data];
      }
    },
    async refresh() {
      await this.init();
    },
  },
});

export default useSchoolCourseStore;
