import { defineStore } from 'pinia';
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';

const useMyGradeClassStore = defineStore('myGradeClass', {
  state() {
    return {
      gradeClasses: [],
    };
  },
  actions: {
    async getGradeClassList() {
      if (this.gradeClasses.length > 0) {
        return this.gradeClasses;
      }
      const { data } = await request('/resourceRoom/gradeClass', {
        params: {
          pageSize: 999,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      this.gradeClasses = data.items?.map((item) => {
        return {
          ...item,
        };
      });

      return this.gradeClasses;
    },
    async getGradeClassMap() {
      const list = await this.getGradeClassList();
      return list.reduce((acc, item) => {
        acc[item.id] = item;
        return acc;
      }, {});
    },
    async getGradeClassOptions() {
      const list = await this.getGradeClassList();
      return list.map((item) => {
        return {
          value: item.id,
          label: item.name,
          ...item,
        };
      });
    },
  },
});

export default useMyGradeClassStore;
