#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=44380, tid=3904
#
# JRE version: OpenJDK Runtime Environment JBR-17.0.11*******.30-jcef (17.0.11+1) (build 17.0.11+1-b1207.30)
# Java VM: OpenJDK 64-Bit Server VM JBR-17.0.11*******.30-jcef (17.0.11+1-b1207.30, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 

Host: Intel(R) Core(TM) i5-10400F CPU @ 2.90GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Thu May 29 11:28:07 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.679994 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001fe4db6e400):  JavaThread "main" [_thread_in_vm, id=3904, stack(0x000000fa7ac00000,0x000000fa7ad00000)]

Stack: [0x000000fa7ac00000,0x000000fa7ad00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6877f9]
V  [jvm.dll+0x8411aa]
V  [jvm.dll+0x842e2e]
V  [jvm.dll+0x843493]
V  [jvm.dll+0x249fdf]
V  [jvm.dll+0x83c03b]
V  [jvm.dll+0x62bc86]
V  [jvm.dll+0x62bcea]
V  [jvm.dll+0x62e51a]
V  [jvm.dll+0x62c626]
V  [jvm.dll+0x23dbef]
V  [jvm.dll+0x6364b5]
V  [jvm.dll+0x1ece6b]
V  [jvm.dll+0x1ed3a5]
V  [jvm.dll+0x1edd45]
V  [jvm.dll+0x1e31fc]
V  [jvm.dll+0x54c22c]
V  [jvm.dll+0x1f3d7b]
V  [jvm.dll+0x7b975b]
V  [jvm.dll+0x7babc9]
V  [jvm.dll+0x7baf18]
V  [jvm.dll+0x241a0e]
V  [jvm.dll+0x241c5f]
V  [jvm.dll+0x56a538]
V  [jvm.dll+0x56d4b7]
V  [jvm.dll+0x38a022]
V  [jvm.dll+0x38968f]
C  0x000001fe5883a623

The last pc belongs to invokestatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  sun.security.jca.ProviderList.<init>()V+45 java.base@17.0.11
j  sun.security.jca.ProviderList$2.run()Lsun/security/jca/ProviderList;+4 java.base@17.0.11
j  sun.security.jca.ProviderList$2.run()Ljava/lang/Object;+1 java.base@17.0.11
j  java.security.AccessController.executePrivileged(Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;+29 java.base@17.0.11
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;+5 java.base@17.0.11
j  sun.security.jca.ProviderList.fromSecurityProperties()Lsun/security/jca/ProviderList;+7 java.base@17.0.11
j  sun.security.jca.Providers.<clinit>()V+16 java.base@17.0.11
v  ~StubRoutines::call_stub
j  sun.security.jca.GetInstance.getInstance(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/String;)Lsun/security/jca/GetInstance$Instance;+0 java.base@17.0.11
j  javax.net.ssl.SSLContext.getInstance(Ljava/lang/String;)Ljavax/net/ssl/SSLContext;+12 java.base@17.0.11
j  externalApp.ExternalAppUtil.sendIdeRequest(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)LexternalApp/ExternalAppUtil$Result;+34
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+37
v  ~StubRoutines::call_stub
invokestatic  184 invokestatic  [0x000001fe5883a580, 0x000001fe5883a860]  736 bytes
[MachCode]
  0x000001fe5883a580: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001fe5883a5a0: 4424 0800 | 0000 00eb | 0150 4c89 | 6dc0 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d120 c1eb 
  0x000001fe5883a5c0: 1081 e3ff | 0000 0081 | fbb8 0000 | 000f 84b6 | 0000 00bb | b800 0000 | e805 0000 | 00e9 9b00 
  0x000001fe5883a5e0: 0000 488b | d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0200 0049 | 8987 9802 
  0x000001fe5883a600: 0000 4883 | ec20 f7c4 | 0f00 0000 | 0f84 1a00 | 0000 4883 | ec08 49ba | 2096 4e62 | fa7f 0000 
  0x000001fe5883a620: 41ff d248 | 83c4 08e9 | 0d00 0000 | 49ba 2096 | 4e62 fa7f | 0000 41ff | d248 83c4 | 2049 c787 
  0x000001fe5883a640: 9802 0000 | 0000 0000 | 49c7 87a8 | 0200 0000 | 0000 0049 | c787 a002 | 0000 0000 | 0000 c5f8 
  0x000001fe5883a660: 7749 817f | 0800 0000 | 000f 8405 | 0000 00e9 | 8c68 feff | 4c8b 6dc0 | 4c8b 75c8 | c341 0fb7 
  0x000001fe5883a680: 5501 488b | 4dd0 c1e2 | 0248 8b5c | d128 488b | 5b08 488b | 5b08 488b | 5b18 80bb | 5b01 0000 
  0x000001fe5883a6a0: 040f 840d | 0000 004c | 3bbb 6001 | 0000 0f85 | 1fff ffff | 488b 5cd1 | 288b 54d1 | 38c1 ea1c 
  0x000001fe5883a6c0: 49ba 5066 | d162 fa7f | 0000 498b | 14d2 5248 | 8b45 d848 | 85c0 0f84 | 1200 0000 | 4883 4008 
  0x000001fe5883a6e0: 0148 8358 | 0800 4883 | c010 4889 | 45d8 488b | 45d8 4885 | c00f 8457 | 0100 0080 | 78f0 0a0f 
  0x000001fe5883a700: 854d 0100 | 0048 83c0 | 084c 8b68 | f841 83ed | 0041 83fd | 020f 8c2c | 0100 004c | 8b6b 0845 
  0x000001fe5883a720: 0fb7 6d2c | 4c2b 2841 | 83ed 014e | 8b6c ec08 | 4d85 ed75 | 1248 f740 | 0801 0000 | 0075 61f0 
  0x000001fe5883a740: 4883 4808 | 01eb 5945 | 8b6d 0849 | ba00 0000 | 0008 0000 | 004d 03ea | 4d8b d54c | 3368 0849 
  0x000001fe5883a760: f7c5 fcff | ffff 7438 | 49f7 c502 | 0000 0075 | 2f48 8178 | 0800 0000 | 0074 2148 | 8178 0801 
  0x000001fe5883a780: 0000 0074 | 174d 8bea | 4c33 6808 | 49f7 c5fc | ffff ff74 | 0b48 8348 | 0802 eb04 | 4c89 6808 
  0x000001fe5883a7a0: 4883 c010 | 4c8b 68e8 | 4183 ed02 | 4183 fd02 | 0f8c 9100 | 0000 4c8b | 6b08 450f | b76d 2c4c 
  0x000001fe5883a7c0: 2b28 4183 | ed01 4e8b | 6cec 084d | 85ed 7512 | 48f7 4008 | 0100 0000 | 7561 f048 | 8348 0801 
  0x000001fe5883a7e0: eb59 458b | 6d08 49ba | 0000 0000 | 0800 0000 | 4d03 ea4d | 8bd5 4c33 | 6808 49f7 | c5fc ffff 
  0x000001fe5883a800: ff74 3849 | f7c5 0200 | 0000 752f | 4881 7808 | 0000 0000 | 7421 4881 | 7808 0100 | 0000 7417 
  0x000001fe5883a820: 4d8b ea4c | 3368 0849 | f7c5 fcff | ffff 740b | 4883 4808 | 02eb 044c | 8968 0848 | 83c0 104c 
  0x000001fe5883a840: 8b68 d841 | 83ed 0441 | c1e5 0349 | 03c5 4889 | 45d8 4c8d | 6c24 084c | 896d f0ff | 6360 6690 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001fe766d50d0, length=12, elements={
0x000001fe4db6e400, 0x000001fe76517740, 0x000001fe765184d0, 0x000001fe76576550,
0x000001fe76576f80, 0x000001fe76543fd0, 0x000001fe765448a0, 0x000001fe7658fcf0,
0x000001fe765a8dd0, 0x000001fe765b1ad0, 0x000001fe766f8880, 0x000001fe76728da0
}

Java Threads: ( => current thread )
=>0x000001fe4db6e400 JavaThread "main" [_thread_in_vm, id=3904, stack(0x000000fa7ac00000,0x000000fa7ad00000)]
  0x000001fe76517740 JavaThread "Reference Handler" daemon [_thread_blocked, id=20292, stack(0x000000fa7b400000,0x000000fa7b500000)]
  0x000001fe765184d0 JavaThread "Finalizer" daemon [_thread_blocked, id=22404, stack(0x000000fa7b500000,0x000000fa7b600000)]
  0x000001fe76576550 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20956, stack(0x000000fa7b600000,0x000000fa7b700000)]
  0x000001fe76576f80 JavaThread "Attach Listener" daemon [_thread_blocked, id=20632, stack(0x000000fa7b700000,0x000000fa7b800000)]
  0x000001fe76543fd0 JavaThread "Service Thread" daemon [_thread_blocked, id=38528, stack(0x000000fa7b800000,0x000000fa7b900000)]
  0x000001fe765448a0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=11024, stack(0x000000fa7b900000,0x000000fa7ba00000)]
  0x000001fe7658fcf0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=32912, stack(0x000000fa7ba00000,0x000000fa7bb00000)]
  0x000001fe765a8dd0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=10556, stack(0x000000fa7bb00000,0x000000fa7bc00000)]
  0x000001fe765b1ad0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=39764, stack(0x000000fa7bc00000,0x000000fa7bd00000)]
  0x000001fe766f8880 JavaThread "Notification Thread" daemon [_thread_blocked, id=14368, stack(0x000000fa7bd00000,0x000000fa7be00000)]
  0x000001fe76728da0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=29580, stack(0x000000fa7bf00000,0x000000fa7c000000)]

Other Threads:
  0x000001fe739b8ac0 VMThread "VM Thread" [stack: 0x000000fa7b300000,0x000000fa7b400000] [id=40212]
  0x000001fe76627d90 WatcherThread [stack: 0x000000fa7be00000,0x000000fa7bf00000] [id=9200]
  0x000001fe4dc1cfe0 GCTaskThread "GC Thread#0" [stack: 0x000000fa7ae00000,0x000000fa7af00000] [id=43608]
  0x000001fe4dc1de10 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000fa7af00000,0x000000fa7b000000] [id=33404]
  0x000001fe737d2040 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000fa7b000000,0x000000fa7b100000] [id=44844]
  0x000001fe738b3100 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000fa7b100000,0x000000fa7b200000] [id=44752]
  0x000001fe738b3a40 ConcurrentGCThread "G1 Service" [stack: 0x000000fa7b200000,0x000000fa7b300000] [id=10904]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001fe4db69510] Metaspace_lock - owner thread: 0x000001fe4db6e400

Heap address: 0x0000000601000000, size: 8176 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 32700M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8176M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 0K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 5204K, committed 5312K, reserved 1114112K
  class space    used 457K, committed 512K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000601000000, 0x0000000601000000, 0x0000000601400000|  0%| F|  |TAMS 0x0000000601000000, 0x0000000601000000| Untracked 
|   1|0x0000000601400000, 0x0000000601400000, 0x0000000601800000|  0%| F|  |TAMS 0x0000000601400000, 0x0000000601400000| Untracked 
|   2|0x0000000601800000, 0x0000000601800000, 0x0000000601c00000|  0%| F|  |TAMS 0x0000000601800000, 0x0000000601800000| Untracked 
|   3|0x0000000601c00000, 0x0000000601c00000, 0x0000000602000000|  0%| F|  |TAMS 0x0000000601c00000, 0x0000000601c00000| Untracked 
|   4|0x0000000602000000, 0x0000000602000000, 0x0000000602400000|  0%| F|  |TAMS 0x0000000602000000, 0x0000000602000000| Untracked 
|   5|0x0000000602400000, 0x0000000602400000, 0x0000000602800000|  0%| F|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|   6|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|   7|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|   8|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|   9|0x0000000603400000, 0x0000000603400000, 0x0000000603800000|  0%| F|  |TAMS 0x0000000603400000, 0x0000000603400000| Untracked 
|  10|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|  11|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|  12|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|  13|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|  14|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|  15|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|  16|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  17|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  18|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  19|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  20|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  21|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  22|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  23|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  24|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  25|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  26|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  27|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  28|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  29|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  30|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  31|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  32|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  33|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  34|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  35|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  36|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  37|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  38|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  39|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  40|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  41|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  42|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  43|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  44|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  45|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  46|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  47|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  48|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  49|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  50|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  51|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  52|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  53|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  54|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  55|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  56|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  57|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  58|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  59|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  60|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  61|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  62|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  63|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  64|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  65|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  66|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  67|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  68|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  69|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  70|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  71|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  72|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  73|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  74|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  75|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  76|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  77|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  78|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  79|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  80|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  81|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  82|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  83|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  84|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  85|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  86|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  87|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  88|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  89|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  90|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  91|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  92|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  93|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  94|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  95|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  96|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  97|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  98|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  99|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
| 100|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
| 101|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
| 102|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
| 103|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
| 104|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
| 105|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
| 106|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
| 107|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
| 108|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
| 109|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
| 110|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
| 111|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
| 112|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
| 113|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 114|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 115|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 116|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 117|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 118|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 119|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 120|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 121|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 122|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 123|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 124|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 125|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 126|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 127|0x0000000620c00000, 0x0000000620deb9a8, 0x0000000621000000| 48%| E|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Complete 

Card table byte_map: [0x000001fe61840000,0x000001fe62840000] _byte_map_base: 0x000001fe5e838000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001fe4dc1d510, (CMBitMap*) 0x000001fe4dc1d550
 Prev Bits: [0x000001fe63840000, 0x000001fe6b800000)
 Next Bits: [0x000001fe6b800000, 0x000001fe737c0000)

Polling page: 0x000001fe4bac0000

Metaspace:

Usage:
  Non-class:      4.64 MB used.
      Class:    457.09 KB used.
       Both:      5.08 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       4.69 MB (  7%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     512.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.99 MB
       Class:  15.53 MB
        Both:  26.52 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 4.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 83.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 11.
num_chunk_merges: 0.
num_chunk_splits: 5.
num_chunks_enlarged: 1.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=15Kb max_used=15Kb free=119984Kb
 bounds [0x000001fe58dc0000, 0x000001fe59030000, 0x000001fe602f0000]
CodeHeap 'profiled nmethods': size=120000Kb used=141Kb max_used=141Kb free=119858Kb
 bounds [0x000001fe512f0000, 0x000001fe51560000, 0x000001fe58820000]
CodeHeap 'non-nmethods': size=5760Kb used=1077Kb max_used=1077Kb free=4682Kb
 bounds [0x000001fe58820000, 0x000001fe58a90000, 0x000001fe58dc0000]
 total_blobs=441 nmethods=97 adapters=257
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.624 Thread 0x000001fe765a8dd0   88       3       java.lang.CharacterData::of (136 bytes)
Event: 0.624 Thread 0x000001fe765a8dd0 nmethod 88 0x000001fe5130d790 code [0x000001fe5130d9c0, 0x000001fe5130e358]
Event: 0.624 Thread 0x000001fe765a8dd0   89       3       java.lang.CharacterDataLatin1::toUpperCase (53 bytes)
Event: 0.624 Thread 0x000001fe765a8dd0 nmethod 89 0x000001fe5130e590 code [0x000001fe5130e740, 0x000001fe5130e9d8]
Event: 0.624 Thread 0x000001fe765a8dd0   90       3       java.lang.Character::toUpperCase (6 bytes)
Event: 0.625 Thread 0x000001fe765a8dd0 nmethod 90 0x000001fe5130eb10 code [0x000001fe5130ecc0, 0x000001fe5130ef18]
Event: 0.625 Thread 0x000001fe765a8dd0   91       3       java.lang.Character::toUpperCase (9 bytes)
Event: 0.625 Thread 0x000001fe765a8dd0 nmethod 91 0x000001fe5130f010 code [0x000001fe5130f1c0, 0x000001fe5130f3b8]
Event: 0.625 Thread 0x000001fe765a8dd0   96       3       java.lang.ProcessEnvironment$NameComparator::compare (90 bytes)
Event: 0.625 Thread 0x000001fe765a8dd0 nmethod 96 0x000001fe5130f490 code [0x000001fe5130f7a0, 0x000001fe513109f8]
Event: 0.625 Thread 0x000001fe765a8dd0   92       3       java.util.TreeMap::parentOf (13 bytes)
Event: 0.626 Thread 0x000001fe765a8dd0 nmethod 92 0x000001fe51311290 code [0x000001fe51311420, 0x000001fe51311598]
Event: 0.626 Thread 0x000001fe765a8dd0   95       3       java.lang.ProcessEnvironment$NameComparator::compare (13 bytes)
Event: 0.626 Thread 0x000001fe765a8dd0 nmethod 95 0x000001fe51311690 code [0x000001fe51311840, 0x000001fe51311b68]
Event: 0.626 Thread 0x000001fe765a8dd0   93       1       java.util.HashMap$Node::getKey (5 bytes)
Event: 0.626 Thread 0x000001fe765a8dd0 nmethod 93 0x000001fe58dc3710 code [0x000001fe58dc38a0, 0x000001fe58dc3978]
Event: 0.626 Thread 0x000001fe765a8dd0   94       1       java.util.HashMap$Node::getValue (5 bytes)
Event: 0.626 Thread 0x000001fe765a8dd0 nmethod 94 0x000001fe58dc3a10 code [0x000001fe58dc3ba0, 0x000001fe58dc3c78]
Event: 0.631 Thread 0x000001fe765a8dd0   97       3       java.util.HashMap::putVal (300 bytes)
Event: 0.632 Thread 0x000001fe765a8dd0 nmethod 97 0x000001fe51311c90 code [0x000001fe51311f20, 0x000001fe513131b8]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (2 events):
Event: 0.621 Executing VM operation: HandshakeAllThreads
Event: 0.621 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 0.630 loading class java/security/Provider$EngineDescription done
Event: 0.632 loading class jdk/internal/math/FloatingDecimal
Event: 0.632 loading class jdk/internal/math/FloatingDecimal done
Event: 0.632 loading class jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
Event: 0.632 loading class jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
Event: 0.632 loading class jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter done
Event: 0.632 loading class jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer done
Event: 0.632 loading class jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
Event: 0.633 loading class jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer done
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$1
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$1 done
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter done
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer done
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
Event: 0.634 loading class jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer done
Event: 0.634 loading class sun/security/jca/ProviderList$2
Event: 0.634 loading class sun/security/jca/ProviderList$2 done
Event: 0.634 loading class java/security/Security


Dynamic libraries:
0x00007ff6b2460000 - 0x00007ff6b246a000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.exe
0x00007ffad0f50000 - 0x00007ffad1167000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffad0550000 - 0x00007ffad0614000 	C:\Windows\System32\KERNEL32.DLL
0x00007fface230000 - 0x00007fface603000 	C:\Windows\System32\KERNELBASE.dll
0x00007fface740000 - 0x00007fface851000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa80540000 - 0x00007ffa80557000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jli.dll
0x00007ffad0630000 - 0x00007ffad07e1000 	C:\Windows\System32\USER32.dll
0x00007ffacebb0000 - 0x00007ffacebd6000 	C:\Windows\System32\win32u.dll
0x00007ffacf4a0000 - 0x00007ffacf4c9000 	C:\Windows\System32\GDI32.dll
0x00007fface610000 - 0x00007fface732000 	C:\Windows\System32\gdi32full.dll
0x00007ffacea50000 - 0x00007ffaceaea000 	C:\Windows\System32\msvcp_win.dll
0x00007ffab2350000 - 0x00007ffab236b000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\VCRUNTIME140.dll
0x00007ffabf240000 - 0x00007ffabf4db000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffacf370000 - 0x00007ffacf417000 	C:\Windows\System32\msvcrt.dll
0x00007ffacf160000 - 0x00007ffacf191000 	C:\Windows\System32\IMM32.DLL
0x00007ffac8720000 - 0x00007ffac872c000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\vcruntime140_1.dll
0x00007ffaa0300000 - 0x00007ffaa038d000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\msvcp140.dll
0x00007ffa62160000 - 0x00007ffa62de3000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server\jvm.dll
0x00007ffacebe0000 - 0x00007ffacec91000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffad0020000 - 0x00007ffad00c7000 	C:\Windows\System32\sechost.dll
0x00007fface860000 - 0x00007fface888000 	C:\Windows\System32\bcrypt.dll
0x00007ffacf1a0000 - 0x00007ffacf2b4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffacced0000 - 0x00007ffaccf1d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffaa2e50000 - 0x00007ffaa2e59000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffacf500000 - 0x00007ffacf571000 	C:\Windows\System32\WS2_32.dll
0x00007ffac5600000 - 0x00007ffac5634000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffac8050000 - 0x00007ffac805a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffacceb0000 - 0x00007ffaccec3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffacd170000 - 0x00007ffacd188000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffaa2e40000 - 0x00007ffaa2e4a000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jimage.dll
0x00007ffacb810000 - 0x00007ffacba42000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffad0990000 - 0x00007ffad0d23000 	C:\Windows\System32\combase.dll
0x00007ffaced70000 - 0x00007ffacee47000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffac71d0000 - 0x00007ffac7202000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fface040000 - 0x00007fface0bb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa7fe90000 - 0x00007ffa7feb5000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll
0x00007ffa7fbc0000 - 0x00007ffa7fbd8000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\zip.dll
0x00007ffacf730000 - 0x00007ffacffcd000 	C:\Windows\System32\SHELL32.dll
0x00007fface910000 - 0x00007ffacea4f000 	C:\Windows\System32\wintypes.dll
0x00007ffacbf90000 - 0x00007ffacc8ad000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffacf5a0000 - 0x00007ffacf6ab000 	C:\Windows\System32\SHCORE.dll
0x00007ffacf430000 - 0x00007ffacf496000 	C:\Windows\System32\shlwapi.dll
0x00007ffacdf70000 - 0x00007ffacdf9b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa7fba0000 - 0x00007ffa7fbb9000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\net.dll
0x00007ffac9f20000 - 0x00007ffaca04c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffacd5f0000 - 0x00007ffacd65a000 	C:\Windows\system32\mswsock.dll
0x00007ffa7fb80000 - 0x00007ffa7fb96000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 
java_class_path (initial): D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/plugins/vcs-git/lib/git4idea-rt.jar;D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8573157376                                {product} {ergonomic}
   size_t MaxNewSize                               = 5142216704                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8573157376                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\APPHOME\JDK11\jdk-11.0.2\bin;D:\APPHOME\nodejs;D:\APPHOME\nodejs;D:\APPHOME\maven3.9\bin;D:\APPHOME\maven3.6.3\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\Bandizip;D:\APPHOME\WechatDevTools\΢��web�����߹���\dll;D:\APPHOME\python_3;D:\APPHOME\python_3\Scripts;D:\APPHOME\geckodriver;D:\APPHOME\MYSQL8.0.4\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\APPHOME\WebStrom\WebStorm 2024.2.1\bin;D:\APPHOME\GoLand 2024.1.6\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 9, weak refs: 0

JNI global refs memory usage: 843, weak refs: 209

Process memory usage:
Resident Set Size: 36576K (0% of 33485756K total physical memory with 2547524K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 648K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 1808B


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 14 days 15:56 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt
Processor Information for all 12 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32700M (2487M free)
TotalPageFile size 63094M (AvailPageFile size 8M)
current process WorkingSet (physical memory assigned to process): 35M, peak: 35M
current process commit charge ("private bytes"): 607M, peak: 609M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30) for windows-amd64 JRE (17.0.11+1-b1207.30), built on 2024-07-12 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
