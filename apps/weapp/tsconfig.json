{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "noEmitOnError": false,
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "noImplicitAny": false,
    "strict": true,
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "lib": ["es2020", "dom"],
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "types": [
      "vite/client",
      "miniprogram-api-typings",
      "node",
      "@dcloudio/types",
      "@uni-helper/uni-ui-types",
      "@dcloudio/uni-app-types"
    ]
  },
  "include": [
    "./src/**/*.ts", "./src/**/*.d.ts", "./src/**/*.tsx",
    "./src/**/*.vue",
    "../../packages/infrastructure/src/openapi/typings.d.ts",
    "../../packages/infrastructure/typings.d.ts",
    "@repo/infrastructure/types"],
  "exclude": [
    "node_modules",
  ]
}
