<template>
  <div v-if="ready">
    <a-card>
      <template #title>
        <div class="flex justify-between">
          <div>儿心量表评测</div>
          <a-space v-if="!assessmentResult.finished" class="gap-2 items-center">
            <span v-if="lastSave || assessmentResult.modifiedDate" class="green mr-2 text-sm">
              最近保存：
              {{ dayjs(lastSave || assessmentResult.modifiedDate).format('YYYY-MM-DD HH:mm:ss') }}
            </span>
            <a-space>
              <a-button
                class="ml-2"
                type="outline"
                size="mini"
                :disabled="autoSaving"
                @click="() => handleSave({ finish: false })"
                >暂存结果</a-button
              >
              <a-button
                type="primary"
                size="mini"
                status="success"
                :disabled="autoSaving"
                @click="() => handleSave({ finish: true })"
                >完成评测</a-button
              >
              <a-button size="mini" :disabled="autoSaving" @click="() => $router.back()">
                <template #icon>
                  <IconLeft />
                </template>
                返回
              </a-button>
            </a-space>
          </a-space>
          <a-button v-else size="mini" :disabled="autoSaving" @click="() => $router.back()">
            <template #icon>
              <IconLeft />
            </template>
            返回
          </a-button>
        </div>
      </template>
      <div class="flex top-area">
        <a-form auto-label-width size="mini">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="选择儿童">
                <student-select size="mini" @change="handleStudentChange" />
              </a-form-item>
            </a-col>
            <a-col v-if="assessmentResult.student" :span="8">
              <a-form-item label="生日">
                <a-date-picker v-model="birthday" />
              </a-form-item>
            </a-col>
            <a-col v-if="assessmentResult.student" :span="8">
              <a-form-item label="实足年龄（月）">
                <a-input-number :model-value="realAgeMonth" readonly class="w-full" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="评估人">
                <a-input v-model.trim="assessmentResult.evaluatePerson" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="评估日期">
                <a-date-picker v-model="assessmentResult.evaluateDate" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="!started && assessmentResult.realAge" :gutter="16">
            <a-col :span="8">
              <a-form-item>
                <a-button size="mini" plain type="primary" @click="handleStart"> 开始评估 </a-button>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <div class="description">选择孩子，确认月龄后点击开始</div>
            </a-col>
          </a-row>
        </a-form>

        <div class="result-preview">
          <table cellpadding="0" cellspacing="0">
            <tr>
              <th>结果预览</th>
              <th>能区智龄(MA)</th>
              <th>发育商(DQ)</th>
              <!--              <th>离差智商(DIQ)</th>-->
            </tr>
            <tr v-for="(domain, index) in domains" :key="index">
              <td>{{ domain }}</td>
              <td>{{ assessmentResult.mentalAgeResultDetail[domain] }}</td>
              <td>{{ assessmentResult.dqResultDetail[domain] }}</td>
              <!--              <td>{{assessmentResult.diqResultDetail[domain]}}</td>-->
            </tr>
            <tr>
              <td>全量表</td>
              <td>{{ assessmentResult.mentalAgeResult || 0 }}</td>
              <td>{{ assessmentResult.dqResult || 0 }}</td>
              <!--              <td>{{assessmentResult.diqResult || 0}}</td>-->
            </tr>
          </table>
        </div>
      </div>
    </a-card>
    <a-card v-if="started && !assessmentResult.finished" class="mt-2">
      <div class="options-form flex justify-between flex items-center">
        <a-space v-if="evaluateSimultaneously" class="gap-2">
          <div class="w-32">当前月龄批量操作:</div>
          <a-button size="mini" shape="round" type="outline" status="success" @click="() => handleSetAllDomainValue(1)"
            >全部通过</a-button
          >
          <a-button size="mini" shape="round" type="outline" status="danger" @click="() => handleSetAllDomainValue(-1)"
            >全部未通过</a-button
          >
        </a-space>
        <a-space v-if="!assessmentResult.finished" class="gap-2 items-center">
          <div>开启同步评测</div>
          <a-switch type="round" size="mini" :model-value="evaluateSimultaneously" @change="handleSimultaneousChange" />
        </a-space>
      </div>
    </a-card>
    <a-card v-if="started" class="mt-2" type="border-card">
      <div v-for="(domain, index) in domains" :key="index" class="flex evaluate-domain-wrapper">
        <div class="domain-title">
          <div class="text">
            {{ domain }}
          </div>
        </div>
        <div class="flex evaluate-wrapper">
          <a-tooltip :content="`点击切换所有能区月龄至 ${monthsList[domainCurrentIndexes[domain]]} 月`">
            <div class="domain-month" @click="() => handleSyncDomainCurrentIndex(domain)">
              <span>{{ monthsList[domainCurrentIndexes[domain]] }}</span
              >月
            </div>
          </a-tooltip>
          <a-button
            class="left-btn"
            shape="circle"
            :disabled="domainCurrentIndexes[domain] < 1"
            @click="() => moveLeft(domain)"
          >
            <template #icon>
              <IconLeft />
            </template>
          </a-button>
          <div class="month-list-wrapper">
            <div
              class="month-items-container"
              :style="{
                left: `-${domainCurrentIndexes[domain] * 333}px`,
                width: `${Object.keys(criterionList[domain]).length * 333}px`,
              }"
            >
              <div v-for="(month, i) in monthsList" :key="i" class="month-item">
                <!--                <div class="title">-->
                <!--                  {{domain}} - 月龄：{{month}}-->
                <!--                </div>-->
                <div class="questions">
                  <div
                    v-for="(q, qi) in criterionList[domain][month].list"
                    :key="qi"
                    class="question-item"
                    :class="{
                      passed: passResult[`${month}-${q.id}`] === 1,
                      notPassed: passResult[`${month}-${q.id}`] === -1,
                    }"
                  >
                    <div class="question">
                      {{ q.assessmentItem }}
                      <sup class="red bold">{{ q.rMark || q.rmark ? 'R' : '' }}</sup>
                      <sup class="red bold">{{ q.starMark ? '*' : '' }}</sup>
                    </div>
                    <a-space class="answer mt-2">
                      <a-button
                        size="mini"
                        :type="passResult[`${month}-${q.id}`] === 1 ? 'primary' : 'outline'"
                        status="success"
                        shape="round"
                        @click="() => handleSetPass(domain, month, q.id)"
                      >
                        <template #icon>
                          <IconCheck />
                        </template>
                        通过
                      </a-button>
                      <a-button
                        size="mini"
                        :type="passResult[`${month}-${q.id}`] === -1 ? 'primary' : 'outline'"
                        status="danger"
                        shape="round"
                        @click="() => handleSetNotPass(domain, month, q.id)"
                      >
                        <template #icon>
                          <IconClose />
                        </template>
                        未通过
                      </a-button>
                    </a-space>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a-button
            class="right-btn"
            :disabled="domainCurrentIndexes[domain] + 1 >= Object.keys(criterionList[domain]).length"
            @click="() => moveRight(domain)"
          >
            <template #icon>
              <IconRight />
            </template>
          </a-button>
        </div>
        <div class="criterion-description text-xs leading-5">
          <div v-for="(cvm, i) in [monthsList[domainCurrentIndexes[domain]]]" :key="i" class="current">
            <div v-for="(q, qi) in criterionList[domain][cvm].list" :key="qi" class="question-desc">
              <!--              <div><strong>月龄</strong>：{{cvm}}</div>-->
              <!--              <div><strong>项目</strong>：{{q.assessmentItem}}</div>-->
              <div>
                <strong>
                  <!--                {{qi + 1}}、-->
                  操作</strong
                >：{{ q.operation }}
              </div>
              <div>
                <strong>
                  <!--                {{qi + 1}}、-->
                  通过</strong
                >：{{ q.passCriterion }}
              </div>
              <div v-if="q.rmark || q.rMark" class="red"> R 该项目的表现可以通过询问家长获得 </div>
              <div v-if="q.starMark" class="red"> * 该项目如果未通过需要引起注意 </div>
            </div>
          </div>
        </div>

        <div class="overview">
          <div
            v-for="(month, mi) in monthsList"
            :key="mi"
            class="overview-month"
            :class="`pass${isMonthPassed(domain, month)} ${
              month === monthsList[domainCurrentIndexes[domain]] ? 'current' : ''
            }`"
            :title="month"
          ></div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
  import DiqFloatRangeRaw from '@/common/diqFloatRangeRaw';
  import { PROJECT_URLS } from '@repo/env-config';
  import StudentSelect from '@repo/components/student/studentSelect.vue';
  import { usePrompt } from '@repo/ui/components';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';

  const domains = ['大运动', '精细动作', '适应能力', '语言', '社会行为'];
  export default {
    name: 'ChildBehaviorAssessmentResultEdit',
    components: { StudentSelect },
    data() {
      return {
        dayjs,
        started: false,
        criterionList: {},
        assessmentResult: {
          mentalAgeResultDetail: {},
          dqResultDetail: {},
          diqResultDetail: {},
        },
        domains,
        currentDomain: '',
        domainCurrentIndexes: {},
        monthsList: [],
        domainLeftFinished: {},
        lastSave: null,
        loading: false,
        ready: false,
        /**
         * 已通过的结果
         * eg: {month-id: result(-1,undefined,1)}
         */
        passResult: {},
        initialIndex: 0,
        initialMonth: 1,
        /**
         * 能区分值
         */
        monthScoreMap: {
          12: 1,
          36: 3,
          84: 6,
        },
        autoSaving: false,
        timer: null,
        birthday: null,
        // 同时评测
        evaluateSimultaneously: false,
      };
    },
    computed: {
      // birthday() {
      //   return this.assessmentResult.student?.birthday;
      // },
      currentVisibleMonth() {
        const domain = this.currentDomain;
        if (!this.domainCurrentIndexes[domain]) {
          return null;
        }
        let thisMonth;
        let lastMonth;
        let nextMonth;
        thisMonth = this.monthsList[this.domainCurrentIndexes[domain]];
        if (this.domainCurrentIndexes[domain] > 1) {
          thisMonth = this.monthsList[this.domainCurrentIndexes[domain]];
        } else {
          thisMonth = 2;
        }

        lastMonth = this.monthsList[this.domainCurrentIndexes[domain] - 1];
        if (thisMonth <= 2) {
          lastMonth = 1;
        }
        nextMonth = this.monthsList[this.domainCurrentIndexes[domain] + 1];
        if (thisMonth >= 78) {
          nextMonth = this.monthsList[this.monthsList.length - 1];
        } else {
          nextMonth = this.monthsList[this.domainCurrentIndexes[domain] + 1];
        }

        return [lastMonth, thisMonth, nextMonth];
      },
      realAgeMonth: {
        get() {
          const { birthday } = this;
          if (!birthday) {
            return 0;
          }
          const now = new Date();
          const birth = new Date(birthday);
          const year = now.getFullYear() - birth.getFullYear();
          const month = now.getMonth() - birth.getMonth();
          const day = now.getDate() - birth.getDate();
          return Number((year * 12 + month + day / 30).toFixed(1));
        },
        set(value) {
          // console.log(111, value);
          this.assessmentResult.realAge = value;
        },
      },
    },
    watch: {
      'assessmentResult.realAge': {
        deep: true,
        handler() {
          this.determineInitialMonthIndex();
        },
        immediate: true,
      },
      'assessmentResult.student.birthday': {
        deep: true,

        handler(val) {
          this.birthday = val;
          this.assessmentResult.realAge = this.realAgeMonth;
        },

        immediate: true,
      },
      'passResult': {
        handler() {
          // console.log(this.passResult);
        },
        deep: true,
      },
    },
    async mounted() {
      await this.loadCriterion();
      if (this.$route.query.id) {
        await this.loadEditData(this.$route.query.id);

        const { domainCurrentIndexes, domainLeftFinished, currentDomain } = this.assessmentResult;
        this.domainCurrentIndexes = domainCurrentIndexes || {};
        this.domainLeftFinished = domainLeftFinished || {};
        this.currentDomain = currentDomain || domains[0];
        this.autoSave();
      } else {
        this.assessmentResult.evaluatePerson = this.globalData.user.name;
        this.assessmentResult.evaluateDate = new Date();

        domains.forEach((domain) => {
          this.domainCurrentIndexes[domain] = 1;
          this.domainLeftFinished[domain] = 0;
        });

        this.currentDomain = domains[0];
      }

      this.handleSimultaneousChange(true);
      this.ready = true;
    },
    methods: {
      // 选择学生
      handleStudentChange(sid, sop, student) {
        this.assessmentResult.student = student;
      },
      // 设置所有能区的当前月龄的值
      handleSetAllDomainValue(val) {
        domains.forEach((dm) => {
          const month = this.monthsList[this.domainCurrentIndexes[dm]];
          this.criterionList[dm][month].list.forEach((item) => {
            if (val > 0) {
              this.handleSetPass(dm, month, item.id);
            } else {
              this.handleSetNotPass(dm, month, item.id);
            }
          });
          // console.log(month, this.criterionList)
        });
      },
      // 切换同步评测，需要设为统一月龄index
      handleSimultaneousChange(val) {
        if (val) {
          // 如果有能区没有左侧完成，则报错
          const notFinished = Object.keys(this.domainLeftFinished).filter((dm) => {
            return !this.domainLeftFinished[dm];
          });
          if (notFinished.length !== 0 && notFinished.length !== 5) {
            Message.error(`请先完成 ${notFinished.join(',')} 的评测`);
            this.evaluateSimultaneously = false;
            return;
          }
          const index = this.domainCurrentIndexes[domains[0]];
          domains.forEach((dm) => {
            this.domainCurrentIndexes[dm] = index;
          });
        }
        this.evaluateSimultaneously = val;
      },
      // 同步所有能区的当前月龄
      handleSyncDomainCurrentIndex(domain) {
        domains.forEach((dm) => {
          this.domainCurrentIndexes[dm] = this.domainCurrentIndexes[domain];
        });
      },
      isMonthPassed(domain, month) {
        const { list } = this.criterionList[domain][month];
        const passList = list.filter((item) => {
          return this.passResult[`${item.month}-${item.id}`] === 1;
        });
        const notPassList = list.filter((item) => {
          return this.passResult[`${item.month}-${item.id}`] === -1;
        });
        if (passList.length === 0 && notPassList.length === 0) {
          return '-not-test';
        }
        if (passList.length === list.length) {
          return '-true';
        }
        return '-false';
      },
      autoSave() {
        // this.timer = setTimeout(() => {
        //   this.handleSave({ finish: false, autoSave: true });
        //   this.autoSave();
        // }, 1000 * 60);
      },
      determineMonthQuestionScore(domain, month) {
        const { list } = this.criterionList[domain][month];
        const notPassList = list.filter((item) => {
          return this.passResult[`${item.month}-${item.id}`] === -1;
        });
        const passList = list.filter((item) => {
          return this.passResult[`${item.month}-${item.id}`] === 1;
        });

        let monthScore = 0;
        const maxMonthList = Object.keys(this.monthScoreMap);
        for (let i = 0; i < maxMonthList.length; i++) {
          const maxMonth = maxMonthList[i];
          if (month <= maxMonth) {
            monthScore = this.monthScoreMap[maxMonth];
            break;
          }
        }

        let score = 0;
        if (month >= this.initialMonth) {
          score = (monthScore / list.length) * passList.length;
        } else {
          score = (monthScore / list.length) * (list.length - notPassList.length);
        }

        return score;
      },
      determineInitialMonthIndex() {
        const { realAge } = this.assessmentResult;
        if (!realAge) {
          return;
        }
        for (let i = 0; i < this.monthsList.length; i++) {
          const month = this.monthsList[i];
          if (realAge < month && realAge > 0) {
            return;
          }
          this.initialIndex = i;
          this.initialMonth = month;
          this.assessmentResult.initialMonth = month;
          this.domainCurrentIndexes['大运动'] = i;
          this.domainCurrentIndexes['精细动作'] = i;
          this.domainCurrentIndexes['适应能力'] = i;
          this.domainCurrentIndexes['语言'] = i;
          this.domainCurrentIndexes['社会行为'] = i;
        }
      },
      async loadEditData(id) {
        const { data } = await request(`/evaluation/childBehaviorAssessmentResult/${id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        this.assessmentResult = data;
        this.passResult = this.assessmentResult.resultDetails || {};
        this.started = true;
      },
      async loadCriterion() {
        const { data } = await request('/evaluation/childBehaviorAssessment', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            pageSize: 9999,
            page: 1,
          },
        });

        const resultMap = {};
        data.items.forEach((item) => {
          const { attribute, month } = item;
          if (!resultMap[attribute]) {
            resultMap[attribute] = {};
          }
          if (!resultMap[attribute][month]) {
            resultMap[attribute][month] = {
              month,
              list: [],
            };
          }
          resultMap[attribute][month].list.push(item);
        });

        this.monthsList = Object.keys(resultMap['大运动']).map((item) => {
          return Number(item);
        });
        this.monthsList.sort((a, b) => a - b);
        this.criterionList = resultMap;
      },
      handleStart() {
        if (this.assessmentResult.realAge >= 79) {
          Message.error('月龄不能高于78.9个月');
          return;
        }
        this.started = true;
        this.autoSave();
      },
      moveLeft(domain) {
        if (this.evaluateSimultaneously) {
          domains.forEach((dm) => {
            this.domainCurrentIndexes[dm] = this.domainCurrentIndexes[dm] -= 1;
          });
        } else {
          this.domainCurrentIndexes[domain] = this.domainCurrentIndexes[domain] -= 1;
        }
      },
      moveRight(domain) {
        if (this.evaluateSimultaneously) {
          domains.forEach((dm) => {
            this.domainCurrentIndexes[dm] = this.domainCurrentIndexes[dm] += 1;
          });
        } else {
          this.domainCurrentIndexes[domain] = this.domainCurrentIndexes[domain] += 1;
        }
      },

      /**
       * 判断当前月份内所有题目都已完成
       */
      isCellFinished(domain, month) {
        const { list } = this.criterionList[domain][month];
        const emptyList = list.filter((item) => {
          return this.passResult[`${item.month}-${item.id}`] === undefined;
        });
        return !emptyList.length;
      },
      /**
       * 计算能区得分
       * 从左侧开始往右找连续两个不通过的
       * 如果找到则将两个连续不通过的月份之前的月份的分数相加
       * 如果没有找到则将所有月份的分数相加
       * @param domain
       */
      calculateScore(domain) {
        let score = 0;
        let notPassed = 0;
        const availableMonths = [];
        for (let i = 0; i < this.monthsList.length; i++) {
          const month = this.monthsList[i];
          const { list } = this.criterionList[domain][month];
          // 如果列表中有任一项不通过 则 notPassed + 1
          const notPassList = list.filter((item) => {
            return this.passResult[`${item.month}-${item.id}`] === -1;
          });

          if (month <= this.initialMonth) {
            notPassed = 0;
          } else if (notPassList.length) {
            notPassed += 1;
          } else {
            notPassed = 0;
          }

          score += this.determineMonthQuestionScore(domain, month);
          availableMonths.push(month);
          if (notPassed >= 2) {
            break;
          }
        }

        const mentalAgeResultDetail = this.assessmentResult.mentalAgeResultDetail || {};
        mentalAgeResultDetail[domain] = score;
        this.assessmentResult.mentalAgeResultDetail = mentalAgeResultDetail;

        Message.success(`能区 ${domain} 评测完成，得分：${score}`);
        this.calculateMentalAge();
      },
      calculateMentalAge() {
        const mentalAgeResultDetail = this.assessmentResult.mentalAgeResultDetail || {};
        const dqResultDetail = this.assessmentResult.dqResultDetail || {};
        const diqResultDetail = this.assessmentResult.diqResultDetail || {};
        Object.keys(mentalAgeResultDetail).forEach((domain) => {
          const mentalAge = mentalAgeResultDetail[domain];
          const dq = Math.round((mentalAge / this.realAgeMonth) * 100);
          dqResultDetail[domain] = dq;

          /**
           * 离差智商=100+15*Z
           * Z=（X-M）/ S
           * M=发育商均数
           * X=发育商（DQ）
           * S=标准差
           * 结果四舍五入取整数
           */
          const diqItem = DiqFloatRangeRaw[domain][this.initialMonth];
          const diq = 100 + 15 * ((dq - diqItem.averageDq) / diqItem.floatRange);
          diqResultDetail[domain] = Math.round(diq);
        });
        this.assessmentResult.dqResultDetail = dqResultDetail;
        this.assessmentResult.diqResultDetail = diqResultDetail;

        let mentalAgeResult = Object.values(mentalAgeResultDetail).reduce((a, b) => a + b);
        mentalAgeResult = Math.round((mentalAgeResult / 5) * 10) / 10;
        this.assessmentResult.mentalAgeResult = mentalAgeResult;

        const dqResult = Math.round((mentalAgeResult / this.realAgeMonth) * 100);
        this.assessmentResult.dqResult = dqResult;

        const totalDiqItem = DiqFloatRangeRaw['总发育商'][this.initialMonth];
        const diqResult = 100 + 15 * ((dqResult - totalDiqItem.averageDq) / totalDiqItem.floatRange);

        this.assessmentResult.diqResult = Math.round(diqResult);
      },
      handleSetPass(domain, month, qi) {
        if (this.assessmentResult.finished) {
          return;
        }
        this.passResult[`${month}-${qi}`] = 1;

        if (this.domainCurrentIndexes[domain] + 2 >= Object.keys(this.criterionList[domain]).length) {
          Message.info('已经是最后一题了');
          return;
        }

        const isCellFinished = this.isCellFinished(domain, month);
        if (this.domainLeftFinished[domain]) {
          if (isCellFinished) {
            // this.$set(this.domainCurrentIndexes, domain, this.domainCurrentIndexes[domain] += 1);
            // this.moveRight(domain)
            // 判断从月龄开始往右中 是否有连续两个不通过的
            let notPassed = 0;
            for (let i = this.initialIndex; i < this.monthsList.length; i++) {
              const month = this.monthsList[i];
              const { list } = this.criterionList[domain][month];
              const passList = list.filter((item) => {
                return this.passResult[`${item.month}-${item.id}`] === 1;
              });
              const emptyList = list.filter((item) => {
                return this.passResult[`${item.month}-${item.id}`] === undefined;
              });

              if (passList.length === list.length) {
                notPassed = 0;
              } else if (!emptyList.length) {
                notPassed += 1;
              }
              if (notPassed >= 2) {
                // 如果有连续两个不通过的 结束 计算分数
                this.calculateScore(domain);
                // this.$set(this.domainCurrentIndexes, domain, i);
                return;
              }
            }
          }
          return;
        }
        // 判断 passResult 中是否有两个月连续通过
        let passed = 0;
        let passedMonth;
        Object.keys(this.criterionList[domain]).forEach((month) => {
          const { list } = this.criterionList[domain][month];
          const passList = list.filter((item) => {
            return this.passResult[`${item.month}-${item.id}`] === 1;
          });
          if (passList.length === list.length && Number(month) !== this.initialMonth) {
            // console.log('left passed')
            passed += 1;
          } else {
            // console.log('left not passed')
            passed = 0;
          }
          if (passed >= 2) {
            this.domainLeftFinished[domain] = 1;
            passedMonth = month;
            return false;
          }
        });

        // 如果已经有两个连续通过的
        if (this.domainLeftFinished[domain]) {
          // 从通过月份开始 左侧全部设置为已通过
          for (let i = 0; i < this.monthsList.length; i++) {
            const month = this.monthsList[i];
            if (month >= passedMonth) {
              break;
            }
            const { list } = this.criterionList[domain][month];
            list.forEach((item) => {
              this.passResult[`${month}-${item.id}`] = 1;
            });
          }

          // 如果已测评项目未找到连续两个未通过的
          // 如果 当前 index > initialIndex 则往左移动一位
          // 否则跳转到 initialIndex
          if (this.domainCurrentIndexes[domain] > this.initialIndex) {
            // this.moveLeft(domain)
            // this.$set(this.domainCurrentIndexes, domain, this.domainCurrentIndexes[domain] -= 1);
          } else {
            this.evaluateSimultaneously = false;
            this.domainCurrentIndexes[domain] =
              this.initialIndex + 1 >= this.monthsList.length ? this.initialIndex : this.initialIndex + 1;
          }
        } else {
          // 否则 往左移动一位
          // 判断本月是否所有题目都通过了
          if (isCellFinished) {
            // this.moveLeft(domain)
            // this.$set(this.domainCurrentIndexes, domain, this.domainCurrentIndexes[domain] -= 1);
          }
        }
      },
      handleSetNotPass(domain, month, qi) {
        if (this.assessmentResult.finished) {
          return;
        }
        this.passResult[`${month}-${qi}`] = -1;

        const isCellFinished = this.isCellFinished(domain, month);

        // 如果左侧未结束 并且当前月已经全部完成 往左移动一格
        // console.log(this.domainLeftFinished[domain], isCellFinished)
        if (!this.domainLeftFinished[domain]) {
          // this.$set(this.domainCurrentIndexes, domain, this.domainCurrentIndexes[domain] -= 1);
          // this.moveLeft(domain)
          return;
        }

        // 如果没有连续两个不通过的
        // 如果当前月份已全部测试 则往右移动一格
        if (isCellFinished) {
          // this.moveRight(domain)
          // this.$set(this.domainCurrentIndexes, domain, this.domainCurrentIndexes[domain] += 1);
          // 本月已全部测试 判断是否有两个连续不通过的，如果是则结束 如果不是则往右移动一格
          let notPassed = 0;
          // console.log('start', this.domainCurrentIndexes)
          for (let i = 0; i < this.monthsList.length; i++) {
            const month = this.monthsList[i];
            const { list } = this.criterionList[domain][month];
            const notPassList = list.filter((item) => {
              return this.passResult[`${item.month}-${item.id}`] === -1;
            });
            if (month <= this.initialMonth) {
              notPassed = 0;
            } else if (notPassList.length && Number(month) !== this.initialMonth) {
              notPassed += 1;
            } else {
              notPassed = 0;
            }
            if (notPassed >= 2) {
              // 结束 计算分数
              this.calculateScore(domain);
              this.domainCurrentIndexes[domain] = i;
              return;
            }
          }
        }
      },
      async handleSave({ finish, autoSave }) {
        // console.log(finish, this.assessmentResult)
        if (!this.assessmentResult.student) {
          Message.error('请选择儿童');
          return;
        }
        if (!this.assessmentResult.realAge) {
          Message.error('请输入实足年龄');
          return;
        }
        if (!this.assessmentResult.evaluatePerson) {
          Message.error('请输入评估人');
          return;
        }
        if (!this.assessmentResult.evaluateDate) {
          Message.error('请选择评估日期');
          return;
        }

        const submit = async () => {
          if (finish) {
            this.assessmentResult.finished = true;
          }
          const data = {
            ...this.assessmentResult,
            resultDetails: {
              ...this.passResult,
            },
            domainCurrentIndexes: {
              ...this.domainCurrentIndexes,
            },
            domainLeftFinished: {
              ...this.domainLeftFinished,
            },
            currentDomain: this.currentDomain,
          };
          data.evaluateDate = dayjs(data.evaluateDate).format('YYYY-MM-DD 00:00:00');
          if (this.assessmentResult.id) {
            await request(`/evaluation/childBehaviorAssessmentResult/${this.assessmentResult.id}`, {
              data,
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });
          } else {
            const { data: res } = request('/evaluation/childBehaviorAssessmentResult', {
              data,
              method: 'POST',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });
            this.assessmentResult.id = data.id;
          }
        };

        try {
          if (autoSave) {
            await submit();
            this.lastSave = new Date();
          } else if (finish) {
            const { prompt } = usePrompt();
            const content = await prompt({
              title: '请输入评估结论',
              value: this.assessmentResult.resultComment,
              confirmText: '确定',
              cancelText: '取消',
              inputType: 'textarea',
              inputPattern: /\S/,
              inputErrorMessage: '请输入评估结论',
            });

            this.assessmentResult.resultComment = content;
            this.loading = true;
            await submit();
            Message.success('评测完成');
            clearInterval(this.timer);
            this.timer = null;

            this.$router.back();
          } else {
            this.loading = true;
            await submit();
            if (finish) {
              Message.success('评测完成');

              this.$router.back();
            } else {
              Message.success('暂存成功');
            }

            clearInterval(this.timer);
            this.timer = null;

            // this.$router.back();
          }
        } catch (e) {
          console.log(e);
        } finally {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .top-area {
    align-content: flex-start;
    align-items: center;
  }
  .description {
    font-size: 12px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    color: #999;
    background: #f0f0f0;
    border-radius: 8px;
    margin-left: 32px;
  }
  $border: 1px solid #ddd;
  .result-preview {
    margin-left: 32px;
    padding-left: 32px;
    border-left: $border;
    font-size: 12px;
    table {
      border-right: $border;
      border-bottom: $border;
      td,
      th {
        text-align: center;
        width: 100px;
        border-top: $border;
        border-left: $border;
        padding: 3px 8px;
      }
    }
  }

  $titleHeight: 22px;
  .evaluate-domain-wrapper {
    align-items: stretch;
    margin: 12px 0 8px;
    position: relative;
    //border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
    &:last-child {
      border: none;
    }
    &:first-child {
      margin-top: 0;
    }
    .overview {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      height: 3px;
      line-height: 3px;
      display: flex;
      .overview-month {
        flex: 1;
        background: #f0f0f0;
        transition: all linear 0.1s;

        //&.pass-not-test {
        //}
        //&.pass-true {
        //  background: $--button-success-border-color !important;
        //}
        //&.pass-false {
        //  background: $--button-danger-border-color !important;
        //}

        &.current {
          //background: $--color-primary;
          position: relative;
          &:before {
            content: '';
            position: absolute;
            left: 50%;
            margin-left: -2px;
            transform: rotate(-90deg);
            bottom: -10px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            //border-left: 6px solid $--color-primary;
          }
        }
      }
    }
    .domain-title {
      writing-mode: vertical-rl;
      font-weight: bold;
      text-align: center;
      letter-spacing: 0.5em;
      padding: 0 16px;
      background: #f9f9f9;
      font-size: 18px;
      margin-right: 16px;
      border-radius: 2px;
    }
  }
  .evaluate-wrapper {
    position: relative;
    align-items: center;
    margin: 0;
    .domain-month {
      position: absolute;
      text-align: center;
      right: 0;
      top: 3px;
      width: 50px;
      background: #fff;
      //border: 1px solid $--color-primary;
      //color: $--color-primary;
      border-radius: 16px;
      cursor: pointer;
      &:hover {
        //background: $--color-primary;
        color: #fff;
      }
    }
    .left-btn,
    .right-btn {
      width: 50px;
      height: 50px;
      line-height: 50px;
      border-radius: 50%;
      font-size: 20px;
      padding: 0;
    }
  }
  .month-list-wrapper {
    margin: 0 16px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    width: 333px;
    height: 160px;
  }
  .month-items-container {
    position: absolute;
    transition: all linear 0.1s;
    left: 0;
    top: 0;
    height: 160px;
    display: flex;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    .month-item {
      flex: 0 0 333px;
      border-right: none;
      border-left: none;
      text-align: center;
      .title {
        font-size: 16px;
        height: $titleHeight;
        line-height: $titleHeight;
        border-bottom: 1px solid #e2e2e2;
      }
    }

    .questions {
      display: flex;
      flex-direction: column;
      //height: calc(100% - $titleHeight);
      height: 100%;
      .question-item {
        flex: 1 0 auto;
        align-items: center;
        justify-content: center;
        //padding: 8px;
        display: flex;
        flex-direction: column;
        &.passed {
          background: rgba(#00ff00, 0.1);
        }
        &.notPassed {
          background: rgba(#ff0000, 0.1);
        }
        &:nth-child(2) {
          border-top: 1px solid #e2e2e2;
        }
        .answer {
          //margin-top: 16px;
        }
      }
    }
  }

  .criterion-description {
    display: flex;
    flex: 1;
    margin-left: 16px;
    transition: all linear 0.2s;
    border: 1px solid #e2e2e2;
    border-radius: 8px;
    > div {
      width: 100%;
      .question-desc {
        transition: all linear 0.2s;
        //background: #f9f9f9;
        color: #999;
        padding: 8px 16px;
        &:last-child {
          border-top: 1px solid #e2e2e2;
        }
      }
    }
    .current {
      display: flex;
      flex-direction: column;
    }
    .current .question-desc {
      flex: 1;
      //background: #fff;
      //border-color: $--color-primary;
      color: #333;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }

  .options-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
</style>
