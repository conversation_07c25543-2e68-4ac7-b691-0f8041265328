<template>
  <div class="social-ability">
    <h2 class="text-lg font-medium mb-4 border-l-4 border-primary pl-2">第二部分：社会能力</h2>
    <p class="text-gray-500 mb-6">此部分用于评估儿童的社会能力表现</p>
    <a-form :model="localData" layout="vertical">
      <!-- 体育运动 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅰ. 体育运动">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="mb-2">1. 请列出你孩子最爱好的体育运动项目（例如游泳，棒球等）：</p>
              <a-checkbox v-model="noSportsActivities">无爱好</a-checkbox>

              <div v-if="!noSportsActivities" class="mt-2">
                <div
                  v-for="(item, index) in localData.sportsActivities?.list"
                  :key="`sport-${index}`"
                  class="mb-2 flex items-center"
                >
                  <span class="mr-2">{{ String.fromCharCode(97 + index) }}.</span>
                  <a-input
                    v-model="localData.sportsActivities.list[index]"
                    placeholder="请输入体育项目"
                    class="flex-1"
                  />
                  <a-button
                    v-if="index > 0"
                    type="text"
                    status="danger"
                    @click="removeItem(localData.sportsActivities.list, index)"
                  >
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </div>

                <a-button
                  v-if="localData.sportsActivities?.list?.length < 3"
                  type="text"
                  class="mt-2"
                  @click="addItem(localData.sportsActivities?.list)"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加项目
                </a-button>
              </div>
            </div>

            <div>
              <p class="mb-2">2. 与同龄儿童相比，他（她）在这些项目上花去时间多少？</p>
              <a-radio-group v-model="localData.sportsActivities.timeSpent">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较少</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较多</a-radio>
              </a-radio-group>

              <p class="mt-4 mb-2">3. 与同龄儿童相比，他（她）的运动水平如何？</p>
              <a-radio-group v-model="localData.sportsActivities.level">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较低</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较高</a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <!-- 爱好 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅱ. 爱好">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="mb-2">1. 请列出你孩子在体育运动以外的爱好（例如集邮、看书、弹琴等，不包括看电视）</p>
              <a-checkbox v-model="noHobbies">无爱好</a-checkbox>

              <div v-if="!noHobbies" class="mt-2">
                <div
                  v-for="(item, index) in localData.hobbies?.list"
                  :key="`hobby-${index}`"
                  class="mb-2 flex items-center"
                >
                  <span class="mr-2">{{ String.fromCharCode(97 + index) }}.</span>
                  <a-input v-model="localData.hobbies.list[index]" placeholder="请输入爱好" class="flex-1" />
                  <a-button
                    v-if="index > 0"
                    type="text"
                    status="danger"
                    @click="removeItem(localData.hobbies.list, index)"
                  >
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </div>

                <a-button
                  v-if="localData.hobbies?.list?.length < 3"
                  type="text"
                  class="mt-2"
                  @click="addItem(localData.hobbies.list)"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加爱好
                </a-button>
              </div>
            </div>

            <div>
              <p class="mb-2">2. 与同龄儿童相比，他（她）花在这些爱好上的时间多少？</p>
              <a-radio-group v-model="localData.hobbies.timeSpent">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较少</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较多</a-radio>
              </a-radio-group>

              <p class="mt-4 mb-2">3. 与同龄儿童相比，他（她）的爱好水平如何？</p>
              <a-radio-group v-model="localData.hobbies.level">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较低</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较高</a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <!-- 组织/俱乐部 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅲ. 组织、俱乐部、团队或小组">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="mb-2">1. 请列出你孩子参加的组织、俱乐部、团队或小组的名称</p>
              <a-checkbox v-model="noOrganizations">未参加</a-checkbox>

              <div v-if="!noOrganizations" class="mt-2">
                <div
                  v-for="(item, index) in localData.organizations.list"
                  :key="`org-${index}`"
                  class="mb-2 flex items-center"
                >
                  <span class="mr-2">{{ String.fromCharCode(97 + index) }}.</span>
                  <a-input v-model="localData.organizations.list[index]" placeholder="请输入组织名称" class="flex-1" />
                  <a-button
                    v-if="index > 0"
                    type="text"
                    status="danger"
                    @click="removeItem(localData.organizations.list, index)"
                  >
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </div>

                <a-button
                  v-if="localData.organizations.list?.length < 3"
                  type="text"
                  class="mt-2"
                  @click="addItem(localData.organizations.list)"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加组织
                </a-button>
              </div>
            </div>

            <div>
              <p class="mb-2">2. 与同龄儿童相比，他（她）在这些组织中的活跃程度如何？</p>
              <a-radio-group v-model="localData.organizations.activeness">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较差</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较高</a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <!-- 工作/家务 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅳ. 工作或家务">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="mb-2">1. 请列出你孩子有无干活或打零工的情况（例如送报、帮人照顾小孩、帮人搞卫生等）</p>
              <a-checkbox v-model="noJobs">没有</a-checkbox>

              <div v-if="!noJobs" class="mt-2">
                <div v-for="(item, index) in localData.jobs.list" :key="`job-${index}`" class="mb-2 flex items-center">
                  <span class="mr-2">{{ String.fromCharCode(97 + index) }}.</span>
                  <a-input v-model="localData.jobs.list[index]" placeholder="请输入工作内容" class="flex-1" />
                  <a-button
                    v-if="index > 0"
                    type="text"
                    status="danger"
                    @click="removeItem(localData.jobs.list, index)"
                  >
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </div>

                <a-button
                  v-if="localData.jobs.list.length < 3"
                  type="text"
                  class="mt-2"
                  @click="addItem(localData.jobs.list)"
                >
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加工作
                </a-button>
              </div>
            </div>

            <div>
              <p class="mb-2">2. 与同龄儿童相比，他（她）工作质量如何？</p>
              <a-radio-group v-model="localData.jobs.quality">
                <a-radio :value="null">不知道</a-radio>
                <a-radio :value="0">较差</a-radio>
                <a-radio :value="1">一般</a-radio>
                <a-radio :value="2">较好</a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <!-- 朋友 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅴ. 朋友">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="mb-2">1. 你孩子有几个要好的朋友？</p>
              <a-radio-group v-model="localData.friends.count">
                <a-radio :value="0">无</a-radio>
                <a-radio :value="1">1个</a-radio>
                <a-radio :value="2">2-3个</a-radio>
                <a-radio :value="3">4个及以上</a-radio>
              </a-radio-group>
            </div>

            <div>
              <p class="mb-2">2. 你孩子与这些朋友每星期大概在一起几次？</p>
              <a-radio-group v-model="localData.friends.frequency">
                <a-radio :value="0">不到一次</a-radio>
                <a-radio :value="1">1-2次</a-radio>
                <a-radio :value="2">3次及以上</a-radio>
              </a-radio-group>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <!-- 相处情况 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅵ. 相处情况">
          <p class="mb-4">与同龄孩子相比，你孩子在下列方面表现如何？</p>
          <!-- Mobile Card List Layout -->
          <a-card v-for="record in relationshipData" :key="record.key">
            <template #title>{{ record.label }}</template>
            <a-radio-group
              :model-value="localData.relationship[record.key]"
              type="button"
              size="small"
              @change="(value) => handleRelationshipChange(record.key, value)"
            >
              <a-radio :value="0">较差</a-radio>
              <a-radio :value="1">差不多</a-radio>
              <a-radio :value="2">较好</a-radio>
            </a-radio-group>
          </a-card>
        </a-collapse-item>
      </a-collapse>

      <!-- 学习情况 -->
      <a-collapse :default-active-key="['1']" class="mb-6">
        <a-collapse-item key="1" header="Ⅶ. 学习情况">
          <div class="mb-4">
            <a-checkbox v-model="notInSchool">未上学</a-checkbox>
          </div>

          <div v-if="localData.academics.inSchool">
            <p class="mb-2">1. 当前学习成绩</p>

            <!-- Mobile Card List Layout -->
            <div class="block md:hidden space-y-4 mb-6">
              <a-card v-for="record in academicData" :key="record.key">
                <template #title>{{ record.label }}</template>
                <a-radio-group
                  :model-value="localData.academics.subjects[record.key]"
                  type="button"
                  size="small"
                  @change="(value) => handleAcademicChange(record.key, value)"
                >
                  <a-radio :value="0">不及格</a-radio>
                  <a-radio :value="1">中下</a-radio>
                  <a-radio :value="2">中等</a-radio>
                  <a-radio :value="3">中上</a-radio>
                </a-radio-group>
              </a-card>
            </div>

            <!-- 其他课程 -->
            <p class="mb-2">其他课（如历史、地理、常识、外语等）</p>
            <div v-for="(item, index) in otherSubjects" :key="`subject-${index}`" class="mb-2 flex items-center">
              <a-input v-model="otherSubjects[index].name" placeholder="课程名称" class="w-32 mr-2" />
              <a-radio-group v-model="otherSubjects[index].score">
                <a-radio :value="0">不及格</a-radio>
                <a-radio :value="1">中等以下</a-radio>
                <a-radio :value="2">中等</a-radio>
                <a-radio :value="3">中等以上</a-radio>
              </a-radio-group>
              <a-button v-if="index > 0" type="text" status="danger" @click="removeOtherSubject(index)">
                <template #icon><icon-delete /></template>
              </a-button>
            </div>

            <a-button v-if="otherSubjects.length < 3" type="text" class="mt-2 mb-4" @click="addOtherSubject">
              <template #icon><icon-plus /></template>
              添加其他课程
            </a-button>

            <!-- 特殊班级 -->
            <div class="mt-6 mb-4">
              <p class="mb-2">2. 你孩子是否在特殊班级？</p>
              <a-space direction="vertical">
                <a-radio-group v-model="localData.academics.specialClass">
                  <a-radio :value="false">不是</a-radio>
                  <a-radio :value="true">是</a-radio>
                </a-radio-group>

                <a-input
                  v-if="localData.academics.specialClass"
                  v-model="localData.academics.specialClassName"
                  placeholder="请说明什么性质的特殊班级"
                  class="max-w-md"
                />
              </a-space>
            </div>

            <!-- 留级情况 -->
            <div class="mt-6 mb-4">
              <p class="mb-2">3. 你孩子是否留级？</p>
              <a-space direction="vertical">
                <a-radio-group v-model="localData.academics.retained">
                  <a-radio :value="false">没有</a-radio>
                  <a-radio :value="true">留过</a-radio>
                </a-radio-group>

                <div v-if="localData.academics.retained" class="pl-4 pt-2">
                  <a-form-item label="几年级留级" class="mb-2">
                    <a-input
                      v-model="localData.academics.retainedGrade"
                      placeholder="请填写留级的年级"
                      class="max-w-md"
                    />
                  </a-form-item>
                  <a-form-item label="留级理由">
                    <a-input
                      v-model="localData.academics.retainedReason"
                      placeholder="请填写留级理由"
                      class="max-w-md"
                    />
                  </a-form-item>
                </div>
              </a-space>
            </div>

            <!-- 学习问题 -->
            <div class="mt-6">
              <p class="mb-2">4. 你孩子在学校里有无学习或其他问题（不包括上面三个问题）？</p>
              <a-space direction="vertical">
                <a-radio-group v-model="localData.academics.problems">
                  <a-radio :value="false">没有</a-radio>
                  <a-radio :value="true">有问题</a-radio>
                </a-radio-group>

                <div v-if="localData.academics.problems" class="pl-4 pt-2">
                  <a-form-item label="问题内容" class="mb-2">
                    <a-textarea
                      v-model="localData.academics.problemsContent"
                      placeholder="请描述问题内容"
                      class="max-w-md"
                    />
                  </a-form-item>
                  <a-form-item label="问题何时开始" class="mb-2">
                    <a-input
                      v-model="localData.academics.problemsStart"
                      placeholder="请填写问题开始时间"
                      class="max-w-md"
                    />
                  </a-form-item>
                  <a-form-item label="问题是否已解决">
                    <a-radio-group v-model="localData.academics.problemsSolved">
                      <a-radio :value="false">未解决</a-radio>
                      <a-radio :value="true">已解决</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item v-if="localData.academics.problemsSolved" label="何时解决">
                    <a-input
                      v-model="localData.academics.problemsSolvedTime"
                      placeholder="请填写问题解决时间"
                      class="max-w-md"
                    />
                  </a-form-item>
                </div>
              </a-space>
            </div>
          </div>
        </a-collapse-item>
      </a-collapse>

      <a-divider />

      <div class="flex justify-between">
        <a-button size="mini" @click="prevStep">上一步</a-button>
        <a-button type="primary" size="mini" @click="saveData">保存并继续</a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { IconPlus, IconDelete } from '@arco-design/web-vue/es/icon';

  const props = defineProps({
    data: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:data', 'next-step', 'nextStep']);

  // 本地数据副本
  const localData = ref(
    { ...props.data } || {
      sportsActivities: { timeSpent: '' },
      organizations: {},
      relationship: { siblings: '' },
      hobbies: { timeSpent: '' },
      jobs: { list: '' },
      friends: { count: '' },
      academics: {
        inSchool: '',
        subjects: {
          others: {},
        },
      },
    },
  );

  // 无爱好/未参加等状态绑定
  const noSportsActivities = computed({
    get: () => localData.value.sportsActivities?.list?.length === 0,
    set: (val) => {
      if (val) {
        localData.value.sportsActivities.list = [];
      } else if (localData.value.sportsActivities?.list?.length === 0) {
        localData.value.sportsActivities.list = [''];
      }
    },
  });

  const noHobbies = computed({
    get: () => localData.value.hobbies?.list?.length === 0,
    set: (val) => {
      if (val) {
        localData.value.hobbies.list = [];
      } else if (localData.value.hobbies.list.length === 0) {
        localData.value.hobbies.list = [''];
      }
    },
  });

  const noOrganizations = computed({
    get: () => localData.value.organizations.list?.length === 0,
    set: (val) => {
      if (val) {
        localData.value.organizations.list = [];
      } else if (localData.value.organizations.list?.length === 0) {
        localData.value.organizations.list = [''];
      }
    },
  });

  const noJobs = computed({
    get: () => localData.value.jobs?.list?.length === 0,
    set: (val) => {
      if (val) {
        localData.value.jobs.list = [];
      } else if (localData.value.jobs?.list?.length === 0) {
        localData.value.jobs.list = [''];
      }
    },
  });

  // 未上学状态绑定
  const notInSchool = computed({
    get: () => !localData.value.academics.inSchool,
    set: (val) => {
      localData.value.academics.inSchool = !val;
    },
  });

  // 相处情况表格列
  const relationshipColumns = [
    { title: '', dataIndex: 'label', slotName: 'item' },
    { title: '', dataIndex: 'worse', slotName: 'worse' },
    { title: '', dataIndex: 'average', slotName: 'average' },
    { title: '', dataIndex: 'better', slotName: 'better' },
  ];

  // 相处情况表格数据
  const relationshipData = [
    { key: 'siblings', label: 'a. 与兄弟姊妹相处' },
    { key: 'otherChildren', label: 'b. 与其他儿童相处' },
    { key: 'parents', label: 'c. 对父母的行为' },
    { key: 'selfPlay', label: 'd. 自己工作和游戏' },
  ];

  // 处理相处情况选择
  const handleRelationshipChange = (key, value) => {
    localData.value.relationship[key] = value;
  };

  // 学业表现表格列
  const academicColumns = [
    { title: '科目', dataIndex: 'subject', slotName: 'subject' },
    { title: '不及格', dataIndex: 'fail', slotName: 'fail' },
    { title: '中等以下', dataIndex: 'belowAverage', slotName: 'belowAverage' },
    { title: '中等', dataIndex: 'average', slotName: 'average' },
    { title: '中等以上', dataIndex: 'aboveAverage', slotName: 'aboveAverage' },
  ];

  // 学业表现表格数据
  const academicData = [
    { key: 'reading', label: 'a. 阅读课' },
    { key: 'writing', label: 'b. 写作课' },
    { key: 'math', label: 'c. 算术课' },
    { key: 'phonetics', label: 'd. 拼音课' },
  ];

  // 处理学业表现选择
  const handleAcademicChange = (key, value) => {
    localData.value.academics.subjects[key] = value;
  };

  // 其他课程
  const otherSubjects = ref([{ name: '', score: null }]);

  // 添加其他课程
  const addOtherSubject = () => {
    otherSubjects.value.push({ name: '', score: null });
  };

  // 移除其他课程
  const removeOtherSubject = (index) => {
    otherSubjects.value.splice(index, 1);
  };

  // 添加项目（体育、爱好等）
  const addItem = (list) => {
    list.push('');
  };

  // 移除项目
  const removeItem = (list, index) => {
    list.splice(index, 1);
  };

  // 保存数据前处理其他课程
  const processOtherSubjects = () => {
    localData.value.academics.subjects.others = otherSubjects.value
      .filter((subject) => subject.name.trim() !== '' && subject.score !== null)
      .map((subject) => ({
        name: subject.name,
        score: subject.score,
      }));
  };

  // 监听数据变化
  watch(
    () => props.data,
    (newValue) => {
      localData.value = JSON.parse(JSON.stringify(newValue));

      // 初始化其他课程
      if (newValue.academics.subjects.others && newValue.academics.subjects.others.length > 0) {
        otherSubjects.value = newValue.academics.subjects.others.map((subject) => ({
          name: subject.name,
          score: subject.score,
        }));
      } else {
        otherSubjects.value = [{ name: '', score: null }];
      }
    },
    { deep: true },
  );

  // 保存数据
  const saveData = () => {
    processOtherSubjects();
    emit('update:data', JSON.parse(JSON.stringify(localData.value)));
    emit('nextStep');
  };

  // 上一步
  const prevStep = () => {
    emit('nextStep');
  };

  onMounted(() => {
    // 初始化其他课程
    if (props.data?.academics.subjects.others && props.data?.academics.subjects.others.length > 0) {
      otherSubjects.value = props.data?.academics.subjects.others.map((subject) => ({
        name: subject.name,
        score: subject.score,
      }));
    }

    // 初始化列表
    if (localData.value.sportsActivities.list?.length === 0) {
      localData.value.sportsActivities.list = [];
    }
    if (localData.value.hobbies.list?.length === 0) {
      localData.value.hobbies.list = [];
    }
    if (localData.value.organizations.list?.length === 0) {
      localData.value.organizations.list = [];
    }
    if (localData.value.jobs.list?.length === 0) {
      localData.value.jobs.list = [];
    }
  });
</script>

<style scoped>
  .border-primary {
    border-color: var(--primary-color);
  }

  :deep(.arco-form-item-label-col) {
    font-weight: 500;
  }

  :deep(.arco-collapse-item-header) {
    font-weight: 500;
  }

  @media (max-width: 640px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }
</style>
