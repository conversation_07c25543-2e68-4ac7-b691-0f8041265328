<script setup lang="ts">
  import { computed, ref, PropType, provide } from 'vue';
  import { CrudForm } from '@repo/ui/components';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import { nationsList } from '@repo/infrastructure/data';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import { Message } from '@arco-design/web-vue';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';

  const periodsList = getPeriodsList({
    from: 2023,
    wholeIsNeeded: false,
  });
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<any>,
    },
    schema: {
      type: Object as PropType<any>,
    },
  });
  const userStore = useUserStore();

  provide('nationsList', nationsList);
  provide('request', request);
  provide('PROJECT_URLS', PROJECT_URLS);
  provide('userStore', userStore);
  provide('Message', Message);
  provide('periodsList', periodsList);

  const emits = defineEmits(['update:visible', 'flush']);
  const modelValue = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });
  const bodyStyle = {
    backgroundColor: 'rgb(241,240,240)',
    height: '100%',
  };

  const customizeRef = ref(null);
  const currentPlan = ref(props.record);
  const formRef = ref(null);
  const addDemos = ref(null);

  const handlePreOk = async () => {
    if (!customizeRef.value?.isDefaultComponent) {
      await customizeRef.value?.handleSubmit();
    }
    // await addDemo.value?.handleSubmit();
    emits('flush');
  };
</script>

<template>
  <!--一版这里是自定义组件 学生应该是直接选择-->
  <a-modal
    v-model:visible="modelValue"
    title="新增安置报告"
    fullscreen
    :body-style="bodyStyle"
    :on-before-ok="handlePreOk"
  >
    <!--<addDemo v-if="modelValue" ref="addDemos" :record="record" />-->
    <customize-component
      v-if="modelValue"
      ref="customizeRef"
      :record="currentPlan"
      :model-value="currentPlan"
      module="SpecialCommitteeTeacherClient"
      page="Edit"
    >
      <template #default>
        <crud-form
          v-if="modelValue && false"
          ref="formRef"
          v-model="currentPlan"
          :schema="schema"
          :show-actions="customizeRef?.isDefaultComponent"
          :callback="() => {}"
        />
      </template>
    </customize-component>
  </a-modal>
</template>

<style scoped lang="scss"></style>
