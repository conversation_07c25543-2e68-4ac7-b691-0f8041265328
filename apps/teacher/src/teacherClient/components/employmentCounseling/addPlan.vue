<script setup lang="ts">
  import { onMounted, ref, computed } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import isBetween from 'dayjs/plugin/isBetween';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    students: {
      type: Array,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const studentOptions = ref([]);
  const formData = ref({});
  const size = 'mini';
  const columns = ref([
    { title: '领域', dataIndex: 'field', slotName: 'field' },
    { title: '目标', dataIndex: 'target', slotName: 'target' },
    { title: '结果', dataIndex: 'result', slotName: 'result' },
    { title: '操作', slotName: 'operation', align: 'center', width: 100 },
  ]);
  const tableData = ref([]);

  const onChange = () => {};
  const onSelect = () => {};

  dayjs.extend(isBetween);

  // 判断当前日期是否在范围内
  const isInRange = computed(() => {
    const currentDate = dayjs();
    const startDate = dayjs(props.record.dateRange[0], 'YYYY-MM-DD'); // 起始日期
    const endDate = dayjs(props.record.dateRange[1], 'YYYY-MM-DD'); // 结束日期
    return currentDate.isBetween(startDate, endDate, 'day', '[]'); // 使用 '[]' 来包含边界
  });
  const msg = '不在时间范围不能操作';

  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    if (!isInRange.value) {
      Message.error(msg);
      return;
    }
    if (props.record.id) {
      try {
        await request(`/resourceRoom/employmentServicePlan/save`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'post',
          data: tableData.value,
        });
        emits('update:modelValue', false);
      } catch (e) {
        /**/
      }
    }
  };
  const loadData = async () => {
    if (props.record.id) {
      try {
        const res = await request(`/resourceRoom/employmentServicePlan/findByServiceId/${props.record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        tableData.value = res.data.items;
      } catch (e) {
        /**/
      }
    }
  };

  const handleAdd = () => {
    if (isInRange.value)
      request('/resourceRoom/employmentServicePlan', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: {
          employmentService: { id: props.record.id },
        },
      }).then((res) => {
        tableData.value.push({
          ...res.data,
        });
      });
    else Message.error(msg);
  };
  const handleDel = async (id: number) => {
    if (!isInRange.value) {
      Message.error(msg);
      return;
    }
    await request(`/resourceRoom/employmentServicePlan/${id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    }).then(() => {
      loadData();
    });
  };

  onMounted(async () => {
    if (props.students != null && props.students.length > 0) {
      props.students.forEach((item: any) => {
        studentOptions.value.push({
          label: item.name,
          value: item.id,
        });
      });
    }
    await loadData();
  });
</script>

<template>
  <a-modal
    :closable="false"
    :on-before-ok="handlePreOk"
    :visible="visible"
    width="900px"
    ok-text="保存"
    cancel-text="返回"
    @cancel="handleCancel"
  >
    <template #title>
      <span>{{ record.student.name }} - 添加支持性就业辅导计划明细</span>
    </template>
    <a-form :auto-label-width="true">
      <a-row>
        <a-col :span="12">
          <a-form-item label="时间范围">
            <a-range-picker
              :size="size"
              :disabled="true"
              :default-value="record.dateRange.map((date) => dayjs(date))"
              class="w-full"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12" style="display: flex; justify-content: flex-end">
          <a-button type="outline" class="mr-10" :size="size" @click="handleAdd"
            ><template #icon><icon-plus /></template> 添加
          </a-button>
        </a-col>
      </a-row>
    </a-form>
    <a-table :columns="columns" :data="tableData" :bordered="{ cell: true }">
      <template #field="{ record }">
        <a-textarea v-model="record.field" placeholder="请输入领域" />
      </template>
      <template #target="{ record }">
        <a-textarea v-model="record.target" placeholder="请输入目标" />
      </template>
      <template #result="{ record }">
        <a-textarea v-model="record.result" placeholder="请输入结果" />
      </template>
      <template #operation="{ record }">
        <a-button status="danger" :size="size" @click="handleDel(record.id)">删除</a-button>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
