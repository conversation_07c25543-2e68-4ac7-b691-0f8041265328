<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    students: {
      type: Array,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const data = ref<Record<string, any>>([]);
  const loadData = async () => {
    if (props.record != null) {
      const { data: res } = await request(`/resourceRoom/employmentServiceRecord/findRecords/${props.record.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      data.value = res;
    }
  };
  const columns = ref([
    { title: '领域', dataIndex: 'field' },
    { title: '指导教师', dataIndex: 'teacher' },
    { title: '辅导日期', dataIndex: 'tutoringDate', slotName: 'tutoringDate' },
  ]);

  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    emits('update:modelValue', false);
  };
  onMounted(async () => {
    await loadData();
  });
</script>

<template>
  <a-modal :visible="visible" width="50%" :closable="false" :on-before-ok="handlePreOk" @cancel="handleCancel">
    <div class="flex justify-between rounded-md bg-gray-50 p-4">
      <div class="flex-1 text-left">
        <span>学生：{{ record.student.name }}</span>
      </div>
      <div class="flex-1 text-left">
        <span>年龄：{{ record.student.age }}</span>
      </div>
      <div class="flex-1 text-left">
        <span>障碍类型：{{ record.student.disorders }}</span>
      </div>
      <div class="flex-1 text-left">
        <span>指导教师 ：{{ record.guideTeacher }}</span>
      </div>
    </div>

    <div class="rounded-md bg-gray-50 pl-4 pr-4 pb-4">
      <div class="mb-4">支持性就业单位 ：{{ record.companyName }}</div>
      <div v-if="record?.dateRange.length > 0">时间范围：{{ record.dateRange[0] }} / {{ record.dateRange[1] }}</div>
    </div>
    <a-card>
      <div v-for="(v, k) in data" :key="k">
        <a-divider orientation="left">
          <span class="font-bold">{{ k }}</span>
        </a-divider>
        <a-table :columns="columns" :data="v" :bordered="{ cell: true }">
          <template #tutoringDate="{ record }">
            <span v-if="record.tutoringDate">{{ record.tutoringDate.split(' ')[0] }}</span>
          </template>
        </a-table>
      </div>
    </a-card>
  </a-modal>
</template>

<style scoped lang="scss"></style>
