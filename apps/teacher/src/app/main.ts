// eslint-disable-next-line import/order
import App from './container.vue';
import _ from 'lodash';
import { axiosInstance } from '@repo/infrastructure/request';
import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';

// import '@/assets/iconfont/iconfont';
import '@arco-design/web-vue/dist/arco.css';

// common view
import { createPinia } from 'pinia';

// eslint-disable-next-line import/order
import { getRouter } from '@/router';
import '@repo/infrastructure/iconfont-online-css';
import '@repo/infrastructure/iconfont';
import directive from '@repo/ui/directive';
// eslint-disable-next-line import/no-cycle
import { vue3ScrollSeamless } from 'vue3-scroll-seamless';
import { initWindowErrorHandler } from '@repo/ui/components/utils/errorHandler.ts';
import { appGlobalConfig } from '@repo/infrastructure/utils';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';

appGlobalConfig();

dayjs.extend(isBetween);

//
const pinia = createPinia();
const app = createApp(App);
// app.use(scroll);
app.use(pinia);
app.use(ArcoVueIcon);
app.use(ArcoVue);

// wps
// @ts-ignore
app.config.globalProperties.wps = window.WebOfficeSDK;

app.config.globalProperties.globalData = {};

// eslint-disable-next-line no-underscore-dangle
// app.config.globalProperties.__ = _;
// import {Message} from "element-ui";
// app.use(baseTable);
// app.use(baseForm);
// @ts-ignore
app.use(directive);
app.config.globalProperties.axios = axiosInstance;

initWindowErrorHandler();

// @ts-ignore

async function initializeApp() {
  const router = await getRouter();
  app.use(router);
  app.provide('router', router);

  app.component('ScrollSeamless', vue3ScrollSeamless);

  app.mount('#app-main');
}

initializeApp();

export default app;
