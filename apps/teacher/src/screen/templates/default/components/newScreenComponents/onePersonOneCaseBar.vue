<template>
  <div class="title">
    <span class="text-2xl mr-10">残障学生一人一案</span>
    <span class="yellow-text">{{ rawData.totalCountCase }}</span>
    <span class="text-2xl ml-10">份</span>
  </div>

  <div id="onePersonOneCaseBar"></div>

  <div class="card-view">
    <div class="title">
      <div class="flex-5 text-center">未完成学校</div>
      <div class="flex-2 text-center">未完成份数</div>
      <div class="flex-3 text-center">未进系统人数</div>
    </div>

    <a-divider />

    <div class="content">
      <div v-for="(item, index) in rawData.caseStatistics" :key="index">
        <div class="flex mb-2 text-stone-400 p-1 cursor-pointer">
          <div class="flex-6 text-left">{{ item.schoolName }}</div>
          <div class="flex-1 text-center">{{ item.missing }}</div>
          <div class="flex-3 text-center">
            <span class="mr-2">{{ item.unRegisterNum }}</span>
            <span class="text-teal-700 ml-3" @click="showStudent(item.schoolName, index)">学生名单</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <a-modal
    :visible="visible"
    ok-text="关闭"
    :closable="false"
    hide-cancel
    class="bg-transparent"
    :title="currentSchool"
    @ok="handelOk"
    @cancel="handelCancel"
  >
    <a-table size="small" :columns="colums" :data="studentList" :pagination="false" class="text-center" />
  </a-modal>
</template>

<script>
  import { Pie } from '@antv/g2plot';

  export default {
    name: 'OnePersonOneCaseBar',
    props: {
      rawData: {
        type: Object,
      },
    },
    emits: ['show'],
    data() {
      return {
        visible: false,
        unComplete: 0,
        unRegisterNum: 0,
        moreRegisterNum: 0,
        complete: 0,
        data: [],
        currentSchool: '',
        studentList: [],
        colums: [
          { title: '姓名', dataIndex: 'name', align: 'center' },
          { title: '性别', dataIndex: 'gender', align: 'center' },
          { title: '残疾障碍', dataIndex: 'disorders', align: 'center' },
        ],
      };
    },
    mounted() {
      this.computed();
      this.pei();
    },
    methods: {
      handelOk() {
        this.visible = false;
      },
      handelCancel() {
        this.visible = false;
      },
      showStudent(name, index) {
        this.studentList = [];
        this.visible = true;
        this.currentSchool = name;
        this.rawData.caseStatistics[index].student.forEach((item) => {
          this.studentList.push({
            name: item.name,
            gender: item.gender,
            disorders: item.disorders,
          });
        });
      },
      computed() {
        this.rawData.caseStatistics.forEach((item) => {
          this.unComplete += item.missing;
          this.complete += item.missing;
          this.unRegisterNum += item.unRegisterNum > 0 ? item.unRegisterNum : 0;
          this.moreRegisterNum += item.unRegisterNum < 0 ? -1 * item.unRegisterNum : 0;
        });
        this.data = [
          { type: '已完成', count: this.rawData.totalCountCase - this.complete },
          { type: '未完成', count: this.unComplete },
          { type: '未进系统', count: this.unRegisterNum },
          { type: '多进系统', count: this.moreRegisterNum },
        ];
      },
      pei() {
        const colors = ['#50eccb', '#ff814b', '#ffc149', '#8e2ab8'];

        const { data } = this;
        const piePlot = new Pie('onePersonOneCaseBar', {
          appendPadding: 10,
          data,
          colorField: 'type',
          angleField: 'count',
          radius: 0.8,
          innerRadius: 0,
          color: colors,
          label: {
            type: 'spider',
            content: '{name}\n{percentage}',
            offset: 10, // 可以调整偏移量来避免标签重叠
            style: {
              fill: '#f3f3f3', // 设置标签文本的颜色
            },
            line: {
              // 使用G2的shape属性来设置线条样式
              shape: 'line',
              style: {
                stroke: '#62ff83', // 设置线条颜色
                lineWidth: 2, // 设置线条宽度
              },
            },
          },
          legend: {
            position: 'bottom',
            marker: {
              symbol: 'circle', // 图例标记的形状
            },
            itemName: {
              style: {
                fill: '#ffffff', // 设置图例文本的颜色
              },
            },
          },
          interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
          statistic: {
            title: false,
            content: {
              style: {
                whiteSpace: 'pre-wrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                color: '#ffffff',
              },
              content: ' ', // 如果需要显示内容，可以在这里修改
            },
          },
        });

        piePlot.render();
      },
    },
  };
</script>

<style scoped lang="scss">
  .content::-webkit-scrollbar {
    display: none; /* 对 Chrome 和 Safari 隐藏滚动条 */
  }

  /* Title */
  .title {
    margin-top: 0.5rem;
    text-align: center;
  }

  .title .text-2xl {
    font-size: 1.5rem;
  }

  .title .yellow-text {
    font-size: 2.5rem;
    color: #fbbf24;
  }

  .title .mr-10 {
    margin-right: 2.5rem;
  }

  .title .ml-10 {
    margin-left: 2.5rem;
  }

  /* One Person One Case Bar */
  #onePersonOneCaseBar {
    height: 360px;
    width: 100%;
    padding: 1.25rem;
  }

  /* Card View */
  .card-view {
    margin-top: 0.75rem;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .card-view .title {
    display: flex;
    justify-content: space-around;
  }

  .card-view .title .flex-5 {
    flex: 5;
    text-align: center;
  }

  .card-view .title .flex-2 {
    flex: 2;
    text-align: center;
  }

  .card-view .title .flex-3 {
    flex: 3;
    text-align: center;
  }

  /* Content Section */
  .card-view .content {
    overflow: auto;
    height: 400px;
  }

  .card-view .content .flex {
    display: flex;
    justify-content: space-around;
    margin-bottom: 0.5rem;
    color: #bcbcbc;
    padding: 0.25rem;
    cursor: pointer;
  }

  .card-view .content .flex .flex-6 {
    flex: 2;
    text-align: left;
  }

  .card-view .content .flex .flex-1,
  .card-view .content .flex .flex-3 {
    flex: 1;
    text-align: center;
  }

  .card-view .content .flex .flex-3 .text-teal-700 {
    color: #00d6a6;
    font-size: 0.875rem;
    margin-left: 0.75rem;
    float: right;
    margin-right: 10px;
  }

  .card-view .content .flex .flex-3 .text-teal-700:hover {
    cursor: pointer;
  }

  /* Divider */
  .a-divider {
    margin-bottom: 0.5rem;
  }

  /* Modal Table */
  .text-center {
    text-align: center;
  }

  .text-yellow-500 {
    color: #fbbf24;
  }

  .bg-transparent {
    background-color: transparent;
  }

  .text-teal-700 {
    color: #1d4ed8;
  }

  .cursor-pointer {
    cursor: pointer;
  }
</style>
