<script setup lang="ts">
  import { useList } from '@repo/infrastructure/crud';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, ref } from 'vue';
  import { Designer, Preview, getPrinterProvider } from '@repo/printer';
  import { request } from '@repo/infrastructure/request';
  import { Message, Modal } from '@arco-design/web-vue';
  import { cloneDeep } from 'lodash';
  import dayjs from 'dayjs';

  const { listData, loadData, listInit, loading, pagination, setLoading } = useList({
    api: '/resourceCenter/conferenceContent',
    defaultQueryParams: {
      sort: '-id',
    },
    axiosConfig: {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
    dataFormat: (raw: any) => {
      const { dateRange } = raw.conference || {};
      if (dateRange && dateRange.length === 2) {
        const startDate = dayjs(dateRange[0]).format('YYYY年MM月DD日');
        const endDate = dayjs(dateRange[1]).format('YYYY年MM月DD日');
        if (startDate === endDate) {
          raw.conferenceDate = startDate;
        } else {
          raw.conferenceDate = `${startDate} 至 ${endDate}`;
        }
      }
      return {
        ...raw,
        speakersDisplay: raw.speakers?.map((item: any) => item.name).join('、'),
      };
    },
  });

  const designerValue = ref<any>({
    type: 'ConferenceCertificate',
  });
  const conferenceSearchLoading = ref<any>(false);
  const userSearchLoading = ref<any>(false);
  const saveLoading = ref<any>(false);
  const editData = ref<any>({});
  const editVisible = ref<any>(false);
  const conferencesList = ref<any[]>([]);
  const editFormRef = ref<any>(null);
  const usersList = ref<any[]>([]);
  const printerDesignVisible = ref<any>(false);
  const previewVisible = ref(false);

  const customElementTypeProvider = getPrinterProvider('customElementType', [
    {
      label: '会议培训证明',
      elements: [
        {
          field: 'conferenceSubject',
          label: '会议主题',
        },
        {
          field: 'subject',
          label: '主讲题目',
        },
        {
          field: 'speakersDisplay',
          label: '主讲人',
        },
        {
          field: 'orgName',
          label: '主讲单位',
        },
        {
          field: 'orgName',
          label: '主讲单位',
        },
        {
          field: 'conferenceDate',
          label: '会议时间',
        },
      ],
    },
  ]);

  const designerRef = ref<any>(null);
  const previewRef = ref<any>(null);

  const handleSearchConferences = async (value?: string) => {
    conferenceSearchLoading.value = true;
    try {
      const { data } = await request('/resourceCenter/conference', {
        params: {
          subject: value ? `%${value}%` : undefined,
          pageSize: 10,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      conferencesList.value = data.items || [];
    } finally {
      conferenceSearchLoading.value = false;
    }
  };

  const handleShowEdit = async (raw?: any) => {
    editData.value = cloneDeep(raw || {});
    editVisible.value = true;

    if (!raw?.id) {
      await handleSearchConferences();
    } else {
      conferencesList.value = [raw.conference];
    }
  };

  const handleSave = async () => {
    saveLoading.value = true;
    try {
      if (!editData.value.conference?.id) {
        Message.error('请选择会议主题');
        throw new Error('请选择会议主题');
      }
      const errors = await editFormRef.value?.validate();
      if (errors) {
        Message.error(errors[0].message);
        throw new Error(errors[0].message);
      }
      const { id, ...data } = editData.value;
      await request(`/resourceCenter/conferenceContent${id ? `/${id}` : ''}`, {
        method: id ? 'put' : 'post',
        data,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      editVisible.value = false;
      await loadData();
    } finally {
      saveLoading.value = false;
    }
  };

  const handleSearchSpeaker = async (value?: string) => {
    if (!value) {
      return;
    }
    try {
      userSearchLoading.value = true;

      const { data } = await request('/org/companyUser', {
        params: {
          name: `%${value}%`,
          pageSize: 10,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      usersList.value = data.items || [];
    } finally {
      userSearchLoading.value = false;
    }
  };

  const handleSpeakerSelect = (value: any) => {
    editData.value.speakers = editData.value.speakers || [];
    if (editData.value.speakers.some((item: any) => item.id === value.id)) {
      return;
    }
    editData.value.speakers.push(value);
    editData.value.querySpeaker = undefined;
    usersList.value = [];
  };

  const handleRemoveSpeaker = (value: any) => {
    editData.value.speakers = editData.value.speakers.filter((item: any) => item.id !== value.id);
  };

  const handleDeleteRecord = async (record: any) => {
    await Modal.confirm({
      title: '删除会议培训证明',
      okButtonProps: { status: 'danger' },
      content: '确定删除该会议培训证明吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await request(`/resourceCenter/conferenceContent/${record.id}`, {
            method: 'delete',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          });
          await loadData();
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleShowPrintDesign = () => {
    printerDesignVisible.value = true;
  };

  const handlePrint = (record: any) => {
    previewVisible.value = true;
    previewRef.value?.handlePrint([record]);
  };

  onMounted(async () => {
    await listInit();
    await loadData();

    const { data } = await request('/common/printTemplate', {
      params: {
        type: 'ConferenceCertificate',
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    if (data.items?.length) {
      designerValue.value = {
        ...data.items[0],
        content: JSON.parse(data.items[0].content),
      };
    }
  });
</script>

<template>
  <div class="py-2 pr-2 flex-1">
    <a-card title="会议培训证明" class="w-full">
      <template #extra>
        <div class="flex gap-2">
          <a-button type="primary" size="mini" @click="() => handleShowEdit()">
            <template #icon>
              <IconPlus />
            </template>
            新增
          </a-button>
          <a-button type="outline" size="mini" @click="handleShowPrintDesign">打印模板设计</a-button>
        </div>
      </template>
      <a-table class="w-full" :data="listData" :loading="loading" :pagination="pagination">
        <template #columns>
          <a-table-column title="序号" :width="60" align="center">
            <template #cell="{ rowIndex }">
              {{ pagination.pageSize * (pagination.current - 1) + rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="会议主题">
            <template #cell="{ record }">
              {{ record.conference.subject }}
            </template>
          </a-table-column>
          <a-table-column title="主讲人">
            <template #cell="{ record }">
              <div>
                {{ record.speakersDisplay }}
              </div>
              <small class="text-gray-500">{{ record.orgName }}</small>
            </template>
          </a-table-column>
          <a-table-column title="主讲题目">
            <template #cell="{ record }">
              {{ record.subject }}
            </template>
          </a-table-column>
          <a-table-column title="指导教师">
            <template #cell="{ record }">
              {{ record.guidanceTeacher }}
            </template>
          </a-table-column>
          <a-table-column>
            <template #cell="{ record }">
              <a-space>
                <a-button size="mini" @click="() => handleShowEdit(record)"> 编辑 </a-button>
                <a-button size="mini" @click="() => handleDeleteRecord(record)"> 删除 </a-button>
                <a-button size="mini" @click="() => handlePrint(record)"> 打印 </a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="editVisible"
      :title="editData.id ? '修改会议培训证明' : '添加会议培训证明'"
      :ok-button-props="{ htmlType: 'submit', type: 'primary' }"
      :ok-loading="saveLoading"
      :on-before-ok="handleSave"
    >
      <a-form ref="editFormRef" :model="editData">
        <a-form-item field="conference" label="会议主题">
          <a-select
            v-model="editData.conference"
            allow-search
            :filter-option="false"
            placeholder="选择或输入会议主题搜索"
            :loading="conferenceSearchLoading"
            @search="handleSearchConferences"
          >
            <a-option
              v-for="conference in conferencesList"
              :key="conference.id"
              :value="conference"
              @click="() => (editData.conference = conference)"
            >
              {{ conference.subject }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="subject" label="主讲题目" :rules="[{ required: true, message: '请输入主讲题目' }]">
          <a-input v-model="editData.subject" />
        </a-form-item>
        <a-form-item field="orgName" label="主讲人单位" :rules="[{ required: true, message: '请输入主讲人单位' }]">
          <a-input v-model="editData.orgName" />
        </a-form-item>
        <a-form-item field="speaker" label="主讲人">
          <div class="flex flex-col gap-2 flex-1">
            <a-select
              v-model="editData.querySpeaker"
              allow-search
              :filter-option="false"
              placeholder="输入用户姓名搜索，可添加多个用户"
              :loading="userSearchLoading"
              @search="handleSearchSpeaker"
            >
              <a-option
                v-for="speaker in usersList"
                :key="speaker.id"
                :value="speaker"
                @click.stop="() => handleSpeakerSelect(speaker)"
              >
                {{ speaker.name }}
              </a-option>
            </a-select>
            <div class="flex gap-2 flex-wrap">
              <a-tag
                v-for="speaker in editData.speakers"
                :key="speaker.id"
                closable
                color="purple"
                @close="() => handleRemoveSpeaker(speaker)"
              >
                {{ speaker.name }}
              </a-tag>
            </div>
          </div>
        </a-form-item>
        <a-form-item field="orgName" label="指导老师">
          <a-input v-model="editData.guidanceTeacher" />
        </a-form-item>
      </a-form>
    </a-modal>

    <designer
      v-if="printerDesignVisible"
      ref="designerRef"
      v-model="designerValue"
      v-model:visible="printerDesignVisible"
      :provider="customElementTypeProvider"
    />
    <preview ref="previewRef" v-model:visible="previewVisible" :template="designerValue.content" />
  </div>
</template>

<style scoped lang="less"></style>
