<template>
  <div class="bg-gray-50 min-h-screen p-6">
    <div class="mx-auto">
      <!-- 顶部导航栏 -->
      <div class="bg-white rounded-lg shadow-sm mb-6 overflow-hidden">
        <div class="flex border-b">
          <div
            v-for="tab in tabs"
            :key="tab.key"
            class="px-6 py-4 font-medium cursor-pointer transition-colors"
            :class="
              currentTab === tab.key ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'
            "
            @click="switchTab(tab.key)"
          >
            {{ tab.label }}
          </div>
        </div>

        <!-- 筛选区域 -->
        <div class="p-4 bg-gray-50 border-b">
          <div class="flex flex-wrap gap-4">
            <!-- 教康评估筛选条件 -->
            <template v-if="currentTab === 'assessment'">
              <a-select placeholder="所属学校" style="width: 180px" allow-clear>
                <a-option value="all">全部学校</a-option>
                <a-option v-for="school in schools" :key="school" :value="school">{{ school }}</a-option>
              </a-select>

              <a-select placeholder="学期" style="width: 150px" allow-clear>
                <a-option value="2023-2024-2">2023-2024学年下学期</a-option>
                <a-option value="2023-2024-1">2023-2024学年上学期</a-option>
                <a-option value="2022-2023-2">2022-2023学年下学期</a-option>
              </a-select>

              <a-select placeholder="科目" style="width: 120px" allow-clear>
                <a-option value="all">全部科目</a-option>
                <a-option v-for="subject in subjects" :key="subject" :value="subject">{{ subject }}</a-option>
              </a-select>

              <a-select placeholder="次数" style="width: 120px" allow-clear>
                <a-option value="1">第1次</a-option>
                <a-option value="2">第2次</a-option>
                <a-option value="3">第3次</a-option>
              </a-select>

              <a-select placeholder="类型" style="width: 120px" allow-clear>
                <a-option value="all">全部类型</a-option>
                <a-option value="individual">个别评估</a-option>
                <a-option value="group">小组评估</a-option>
              </a-select>

              <a-button type="outline" status="normal">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>

              <a-button type="text" status="normal">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </template>

            <!-- 教康计划筛选条件 -->
            <template v-if="currentTab === 'plan'">
              <a-select placeholder="所属学校" style="width: 180px" allow-clear>
                <a-option value="all">全部学校</a-option>
                <a-option v-for="school in schools" :key="school" :value="school">{{ school }}</a-option>
              </a-select>

              <a-select placeholder="学期" style="width: 150px" allow-clear>
                <a-option value="2023-2024-2">2023-2024学年下学期</a-option>
                <a-option value="2023-2024-1">2023-2024学年上学期</a-option>
                <a-option value="2022-2023-2">2022-2023学年下学期</a-option>
              </a-select>

              <a-select placeholder="科目" style="width: 120px" allow-clear>
                <a-option value="all">全部科目</a-option>
                <a-option v-for="subject in subjects" :key="subject" :value="subject">{{ subject }}</a-option>
              </a-select>

              <a-select placeholder="类型" style="width: 120px" allow-clear>
                <a-option value="all">全部类型</a-option>
                <a-option value="iep">个别化教育计划</a-option>
                <a-option value="support">支持计划</a-option>
                <a-option value="homeTeaching">送教计划</a-option>
                <a-option value="rehabilitation">康复计划</a-option>
              </a-select>

              <a-button type="outline" status="normal">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>

              <a-button type="text" status="normal">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </template>

            <!-- 教康实施筛选条件 -->
            <template v-if="currentTab === 'implementation'">
              <a-select placeholder="所属学校" style="width: 180px" allow-clear>
                <a-option value="all">全部学校</a-option>
                <a-option v-for="school in schools" :key="school" :value="school">{{ school }}</a-option>
              </a-select>

              <a-select placeholder="学期" style="width: 150px" allow-clear>
                <a-option value="2023-2024-2">2023-2024学年下学期</a-option>
                <a-option value="2023-2024-1">2023-2024学年上学期</a-option>
                <a-option value="2022-2023-2">2022-2023学年下学期</a-option>
              </a-select>

              <a-select placeholder="任教班级" style="width: 120px" allow-clear>
                <a-option value="all">全部班级</a-option>
                <a-option value="1-1">一年级1班</a-option>
                <a-option value="1-2">一年级2班</a-option>
                <a-option value="2-1">二年级1班</a-option>
              </a-select>

              <a-select placeholder="任教年级" style="width: 120px" allow-clear>
                <a-option value="all">全部年级</a-option>
                <a-option value="1">一年级</a-option>
                <a-option value="2">二年级</a-option>
                <a-option value="3">三年级</a-option>
              </a-select>

              <a-select placeholder="类型" style="width: 120px" allow-clear>
                <a-option value="all">全部类型</a-option>
                <a-option value="regular">常规教学</a-option>
                <a-option value="special">专项教学</a-option>
              </a-select>

              <a-select placeholder="科目" style="width: 120px" allow-clear>
                <a-option value="all">全部科目</a-option>
                <a-option v-for="subject in subjects" :key="subject" :value="subject">{{ subject }}</a-option>
              </a-select>

              <a-button type="outline" status="normal">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>

              <a-button type="text" status="normal">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </template>
          </div>
        </div>
      </div>

      <!-- 教康评估视图 -->
      <div v-if="currentTab === 'assessment'">
        <!-- 数据统计区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
          <!-- 评估概况 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">评估概况</span>
              <div class="w-7 h-7 rounded-full bg-blue-50 flex items-center justify-center">
                <icon-bar-chart class="text-blue-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div>
                <div class="text-2xl font-semibold text-gray-800">{{ assessmentStats.total }}</div>
                <div class="text-xs text-gray-500">总评估</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ assessmentStats.completed }}</div>
                <div class="text-xs text-gray-500">已完成</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ assessmentStats.inProgress }}</div>
                <div class="text-xs text-gray-500">进行中</div>
              </div>
            </div>
          </div>

          <!-- 评估详情 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">评估详情</span>
              <div class="w-7 h-7 rounded-full bg-green-50 flex items-center justify-center">
                <icon-user-group class="text-green-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div
                class="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors"
                @click="showAssessedStudents"
              >
                <div class="text-2xl font-semibold text-green-500">{{ assessmentStats.assessedStudents }}</div>
                <div class="text-xs text-gray-500">已评估</div>
              </div>
              <div class="text-gray-300">|</div>
              <div
                class="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors"
                @click="showUnassessedStudents"
              >
                <div class="text-2xl font-semibold text-red-500">{{ assessmentStats.unassessedStudents }}</div>
                <div class="text-xs text-gray-500">未评估</div>
              </div>
            </div>
          </div>

          <!-- 评估进度 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">评估进度</span>
              <div class="w-7 h-7 rounded-full bg-purple-50 flex items-center justify-center">
                <icon-file class="text-purple-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div
                class="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors"
                @click="showAssessmentTeachers"
              >
                <div class="text-2xl font-semibold text-blue-500">{{ assessmentStats.teachers }}</div>
                <div class="text-xs text-gray-500">参与教师</div>
              </div>
              <div class="text-gray-300">|</div>
              <div
                class="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors"
                @click="showAssessmentScales"
              >
                <div class="text-2xl font-semibold text-purple-500">{{ assessmentStats.scales }}</div>
                <div class="text-xs text-gray-500">量表份数</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 学生列表区域 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
          <div class="flex justify-between items-center p-5 border-b border-gray-100">
            <h2 class="text-lg font-medium text-gray-800">{{ getListTitle() }}</h2>
            <div class="flex items-center space-x-3">
              <a-input-search placeholder="搜索学生/教师/量表" style="width: 240px" allow-clear size="medium" />
              <a-button type="primary" status="success">
                <template #icon>
                  <icon-plus />
                </template>
                新增评估
              </a-button>
            </div>
          </div>

          <!-- 默认视图：学生评估列表 -->
          <a-table
            v-if="currentListView === 'default'"
            :columns="assessmentColumns"
            :data="assessmentRecords"
            :pagination="{
              total: 103,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #gender="{ record }">
              <a-tag :color="record.gender === '男' ? 'blue' : 'pink'" size="small">
                {{ record.gender }}
              </a-tag>
            </template>

            <template #disabilityType="{ record }">
              <a-tag :color="getDisabilityColor(record.disabilityType)" size="small">
                {{ record.disabilityType }}
              </a-tag>
            </template>

            <template #status="{ record }">
              <a-tag :color="getStatusColor(record.status)" size="small">
                {{ record.status }}
              </a-tag>
            </template>

            <template #operations>
              <a-space>
                <a-button type="text" size="small">查看</a-button>
                <a-button type="text" size="small">编辑</a-button>
                <a-dropdown>
                  <a-button type="text" size="small">
                    <icon-more />
                  </a-button>
                  <template #content>
                    <a-doption>导出</a-doption>
                    <a-doption>删除</a-doption>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </a-table>

          <!-- 已评估学生列表 -->
          <a-table
            v-if="currentListView === 'assessedStudents'"
            :columns="assessedStudentsColumns"
            :data="assessedStudents"
            :pagination="{
              total: assessedStudents.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #gender="{ record }">
              <a-tag :color="record.gender === '男' ? 'blue' : 'pink'" size="small">
                {{ record.gender }}
              </a-tag>
            </template>

            <template #disabilityType="{ record }">
              <a-tag :color="getDisabilityColor(record.disabilityType)" size="small">
                {{ record.disabilityType }}
              </a-tag>
            </template>

            <template #operations="{ record }">
              <a-button type="text" size="small" @click="showStudentDetail(record)">详情</a-button>
            </template>
          </a-table>

          <!-- 未评估学生列表 -->
          <a-table
            v-if="currentListView === 'unassessedStudents'"
            :columns="unassessedStudentsColumns"
            :data="unassessedStudents"
            :pagination="{
              total: unassessedStudents.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #gender="{ record }">
              <a-tag :color="record.gender === '男' ? 'blue' : 'pink'" size="small">
                {{ record.gender }}
              </a-tag>
            </template>

            <template #disabilityType="{ record }">
              <a-tag :color="getDisabilityColor(record.disabilityType)" size="small">
                {{ record.disabilityType }}
              </a-tag>
            </template>

            <template #operations>
              <a-button type="text" size="small">详情</a-button>
            </template>
          </a-table>

          <!-- 参与评估教师列表 -->
          <a-table
            v-if="currentListView === 'assessmentTeachers'"
            :columns="assessmentTeachersColumns"
            :data="assessmentTeachers"
            :pagination="{
              total: assessmentTeachers.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #operations="{ record }">
              <a-button type="text" size="small" @click="showTeacherDetail(record)">详情</a-button>
            </template>
          </a-table>

          <!-- 评估量表列表 -->
          <a-table
            v-if="currentListView === 'assessmentScales'"
            :columns="assessmentScalesColumns"
            :data="assessmentScales"
            :pagination="{
              total: assessmentScales.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #operations="{ record }">
              <a-button type="text" size="small" @click="showScaleDetail(record)">详情</a-button>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 教康计划视图 -->
      <div v-if="currentTab === 'plan'">
        <!-- 数据统计区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
          <!-- 个别化教育计划 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">个别化教育计划</span>
              <div class="w-7 h-7 rounded-full bg-blue-50 flex items-center justify-center">
                <icon-book class="text-blue-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4 mb-3">
              <div>
                <div class="text-2xl font-semibold text-red-500">{{ planStats.iep.notStarted }}</div>
                <div class="text-xs text-gray-500">未开始</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ planStats.iep.inProgress }}</div>
                <div class="text-xs text-gray-500">进行中</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ planStats.iep.completed }}</div>
                <div class="text-xs text-gray-500">已完成</div>
              </div>
            </div>
            <div class="relative">
              <div class="flex items-center justify-between mb-1">
                <div
                  class="text-xs font-semibold"
                  :class="{
                    'text-red-600': getCompletionPercent(planStats.iep) < 30,
                    'text-amber-600':
                      getCompletionPercent(planStats.iep) >= 30 && getCompletionPercent(planStats.iep) < 90,
                    'text-green-600': getCompletionPercent(planStats.iep) >= 90,
                  }"
                >
                  {{ getCompletionPercent(planStats.iep) }}%
                </div>
              </div>
              <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                <div
                  :class="{
                    defaultClass,
                    'bg-red-500': getCompletionPercent(planStats.iep) < 30,
                    'bg-amber-500':
                      getCompletionPercent(planStats.iep) >= 30 && getCompletionPercent(planStats.iep) < 90,
                    'bg-green-500': getCompletionPercent(planStats.iep) >= 90,
                  }"
                  :style="{ width: `${getCompletionPercent(planStats.iep)}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 支持计划 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">支持计划</span>
              <div class="w-7 h-7 rounded-full bg-green-50 flex items-center justify-center">
                <icon-thumb-up class="text-green-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4 mb-3">
              <div>
                <div class="text-2xl font-semibold text-red-500">{{ planStats.support.notStarted }}</div>
                <div class="text-xs text-gray-500">未开始</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ planStats.support.inProgress }}</div>
                <div class="text-xs text-gray-500">进行中</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ planStats.support.completed }}</div>
                <div class="text-xs text-gray-500">已完成</div>
              </div>
            </div>
            <div class="relative">
              <div class="flex items-center justify-between mb-1">
                <div
                  class="text-xs font-semibold"
                  :class="{
                    'text-red-600': getCompletionPercent(planStats.support) < 30,
                    'text-amber-600':
                      getCompletionPercent(planStats.support) >= 30 && getCompletionPercent(planStats.support) < 90,
                    'text-green-600': getCompletionPercent(planStats.support) >= 90,
                  }"
                >
                  {{ getCompletionPercent(planStats.support) }}%
                </div>
              </div>
              <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                <div
                  :class="{
                    defaultClass,
                    'bg-red-500': getCompletionPercent(planStats.support) < 30,
                    'bg-amber-500':
                      getCompletionPercent(planStats.support) >= 30 && getCompletionPercent(planStats.support) < 90,
                    'bg-green-500': getCompletionPercent(planStats.support) >= 90,
                  }"
                  :style="{ width: `${getCompletionPercent(planStats.support)}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 送教计划 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">送教计划</span>
              <div class="w-7 h-7 rounded-full bg-purple-50 flex items-center justify-center">
                <icon-home class="text-purple-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4 mb-3">
              <div>
                <div class="text-2xl font-semibold text-red-500">{{ planStats.homeTeaching.notStarted }}</div>
                <div class="text-xs text-gray-500">未开始</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ planStats.homeTeaching.inProgress }}</div>
                <div class="text-xs text-gray-500">进行中</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ planStats.homeTeaching.completed }}</div>
                <div class="text-xs text-gray-500">已完成</div>
              </div>
            </div>
            <div class="relative">
              <div class="flex items-center justify-between mb-1">
                <div
                  class="text-xs font-semibold"
                  :class="{
                    'text-red-600': getCompletionPercent(planStats.homeTeaching) < 30,
                    'text-amber-600':
                      getCompletionPercent(planStats.homeTeaching) >= 30 &&
                      getCompletionPercent(planStats.homeTeaching) < 90,
                    'text-green-600': getCompletionPercent(planStats.homeTeaching) >= 90,
                  }"
                >
                  {{ getCompletionPercent(planStats.homeTeaching) }}%
                </div>
              </div>
              <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                <div
                  :class="{
                    defaultClass,
                    'bg-red-500': getCompletionPercent(planStats.homeTeaching) < 30,
                    'bg-amber-500':
                      getCompletionPercent(planStats.homeTeaching) >= 30 &&
                      getCompletionPercent(planStats.homeTeaching) < 90,
                    'bg-green-500': getCompletionPercent(planStats.homeTeaching) >= 90,
                  }"
                  :style="{ width: `${getCompletionPercent(planStats.homeTeaching)}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 康复计划 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">康复计划</span>
              <div class="w-7 h-7 rounded-full bg-amber-50 flex items-center justify-center">
                <icon-heart class="text-amber-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4 mb-3">
              <div>
                <div class="text-2xl font-semibold text-red-500">{{ planStats.rehabilitation.notStarted }}</div>
                <div class="text-xs text-gray-500">未开始</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ planStats.rehabilitation.inProgress }}</div>
                <div class="text-xs text-gray-500">进行中</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ planStats.rehabilitation.completed }}</div>
                <div class="text-xs text-gray-500">已完成</div>
              </div>
            </div>
            <div class="relative">
              <div class="flex items-center justify-between mb-1">
                <div
                  class="text-xs font-semibold"
                  :class="{
                    'text-red-600': getCompletionPercent(planStats.rehabilitation) < 30,
                    'text-amber-600':
                      getCompletionPercent(planStats.rehabilitation) >= 30 &&
                      getCompletionPercent(planStats.rehabilitation) < 90,
                    'text-green-600': getCompletionPercent(planStats.rehabilitation) >= 90,
                  }"
                >
                  {{ getCompletionPercent(planStats.rehabilitation) }}%
                </div>
              </div>
              <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                <div
                  :class="{
                    defaultClass,
                    'bg-red-500': getCompletionPercent(planStats.rehabilitation) < 30,
                    'bg-amber-500':
                      getCompletionPercent(planStats.rehabilitation) >= 30 &&
                      getCompletionPercent(planStats.rehabilitation) < 90,
                    'bg-green-500': getCompletionPercent(planStats.rehabilitation) >= 90,
                  }"
                  :style="{ width: `${getCompletionPercent(planStats.rehabilitation)}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 计划列表区域 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
          <div class="flex justify-between items-center p-5 border-b border-gray-100">
            <h2 class="text-lg font-medium text-gray-800">计划列表</h2>
            <div class="flex items-center space-x-3">
              <a-input-search placeholder="搜索计划/学生" style="width: 240px" allow-clear size="medium" />
              <a-button type="primary" status="success">
                <template #icon>
                  <icon-plus />
                </template>
                新增计划
              </a-button>
            </div>
          </div>

          <a-table
            :columns="planColumns"
            :data="planRecords"
            :pagination="{
              total: planRecords.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #parentConfirmation="{ record }">
              <a-tag :color="record.parentConfirmation ? 'green' : 'red'" size="small">
                {{ record.parentConfirmation ? '已确认' : '未确认' }}
              </a-tag>
            </template>

            <template #status="{ record }">
              <a-tag :color="getStatusColor(record.status)" size="small">
                {{ record.status }}
              </a-tag>
            </template>

            <template #operations="{}">
              <a-space>
                <a-button type="text" size="small">计划目标</a-button>
                <a-button type="text" size="small">查看</a-button>
                <a-dropdown>
                  <a-button type="text" size="small">
                    <icon-more />
                  </a-button>
                  <template #content>
                    <a-doption>编辑</a-doption>
                    <a-doption>删除</a-doption>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 教康实施视图 -->
      <div v-if="currentTab === 'implementation'">
        <!-- 数据统计区域 -->
        <div class="flex flex-wrap gap-4 mb-6">
          <!-- 教案统计 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">教案统计</span>
              <div class="w-7 h-7 rounded-full bg-blue-50 flex items-center justify-center">
                <icon-file class="text-blue-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div>
                <div class="text-2xl font-semibold text-gray-800">{{ implementationStats.total }}</div>
                <div class="text-xs text-gray-500">总教案</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-green-500">{{ implementationStats.submitted }}</div>
                <div class="text-xs text-gray-500">已提交</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-red-500">{{ implementationStats.unsubmitted }}</div>
                <div class="text-xs text-gray-500">未提交</div>
              </div>
            </div>
          </div>

          <!-- 教案类型 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">教案类型</span>
              <div class="w-7 h-7 rounded-full bg-purple-50 flex items-center justify-center">
                <icon-edit class="text-purple-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div>
                <div class="text-2xl font-semibold text-purple-500">{{ implementationStats.original }}</div>
                <div class="text-xs text-gray-500">原创教案</div>
              </div>
              <div class="text-gray-300">|</div>
              <div>
                <div class="text-2xl font-semibold text-amber-500">{{ implementationStats.referenced }}</div>
                <div class="text-xs text-gray-500">引用教案</div>
              </div>
            </div>
          </div>

          <!-- 人均教案 -->
          <div class="flex-1 min-w-[200px] bg-white rounded-lg shadow-sm px-4 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-500">人均教案</span>
              <div class="w-7 h-7 rounded-full bg-cyan-50 flex items-center justify-center">
                <icon-user class="text-cyan-500 text-sm" />
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div>
                <div class="text-2xl font-semibold text-cyan-500">{{ implementationStats.averagePerTeacher }}</div>
                <div class="text-xs text-gray-500">人均教案数</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实施列表区域 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
          <div class="flex justify-between items-center p-5 border-b border-gray-100">
            <h2 class="text-lg font-medium text-gray-800">实施列表</h2>
            <div class="flex items-center space-x-3">
              <a-input-search placeholder="搜索教师/班级" style="width: 240px" allow-clear size="medium" />
              <a-button type="primary" status="success">
                <template #icon>
                  <icon-plus />
                </template>
                新增教案
              </a-button>
            </div>
          </div>

          <a-table
            :columns="implementationColumns"
            :data="implementationRecords"
            :pagination="{
              total: implementationRecords.length,
              showTotal: true,
              pageSize: 10,
            }"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #operations>
              <a-space>
                <a-button type="text" size="small">查看教案</a-button>
                <a-button type="text" size="small">教学记录</a-button>
                <a-dropdown>
                  <a-button type="text" size="small">
                    <icon-more />
                  </a-button>
                  <template #content>
                    <a-doption>导出</a-doption>
                    <a-doption>统计</a-doption>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <!-- 学生详情模态框 -->
    <a-modal
      v-model:visible="showStudentDetailModal"
      title="学生评估详情"
      :footer="false"
      width="700px"
      modal-class="custom-modal"
    >
      <div v-if="selectedStudent">
        <div class="flex items-center mb-6">
          <div :class="boxClass">
            {{ selectedStudent.name.substring(0, 1) }}
          </div>
          <div>
            <h3 class="text-lg font-medium">{{ selectedStudent.name }}</h3>
            <div class="text-sm text-gray-500 mt-1">
              {{ selectedStudent.gender === '男' ? '男' : '女' }} | {{ selectedStudent.age }}岁 |
              {{ selectedStudent.disabilityType }}
            </div>
          </div>
        </div>

        <div class="border-t pt-4">
          <h4 class="font-medium mb-3">评估量表</h4>
          <a-table
            :columns="studentDetailColumns"
            :data="selectedStudent.assessments || []"
            :pagination="false"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #status="{ record }">
              <a-tag :color="getStatusColor(record.status)" size="small">
                {{ record.status }}
              </a-tag>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 教师详情模态框 -->
    <a-modal
      v-model:visible="showTeacherDetailModal"
      title="教师评估详情"
      :footer="false"
      width="700px"
      modal-class="custom-modal"
    >
      <div v-if="selectedTeacher">
        <div class="flex items-center mb-6">
          <div :class="boxClass">
            {{ selectedTeacher.name.substring(0, 1) }}
          </div>
          <div>
            <h3 class="text-lg font-medium">{{ selectedTeacher.name }}</h3>
            <div class="text-sm text-gray-500 mt-1">
              {{ selectedTeacher.school }} | 评估学生: {{ selectedTeacher.assessedCount }}人 | 评估次数:
              {{ selectedTeacher.assessmentCount }}次
            </div>
          </div>
        </div>

        <div class="border-t pt-4">
          <h4 class="font-medium mb-3">评估记录</h4>
          <a-table
            :columns="teacherDetailColumns"
            :data="selectedTeacher.assessments || []"
            :pagination="false"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #status="{ record }">
              <a-tag :color="getStatusColor(record.status)" size="small">
                {{ record.status }}
              </a-tag>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 量表详情模态框 -->
    <a-modal
      v-model:visible="showScaleDetailModal"
      title="量表评估详情"
      :footer="false"
      width="700px"
      modal-class="custom-modal"
    >
      <div v-if="selectedScale">
        <div class="mb-6">
          <h3 class="text-lg font-medium">{{ selectedScale.name }}</h3>
          <div class="text-sm text-gray-500 mt-1">
            {{ selectedScale.category }} | 评估学生: {{ selectedScale.assessedCount }}人 | 评估教师:
            {{ selectedScale.teacherCount }}人
          </div>
        </div>

        <div class="border-t pt-4">
          <h4 class="font-medium mb-3">评估记录</h4>
          <a-table
            :columns="scaleDetailColumns"
            :data="selectedScale.assessments || []"
            :pagination="false"
            :bordered="false"
            stripe
            class="custom-table"
          >
            <template #gender="{ record }">
              <a-tag :color="record.gender === '男' ? 'blue' : 'pink'" size="small">
                {{ record.gender }}
              </a-tag>
            </template>

            <template #disabilityType="{ record }">
              <a-tag :color="getDisabilityColor(record.disabilityType)" size="small">
                {{ record.disabilityType }}
              </a-tag>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import {
    IconFile,
    IconUser,
    IconUserGroup,
    IconBarChart,
    IconPlus,
    IconMore,
    IconSearch,
    IconRefresh,
    IconBook,
    IconThumbUp,
    IconHome,
    IconHeart,
    IconCheck,
    IconClose,
    IconEdit,
    IconCopy,
    IconRight,
  } from '@arco-design/web-vue/es/icon';

  // 注册图标组件
  const icons = {
    IconFile,
    IconUser,
    IconUserGroup,
    IconBarChart,
    IconPlus,
    IconMore,
    IconSearch,
    IconRefresh,
    IconBook,
    IconThumbUp,
    IconHome,
    IconHeart,
    IconCheck,
    IconClose,
    IconEdit,
    IconCopy,
    IconRight,
  };

  const defaultClass =
    'shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center transition-all duration-300';
  const boxClass =
    'w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-4 text-lg font-medium';

  // 标签页定义
  const tabs = [
    { key: 'assessment', label: '教康评估' },
    { key: 'plan', label: '教康计划' },
    { key: 'implementation', label: '教康实施' },
  ];

  const currentTab = ref('assessment');
  const currentListView = ref('default');

  // 模拟数据 - 学校列表
  const schools = [
    '成都市武侯区特殊教育学校',
    '成都市锦江区特殊教育学校',
    '成都市青羊区特殊教育学校',
    '成都市金牛区特殊教育学校',
  ];

  // 模拟数据 - 科目列表
  const subjects = ['生活语文', '生活数学', '生活适应', '运动康复', '劳动技能', '音乐', '美术'];

  // 模拟数据 - 障碍类型
  const disabilityTypes = [
    { type: '智力障碍', color: '#2B65D9' },
    { type: '视力残疾', color: '#5AD8A6' },
    { type: '听力残疾', color: '#5B8FF9' },
    { type: '言语残疾', color: '#F6BD16' },
    { type: '肢体残疾', color: '#E8684A' },
    { type: '精神残疾', color: '#6DC8EC' },
    { type: '多重残疾', color: '#9270CA' },
    { type: '其他障碍', color: '#FF9D4D' },
  ];

  // 评估统计数据
  const assessmentStats = reactive({
    total: 156,
    completed: 103,
    archived: 87,
    inProgress: 53,
    assessedStudents: 103,
    unassessedStudents: 47,
    teachers: 24,
    scales: 35,
  });

  // 计划统计数据
  const planStats = reactive({
    iep: {
      notStarted: 18,
      inProgress: 32,
      completed: 45,
    },
    support: {
      notStarted: 12,
      inProgress: 28,
      completed: 36,
    },
    homeTeaching: {
      notStarted: 8,
      inProgress: 15,
      completed: 22,
    },
    rehabilitation: {
      notStarted: 15,
      inProgress: 30,
      completed: 40,
    },
  });

  // 实施统计数据
  const implementationStats = reactive({
    total: 245,
    submitted: 198,
    unsubmitted: 47,
    original: 156,
    referenced: 89,
    averagePerTeacher: 10.2,
  });

  // 评估记录列表列定义
  const assessmentColumns = [
    { title: '学生姓名', dataIndex: 'name', width: 100 },
    { title: '性别', dataIndex: 'gender', width: 80, slotName: 'gender' },
    { title: '障碍类型', dataIndex: 'disabilityType', width: 120, slotName: 'disabilityType' },
    { title: '年龄', dataIndex: 'age', width: 80 },
    { title: '所属学校', dataIndex: 'school', width: 200 },
    { title: '评估量表', dataIndex: 'scale', width: 120 },
    { title: '评估次数', dataIndex: 'assessmentCount', width: 100 },
    { title: '状态', dataIndex: 'status', width: 100, slotName: 'status' },
    { title: '评估教师', dataIndex: 'teacher', width: 100 },
    { title: '得分', dataIndex: 'score', width: 80 },
    { title: '评估日期', dataIndex: 'date', width: 120 },
    { title: '操作', dataIndex: 'operations', width: 150, slotName: 'operations', fixed: 'right' },
  ];

  // 已评估学生列表列定义
  const assessedStudentsColumns = [
    { title: '学生姓名', dataIndex: 'name', width: 100 },
    { title: '性别', dataIndex: 'gender', width: 80, slotName: 'gender' },
    { title: '障碍类型', dataIndex: 'disabilityType', width: 120, slotName: 'disabilityType' },
    { title: '年龄', dataIndex: 'age', width: 80 },
    { title: '所属学校', dataIndex: 'school', width: 200 },
    { title: '评测量表(份)', dataIndex: 'scaleCount', width: 120 },
    { title: '评估教师(数量)', dataIndex: 'teacherCount', width: 150 },
    { title: '最近评估日期', dataIndex: 'lastAssessmentDate', width: 150 },
    { title: '操作', dataIndex: 'operations', width: 100, slotName: 'operations', fixed: 'right' },
  ];

  // 未评估学生列表列定义
  const unassessedStudentsColumns = [
    { title: '学生姓名', dataIndex: 'name', width: 100 },
    { title: '性别', dataIndex: 'gender', width: 80, slotName: 'gender' },
    { title: '障碍类型', dataIndex: 'disabilityType', width: 120, slotName: 'disabilityType' },
    { title: '年龄', dataIndex: 'age', width: 80 },
    { title: '所属学校', dataIndex: 'school', width: 200 },
    { title: '操作', dataIndex: 'operations', width: 100, slotName: 'operations', fixed: 'right' },
  ];

  // 评估教师列表列定义
  const assessmentTeachersColumns = [
    { title: '教师姓名', dataIndex: 'name', width: 100 },
    { title: '所属学校', dataIndex: 'school', width: 200 },
    { title: '评估人数', dataIndex: 'assessedCount', width: 100 },
    { title: '评估次数', dataIndex: 'assessmentCount', width: 100 },
    { title: '使用量表(份)', dataIndex: 'scaleCount', width: 120 },
    { title: '最近评估日期', dataIndex: 'lastAssessmentDate', width: 150 },
    { title: '操作', dataIndex: 'operations', width: 100, slotName: 'operations', fixed: 'right' },
  ];

  // 评估量表列表列定义
  const assessmentScalesColumns = [
    { title: '量表名称', dataIndex: 'name', width: 150 },
    { title: '量表分类', dataIndex: 'category', width: 120 },
    { title: '评估人数', dataIndex: 'assessedCount', width: 100 },
    { title: '评估教师', dataIndex: 'teacherCount', width: 100 },
    { title: '最近评估日期', dataIndex: 'lastAssessmentDate', width: 150 },
    { title: '操作', dataIndex: 'operations', width: 100, slotName: 'operations', fixed: 'right' },
  ];

  // 计划列表列定义
  const planColumns = [
    { title: '所属学期', dataIndex: 'semester', width: 150 },
    { title: '学生', dataIndex: 'student', width: 100 },
    { title: '制定日期', dataIndex: 'createDate', width: 120 },
    { title: '参与会议人员', dataIndex: 'participants', width: 200 },
    { title: '家长确认', dataIndex: 'parentConfirmation', width: 100, slotName: 'parentConfirmation' },
    { title: '协作者', dataIndex: 'collaborators', width: 150 },
    { title: '状态', dataIndex: 'status', width: 100, slotName: 'status' },
    { title: '操作', dataIndex: 'operations', width: 200, slotName: 'operations', fixed: 'right' },
  ];

  // 实施列表列定义
  const implementationColumns = [
    { title: '学校', dataIndex: 'school', width: 200 },
    { title: '教师姓名', dataIndex: 'teacherName', width: 100 },
    { title: '任教班级', dataIndex: 'class', width: 120 },
    { title: '任教年级', dataIndex: 'grade', width: 100 },
    { title: '已提交教案', dataIndex: 'submittedCount', width: 120 },
    { title: '周课时', dataIndex: 'weeklyHours', width: 100 },
    { title: '原创', dataIndex: 'originalCount', width: 80 },
    { title: '引用', dataIndex: 'referencedCount', width: 80 },
    { title: '操作', dataIndex: 'operations', width: 200, slotName: 'operations', fixed: 'right' },
  ];

  // 学生详情模态框列定义
  const studentDetailColumns = [
    { title: '量表名称', dataIndex: 'scaleName', width: 150 },
    { title: '评估教师', dataIndex: 'teacher', width: 100 },
    { title: '评估日期', dataIndex: 'date', width: 120 },
    { title: '得分', dataIndex: 'score', width: 80 },
    { title: '状态', dataIndex: 'status', width: 100, slotName: 'status' },
  ];

  // 教师详情模态框列定义
  const teacherDetailColumns = [
    { title: '学生姓名', dataIndex: 'studentName', width: 100 },
    { title: '量表名称', dataIndex: 'scaleName', width: 150 },
    { title: '评估日期', dataIndex: 'date', width: 120 },
    { title: '得分', dataIndex: 'score', width: 80 },
    { title: '状态', dataIndex: 'status', width: 100, slotName: 'status' },
  ];

  // 量表详情模态框列定义
  const scaleDetailColumns = [
    { title: '学生姓名', dataIndex: 'studentName', width: 100 },
    { title: '性别', dataIndex: 'gender', width: 80, slotName: 'gender' },
    { title: '障碍类型', dataIndex: 'disabilityType', width: 120, slotName: 'disabilityType' },
    { title: '年龄', dataIndex: 'age', width: 80 },
    { title: '评估教师', dataIndex: 'teacher', width: 100 },
    { title: '评估日期', dataIndex: 'date', width: 120 },
    { title: '得分', dataIndex: 'score', width: 80 },
  ];

  // 模拟数据 - 评估记录
  const assessmentRecords = reactive([
    {
      id: 1,
      name: '周凤凰',
      gender: '男',
      age: 8,
      disabilityType: '智力障碍',
      school: '成都市武侯区特殊教育学校',
      scale: '生活适应',
      assessmentCount: 3,
      status: '已完成',
      teacher: '李老师',
      score: 85,
      date: '2023-12-15',
    },
    {
      id: 2,
      name: '赵雪雪',
      gender: '女',
      age: 8,
      disabilityType: '多重残疾',
      school: '成都市武侯区特殊教育学校',
      scale: '运动康复',
      assessmentCount: 2,
      status: '进行中',
      teacher: '王老师',
      score: 72,
      date: '2023-12-10',
    },
    {
      id: 3,
      name: '李明明',
      gender: '女',
      age: 12,
      disabilityType: '精神残疾',
      school: '成都市武侯区特殊教育学校',
      scale: '生活语文',
      assessmentCount: 4,
      status: '已完成',
      teacher: '张老师',
      score: 90,
      date: '2023-12-05',
    },
    {
      id: 4,
      name: '付艺琳',
      gender: '女',
      age: 13,
      disabilityType: '精神残疾',
      school: '成都市武侯区特殊教育学校',
      scale: '生活数学',
      assessmentCount: 3,
      status: '已完成',
      teacher: '刘老师',
      score: 78,
      date: '2023-12-01',
    },
    {
      id: 5,
      name: '冯浩宇',
      gender: '男',
      age: 10,
      disabilityType: '智力障碍',
      school: '成都市武侯区特殊教育学校',
      scale: '劳动技能',
      assessmentCount: 2,
      status: '进行中',
      teacher: '陈老师',
      score: 65,
      date: '2023-11-28',
    },
    {
      id: 6,
      name: '刘翰浩',
      gender: '男',
      age: 7,
      disabilityType: '多重残疾',
      school: '成都市武侯区特殊教育学校',
      scale: '生活适应',
      assessmentCount: 1,
      status: '未开始',
      teacher: '李老师',
      score: 0,
      date: '2023-11-25',
    },
    {
      id: 7,
      name: '彭梓航',
      gender: '男',
      age: 11,
      disabilityType: '智力残疾',
      school: '成都市武侯区特殊教育学校',
      scale: '运动康复',
      assessmentCount: 3,
      status: '已完成',
      teacher: '王老师',
      score: 82,
      date: '2023-11-20',
    },
    {
      id: 8,
      name: '李彤泽',
      gender: '男',
      age: 7,
      disabilityType: '智力障碍',
      school: '成都市武侯区特殊教育学校',
      scale: '生活语文',
      assessmentCount: 2,
      status: '进行中',
      teacher: '张老师',
      score: 68,
      date: '2023-11-15',
    },
    {
      id: 9,
      name: '刘存楠',
      gender: '男',
      age: 8,
      disabilityType: '精神障碍',
      school: '成都市武侯区特殊教育学校',
      scale: '生活数学',
      assessmentCount: 4,
      status: '已完成',
      teacher: '刘老师',
      score: 92,
      date: '2023-11-10',
    },
    {
      id: 10,
      name: '邵宸汉',
      gender: '男',
      age: 6,
      disabilityType: '多重障碍',
      school: '成都市武侯区特殊教育学校',
      scale: '劳动技能',
      assessmentCount: 1,
      status: '未开始',
      teacher: '陈老师',
      score: 0,
      date: '2023-11-05',
    },
  ]);

  // 模拟数据 - 已评估学生
  const assessedStudents = reactive([
    {
      id: 1,
      name: '周凤凰',
      gender: '男',
      age: 8,
      disabilityType: '智力障碍',
      school: '成都市武侯区特殊教育学校',
      scaleCount: 3,
      teacherCount: 2,
      lastAssessmentDate: '2023-12-15',
      assessments: [
        { scaleName: '生活适应', teacher: '李老师', date: '2023-12-15', score: 85, status: '已完成' },
        { scaleName: '生活语文', teacher: '王老师', date: '2023-11-20', score: 78, status: '已完成' },
        { scaleName: '运动康复', teacher: '李老师', date: '2023-10-05', score: 82, status: '已完成' },
      ],
    },
    {
      id: 3,
      name: '李明明',
      gender: '女',
      age: 12,
      disabilityType: '精神残疾',
      school: '成都市武侯区特殊教育学校',
      scaleCount: 4,
      teacherCount: 3,
      lastAssessmentDate: '2023-12-05',
      assessments: [
        { scaleName: '生活语文', teacher: '张老师', date: '2023-12-05', score: 90, status: '已完成' },
        { scaleName: '生活数学', teacher: '刘老师', date: '2023-11-10', score: 85, status: '已完成' },
        { scaleName: '生活适应', teacher: '李老师', date: '2023-10-15', score: 88, status: '已完成' },
        { scaleName: '劳动技能', teacher: '张老师', date: '2023-09-20', score: 92, status: '已完成' },
      ],
    },
    {
      id: 4,
      name: '付艺琳',
      gender: '女',
      age: 13,
      disabilityType: '精神残疾',
      school: '成都市武侯区特殊教育学校',
      scaleCount: 3,
      teacherCount: 2,
      lastAssessmentDate: '2023-12-01',
      assessments: [
        { scaleName: '生活数学', teacher: '刘老师', date: '2023-12-01', score: 78, status: '已完成' },
        { scaleName: '生活语文', teacher: '张老师', date: '2023-11-05', score: 82, status: '已完成' },
        { scaleName: '劳动技能', teacher: '刘老师', date: '2023-10-10', score: 75, status: '已完成' },
      ],
    },
    {
      id: 7,
      name: '彭梓航',
      gender: '男',
      age: 11,
      disabilityType: '智力残疾',
      school: '成都市武侯区特殊教育学校',
      scaleCount: 3,
      teacherCount: 2,
      lastAssessmentDate: '2023-11-20',
      assessments: [
        { scaleName: '运动康复', teacher: '王老师', date: '2023-11-20', score: 82, status: '已完成' },
        { scaleName: '生活适应', teacher: '李老师', date: '2023-10-25', score: 78, status: '已完成' },
        { scaleName: '生活语文', teacher: '王老师', date: '2023-09-30', score: 75, status: '已完成' },
      ],
    },
    {
      id: 9,
      name: '刘存楠',
      gender: '男',
      age: 8,
      disabilityType: '精神障碍',
      school: '成都市武侯区特殊教育学校',
      scaleCount: 4,
      teacherCount: 2,
      lastAssessmentDate: '2023-11-10',
      assessments: [
        { scaleName: '生活数学', teacher: '刘老师', date: '2023-11-10', score: 92, status: '已完成' },
        { scaleName: '生活语文', teacher: '张老师', date: '2023-10-15', score: 88, status: '已完成' },
        { scaleName: '生活适应', teacher: '刘老师', date: '2023-09-20', score: 90, status: '已完成' },
        { scaleName: '运动康复', teacher: '张老师', date: '2023-08-25', score: 85, status: '已完成' },
      ],
    },
  ]);

  // 模拟数据 - 未评估学生
  const unassessedStudents = reactive([
    { id: 6, name: '刘翰浩', gender: '男', age: 7, disabilityType: '多重残疾', school: '成都市武侯区特殊教育学校' },
    { id: 10, name: '邵宸汉', gender: '男', age: 6, disabilityType: '多重障碍', school: '成都市武侯区特殊教育学校' },
    { id: 11, name: '王小明', gender: '男', age: 7, disabilityType: '听力残疾', school: '成都市武侯区特殊教育学校' },
    { id: 12, name: '张丽丽', gender: '女', age: 8, disabilityType: '视力残疾', school: '成都市武侯区特殊教育学校' },
    { id: 13, name: '陈小华', gender: '男', age: 7, disabilityType: '言语残疾', school: '成都市武侯区特殊教育学校' },
  ]);

  // 模拟数据 - 评估教师
  const assessmentTeachers = reactive([
    {
      id: 1,
      name: '李老师',
      school: '成都市武侯区特殊教育学校',
      assessedCount: 15,
      assessmentCount: 28,
      scaleCount: 3,
      lastAssessmentDate: '2023-12-15',
      assessments: [
        { studentName: '周凤凰', scaleName: '生活适应', date: '2023-12-15', score: 85, status: '已完成' },
        { studentName: '李明明', scaleName: '生活适应', date: '2023-10-15', score: 88, status: '已完成' },
        { studentName: '彭梓航', scaleName: '生活适应', date: '2023-10-25', score: 78, status: '已完成' },
      ],
    },
    {
      id: 2,
      name: '王老师',
      school: '成都市武侯区特殊教育学校',
      assessedCount: 12,
      assessmentCount: 22,
      scaleCount: 2,
      lastAssessmentDate: '2023-12-10',
      assessments: [
        { studentName: '赵雪雪', scaleName: '运动康复', date: '2023-12-10', score: 72, status: '进行中' },
        { studentName: '彭梓航', scaleName: '运动康复', date: '2023-11-20', score: 82, status: '已完成' },
        { studentName: '周凤凰', scaleName: '生活语文', date: '2023-11-20', score: 78, status: '已完成' },
      ],
    },
    {
      id: 3,
      name: '张老师',
      school: '成都市武侯区特殊教育学校',
      assessedCount: 18,
      assessmentCount: 32,
      scaleCount: 4,
      lastAssessmentDate: '2023-12-05',
      assessments: [
        { studentName: '李明明', scaleName: '生活语文', date: '2023-12-05', score: 90, status: '已完成' },
        { studentName: '付艺琳', scaleName: '生活语文', date: '2023-11-05', score: 82, status: '已完成' },
        { studentName: '李明明', scaleName: '劳动技能', date: '2023-09-20', score: 92, status: '已完成' },
      ],
    },
    {
      id: 4,
      name: '刘老师',
      school: '成都市武侯区特殊教育学校',
      assessedCount: 14,
      assessmentCount: 26,
      scaleCount: 3,
      lastAssessmentDate: '2023-12-01',
      assessments: [
        { studentName: '付艺琳', scaleName: '生活数学', date: '2023-12-01', score: 78, status: '已完成' },
        { studentName: '刘存楠', scaleName: '生活数学', date: '2023-11-10', score: 92, status: '已完成' },
        { studentName: '刘存楠', scaleName: '生活适应', date: '2023-09-20', score: 90, status: '已完成' },
      ],
    },
    {
      id: 5,
      name: '陈老师',
      school: '成都市武侯区特殊教育学校',
      assessedCount: 10,
      assessmentCount: 18,
      scaleCount: 2,
      lastAssessmentDate: '2023-11-28',
      assessments: [
        { studentName: '冯浩宇', scaleName: '劳动技能', date: '2023-11-28', score: 65, status: '进行中' },
        { studentName: '付艺琳', scaleName: '劳动技能', date: '2023-10-10', score: 75, status: '已完成' },
      ],
    },
  ]);

  // 模拟数据 - 评估量表
  const assessmentScales = reactive([
    {
      id: 1,
      name: '生活适应',
      category: '适应能力',
      assessedCount: 25,
      teacherCount: 4,
      lastAssessmentDate: '2023-12-15',
      assessments: [
        {
          studentName: '周凤凰',
          gender: '男',
          age: 8,
          disabilityType: '智力障碍',
          teacher: '李老师',
          date: '2023-12-15',
          score: 85,
        },
        {
          studentName: '李明明',
          gender: '女',
          age: 12,
          disabilityType: '精神残疾',
          teacher: '李老师',
          date: '2023-10-15',
          score: 88,
        },
        {
          studentName: '彭梓航',
          gender: '男',
          age: 11,
          disabilityType: '智力残疾',
          teacher: '李老师',
          date: '2023-10-25',
          score: 78,
        },
        {
          studentName: '刘存楠',
          gender: '男',
          age: 8,
          disabilityType: '精神障碍',
          teacher: '刘老师',
          date: '2023-09-20',
          score: 90,
        },
      ],
    },
    {
      id: 2,
      name: '运动康复',
      category: '运动能力',
      assessedCount: 18,
      teacherCount: 3,
      lastAssessmentDate: '2023-12-10',
      assessments: [
        {
          studentName: '赵雪雪',
          gender: '女',
          age: 8,
          disabilityType: '多重残疾',
          teacher: '王老师',
          date: '2023-12-10',
          score: 72,
        },
        {
          studentName: '彭梓航',
          gender: '男',
          age: 11,
          disabilityType: '智力残疾',
          teacher: '王老师',
          date: '2023-11-20',
          score: 82,
        },
        {
          studentName: '刘存楠',
          gender: '男',
          age: 8,
          disabilityType: '精神障碍',
          teacher: '张老师',
          date: '2023-08-25',
          score: 85,
        },
      ],
    },
    {
      id: 3,
      name: '生活语文',
      category: '语言能力',
      assessedCount: 22,
      teacherCount: 3,
      lastAssessmentDate: '2023-12-05',
      assessments: [
        {
          studentName: '李明明',
          gender: '女',
          age: 12,
          disabilityType: '精神残疾',
          teacher: '张老师',
          date: '2023-12-05',
          score: 90,
        },
        {
          studentName: '付艺琳',
          gender: '女',
          age: 13,
          disabilityType: '精神残疾',
          teacher: '张老师',
          date: '2023-11-05',
          score: 82,
        },
        {
          studentName: '周凤凰',
          gender: '男',
          age: 8,
          disabilityType: '智力障碍',
          teacher: '王老师',
          date: '2023-11-20',
          score: 78,
        },
        {
          studentName: '彭梓航',
          gender: '男',
          age: 11,
          disabilityType: '智力残疾',
          teacher: '王老师',
          date: '2023-09-30',
          score: 75,
        },
      ],
    },
    {
      id: 4,
      name: '生活数学',
      category: '数学能力',
      assessedCount: 20,
      teacherCount: 2,
      lastAssessmentDate: '2023-12-01',
      assessments: [
        {
          studentName: '付艺琳',
          gender: '女',
          age: 13,
          disabilityType: '精神残疾',
          teacher: '刘老师',
          date: '2023-12-01',
          score: 78,
        },
        {
          studentName: '刘存楠',
          gender: '男',
          age: 8,
          disabilityType: '精神障碍',
          teacher: '刘老师',
          date: '2023-11-10',
          score: 92,
        },
      ],
    },
    {
      id: 5,
      name: '劳动技能',
      category: '劳动能力',
      assessedCount: 15,
      teacherCount: 3,
      lastAssessmentDate: '2023-11-28',
      assessments: [
        {
          studentName: '冯浩宇',
          gender: '男',
          age: 10,
          disabilityType: '智力障碍',
          teacher: '陈老师',
          date: '2023-11-28',
          score: 65,
        },
        {
          studentName: '付艺琳',
          gender: '女',
          age: 13,
          disabilityType: '精神残疾',
          teacher: '陈老师',
          date: '2023-10-10',
          score: 75,
        },
        {
          studentName: '李明明',
          gender: '女',
          age: 12,
          disabilityType: '精神残疾',
          teacher: '张老师',
          date: '2023-09-20',
          score: 92,
        },
      ],
    },
  ]);

  // 模拟数据 - 计划记录
  const planRecords = reactive([
    {
      id: 1,
      semester: '2023-2024学年上学期',
      student: '周凤凰',
      createDate: '2023-09-01',
      participants: '李老师,王老师,周家长',
      parentConfirmation: true,
      collaborators: '李老师,王老师',
      status: '已完成',
    },
    {
      id: 2,
      semester: '2023-2024学年上学期',
      student: '赵雪雪',
      createDate: '2023-09-05',
      participants: '张老师,刘老师,赵家长',
      parentConfirmation: true,
      collaborators: '张老师,刘老师',
      status: '进行中',
    },
    {
      id: 3,
      semester: '2023-2024学年上学期',
      student: '李明明',
      createDate: '2023-09-10',
      participants: '李老师,陈老师,李家长',
      parentConfirmation: false,
      collaborators: '李老师,陈老师',
      status: '未开始',
    },
    {
      id: 4,
      semester: '2023-2024学年上学期',
      student: '付艺琳',
      createDate: '2023-09-15',
      participants: '王老师,张老师,付家长',
      parentConfirmation: true,
      collaborators: '王老师,张老师',
      status: '已完成',
    },
    {
      id: 5,
      semester: '2023-2024学年上学期',
      student: '冯浩宇',
      createDate: '2023-09-20',
      participants: '刘老师,李老师,冯家长',
      parentConfirmation: false,
      collaborators: '刘老师,李老师',
      status: '进行中',
    },
    {
      id: 6,
      semester: '2023-2024学年上学期',
      student: '刘翰浩',
      createDate: '2023-09-25',
      participants: '陈老师,王老师,刘家长',
      parentConfirmation: true,
      collaborators: '陈老师,王老师',
      status: '已完成',
    },
    {
      id: 7,
      semester: '2023-2024学年上学期',
      student: '彭梓航',
      createDate: '2023-09-30',
      participants: '李老师,张老师,彭家长',
      parentConfirmation: false,
      collaborators: '李老师,张老师',
      status: '未开始',
    },
    {
      id: 8,
      semester: '2023-2024学年上学期',
      student: '李彤泽',
      createDate: '2023-10-05',
      participants: '王老师,刘老师,李家长',
      parentConfirmation: true,
      collaborators: '王老师,刘老师',
      status: '进行中',
    },
    {
      id: 9,
      semester: '2023-2024学年上学期',
      student: '刘存楠',
      createDate: '2023-10-10',
      participants: '张老师,陈老师,刘家长',
      parentConfirmation: true,
      collaborators: '张老师,陈老师',
      status: '已完成',
    },
    {
      id: 10,
      semester: '2023-2024学年上学期',
      student: '邵宸汉',
      createDate: '2023-10-15',
      participants: '李老师,王老师,邵家长',
      parentConfirmation: false,
      collaborators: '李老师,王老师',
      status: '未开始',
    },
  ]);

  // 模拟数据 - 实施记录
  const implementationRecords = reactive([
    {
      id: 1,
      school: '成都市武侯区特殊教育学校',
      teacherName: '李老师',
      class: '一年级1班',
      grade: '一年级',
      submittedCount: 25,
      weeklyHours: 18,
      originalCount: 18,
      referencedCount: 7,
    },
    {
      id: 2,
      school: '成都市武侯区特殊教育学校',
      teacherName: '王老师',
      class: '一年级2班',
      grade: '一年级',
      submittedCount: 22,
      weeklyHours: 16,
      originalCount: 15,
      referencedCount: 7,
    },
    {
      id: 3,
      school: '成都市武侯区特殊教育学校',
      teacherName: '张老师',
      class: '二年级1班',
      grade: '二年级',
      submittedCount: 28,
      weeklyHours: 20,
      originalCount: 20,
      referencedCount: 8,
    },
    {
      id: 4,
      school: '成都市武侯区特殊教育学校',
      teacherName: '刘老师',
      class: '二年级2班',
      grade: '二年级',
      submittedCount: 24,
      weeklyHours: 18,
      originalCount: 16,
      referencedCount: 8,
    },
    {
      id: 5,
      school: '成都市武侯区特殊教育学校',
      teacherName: '陈老师',
      class: '三年级1班',
      grade: '三年级',
      submittedCount: 26,
      weeklyHours: 20,
      originalCount: 18,
      referencedCount: 8,
    },
    {
      id: 6,
      school: '成都市锦江区特殊教育学校',
      teacherName: '赵老师',
      class: '一年级1班',
      grade: '一年级',
      submittedCount: 20,
      weeklyHours: 16,
      originalCount: 14,
      referencedCount: 6,
    },
    {
      id: 7,
      school: '成都市锦江区特殊教育学校',
      teacherName: '钱老师',
      class: '一年级2班',
      grade: '一年级',
      submittedCount: 18,
      weeklyHours: 14,
      originalCount: 12,
      referencedCount: 6,
    },
    {
      id: 8,
      school: '成都市锦江区特殊教育学校',
      teacherName: '孙老师',
      class: '二年级1班',
      grade: '二年级',
      submittedCount: 22,
      weeklyHours: 18,
      originalCount: 15,
      referencedCount: 7,
    },
    {
      id: 9,
      school: '成都市青羊区特殊教育学校',
      teacherName: '周老师',
      class: '一年级1班',
      grade: '一年级',
      submittedCount: 19,
      weeklyHours: 16,
      originalCount: 14,
      referencedCount: 5,
    },
    {
      id: 10,
      school: '成都市青羊区特殊教育学校',
      teacherName: '吴老师',
      class: '二年级1班',
      grade: '二年级',
      submittedCount: 21,
      weeklyHours: 18,
      originalCount: 16,
      referencedCount: 5,
    },
  ]);

  // 详情模态框状态
  const showStudentDetailModal = ref(false);
  const showTeacherDetailModal = ref(false);
  const showScaleDetailModal = ref(false);
  const selectedStudent = ref(null);
  const selectedTeacher = ref(null);
  const selectedScale = ref(null);

  // 切换标签页
  const switchTab = (tab) => {
    currentTab.value = tab;
    currentListView.value = 'default';
  };

  // 显示已评估学生列表
  const showAssessedStudents = () => {
    currentListView.value = 'assessedStudents';
  };

  // 显示未评估学生列表
  const showUnassessedStudents = () => {
    currentListView.value = 'unassessedStudents';
  };

  // 显示评估教师列表
  const showAssessmentTeachers = () => {
    currentListView.value = 'assessmentTeachers';
  };

  // 显示评估量表列表
  const showAssessmentScales = () => {
    currentListView.value = 'assessmentScales';
  };

  // 显示学生详情
  const showStudentDetail = (student) => {
    selectedStudent.value = student;
    showStudentDetailModal.value = true;
  };

  // 显示教师详情
  const showTeacherDetail = (teacher) => {
    selectedTeacher.value = teacher;
    showTeacherDetailModal.value = true;
  };

  // 显示量表详情
  const showScaleDetail = (scale) => {
    selectedScale.value = scale;
    showScaleDetailModal.value = true;
  };

  // 获取列表标题
  const getListTitle = () => {
    switch (currentListView.value) {
      case 'assessedStudents':
        return '已评估学生列表';
      case 'unassessedStudents':
        return '未评估学生列表';
      case 'assessmentTeachers':
        return '参与评估教师列表';
      case 'assessmentScales':
        return '评估量表列表';
      default:
        return '学生评估列表';
    }
  };

  // 获取障碍类型对应的颜色
  const getDisabilityColor = (type) => {
    const disabilityType = disabilityTypes.find((d) => d.type === type);
    return disabilityType ? disabilityType.color : 'gray';
  };

  // 获取状态对应的颜色
  const getStatusColor = (status) => {
    switch (status) {
      case '已完成':
        return 'green';
      case '进行中':
        return 'orange';
      case '未开始':
        return 'red';
      default:
        return 'gray';
    }
  };

  // 计算完成百分比
  const getCompletionPercent = (stats) => {
    const total = stats.notStarted + stats.inProgress + stats.completed;
    return Math.round((stats.completed / total) * 100);
  };
</script>

<style scoped>
  :deep(.arco-table-th) {
    background-color: #f5f7fa;
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-table-td) {
    color: #4e5969;
  }

  :deep(.arco-card-header) {
    padding: 12px 16px;
  }

  :deep(.arco-card-body) {
    padding: 16px;
  }

  :deep(.arco-progress-text) {
    font-size: 12px;
  }

  :deep(.custom-table .arco-table-container) {
    border-radius: 0;
  }

  :deep(.custom-table .arco-table-tr:hover) {
    background-color: #f5f7fa;
  }

  :deep(.arco-btn-text:hover) {
    background-color: rgba(var(--primary-6), 0.05);
  }

  :deep(.custom-modal .arco-modal-header) {
    border-bottom: 1px solid #f2f3f5;
  }

  :deep(.arco-list-item) {
    padding: 12px 0;
  }
</style>
