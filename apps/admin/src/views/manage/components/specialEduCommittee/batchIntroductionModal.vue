<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:visible']);

  const modelValue = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  const size = 'mini';
  const filterType = ref('过滤');
  const committee = ref();
  const boList = ref([]);

  const filters = ref<any>({ schools: {} });
  const currentType = ref();
  const loadTypes = async () => {
    const { data: res } = await request('/resourceCenter/fusionSchool/getCompanyNatureList', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    filters.value.types = res.items;
  };

  const loadBoList = async () => {
    try {
      const { data: res } = await request(`/org/branchOffice/specialCommittee`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      boList.value = res.items.map((item) => ({
        label: item.name,
        value: item.id,
        raw: item,
      }));
    } catch (e) {
      console.error(e);
    }
  };

  const loadSchool = async (type: string) => {
    filters.value.school = null;
    currentType.value = type;
    if (!filters.value?.schools[type]) {
      const { data: res } = await request('/resourceCenter/fusionSchool', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          nature: type,
        },
      });
      filters.value.schools[type] = res.items;
    }
  };

  const branchStudentsCatch = ref({});
  const studentList = ref([]);
  const filterStudentList = ref([]);

  const handleSchoolChange = async (val) => {
    if (branchStudentsCatch[val]) {
      studentList.value = branchStudentsCatch[val];
    } else {
      const { data: res } = await request(`/resourceRoom/student/searchBoStudents/${Number(val)}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      branchStudentsCatch.value[val] = res.items;
      studentList.value = res.items;
      filterStudentList.value = res.items.map((item) => ({
        label: item.name,
        value: item.id,
        raw: item,
      }));
    }
  };

  const currentDisorders = ref();
  const disorders = computed(() => {
    const category = new Set<string>();
    studentList.value.forEach((item) => {
      if (item.disorders) category.add(item.disorders);
    });
    return category;
  });

  const handleDisordersChange = (val) => {
    filterStudentList.value = studentList.value
      .filter((item) => item.disorders === val)
      .map((item) => ({
        label: item.name,
        value: item.id,
        raw: item,
      }));
  };

  const targetStudents = ref();
  const handleTransfer = (val) => {
    targetStudents.value = val;
  };

  const targetStudentInfo: any = computed(() => {
    if (!targetStudents.value || !branchStudentsCatch.value) return [];
    const allStudents = Object.values(branchStudentsCatch.value).flat().filter(Boolean);
    return allStudents.filter((student: any) => targetStudents.value.includes(student.id));
  });

  const handleFilterByGender = (val) => {
    switch (val) {
      case '男':
      case '女': {
        currentType.value = val;
        filterStudentList.value = studentList.value
          .filter((item) => {
            if (!currentDisorders.value) {
              return true;
            }
            return item.raw?.disorders === currentDisorders.value;
          })
          .filter((item) => item.raw.gender === val);
        break;
      }
      default: {
        if (currentDisorders.value)
          filterStudentList.value = studentList.value.filter((item) => item.raw?.disorders === currentDisorders.value);
        else filterStudentList.value = studentList.value;
        break;
      }
    }
  };

  const handleDle = async (studentId: number) => {
    if (!targetStudents.value) return;
    const index = targetStudents.value.indexOf(studentId);
    if (index !== -1) {
      targetStudents.value.splice(index, 1);
    }
  };

  const handleCommitteeChange = (val) => {
    committee.value = boList.value.find((item) => item.value === val);
  };

  const resetData = () => {};

  const handlePreOk = async () => {
    try {
      if (!committee.value?.value || !committee.value.label) {
        Message.warning('请选择专委会');
        return false;
      }
      const data = {
        committee: {
          id: committee.value.value,
          name: committee.value.label,
        },
        studentIds: targetStudentInfo.value.map((item: any) => item.id),
      };
      await request('/resourceRoom/student/updateCommittee', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data,
      });
      resetData();
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  };

  onMounted(async () => {
    await Promise.all([loadTypes(), loadBoList()]);
  });
</script>

<template>
  <a-modal v-model:visible="modelValue" :on-before-ok="handlePreOk" width="50%" title="学生引入">
    <a-form auto-label-width>
      <div class="w-full flex space-x-2">
        <a-form-item required>
          <a-select placeholder="请选择专委会" :options="boList" :size="size" @change="handleCommitteeChange" />
        </a-form-item>
        <a-form-item required>
          <a-select :size="size" :options="filters?.types" placeholder="请选择学校类型" @change="loadSchool" />
        </a-form-item>
        <a-form-item required>
          <a-select
            v-model="filters.school"
            :size="size"
            :options="filters.schools[currentType]"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择学校"
            @change="handleSchoolChange"
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model="currentDisorders"
            :size="size"
            :options="disorders"
            placeholder="请选择障碍类型"
            @change="handleDisordersChange"
          />
        </a-form-item>
      </div>
    </a-form>

    <a-divider />

    <!--优化: 增加一个全选-->
    <div class="w-full flex">
      <div class="w-1/2 pr-4">
        <a-transfer
          show-search
          class="w-full h-full"
          :data="filterStudentList"
          :default-value="targetStudents"
          @change="handleTransfer"
        >
          <template #source-title>
            <div class="flex justify-between items-center">
              <span>学生列表</span>
              <a-dropdown v-if="false" trigger="hover" @select="handleFilterByGender">
                <a-button :size="size">
                  <template #icon>
                    <icon-filter />
                  </template>
                  {{ filterType }}
                </a-button>
                <template #content>
                  <a-doption value="男">男生</a-doption>
                  <a-doption value="女">女生</a-doption>
                  <a-doption value="不限">不限</a-doption>
                </template>
              </a-dropdown>
            </div>
          </template>
          <template #target-title>
            <div class="flex justify-between items-center">
              <span>选中学生</span>
              <a-dropdown v-if="false" trigger="hover">
                <a-button :size="size">
                  <template #icon>
                    <icon-filter />
                  </template>
                  过滤
                </a-button>
                <template #content>
                  <a-doption>男生</a-doption>
                  <a-doption>女生</a-doption>
                  <a-doption>不限</a-doption>
                </template>
              </a-dropdown>
            </div>
          </template>
        </a-transfer>
      </div>

      <a-divider direction="vertical" class="h-auto mx-4" />

      <div class="w-1/2 pl-4">
        <div class="h-[300px] overflow-auto border rounded py-2 px-4 relative">
          <div class="flex flex-col gap-2">
            <div
              v-for="student in targetStudentInfo"
              :key="student.id"
              class="w-full px-1 py-1 items-center flex justify-around bg-gray-50 rounded relative group"
            >
              <span>
                <span class="text-xs text-gray-400">姓名：</span>
                {{ student.name }}
              </span>
              <span>
                <span class="text-xs text-gray-400">性别：</span>
                {{ student.gender }}
              </span>
              <span>
                <span class="text-xs text-gray-400">年龄：</span>
                {{ student.age }}
              </span>
              <span class="mr-2">
                <span class="text-xs text-gray-400">障碍类型：</span>
                {{ student.disorders }}
              </span>
              <a-tooltip content="删除">
                <icon-close-circle
                  class="absolute right-1 hidden text-red-500 group-hover:block"
                  @click="handleDle(student.id)"
                />
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
