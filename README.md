## 1. Branches
- test: unstable feature updates
- release: stable feature updates
- bugfix-xx: bug fixes
- feature-xx: new features

## 2. Pushing
- 新特性请转到 feature-xx 分支
- 紧急BUG修复请从 release 分支拉出 bugfix-xx 分支，修复后合并到 release 分支
- 紧急BUG修复后请将 bugfix-xx 分支合并到 test 分支
- test 分支的代码测试后可以合并到 release 分支

## 3. CI/CD
- 只有 test 分支和 release 分支会触发 CI/CD
- test 分支需要修改 ver 文件后才会触发 CI/CD
- release 分支需要 git tag 后才会触发 CI/CD
- 永远不要手动打 tag
- 永远只在 release 分支上打 tag
- 小程序目前通过云效触发 CI/CD

## 4. Tagging
- Tag 格式: v1.0.0
- Java 使用 Maven Release Plugin 打 tag
- Go 使用 python ./release.py 打 tag
- 前端使用 pnpm run release:xxx 打 tag

### Java
```Shell
# merge test -> release
# switch to release branch then run:
$ [git release] mvn release: # 将自动将SNAPSHOT版本号改为release版本号 打tag 并提交到git
$ [git release] mvn release:clean # 清理release:prepare的临时文件

# checkout test branch
# 将 release 分支合并到 test 分支
```

### Go
```Shell
# merge test -> release
# switch to release branch then run:
$ [git release] python ./release.py (major|minor|patch) # major|minor|patch 更新版本号并打tag push
```

### Frontend
```Shell
# merge test -> release
# switch to release branch then run:
$ [git release] pnpm run release:[major|minor|patch] # major|minor|patch 更新版本号并打tag push
```

## 5. PC Client
```shell
# 切换环境
$ pnpm run switch:env --pc --env=[dev|whdev|v2cloud] # 切换环境 后续应改为从 配置文件仓库拉取配置
$ pnpm run build:deploy # vite 打包
$ pnpm run pack:deploy # electron 打包

# 然后上传到 oss 并发布
```

## 6. Weapp

### Dev
```shell
# 切换环境
$ cd /apps/weapp
$ pnpm run switch:[v2|wh] # v2 或 wh 两个不同的小程序 其他的需要自定义修改
$ pnpm run dev:mp-weixin 

# 小程序开发者工具中打开 dist/dev/mp-weixin 目录
```

### CI/CD
- 直接 push 至代码库，云效会自动触发 CI/CD
- 云效会自动将代码打包并上传到小程序后台
- 程序根据开发版/体验版/正式版自动选择相应配置

## Database Migration
- ON DELETE CASCADE: (all @ManyToMany)
  - teacher_teaching_config_teaching_research_group
  - teacher_teaching_config_teaching_subject