{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"]}, "build:deploy": {"dependsOn": ["^build:deploy"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "build:test": {"dependsOn": ["^build:test"], "outputs": [".next/**", "!.next/cache/**"]}, "build:whdev": {"dependsOn": ["^build:whdev"], "outputs": []}, "build:whprod": {"dependsOn": ["^build:whprod"], "outputs": []}, "build:v2cloud": {"dependsOn": ["^build:v2cloud"], "outputs": []}, "lint": {"dependsOn": ["^lint"]}, "dev": {"dependsOn": ["^dev"], "cache": false, "persistent": true}, "build:pc": {"dependsOn": ["^build:pc"], "cache": false}, "dev:mp-weixin": {"dependsOn": ["^dev:mp-weixin"], "cache": false, "persistent": true}, "dev:h5": {"dependsOn": ["^dev:h5"], "cache": false, "persistent": true}, "release": {"dependsOn": ["build:deploy"], "outputs": ["dist/**"]}}}