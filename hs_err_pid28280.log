#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=28280, tid=23952
#
# JRE version:  (17.0.11+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 

Host: Intel(R) Core(TM) i5-10400F CPU @ 2.90GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon Mar 31 14:32:05 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4974) elapsed time: 0.024170 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000015ee035e760):  JavaThread "Unknown thread" [_thread_in_vm, id=23952, stack(0x0000002272900000,0x0000002272a00000)]

Stack: [0x0000002272900000,0x0000002272a00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6877f9]
V  [jvm.dll+0x8411aa]
V  [jvm.dll+0x842e2e]
V  [jvm.dll+0x843493]
V  [jvm.dll+0x249fdf]
V  [jvm.dll+0x6845c9]
V  [jvm.dll+0x678e7a]
V  [jvm.dll+0x30ab4b]
V  [jvm.dll+0x311ff6]
V  [jvm.dll+0x361a5e]
V  [jvm.dll+0x361c8f]
V  [jvm.dll+0x2e0978]
V  [jvm.dll+0x2e18e4]
V  [jvm.dll+0x811c71]
V  [jvm.dll+0x36f7c8]
V  [jvm.dll+0x7f05f6]
V  [jvm.dll+0x3f398f]
V  [jvm.dll+0x3f5541]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffb9c45efd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000015ee040c250 GCTaskThread "GC Thread#0" [stack: 0x0000002272a00000,0x0000002272b00000] [id=19900]
  0x0000015efe000270 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002272b00000,0x0000002272c00000] [id=27244]
  0x0000015efe000aa0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002272c00000,0x0000002272d00000] [id=4476]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffb9bc11547]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000015ee035a710] Heap_lock - owner thread: 0x0000015ee035e760

Heap address: 0x0000000601000000, size: 8176 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x0000015ef4030000,0x0000015ef5030000] _byte_map_base: 0x0000015ef1028000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000015ee040c780, (CMBitMap*) 0x0000015ee040c7c0
 Prev Bits: [0x0000015ef6030000, 0x0000015efdff0000)
 Next Bits: [0x0000015e80000000, 0x0000015e87fc0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff691a20000 - 0x00007ff691a2a000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.exe
0x00007ffc35850000 - 0x00007ffc35a67000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffc34d60000 - 0x00007ffc34e24000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffc32e30000 - 0x00007ffc33201000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffc33210000 - 0x00007ffc33321000 	C:\Windows\System32\ucrtbase.dll
0x00007ffc23100000 - 0x00007ffc23117000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jli.dll
0x00007ffc19580000 - 0x00007ffc1959b000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\VCRUNTIME140.dll
0x00007ffc33980000 - 0x00007ffc33b31000 	C:\Windows\System32\USER32.dll
0x00007ffc206f0000 - 0x00007ffc20982000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffc32990000 - 0x00007ffc329b6000 	C:\Windows\System32\win32u.dll
0x00007ffc33620000 - 0x00007ffc336c7000 	C:\Windows\System32\msvcrt.dll
0x00007ffc35450000 - 0x00007ffc35479000 	C:\Windows\System32\GDI32.dll
0x00007ffc32a80000 - 0x00007ffc32b9b000 	C:\Windows\System32\gdi32full.dll
0x00007ffc32d90000 - 0x00007ffc32e2a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffc335d0000 - 0x00007ffc33601000 	C:\Windows\System32\IMM32.DLL
0x00007ffc308d0000 - 0x00007ffc308dc000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\vcruntime140_1.dll
0x00007ffc0bf60000 - 0x00007ffc0bfed000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\msvcp140.dll
0x00007ffb9b920000 - 0x00007ffb9c5a3000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server\jvm.dll
0x00007ffc34f90000 - 0x00007ffc35041000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffc33520000 - 0x00007ffc335c7000 	C:\Windows\System32\sechost.dll
0x00007ffc32c20000 - 0x00007ffc32c48000 	C:\Windows\System32\bcrypt.dll
0x00007ffc35330000 - 0x00007ffc35444000 	C:\Windows\System32\RPCRT4.dll
0x00007ffc2af20000 - 0x00007ffc2af29000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffc31840000 - 0x00007ffc3188d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffc28a50000 - 0x00007ffc28a84000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffc350f0000 - 0x00007ffc35161000 	C:\Windows\System32\WS2_32.dll
0x00007ffc2cd70000 - 0x00007ffc2cd7a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffc31820000 - 0x00007ffc31833000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffc31ad0000 - 0x00007ffc31ae8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffc2a330000 - 0x00007ffc2a33a000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jimage.dll
0x00007ffc30180000 - 0x00007ffc303b2000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffc35480000 - 0x00007ffc35810000 	C:\Windows\System32\combase.dll
0x00007ffc34850000 - 0x00007ffc34927000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffc2bb60000 - 0x00007ffc2bb92000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffc32ba0000 - 0x00007ffc32c1b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffc23120000 - 0x00007ffc23145000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 
java_class_path (initial): D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/plugins/vcs-git/lib/git4idea-rt.jar;D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8573157376                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8573157376                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\APPHOME\JDK11\jdk-11.0.2\bin;D:\APPHOME\nodejs;D:\APPHOME\nodejs;D:\APPHOME\maven3.9\bin;D:\APPHOME\maven3.6.3\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\Bandizip;D:\APPHOME\WechatDevTools\΢��web�����߹���\dll;D:\APPHOME\python_3;D:\APPHOME\python_3\Scripts;D:\APPHOME\geckodriver;D:\APPHOME\MYSQL8.0.4\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\APPHOME\WebStrom\WebStorm 2024.2.1\bin;D:\APPHOME\GoLand 2024.1.6\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13320K (0% of 33485756K total physical memory with 860284K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 18 days 11:00 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt
Processor Information for all 12 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32700M (840M free)
TotalPageFile size 63107M (AvailPageFile size 75M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 65M, peak: 577M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30) for windows-amd64 JRE (17.0.11+1-b1207.30), built on 2024-07-12 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
