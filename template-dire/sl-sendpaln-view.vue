<script setup lang="ts">
import {ref, inject, PropType, onMounted, watch} from 'vue';

const customizeComponentViewTemplate = inject('customizeComponentViewTemplate');
const PROJECT_URLS = inject('PROJECT_URLS')
const request = inject('request')
const inputTable= inject('inputTable')

const props = defineProps({
  modelValue: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
  record: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
})
const sendPlanShuangLiuSchema = ref<any[]>([
  {
    description: '基本信息',
    fields: [
      {
        label: '学生姓名',
        required: true,
        field: 'studentId',
        inputType: 'select',
        inputWidgetProps: {
          labelField: 'name',
          valueField: 'id',
          api:'/resourceRoom/student',
          getOptions: async () => {
            const res = await request('/resourceRoom/student', {
              baseURL: PROJECT_URLS,
              method: 'GET',
            });
            return res.data.items;
          },
        },
        options: [],
      },
    ],
  },
  {
    description: '障碍信息',
    descriptionColumn: 3,
    fields: [
      {
        label: '视力障碍',
        field: 'visualImpairment',
        inputType: 'radio',
        max: 1,
        options: [
          { label: '全盲', value: 1 },
          { label: '低视力', value: 2 },
        ],
      },
      {
        label: '听力障碍',
        field: 'hearingImpairment',
        inputType: 'radio',
        max: 1,
        options: [
          { label: '左耳分贝', value: 1 },
          { label: '右耳分贝', value: 2 },
        ],
      },
      {
        label: '残疾类别',
        span: 12,
        field: 'disCategory',
        inputType: 'radio', // 'checkbox',
        max: 1,
        options: [
          { label: '言语障碍', value: 1 },
          { label: '肢体障碍', value: 2 },
          { label: '自闭症', value: 3 },
          { label: '精神障碍', value: 4 },
          { label: '多重障碍', value: 5 },
        ],
      },
      { label: '残疾证号', field: 'disNum', inputType: 'text' },
      { label: '致残原因', span: 12, field: 'disReason', inputType: 'text' },
    ],
  },
  {
    description: '其它',
    fields: [
      {
        span: 24,
        label: '辅助用具',
        field: 'others',
        inputType: 'checkbox',
        options: [
          { label: '盲杖', value: 1 },
          { label: '助视器', value: 2 },
          { label: '电子耳蜗植入', value: 3 },
          { label: 'FM调频', value: 4 },
          { label: '轮椅', value: 5 },
          { label: '拐杖', value: 6 },
          { label: '义肢', value: 7 },
          { label: '其它', value: 8 },
        ],
      },
    ],
  },
  {
    description: '发展及适应能力现状',
    orientation: 'center',
    component: {
      inputWidget: inputTable,
      field: 'present',
      data: [
        { project: '健康状况', ability: '', way: '', people: '', date: null },
        { project: '感官知觉', ability: '', way: '', people: '', date: null },
        { project: '粗大动作', ability: '', way: '', people: '', date: null },
        { project: '精细动作', ability: '', way: '', people: '', date: null },
        { project: '生活自理', ability: '', way: '', people: '', date: null },
        { project: '认知（语数）', ability: '', way: '', people: '', date: null },
        { project: '沟通', ability: '', way: '', people: '', date: null },
        { project: '情绪入社会行为', ability: '', way: '', people: '', date: null },
      ],
      columns: [
        { title: '项目', align: 'center', dataIndex: 'project', type: 'title', allowDbClick: true },
        { title: '能力现状', align: 'center', dataIndex: 'ability', type: 'textarea' },
        { title: '评量方式', align: 'center', dataIndex: 'way', type: 'text' },
        {
          title: '评量者',
          align: 'center',
          dataIndex: 'people',
          type: 'select',
          labelField: 'name', // 选择的展示信息
          valueField: 'id', // 选择的值
          options: [],
          api:'/resourceRoom/student',
          getOptions: async () => {
            const res = await request('/org/companyUser', {
              baseURL: PROJECT_URLS,
              method: 'GET',
            });
            return res.data.items;
          },
        },
        { title: '评量日期', align: 'center', dataIndex: 'date', type: 'date', width: 200 },
        // { title: '操作', align: 'center', slotName: 'operation', width: 120 },
      ],
    },
  },
  {
    description: '综合分析与建议',
    orientation: 'center',
    fields: [{ label: '优势能力分析', span: 24, field: 'strengthsAnalysis', inputType: 'textarea', required: true}],
  },
  {
    description: '教育需求分析',
    fields: [
      { label: '健康状况', span: 12, field: 'healthStatus', inputType: 'textarea' },
      { label: '感官功能', span: 12, field: 'sensoryFunctions', inputType: 'textarea' },
      { label: '知觉动作', span: 12, field: 'perceptionMovement', inputType: 'textarea' },
      { label: '生活自理', span: 12, field: 'selfCare', inputType: 'textarea' },
      { label: '认知', span: 12, field: 'cognition', inputType: 'textarea' },
      { label: '沟通', span: 12, field: 'communication', inputType: 'textarea' },
      { label: '情绪及社会行为', span: 12, field: 'emotionsSocialBehavior', inputType: 'textarea' },
    ],
  },
  {
    description: '送教计划',
    orientation: 'center',
    fields: [],
    component: {
      field: 'sendPlan',
      inputWidget: inputTable,
      data: [],
      orderVisible:true,
      columns: [
        { title: '月份', align: 'center', dataIndex: 'month', type: 'rangeDate', allowDbClick: false, width: 250,required: true },
        { title: '目标任务', align: 'center', dataIndex: 'task', type: 'text' },
        { title: '教学策略', align: 'center', dataIndex: 'strategy', type: 'textarea' },
        {
          title: '执行人',
          align: 'center',
          dataIndex: 'executor',
          type: 'select',
          labelField: 'name',
          valueField: 'id',
          options: [],
          api:'/org/companyUser',
          getOptions: async () => {
            const res = await request('/org/companyUser', {
              baseURL: PROJECT_URLS,
              method: 'GET',
            });
            return res.data.items;
          },
        },
        { title: '效果评估', align: 'center', dataIndex: 'evaluation', type: 'text' },
        { title: '操作', align: 'center', slotName: 'operation', width: 120 },
      ],
    },
  },
]);
const record=ref()
onMounted(()=>{
  record.value = {...props.record};
})

</script>

<template>
  <customizeComponentViewTemplate
      v-model="record"
      :form-schema="sendPlanShuangLiuSchema"
  />
</template>

<style scoped lang="scss"></style>