<script setup lang="ts">
import {inject, onMounted, PropType, ref} from 'vue';

const request = inject('request');
const PROJECT_URLS = inject('PROJECT_URLS');

const props = defineProps({
  currentPlan: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
  schema: {
    type: Object,
    required: true,
  },
});

const RecordDetail = inject('RecordDetail');

const detailFields = [
  {key: 'visualImpairment', label: '视力障碍'},
  {key: 'hearingImpairment', label: '听力障碍'},
  {key: 'disCategory', label: '障碍类别'},
  {key: 'reason', label: '致残原因'},
  {key: 'disabilityIdNumber', label: '残疾证号'},
  {key: 'assistiveDevices', label: '辅助用具'},
  {key: 'suggestion', label: '优势能力分析', span: 4},
  {key: 'healthStatus', label: '健康状况', span: 4},
  {key: 'sensoryFunction', label: '感官功能', span: 4},
  {key: 'perceptualMotor', label: '知觉动作', span: 4},
  {key: 'selfCare', label: '生活自理', span: 4},
  {key: 'cognition', label: '认知', span: 4},
  {key: 'communication', label: '沟通', span: 4},
  {key: 'emotionalSocialBehavior', label: '情绪及社会行为', span: 4},
];
const solutionsInputColumns = [
  {dataIndex: 'item', title: '项目'},
  {dataIndex: 'advantages', title: '优势'},
  {dataIndex: 'limit', title: '限制'},
  {dataIndex: 'evaluationMethod', title: '评量方式'},
  {dataIndex: 'evaluator', title: '评量者'},
  {dataIndex: 'evaluateTime', title: '评量日期'},
];
const handleDataToDisplay = (key: string, record: any): any => {
  const disCategoryList = ['言语障碍', '肢体障碍', '自闭症', '精神障碍', '多重障碍'];
  const visualImpairmentList = ['正常', '全盲', '低视力'];
  const assistiveDevicesList = ['盲杖', '助视器', '电子耳蜗植入', 'оFM调频', '轮椅', '拐杖', '义肢', '其它'];
  const val = record?.additionalData?.[key];

  switch (key) {
    case 'visualImpairment':
      if (typeof val !== 'number' || val < 0 || val >= visualImpairmentList.length) return visualImpairmentList[0];
      return visualImpairmentList[val];
    case 'hearingImpairment':
      return `左耳：${val?.left || ''}， 右耳：${val?.right || ''}`;
    case 'disCategory':
      if (Array.isArray(val)) {
        return val
            .map((item: number) => disCategoryList[item - 1])
            .filter(Boolean)
            .join(', ');
      }
      return '';
    case 'assistiveDevices':
      if (Array.isArray(val)) {
        return val
            .map((item: number) => assistiveDevicesList[item - 1])
            .filter(Boolean)
            .join(', ');
      }
      return '';
    default:
      return val;
  }
};
</script>

<template>
  <!--<div>
      <record-detail ref="recordDetailRef" :raw="currentPlan" :schema="schema">
        <template #default="{ detailData }">
          <a-descriptions :column="4" bordered class="mt-4">
            <a-descriptions-item
                v-for="(item, index) in detailFields"
                :key="index"
                :span="item.span || 1"
                :label="item.label"
            >
              {{ handleDataToDisplay(item.key, detailData) }}
            </a-descriptions-item>
            <a-descriptions-item label="发展及适应能力现状" :span="4">
              <a-table
                  :data="detailData.additionalData?.['solutions'] || []"
                  :columns="solutionsInputColumns"
                  :bordered="{ cell: true }"
              />
            </a-descriptions-item>
          </a-descriptions>
        </template>
      </record-detail>
    </div>-->
  <a-descriptions :column="3" bordered>
    <a-descriptions-item :span="1" label="姓名">
      <span>{{ currentPlan?.student.name }}</span>
    </a-descriptions-item>
    <a-descriptions-item :span="1" label="性别">
      <span>{{ currentPlan?.student.gender }}</span>
    </a-descriptions-item>
    <a-descriptions-item :span="1" label="民族">
      <span>{{ currentPlan?.student.nation }}</span>
    </a-descriptions-item>

    <a-descriptions-item :span="1" label="出生日期">
      <span>{{ currentPlan?.student.birthday }}</span>
    </a-descriptions-item>
    <a-descriptions-item :span="1" label="身份证号">
      <span>{{ currentPlan?.student.idCardNo }}</span>
    </a-descriptions-item>
    <a-descriptions-item :span="1" label="残疾证号">
      <span>{{ currentPlan?.student.disabilityCertificateNo }}</span>
    </a-descriptions-item>

    <a-descriptions-item :span="1" label="残疾等级">
      <span>{{ currentPlan?.student.disabilityLevel }}</span>
    </a-descriptions-item>
    <a-descriptions-item :span="1" label="残疾类别">
      <span>{{ currentPlan?.student.disorders }}</span>
    </a-descriptions-item>

  </a-descriptions>
  <a-descriptions :column="4" bordered class="mt-4">
    <a-descriptions-item
        v-for="(item, index) in detailFields"
        :key="index"
        :span="item.span || 1"
        :label="item.label"
    >
      {{ handleDataToDisplay(item.key, detailData) }}
    </a-descriptions-item>
  </a-descriptions>

  <a-divider orientation="left">发展及适应能力现状</a-divider>
  <a-table
      :data="currentPlan.additionalData?.['solutions'] || []"
      :columns="solutionsInputColumns"
      :bordered="{ cell: true }"
  />
</template>

<style scoped lang="scss"></style>
