# 定义流水线阶段
stages:
  - build
  - deploy

# 默认配置
default:
  timeout: 20m
  image: docker.1ms.run/node:20
  tags:
    - build

# 生产环境规则
.prod-rules:
  rules:
    - if: $CI_COMMIT_TAG
      when: on_success
    - when: never

# 测试环境规则
.test-rules:
  rules:
    - if: $CI_COMMIT_BRANCH == "test" && $CI_PIPELINE_SOURCE == "push"
      changes:
        - ver
      when: always
    - when: never

# 基础构建产物配置
.artifacts-base:
  artifacts:
    paths:
      - web-client-*.tgz
    expire_in: 1 week
    when: on_success
    #name: "${ENV_NAME}"

# 配置获取模板
.get-config:
  before_script:
    - |
      set -eo pipefail
      echo "🚀 开始执行配置获取..."
      
      # 环境变量验证
      for var in ENV_NAME CI_PROJECT_ID CI_JOB_TOKEN CONFIG_PROJECT_ID PROJECT_ACCESS_TOKEN; do
        [[ -z "${!var}" ]] && echo "❌ 错误：${var} 未设置" && exit 1
      done
      echo "✅ 环境变量验证通过"
      
      echo "🔍 当前环境: ${ENV_NAME}"
      echo "📌 当前分支: ${CI_COMMIT_REF_NAME}"
      
      # 获取配置文件
      REQUEST_URL="https://gitlab.tejiaoedu.com:19443/api/v4/projects/${CONFIG_PROJECT_ID}/repository/files/${ENV_NAME}%2F.env.deploy/raw?ref=main"
      if ! curl --insecure --fail -H "PRIVATE-TOKEN: ${PROJECT_ACCESS_TOKEN}" "${REQUEST_URL}" -o env/.env.deploy; then
        echo "❌ 配置文件获取失败（退出码: $?）"
        exit 1
      fi

# 构建基础模板
.build-template:
  extends: .artifacts-base
  cache:
    key:
      files:
        - pnpm-lock.yaml
        - package.json
      prefix: ${CI_COMMIT_REF_SLUG}-web
    paths:
      - node_modules/.pnpm
    policy: pull-push
    when: on_success
  script:
    - echo "📦 开始构建 ${ENV_NAME} 环境 (仅 Web 应用)..."
    - corepack enable
    - pnpm --version
    - echo "🧹 清理不需要的 Workspace 目录 (electron, preload, hover-ball, desktop-widget)..."
    - rm -rf apps/electron apps/preload apps/hover-ball apps/desktop-widget
    - echo "✅ 清理完成"
    - echo "🚀 安装 Web 应用相关依赖..."
    - pnpm install --frozen-lockfile --ignore-scripts
    - echo "🚀 构建 Web 应用..."
    # !!! 请将下面的包名替换为你实际的包名 !!!
    - NODE_OPTIONS="--max-old-space-size=4096" pnpm dotenv -e env/.env.deploy -- pnpm turbo run build:deploy --concurrency=2 --filter=linch-se-admin --filter=linch-se-teacher --filter=linch-se-operator
    - echo "📦 打包构建产物..."
    - |
      mkdir -p temp_dist/apps
      for app in admin operator teacher; do
        if [ -d "apps/$app/dist" ]; then
          echo "📦 复制 $app 应用..."
          mkdir -p "temp_dist/apps/$app"
          cp -r "apps/$app/dist" "temp_dist/apps/$app/"
        fi
      done
      cd temp_dist
      tar -czf "../web-client-$ENV_NAME.tgz" ./*
      cd ..
    - echo "✅ 构建完成"
  timeout: 20m

# 部署基础模板
.deploy-template:
  stage: deploy
  image: docker.1ms.run/alpine:3.19
  retry:
    max: 2
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk add --no-cache openssh-client sshpass jq
    - mkdir -p ~/.ssh
    - echo "StrictHostKeyChecking no" >> ~/.ssh/config
  script:
    - echo "🚀 开始部署 $ENV_NAME 环境..."
    - |
      # 定义重试函数
      retry_command() {
        local max_attempts=3
        local attempt=1
        local delay=5
        local command="$1"
        
        while [ $attempt -le $max_attempts ]; do
          echo "🔄 执行命令(尝试 $attempt/$max_attempts)..."
          if eval "$command"; then
            return 0
          else
            if [ $attempt -lt $max_attempts ]; then
              echo "⚠️ 命令失败，${delay}秒后重试..."
              sleep $delay
              delay=$((delay * 2))
            fi
          fi
          attempt=$((attempt + 1))
        done
        
        echo "❌ 命令执行失败，已达到最大重试次数"
        return 1
      }
      # 解析环境配置
      CONFIG_VAR="${ENV_NAME}_CONFIG"
      eval "CONFIG_JSON=\$$CONFIG_VAR"
      SERVER_HOST=$(printf '%s' "$CONFIG_JSON" | jq -r '.host')
      SERVER_PORT=$(printf '%s' "$CONFIG_JSON" | jq -r '.port')
      SERVER_USER=$(printf '%s' "$CONFIG_JSON" | jq -r '.user')
      SERVER_PASSWORD=$(printf '%s' "$CONFIG_JSON" | jq -r '.password')
      
      # 项目类型识别
      PROJECT_TYPE="web"
      DEPLOY_PATH=$(printf '%s' "$CONFIG_JSON" | jq -r ".services.${PROJECT_TYPE}.deploy_path")
      PACKAGE_PATH=$(printf '%s' "$CONFIG_JSON" | jq -r ".services.${PROJECT_TYPE}.package_path")
      PACKAGE_NAME=$(printf '%s' "$CONFIG_JSON" | jq -r ".services.${PROJECT_TYPE}.package_name")

      # 部署文件
      echo "📂 创建目录并设置权限..."
      retry_command "sshpass -p '$SERVER_PASSWORD' ssh -p $SERVER_PORT '$SERVER_USER@$SERVER_HOST' \"
        echo '$SERVER_PASSWORD' | sudo -S mkdir -p $PACKAGE_PATH $DEPLOY_PATH && \
        echo '$SERVER_PASSWORD' | sudo -S chown -R $SERVER_USER:$SERVER_USER $PACKAGE_PATH $DEPLOY_PATH && \
        echo '$SERVER_PASSWORD' | sudo -S chmod -R 755 $PACKAGE_PATH $DEPLOY_PATH
      \""

      # 上传构建产物
      echo "📤 上传构建产物..."
      retry_command "sshpass -p '$SERVER_PASSWORD' scp -P $SERVER_PORT \
        '${PACKAGE_NAME}-${ENV_NAME}.tgz' \
        '$SERVER_USER@$SERVER_HOST:$PACKAGE_PATH/'"

      # 执行部署命令
      echo "🔧 执行部署命令..."
      retry_command "sshpass -p '$SERVER_PASSWORD' ssh -p $SERVER_PORT '$SERVER_USER@$SERVER_HOST' \"
        rm -rf $DEPLOY_PATH/* && \
        tar -zxvf $PACKAGE_PATH/${PACKAGE_NAME}-${ENV_NAME}.tgz -C $DEPLOY_PATH && \
        chmod -R 755 $DEPLOY_PATH
      \""

    - echo "✅ 部署完成"
# 环境构建模板
.env-build:
  stage: build
  extends:
    - .get-config
    - .build-template

# 环境部署模板
.env-deploy:
  extends: .deploy-template

# 测试环境配置
test-build:
  extends:
    - .env-build
    - .test-rules
  variables:
    ENV_NAME: "test"

test-deploy:
  extends:
    - .env-deploy
    - .test-rules
  variables:
    ENV_NAME: "test"
  needs:
    - test-build

# 生产环境 - 武侯
wuhou-build:
  extends:
    - .env-build
    - .prod-rules
  variables:
    ENV_NAME: "wuhou"

wuhou-deploy:
  extends:
    - .env-deploy
    - .prod-rules
  variables:
    ENV_NAME: "wuhou"
  needs:
    - wuhou-build

# 生产环境 - V2Cloud
v2cloud-build:
  extends:
    - .env-build
    - .prod-rules
  variables:
    ENV_NAME: "v2cloud"

v2cloud-deploy:
  extends:
    - .env-deploy
    - .prod-rules
  variables:
    ENV_NAME: "v2cloud"
  needs:
    - v2cloud-build