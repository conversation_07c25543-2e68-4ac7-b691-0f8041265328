#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=43024, tid=44400
#
# JRE version:  (17.0.11+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 

Host: Intel(R) Core(TM) i5-10400F CPU @ 2.90GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Thu May 29 11:45:40 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.028436 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000249c4eaffb0):  JavaThread "Unknown thread" [_thread_in_vm, id=44400, stack(0x000000cad5300000,0x000000cad5400000)]

Stack: [0x000000cad5300000,0x000000cad5400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6877f9]
V  [jvm.dll+0x8411aa]
V  [jvm.dll+0x842e2e]
V  [jvm.dll+0x843493]
V  [jvm.dll+0x249fdf]
V  [jvm.dll+0x6845c9]
V  [jvm.dll+0x678e7a]
V  [jvm.dll+0x30ab4b]
V  [jvm.dll+0x311ff6]
V  [jvm.dll+0x361a5e]
V  [jvm.dll+0x361c8f]
V  [jvm.dll+0x2e0978]
V  [jvm.dll+0x2e18e4]
V  [jvm.dll+0x811c71]
V  [jvm.dll+0x36f7c8]
V  [jvm.dll+0x7f05f6]
V  [jvm.dll+0x3f398f]
V  [jvm.dll+0x3f5541]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffa62c9efd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000249ecab0910 GCTaskThread "GC Thread#0" [stack: 0x000000cad5400000,0x000000cad5500000] [id=44104]
  0x00000249ecac3600 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000cad5500000,0x000000cad5600000] [id=40796]
  0x00000249ecac46d0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000cad5600000,0x000000cad5700000] [id=44156]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa62451547]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000249c4eab580] Heap_lock - owner thread: 0x00000249c4eaffb0

Heap address: 0x0000000601000000, size: 8176 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000601000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x00000249daaa0000,0x00000249dbaa0000] _byte_map_base: 0x00000249d7a98000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000249ecab0d40, (CMBitMap*) 0x00000249ecab0d80
 Prev Bits: [0x00000249dcaa0000, 0x00000249e4a60000)
 Next Bits: [0x00000249e4a60000, 0x00000249eca20000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff742cd0000 - 0x00007ff742cda000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.exe
0x00007ffad0f50000 - 0x00007ffad1167000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffad0550000 - 0x00007ffad0614000 	C:\Windows\System32\KERNEL32.DLL
0x00007fface230000 - 0x00007fface603000 	C:\Windows\System32\KERNELBASE.dll
0x00007fface740000 - 0x00007fface851000 	C:\Windows\System32\ucrtbase.dll
0x00007ffab2350000 - 0x00007ffab236b000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\VCRUNTIME140.dll
0x00007ffa80540000 - 0x00007ffa80557000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jli.dll
0x00007ffad0630000 - 0x00007ffad07e1000 	C:\Windows\System32\USER32.dll
0x00007ffacebb0000 - 0x00007ffacebd6000 	C:\Windows\System32\win32u.dll
0x00007ffabf240000 - 0x00007ffabf4db000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffacf370000 - 0x00007ffacf417000 	C:\Windows\System32\msvcrt.dll
0x00007ffacf4a0000 - 0x00007ffacf4c9000 	C:\Windows\System32\GDI32.dll
0x00007fface610000 - 0x00007fface732000 	C:\Windows\System32\gdi32full.dll
0x00007ffacea50000 - 0x00007ffaceaea000 	C:\Windows\System32\msvcp_win.dll
0x00007ffacf160000 - 0x00007ffacf191000 	C:\Windows\System32\IMM32.DLL
0x00007ffac8720000 - 0x00007ffac872c000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\vcruntime140_1.dll
0x00007ffaa0300000 - 0x00007ffaa038d000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\msvcp140.dll
0x00007ffa62160000 - 0x00007ffa62de3000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server\jvm.dll
0x00007ffacebe0000 - 0x00007ffacec91000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffad0020000 - 0x00007ffad00c7000 	C:\Windows\System32\sechost.dll
0x00007fface860000 - 0x00007fface888000 	C:\Windows\System32\bcrypt.dll
0x00007ffacf1a0000 - 0x00007ffacf2b4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffacced0000 - 0x00007ffaccf1d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffac5600000 - 0x00007ffac5634000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffaa2e50000 - 0x00007ffaa2e59000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffac8050000 - 0x00007ffac805a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffacf500000 - 0x00007ffacf571000 	C:\Windows\System32\WS2_32.dll
0x00007ffacceb0000 - 0x00007ffaccec3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffacd170000 - 0x00007ffacd188000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffaa2e40000 - 0x00007ffaa2e4a000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\jimage.dll
0x00007ffacb810000 - 0x00007ffacba42000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffad0990000 - 0x00007ffad0d23000 	C:\Windows\System32\combase.dll
0x00007ffaced70000 - 0x00007ffacee47000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffac71d0000 - 0x00007ffac7202000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fface040000 - 0x00007fface0bb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa7fe90000 - 0x00007ffa7feb5000 	D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;D:\APPHOME\WebStrom\WebStorm-2024.1.6.win\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.tejiaoedu.com:19443': 
java_class_path (initial): D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/plugins/vcs-git/lib/git4idea-rt.jar;D:/APPHOME/WebStrom/WebStorm-2024.1.6.win/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8573157376                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8573157376                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\libexec\git-core;D:\APPHOME\Git\Git\mingw64\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\APPHOME\JDK11\jdk-11.0.2\bin;D:\APPHOME\nodejs;D:\APPHOME\nodejs;D:\APPHOME\maven3.9\bin;D:\APPHOME\maven3.6.3\bin;D:\APPHOME\Git\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\Bandizip;D:\APPHOME\WechatDevTools\΢��web�����߹���\dll;D:\APPHOME\python_3;D:\APPHOME\python_3\Scripts;D:\APPHOME\geckodriver;D:\APPHOME\MYSQL8.0.4\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\APPHOME\WebStrom\WebStorm 2024.2.1\bin;D:\APPHOME\GoLand 2024.1.6\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13240K (0% of 33485756K total physical memory with 2025264K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 14 days 16:13 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt
Processor Information for all 12 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32700M (1977M free)
TotalPageFile size 63094M (AvailPageFile size 239M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 64M, peak: 576M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.30) for windows-amd64 JRE (17.0.11+1-b1207.30), built on 2024-07-12 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
